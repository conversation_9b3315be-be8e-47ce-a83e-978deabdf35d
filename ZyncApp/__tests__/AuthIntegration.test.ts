/**
 * Authentication Integration Test
 * 
 * This test verifies that the ZyncApp frontend can successfully integrate
 * with the Zync_workspace backend authentication endpoints.
 */

import { authServices } from '../src/services/authServices';
import { userServices } from '../src/services/userServices';
import apiController from '../src/services/apiController';

// Mock data for testing
const testUser = {
  name: 'Test User',
  username: 'testuser',
  email: '<EMAIL>',
  password: 'testpassword123'
};

const testOtp = '12345';

describe('Authentication Integration', () => {
  beforeAll(() => {
    // Ensure we're using the correct backend URL
    console.log('Testing against backend:', process.env.BACKEND_API_URL || 'http://localhost:3333');
  });

  describe('API Controller Configuration', () => {
    test('should have correct base URL with /api prefix', () => {
      // Check that the API controller is configured correctly
      expect(apiController).toBeDefined();
      // Note: We can't directly access the private baseURL, but we can verify the instance exists
    });
  });

  describe('Authentication Flow', () => {
    test('should have all required authentication methods', () => {
      // Verify all required methods exist
      expect(typeof authServices.login).toBe('function');
      expect(typeof authServices.register).toBe('function');
      expect(typeof authServices.verifyRegisterOtp).toBe('function');
      expect(typeof authServices.regenerateRegisterOTP).toBe('function');
      expect(typeof authServices.activeAccount).toBe('function');
      expect(typeof authServices.forgotPassword).toBe('function');
      expect(typeof authServices.verifyForgotPasswordOtp).toBe('function');
      expect(typeof authServices.logout).toBe('function');
      expect(typeof authServices.verifyEmailBeforeRegister).toBe('function');
    });

    test('should have correct method signatures', () => {
      // Test that methods accept the expected parameters
      expect(authServices.login).toHaveLength(1); // credentials
      expect(authServices.register).toHaveLength(1); // userData
      expect(authServices.verifyRegisterOtp).toHaveLength(1); // otpData
      expect(authServices.regenerateRegisterOTP).toHaveLength(1); // email
      expect(authServices.activeAccount).toHaveLength(1); // email
      expect(authServices.forgotPassword).toHaveLength(1); // data
      expect(authServices.verifyForgotPasswordOtp).toHaveLength(1); // data
      expect(authServices.logout).toHaveLength(0); // no parameters
    });
  });

  describe('User Services', () => {
    test('should have all required user management methods', () => {
      // Verify user service methods exist
      expect(typeof userServices.getUserProfile).toBe('function');
      expect(typeof userServices.updateUserProfile).toBe('function');
      expect(typeof userServices.uploadAvatar).toBe('function');
      expect(typeof userServices.getAllUsers).toBe('function');
      expect(typeof userServices.getSingleUser).toBe('function');
      expect(typeof userServices.changePassword).toBe('function');
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      // Test with invalid credentials to check error handling
      const result = await authServices.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Token Management', () => {
    test('should have token storage methods', () => {
      expect(typeof authServices.getStoredToken).toBe('function');
      expect(typeof authServices.getCurrentUser).toBe('function');
      expect(typeof authServices.isAuthenticated).toBe('function');
    });
  });
});

describe('Integration Test Instructions', () => {
  test('should provide setup instructions', () => {
    const instructions = `
    To test the full integration:
    
    1. Start the Zync_workspace backend:
       cd Zync_workspace
       npm start (or appropriate start command)
    
    2. Ensure the backend is running on http://localhost:3333
    
    3. Test the authentication flow:
       - Email verification: POST /api/auth/verify-email
       - Registration: POST /api/auth/register  
       - OTP verification: POST /api/auth/verify-register-otp
       - Login: POST /api/auth/login
       - Profile: GET /api/auth/get-profile
    
    4. Check the backend logs for any errors
    
    5. Verify the frontend can successfully:
       - Register a new user
       - Verify email with OTP
       - Login with credentials
       - Access protected endpoints
    `;
    
    console.log(instructions);
    expect(instructions).toContain('Zync_workspace');
  });
});
