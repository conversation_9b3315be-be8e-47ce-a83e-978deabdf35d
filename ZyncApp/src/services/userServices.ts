import apiController from './apiController';

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  pic?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserData {
  name?: string;
  username?: string;
  email?: string;
}

// Note: User preferences are not implemented in the current backend
// This interface is kept for future implementation if needed
export interface UserPreferences {
  // Will be implemented when backend supports it
}

class UserServices {
  // Get current user profile
  public async getUserProfile(userId?: number) {
    try {
      if (userId) {
        // Get specific user info
        const response = await apiController.get<User>(`/auth/get-user-info?id=${userId}`);
        return response;
      } else {
        // Get current user profile
        const response = await apiController.get<User>('/auth/get-profile');
        return response;
      }
    } catch (error) {
      console.error('Get user profile error:', error);
      throw error;
    }
  }

  // Update user profile
  public async updateUserProfile(userData: UpdateUserData) {
    try {
      // Note: Backend expects user ID in the request, but we'll use current user
      const response = await apiController.post<{ success: boolean }>('/auth/update-user-profile', userData);
      return response;
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Upload user avatar/thumbnail
  public async uploadAvatar(imageFile: any) {
    try {
      const response = await apiController.uploadFile<{ data: any }>('/auth/set-thumbnail', imageFile);
      return response;
    } catch (error) {
      console.error('Upload avatar error:', error);
      throw error;
    }
  }

  // Note: Backend doesn't have separate delete avatar endpoint
  // Avatar can be updated by uploading a new one

  // Get all users (for admin/search purposes)
  public async getAllUsers() {
    try {
      const response = await apiController.get<User[]>('/auth/get-all-user');
      return response;
    } catch (error) {
      console.error('Get all users error:', error);
      throw error;
    }
  }

  // Get single user by ID
  public async getSingleUser(userId: number) {
    try {
      const response = await apiController.get<User>(`/auth/get-single-user?id=${userId}`);
      return response;
    } catch (error) {
      console.error('Get single user error:', error);
      throw error;
    }
  }

  // Change password (through profile update)
  public async changePassword(currentPassword: string, newPassword: string) {
    try {
      const response = await apiController.post('/auth/update-user-profile', {
        oldPassword: currentPassword,
        password: newPassword,
      });
      return response;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // Note: The following features are not available in the current backend:
  // - User preferences
  // - Search users
  // - Contacts management
  // - Block/unblock users
  // - Online status
  // - Delete account
  // These would need to be implemented in the backend if required
}

// Export singleton instance
export const userServices = new UserServices();
export default userServices;
