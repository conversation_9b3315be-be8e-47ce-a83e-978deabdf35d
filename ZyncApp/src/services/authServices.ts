import apiController from './apiController';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  username: string;
  email: string;
  password: string;
  authProvider?: string;
}

export interface AuthResponse {
  message: string;
  second_message?: string;
  token: string;
  authExpireTime: string;
  user: {
    id: number;
    name: string;
    username: string;
    email: string;
    is_email_verified: boolean;
    isActive?: boolean;
    role_id: number;
  };
  otp?: string;
  otpExpireAt?: string;
  otpExpireTime?: string;
  email_sent?: boolean;
  is_active?: number;
  auth_provider?: string;
}

export interface OtpVerificationData {
  email: string;
  otp: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

export interface ApiError {
  message: string;
  error?: string;
}

class AuthServices {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'user_data';

  // Login user
  public async login(credentials: LoginCredentials) {
    try {
      // Map email to username field for backend compatibility
      const loginData = {
        email: credentials.email,
        password: credentials.password,
        device: 'android' // Set device type for mobile app
      };

      const response = await apiController.post<AuthResponse>('/auth/login', loginData);

      if (response.success && response.data) {
        // Handle successful login
        if (response.data.token) {
          await this.storeAuthData(response.data);
          apiController.setAuthToken(response.data.token);
          return {
            success: true,
            data: response.data,
          };
        }

        // Handle inactive account case
        if (response.data.is_active === 0) {
          return {
            success: true,
            data: response.data,
          };
        }
      }

      return {
        success: false,
        error: response.error || response.message || 'Login failed',
      };
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle backend error responses
      if (error.response?.data) {
        const responseData = error.response.data;

        // Check if this is an email verification case
        if (responseData.is_active === 0 && responseData.user) {
          return {
            success: true,
            data: responseData,
          };
        }

        // Check if this is an auth provider mismatch case
        if (responseData.auth_provider && responseData.is_active === 0) {
          return {
            success: true,
            data: responseData,
          };
        }
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Verify email before registration
  public async verifyEmailBeforeRegister(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/verify-email', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Email verification failed',
        };
      }
    } catch (error: any) {
      console.error('Email verification error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Register user
  public async register(userData: RegisterData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/register', userData);

      if (response.success && response.data) {
        // Registration successful, but user needs email verification
        // Don't store auth data yet since account is not active
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Registration failed',
        };
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Verify Registration OTP
  public async verifyRegisterOtp(otpData: OtpVerificationData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/verify-register-otp', otpData);

      if (response.success && response.data) {
        // Store auth data after successful verification
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'OTP verification failed',
        };
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Regenerate Registration OTP
  public async regenerateRegisterOTP(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/regenerate-otp', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to regenerate OTP',
        };
      }
    } catch (error: any) {
      console.error('Regenerate OTP error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Activate Account (for email verification flow)
  public async activeAccount(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/active-account', { email });

      if (response.success && response.data) {
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to activate account',
        };
      }
    } catch (error: any) {
      console.error('Account activation error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Regenerate Forgot Password OTP (reuse forgot password endpoint)
  public async regenerateForgotPasswordOTP(email: string) {
    try {
      // Backend doesn't have separate regenerate endpoint, so we call forgot-password again
      const response = await apiController.post<AuthResponse>('/auth/forgot-password', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to regenerate OTP',
        };
      }
    } catch (error: any) {
      console.error('Regenerate forgot password OTP error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Forgot password
  public async forgotPassword(data: ForgotPasswordData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/forgot-password', data);

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to send reset email',
        };
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Note: Backend doesn't have separate OTP verification endpoint
  // This functionality is combined with password reset in verifyForgotPasswordOtp method

  // Verify Forgot Password OTP and Reset Password
  public async verifyForgotPasswordOtp(data: ResetPasswordData) {
    try {
      // Map to backend expected format
      const resetData = {
        email: data.email,
        token: data.otp,
        password: data.newPassword
      };

      const response = await apiController.post<AuthResponse>('/auth/reset-pwd', resetData);

      if (response.success && response.data) {
        // Password reset successful, user can now login
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Password reset failed',
        };
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }



  // Logout user
  public async logout() {
    try {
      // Backend doesn't have logout endpoint, so just clear local data
      await this.clearAuthData();
      apiController.removeAuthToken();
      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Logout error:', error);
      // Clear local data even if there's an error
      await this.clearAuthData();
      apiController.removeAuthToken();
      return {
        success: true, // Still return success since local data is cleared
      };
    }
  }

  // Note: Backend doesn't have refresh token endpoint, so we'll remove this method
  // If needed in the future, it can be added when backend supports it

  // Get current user
  public async getCurrentUser() {
    try {
      const userData = await AsyncStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Check if user is authenticated
  public async isAuthenticated(): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem(this.TOKEN_KEY);
      return !!token;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  // Store authentication data
  private async storeAuthData(authData: AuthResponse) {
    try {
      await AsyncStorage.multiSet([
        [this.TOKEN_KEY, authData.token],
        [this.USER_KEY, JSON.stringify(authData.user)],
      ]);
    } catch (error) {
      console.error('Store auth data error:', error);
      throw error;
    }
  }

  // Clear authentication data
  private async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        this.TOKEN_KEY,
        this.USER_KEY,
      ]);
    } catch (error) {
      console.error('Clear auth data error:', error);
      throw error;
    }
  }

  // Get stored token
  public async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error('Get stored token error:', error);
      return null;
    }
  }
}

// Export singleton instance
export const authServices = new AuthServices();
export default authServices;
