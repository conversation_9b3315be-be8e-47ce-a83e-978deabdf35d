# ZyncApp Authentication Integration with Zync_workspace Backend

This document outlines the integration between the ZyncApp React Native frontend and the Zync_workspace backend authentication system.

## Overview

The ZyncApp has been successfully integrated with the Zync_workspace backend to use centralized authentication APIs instead of integrated/local authentication services.

## Backend Endpoints Integrated

### Authentication Endpoints (Basic Only)

#### 🔑 Login Flow
- **POST /api/auth/login**
  - Basic login with username/email + password
  - Returns JWT token
  - Handles inactive account scenarios

#### 📝 Registration Flow
- **POST /api/auth/verify-email**
  - Send email verification OTP (before registration)
  - Checks if email is already registered

- **POST /api/auth/register**
  - User registration – creates new user account
  - Account starts as inactive until email verification

- **POST /api/auth/verify-register-otp**
  - Verify registration OTP to complete email verification
  - Activates account and returns login token

- **POST /api/auth/regenerate-otp**
  - Resend registration OTP if the first one expired

- **POST /api/auth/active-account**
  - Activate account after verification
  - Alternative activation method

#### 🔒 Password Reset Flow
- **POST /api/auth/forgot-password**
  - Request password reset – sends reset token via email

- **POST /api/auth/reset-pwd**
  - Reset password with token – complete password reset

#### 👤 User Management
- **GET /api/auth/get-profile**
  - Get current user profile

- **POST /api/auth/update-user-profile**
  - Update user profile information

- **POST /api/auth/set-thumbnail**
  - Upload user avatar/profile picture

## Frontend Changes Made

### 1. API Controller Configuration
- **File**: `src/services/apiController.ts`
- **Changes**: 
  - Updated base URL to include `/api` prefix for Zync_workspace backend
  - Configured to use `BACKEND_API_URL=http://localhost:3333`

### 2. Authentication Service Refactoring
- **File**: `src/services/authServices.ts`
- **Changes**:
  - Updated all authentication methods to use new backend endpoints
  - Modified login to handle email-based authentication
  - Updated registration flow to handle email verification
  - Mapped frontend data structures to backend expected formats
  - Removed unsupported endpoints and added fallbacks

### 3. User Services Integration
- **File**: `src/services/userServices.ts`
- **Changes**:
  - Updated user data interfaces to match backend schema
  - Integrated with backend user management endpoints
  - Removed unsupported features (preferences, contacts, etc.)
  - Added proper error handling

### 4. Backend Endpoint Additions
- **File**: `Zync_workspace/apps/api/src/app/controllers/auth/auth.controller.ts`
- **Added Methods**:
  - `register()` - User registration with email verification
  - `verifyRegisterOtp()` - OTP verification for registration
  - `regenerateOtp()` - Regenerate registration OTP
  - `activeAccount()` - Account activation
  - Updated `login()` to handle email authentication and inactive accounts

## Environment Configuration

The frontend is configured to connect to the backend via:

```env
BACKEND_API_URL=http://localhost:3333
```

## Authentication Flow

### Registration Flow
1. **Email Verification**: `POST /api/auth/verify-email`
2. **Registration**: `POST /api/auth/register`
3. **OTP Verification**: `POST /api/auth/verify-register-otp`
4. **Account Activation**: Automatic or `POST /api/auth/active-account`

### Login Flow
1. **Login**: `POST /api/auth/login`
2. **Handle Response**: 
   - Success: Store token and user data
   - Inactive Account: Redirect to email verification
   - Error: Display error message

### Password Reset Flow
1. **Request Reset**: `POST /api/auth/forgot-password`
2. **Reset Password**: `POST /api/auth/reset-pwd`

## Testing

A comprehensive test suite has been created:
- **File**: `__tests__/AuthIntegration.test.ts`
- **Coverage**: API configuration, method signatures, error handling

## TFA Integration (Future)

The backend supports Two-Factor Authentication (TFA) but it's not integrated in the frontend yet. This will be added later as mentioned in the requirements.

## Notes

1. **Removed Features**: Some frontend features were removed as they're not supported by the current backend:
   - User preferences
   - Contact management
   - Block/unblock users
   - Online status
   - Account deletion

2. **Backend Compatibility**: The integration follows the same pattern used in the `propped` project for consistency.

3. **Error Handling**: Comprehensive error handling has been implemented to gracefully handle backend errors and network issues.

## Next Steps

1. Start the Zync_workspace backend server
2. Test the authentication flow end-to-end
3. Implement TFA integration when ready
4. Add any missing user management features as needed
