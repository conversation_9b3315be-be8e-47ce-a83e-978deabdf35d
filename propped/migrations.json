{"migrations": [{"version": "21.0.0-beta.8", "description": "Removes the legacy cache configuration from nx.json", "implementation": "./src/migrations/update-21-0-0/remove-legacy-cache", "package": "nx", "name": "remove-legacy-cache"}, {"version": "21.0.0-beta.8", "description": "Removes the legacy cache configuration from nx.json", "implementation": "./src/migrations/update-21-0-0/remove-custom-tasks-runner", "package": "nx", "name": "remove-custom-tasks-runner"}, {"version": "21.0.0-beta.11", "description": "Updates release version config based on the breaking changes in Nx v21", "implementation": "./src/migrations/update-21-0-0/release-version-config-changes", "package": "nx", "name": "release-version-config-changes"}, {"version": "21.0.0-beta.11", "description": "Updates release changelog config based on the breaking changes in Nx v21", "implementation": "./src/migrations/update-21-0-0/release-changelog-config-changes", "package": "nx", "name": "release-changelog-config-changes"}, {"cli": "nx", "version": "21.0.0-beta.11", "description": "Remove isolatedConfig option for @nx/webpack:webpack", "implementation": "./src/migrations/update-21-0-0/remove-isolated-config", "package": "@nx/webpack", "name": "update-21-0-0-remove-isolated-config"}, {"cli": "nx", "version": "21.0.0-beta.3", "description": "Set the `continuous` option to `true` for continuous tasks.", "factory": "./src/migrations/update-21-0-0/set-continuous-option", "package": "@nx/angular", "name": "set-continuous-option"}]}