import mongoose from 'mongoose';
import { checkEthnicQuota } from '../packages/api/src/app/controllers/hdb/check-ethnic-quota.fun';
import { HDBDataModel } from '../packages/api/src/app/controllers/hdb/hdb.model';

const BATCH_SIZE = 50;

async function updateAllSellerEthnicData() {
  const total = await HDBDataModel.countDocuments();
  await mongoose.connect(
    'mongodb+srv://proptech_dev:<EMAIL>/proptech',
  );
  console.log(`Total HDB documents: ${total}`);

  for (let skip = 0; skip < total; skip += BATCH_SIZE) {
    const docs = await HDBDataModel.find().skip(skip).limit(BATCH_SIZE).lean();

    for (const doc of docs) {
      try {
        if (
          !doc.blockNumber ||
          !doc.title ||
          !doc.blockNumber.substring(4) ||
          !doc.title.split(',')[1]
        ) {
          console.warn(`Skipping doc with _id=${doc._id} due to missing blockNumber or title`);
          continue;
        }

        const blockId = doc.blockNumber.substring(4);
        const street = doc.title.split(',')[1].trim();
        const groups = [
          { citizenship: 'SC', ethnicGrp: 'C' },
          { citizenship: 'SC', ethnicGrp: 'M' },
          { citizenship: 'SC', ethnicGrp: 'I' },
          { citizenship: 'NSPR', ethnicGrp: 'C' },
          { citizenship: 'NSPR', ethnicGrp: 'M' },
          { citizenship: 'NSPR', ethnicGrp: 'I' },
        ];
        const data = [];
        for (const { ethnicGrp, citizenship } of groups) {
          const output = await checkEthnicQuota({
            enquiryBy: 'SLR',
            blockId,
            street,
            ethnicGrp,
            citizenship,
          });
          data.push(output);
        }

        // Call the function (make sure getSellerEthnicGroupData is exported as a function)

        await HDBDataModel.updateOne({ _id: doc._id }, { $set: { sellerEthnicData: data } });
        console.log(`Updated _id=${doc._id}`);
      } catch (err) {
        console.error(`Failed to update sellerEthnicData for HDB _id=${doc._id}:`, err);
      }
    }
    console.log(`Processed batch: ${skip} - ${skip + docs.length}`);
  }

  await mongoose.disconnect();
  console.log('Done!');
}

updateAllSellerEthnicData().catch((err) => {
  console.error('Script failed:', err);
  process.exit(1);
});
