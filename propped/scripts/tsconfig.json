{"extends": "../tsconfig.base.json", "files": [], "include": [], "references": [{"path": "./tsconfig.tools.json"}], "compilerOptions": {"target": "ESNext", "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": false, "strictInputAccessModifiers": false, "strictTemplates": false}}