{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "strict": false, "importHelpers": true, "target": "esnext", "module": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@api/*": ["packages/api/src/app/*"], "@lib/angular/*": ["packages/shared/src/lib/angular/*"], "@lib/common/*": ["packages/shared/src/lib/common/*"], "@lib/motors/*": ["packages/shared/src/lib/motors/*"], "@lib/web/*": ["packages/shared/src/lib/web/*"], "@package/api/*": ["packages/api/*"], "@web/*": ["packages/web/src/app/*"]}}, "exclude": ["node_modules", "tmp"]}