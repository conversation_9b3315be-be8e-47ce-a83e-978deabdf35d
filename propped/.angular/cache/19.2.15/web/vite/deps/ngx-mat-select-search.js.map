{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+material@19.2.18_285bc3c224c839ebf1057ffc6879888d/node_modules/@angular/material/fesm2022/checkbox.mjs", "../../../../../../node_modules/.pnpm/@angular+material@19.2.18_285bc3c224c839ebf1057ffc6879888d/node_modules/@angular/material/fesm2022/progress-spinner.mjs", "../../../../../../node_modules/.pnpm/ngx-mat-select-search@8.0.2_8961980176f5f8532d9306e9e32b79d9/node_modules/ngx-mat-select-search/fesm2022/ngx-mat-select-search.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ElementRef, ChangeDetectorRef, NgZone, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-grv62mCZ.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source;\n  /** The new `checked` value of the checkbox. */\n  checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n  };\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  ariaLabel = '';\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  ariaLabelledby = null;\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  ariaDescribedby;\n  /**\n   * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n   */\n  ariaExpanded;\n  /**\n   * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n   */\n  ariaControls;\n  /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n  ariaOwns;\n  _uniqueId;\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  id;\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n  required;\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  labelPosition = 'after';\n  /** Name value will be applied to the input element if present */\n  name = null;\n  /** Event emitted when the checkbox's `checked` value changes. */\n  change = new EventEmitter();\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  indeterminateChange = new EventEmitter();\n  /** The value attribute of the native input element */\n  value;\n  /** Whether the checkbox has a ripple. */\n  disableRipple;\n  /** The native `<input type=\"checkbox\">` element */\n  _inputElement;\n  /** The native `<label>` element */\n  _labelElement;\n  /** Tabindex for the checkbox. */\n  tabIndex;\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the checkbox. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the checkbox should remain interactive when it is disabled. */\n  disabledInteractive;\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  _currentAnimationClass = '';\n  _currentCheckState = TransitionCheckState.Init;\n  _controlValueAccessorChangeFn = () => {};\n  _validatorChangeFn = () => {};\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = this._options?.disabledInteractive ?? false;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _checked = false;\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _disabled = false;\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate;\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n    this._syncIndeterminate(this._indeterminate);\n  }\n  _indeterminate = false;\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (this.disabled && this.disabledInteractive || !this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static ɵfac = function MatCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCheckbox,\n    selectors: [[\"mat-checkbox\"]],\n    viewQuery: function MatCheckbox_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-checkbox\"],\n    hostVars: 16,\n    hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked)(\"mat-mdc-checkbox-disabled-interactive\", ctx.disabledInteractive);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n      ariaExpanded: [2, \"aria-expanded\", \"ariaExpanded\", booleanAttribute],\n      ariaControls: [0, \"aria-controls\", \"ariaControls\"],\n      ariaOwns: [0, \"aria-owns\", \"ariaOwns\"],\n      id: \"id\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      labelPosition: \"labelPosition\",\n      name: \"name\",\n      value: \"value\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n      color: \"color\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      checked: [2, \"checked\", \"checked\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute]\n    },\n    outputs: {\n      change: \"change\",\n      indeterminateChange: \"indeterminateChange\"\n    },\n    exportAs: [\"matCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 15,\n    vars: 23,\n    consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n    template: function MatCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onTouchTargetClick());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"input\", 6, 1);\n        i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onBlur());\n        })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInputClick());\n        })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"div\", 7);\n        i0.ɵɵelementStart(7, \"div\", 8);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 9);\n        i0.ɵɵelement(9, \"path\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(10, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"label\", 13, 2);\n        i0.ɵɵprojection(14);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const checkbox_r2 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n        i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"aria-controls\", ctx.ariaControls)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? true : null)(\"aria-expanded\", ctx.ariaExpanded)(\"aria-owns\", ctx.ariaOwns)(\"name\", ctx.name)(\"value\", ctx.value);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"for\", ctx.inputId);\n      }\n    },\n    dependencies: [MatRipple, _MatInternalFormField],\n    styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    ariaExpanded: [{\n      type: Input,\n      args: [{\n        alias: 'aria-expanded',\n        transform: booleanAttribute\n      }]\n    }],\n    ariaControls: [{\n      type: Input,\n      args: ['aria-controls']\n    }],\n    ariaOwns: [{\n      type: Input,\n      args: ['aria-owns']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatCheckboxRequiredValidator_BaseFactory;\n    return function MatCheckboxRequiredValidator_Factory(__ngFactoryType__) {\n      return (ɵMatCheckboxRequiredValidator_BaseFactory || (ɵMatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatCheckboxRequiredValidator)))(__ngFactoryType__ || MatCheckboxRequiredValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCheckboxRequiredValidator,\n    selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n      providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR]\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n  static ɵfac = function _MatCheckboxRequiredValidatorModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatCheckboxRequiredValidatorModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: _MatCheckboxRequiredValidatorModule,\n    imports: [MatCheckboxRequiredValidator],\n    exports: [MatCheckboxRequiredValidator]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckboxRequiredValidator],\n      exports: [MatCheckboxRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatCheckboxModule {\n  static ɵfac = function MatCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCheckboxModule,\n    imports: [MatCheckbox, MatCommonModule],\n    exports: [MatCheckbox, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst _c0 = [\"determinateSpinner\"];\nfunction MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  _elementRef = inject(ElementRef);\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  _defaultColor = 'primary';\n  /** The element of the determinate spinner. */\n  _determinateCircle;\n  constructor() {\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  mode;\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  _value = 0;\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  _diameter = BASE_SIZE;\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  _strokeWidth;\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  static ɵfac = function MatProgressSpinner_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatProgressSpinner,\n    selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n    viewQuery: function MatProgressSpinner_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n    hostVars: 18,\n    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        i0.ɵɵclassMap(\"mat-\" + ctx.color);\n        i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mdc-circular-progress-size\", ctx.diameter + \"px\")(\"--mdc-circular-progress-active-indicator-width\", ctx.diameter + \"px\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      value: [2, \"value\", \"value\", numberAttribute],\n      diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n      strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n    },\n    exportAs: [\"matProgressSpinner\"],\n    decls: 14,\n    vars: 11,\n    consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n    template: function MatProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 2, 1);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 3);\n        i0.ɵɵelement(5, \"circle\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵelementContainer(9, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵelementContainer(11, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementContainer(13, 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const circle_r2 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n        i0.ɵɵattribute(\"r\", ctx._circleRadius());\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n        '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {\n  static ɵfac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatProgressSpinnerModule,\n    imports: [MatProgressSpinner, MatSpinner],\n    exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n", "import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, ElementRef, forwardRef, ContentChild, ViewChild, Output, Input, Inject, Optional, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormControl, ReactiveFormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i5 from '@angular/material/core';\nimport { MatOption } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MatFormField } from '@angular/material/form-field';\nimport * as i4 from '@angular/material/select';\nimport { MatSelect } from '@angular/material/select';\nimport { BehaviorSubject, of, combineLatest, Subject } from 'rxjs';\nimport { switchMap, map, startWith, delay, takeUntil, take, tap, filter } from 'rxjs/operators';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { MatDivider } from '@angular/material/divider';\nimport { AsyncPipe } from '@angular/common';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { MatProgressSpinner } from '@angular/material/progress-spinner';\nimport { MatIcon } from '@angular/material/icon';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i1 from '@angular/cdk/scrolling';\n\n/**\n * Directive for providing a custom clear-icon.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <mat-icon ngxMatSelectSearchClear>delete</mat-icon>\n * </ngx-mat-select-search>\n */\nconst _c0 = [\"searchSelectInput\"];\nconst _c1 = [\"innerSelectSearch\"];\nconst _c2 = [[[\"\", 8, \"mat-select-search-custom-header-content\"]], [[\"\", \"ngxMatSelectSearchClear\", \"\"]], [[\"\", \"ngxMatSelectNoEntriesFound\", \"\"]]];\nconst _c3 = [\".mat-select-search-custom-header-content\", \"[ngxMatSelectSearchClear]\", \"[ngxMatSelectNoEntriesFound]\"];\nfunction MatSelectSearchComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 10);\n    i0.ɵɵlistener(\"change\", function MatSelectSearchComponent_Conditional_4_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._emitSelectAllBooleanToParent($event.checked));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ctx_r2.matFormField == null ? null : ctx_r2.matFormField.color)(\"checked\", ctx_r2.toggleAllCheckboxChecked)(\"indeterminate\", ctx_r2.toggleAllCheckboxIndeterminate)(\"matTooltip\", ctx_r2.toggleAllCheckboxTooltipMessage)(\"matTooltipPosition\", ctx_r2.toggleAllCheckboxTooltipPosition);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 7);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_8_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_8_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"svgIcon\", ctx_r2.closeSvgIcon);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_8_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.closeIcon, \" \");\n  }\n}\nfunction MatSelectSearchComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function MatSelectSearchComponent_Conditional_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._reset(true));\n    });\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_Conditional_8_Conditional_1_Template, 1, 0)(2, MatSelectSearchComponent_Conditional_8_Conditional_2_Template, 1, 1, \"mat-icon\", 12)(3, MatSelectSearchComponent_Conditional_8_Conditional_3_Template, 2, 1, \"mat-icon\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r2.clearIcon ? 1 : ctx_r2.closeSvgIcon ? 2 : 3);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_11_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatSelectSearchComponent_Conditional_11_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.noEntriesFoundLabel, \" \");\n  }\n}\nfunction MatSelectSearchComponent_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_Conditional_11_Conditional_1_Template, 1, 0)(2, MatSelectSearchComponent_Conditional_11_Conditional_2_Template, 1, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r2.noEntriesFound ? 1 : 2);\n  }\n}\nclass MatSelectSearchClearDirective {\n  static ɵfac = function MatSelectSearchClearDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectSearchClearDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectSearchClearDirective,\n    selectors: [[\"\", \"ngxMatSelectSearchClear\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchClearDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMatSelectSearchClear]'\n    }]\n  }], null, null);\n})();\n\n/** List of inputs of NgxMatSelectSearchComponent that can be configured with a global default. */\nconst configurableDefaultOptions = ['ariaLabel', 'clearSearchInput', 'closeIcon', 'closeSvgIcon', 'disableInitialFocus', 'disableScrollToActiveOnOptionsChanged', 'enableClearOnEscapePressed', 'hideClearSearchButton', 'noEntriesFoundLabel', 'placeholderLabel', 'preventHomeEndKeyPropagation', 'searching'];\n/**\n * InjectionToken that can be used to specify global options. e.g.\n *\n * ```typescript\n * providers: [\n *   {\n *     provide: MAT_SELECTSEARCH_DEFAULT_OPTIONS,\n *     useValue: <MatSelectSearchOptions>{\n *       closeIcon: 'delete',\n *       noEntriesFoundLabel: 'No options found'\n *     }\n *   }\n * ]\n * ```\n *\n * See the corresponding inputs of `MatSelectSearchComponent` for documentation.\n */\nconst MAT_SELECTSEARCH_DEFAULT_OPTIONS = new InjectionToken('mat-selectsearch-default-options');\n\n/**\n * Directive for providing a custom no entries found element.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <span ngxMatSelectNoEntriesFound>\n *     No entries found <button>Add</button>\n *   </span>\n * </ngx-mat-select-search>\n */\nclass MatSelectNoEntriesFoundDirective {\n  static ɵfac = function MatSelectNoEntriesFoundDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectNoEntriesFoundDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectNoEntriesFoundDirective,\n    selectors: [[\"\", \"ngxMatSelectNoEntriesFound\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectNoEntriesFoundDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMatSelectNoEntriesFound]'\n    }]\n  }], null, null);\n})();\n\n/**\n * Component providing an input field for searching MatSelect options.\n *\n * Example usage:\n *\n * interface Bank {\n *  id: string;\n *  name: string;\n * }\n *\n * @Component({\n *   selector: 'my-app-data-selection',\n *   template: `\n *     <mat-form-field>\n *       <mat-select [formControl]=\"bankCtrl\" placeholder=\"Bank\">\n *         <mat-option>\n *           <ngx-mat-select-search [formControl]=\"bankFilterCtrl\"></ngx-mat-select-search>\n *         </mat-option>\n *         <mat-option *ngFor=\"let bank of filteredBanks | async\" [value]=\"bank.id\">\n *           {{bank.name}}\n *         </mat-option>\n *       </mat-select>\n *     </mat-form-field>\n *   `\n * })\n * export class DataSelectionComponent implements OnInit, OnDestroy {\n *\n *   // control for the selected bank\n *   public bankCtrl: FormControl = new FormControl();\n *   // control for the MatSelect filter keyword\n *   public bankFilterCtrl: FormControl = new FormControl();\n *\n *   // list of banks\n *   private banks: Bank[] = [{name: 'Bank A', id: 'A'}, {name: 'Bank B', id: 'B'}, {name: 'Bank C', id: 'C'}];\n *   // list of banks filtered by search keyword\n *   public filteredBanks: ReplaySubject<Bank[]> = new ReplaySubject<Bank[]>(1);\n *\n *   // Subject that emits when the component has been destroyed.\n *   private _onDestroy = new Subject<void>();\n *\n *\n *   ngOnInit() {\n *     // load the initial bank list\n *     this.filteredBanks.next(this.banks.slice());\n *     // listen for search field value changes\n *     this.bankFilterCtrl.valueChanges\n *       .pipe(takeUntil(this._onDestroy))\n *       .subscribe(() => {\n *         this.filterBanks();\n *       });\n *   }\n *\n *   ngOnDestroy() {\n *     this._onDestroy.next();\n *     this._onDestroy.complete();\n *   }\n *\n *   private filterBanks() {\n *     if (!this.banks) {\n *       return;\n *     }\n *\n *     // get the search keyword\n *     let search = this.bankFilterCtrl.value;\n *     if (!search) {\n *       this.filteredBanks.next(this.banks.slice());\n *       return;\n *     } else {\n *       search = search.toLowerCase();\n *     }\n *\n *     // filter the banks\n *     this.filteredBanks.next(\n *       this.banks.filter(bank => bank.name.toLowerCase().indexOf(search) > -1)\n *     );\n *   }\n * }\n */\nclass MatSelectSearchComponent {\n  matSelect;\n  changeDetectorRef;\n  _viewportRuler;\n  matOption;\n  matFormField;\n  /** Label of the search placeholder */\n  placeholderLabel = 'Suche';\n  /** Type of the search input field */\n  type = 'text';\n  /** Font-based icon used for displaying Close-Icon */\n  closeIcon = 'close';\n  /** SVG-based icon used for displaying Close-Icon. If set, closeIcon is overridden */\n  closeSvgIcon;\n  /** Label to be shown when no entries are found. Set to null if no message should be shown. */\n  noEntriesFoundLabel = 'Keine Optionen gefunden';\n  /**\n    * Whether the search field should be cleared after the dropdown menu is closed.\n    * Useful for server-side filtering. See [#3](https://github.com/bithost-gmbh/ngx-mat-select-search/issues/3)\n    */\n  clearSearchInput = true;\n  /** Whether to show the search-in-progress indicator */\n  searching = false;\n  /** Disables initial focusing of the input field */\n  disableInitialFocus = false;\n  /** Enable clear input on escape pressed */\n  enableClearOnEscapePressed = false;\n  /**\n   * Prevents home / end key being propagated to mat-select,\n   * allowing to move the cursor within the search input instead of navigating the options\n   */\n  preventHomeEndKeyPropagation = false;\n  /** Disables scrolling to active options when option list changes. Useful for server-side search */\n  disableScrollToActiveOnOptionsChanged = false;\n  /** Adds 508 screen reader support for search box */\n  ariaLabel = 'dropdown search';\n  /** Whether to show Select All Checkbox (for mat-select[multi=true]) */\n  showToggleAllCheckbox = false;\n  /** Select all checkbox checked state */\n  toggleAllCheckboxChecked = false;\n  /** select all checkbox indeterminate state */\n  toggleAllCheckboxIndeterminate = false;\n  /** Display a message in a tooltip on the toggle-all checkbox */\n  toggleAllCheckboxTooltipMessage = '';\n  /** Define the position of the tooltip on the toggle-all checkbox. */\n  toggleAllCheckboxTooltipPosition = 'below';\n  /** Show/Hide the search clear button of the search input */\n  hideClearSearchButton = false;\n  /**\n   * Always restore selected options on selectionChange for mode multi (e.g. for lazy loading/infinity scrolling).\n   * Defaults to false, so selected options are only restored while filtering is active.\n   */\n  alwaysRestoreSelectedOptionsMulti = false;\n  /**\n   * Recreate array of selected values for multi-selects.\n   *\n   * This is useful if the selected values are stored in an immutable data structure.\n   */\n  recreateValuesArray = false;\n  /** Output emitter to send to parent component with the toggle all boolean */\n  toggleAll = new EventEmitter();\n  /** Reference to the search input field */\n  searchSelectInput;\n  /** Reference to the search input field */\n  innerSelectSearch;\n  /** Reference to custom search input clear icon */\n  clearIcon;\n  /** Reference to custom no entries found element */\n  noEntriesFound;\n  /** Current search value */\n  get value() {\n    return this._formControl.value;\n  }\n  _lastExternalInputValue;\n  onTouched = () => {\n    // do nothing.\n  };\n  /** Reference to the MatSelect options */\n  set _options(_options) {\n    this._options$.next(_options);\n  }\n  get _options() {\n    return this._options$.getValue();\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  _options$ = new BehaviorSubject(null);\n  optionsList$ = this._options$.pipe(switchMap(_options => _options ? _options.changes.pipe(map(options => options.toArray()), startWith(_options.toArray())) : of(null)));\n  optionsLength$ = this.optionsList$.pipe(map(options => options ? options.length : 0));\n  /** Previously selected values when using <mat-select [multiple]=\"true\">*/\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  previousSelectedValues;\n  _formControl = new FormControl('', {\n    nonNullable: true\n  });\n  /** Whether to show the no entries found message */\n  _showNoEntriesFound$ = combineLatest([this._formControl.valueChanges, this.optionsLength$]).pipe(map(([value, optionsLength]) => !!(this.noEntriesFoundLabel && value && optionsLength === this.getOptionsLengthOffset())));\n  /** Subject that emits when the component has been destroyed. */\n  _onDestroy = new Subject();\n  /** Reference to active descendant for ARIA Support. */\n  activeDescendant;\n  constructor(matSelect, changeDetectorRef, _viewportRuler, matOption, matFormField, defaultOptions) {\n    this.matSelect = matSelect;\n    this.changeDetectorRef = changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this.matOption = matOption;\n    this.matFormField = matFormField;\n    this.applyDefaultOptions(defaultOptions);\n  }\n  applyDefaultOptions(defaultOptions) {\n    if (!defaultOptions) {\n      return;\n    }\n    for (const key of configurableDefaultOptions) {\n      if (Object.prototype.hasOwnProperty.call(defaultOptions, key)) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this[key] = defaultOptions[key];\n      }\n    }\n  }\n  ngOnInit() {\n    // set custom mat-option class if the component was placed inside a mat-option\n    if (this.matOption) {\n      this.matOption.disabled = true;\n      this.matOption._getHostElement().classList.add('contains-mat-select-search');\n      this.matOption._getHostElement().setAttribute('role', 'presentation');\n    } else {\n      console.error('<ngx-mat-select-search> must be placed inside a <mat-option> element');\n    }\n    // when the select dropdown panel is opened or closed\n    this.matSelect.openedChange.pipe(delay(1), takeUntil(this._onDestroy)).subscribe(opened => {\n      if (opened) {\n        this.updateInputWidth();\n        // focus the search field when opening\n        if (!this.disableInitialFocus) {\n          this._focus();\n        }\n      } else {\n        // clear it when closing\n        if (this.clearSearchInput) {\n          this._reset();\n        }\n      }\n    });\n    // set the first item active after the options changed\n    this.matSelect.openedChange.pipe(take(1), switchMap(() => {\n      this._options = this.matSelect.options;\n      // Closure variable for tracking the most recent first option.\n      // In order to avoid causing the list to\n      // scroll to the top when options are added to the bottom of\n      // the list (eg: infinite scroll), we compare only\n      // the changes to the first options to determine if we\n      // should set the first item as active.\n      // This prevents unnecessary scrolling to the top of the list\n      // when options are appended, but allows the first item\n      // in the list to be set as active by default when there\n      // is no active selection\n      let previousFirstOption = this._options.toArray()[this.getOptionsLengthOffset()];\n      return this._options.changes.pipe(tap(() => {\n        // avoid \"expression has been changed\" error\n        setTimeout(() => {\n          // Convert the QueryList to an array\n          const options = this._options.toArray();\n          // The true first item is offset by 1\n          const currentFirstOption = options[this.getOptionsLengthOffset()];\n          const keyManager = this.matSelect._keyManager;\n          if (keyManager && this.matSelect.panelOpen && currentFirstOption) {\n            // set first item active and input width\n            // Check to see if the first option in these changes is different from the previous.\n            const firstOptionIsChanged = !previousFirstOption || !this.matSelect.compareWith(previousFirstOption.value, currentFirstOption.value);\n            // CASE: The first option is different now.\n            // Indicates we should set it as active and scroll to the top.\n            if (firstOptionIsChanged || !keyManager.activeItem || !options.find(option => this.matSelect.compareWith(option.value, keyManager.activeItem?.value))) {\n              keyManager.setActiveItem(this.getOptionsLengthOffset());\n            }\n            // wait for panel width changes\n            setTimeout(() => {\n              this.updateInputWidth();\n            });\n          }\n          // Update our reference\n          previousFirstOption = currentFirstOption;\n        });\n      }));\n    })).pipe(takeUntil(this._onDestroy)).subscribe();\n    // add or remove css class depending on whether to show the no entries found message\n    // note: this is hacky\n    this._showNoEntriesFound$.pipe(takeUntil(this._onDestroy)).subscribe(showNoEntriesFound => {\n      // set no entries found class on mat option\n      if (this.matOption) {\n        if (showNoEntriesFound) {\n          this.matOption._getHostElement().classList.add('mat-select-search-no-entries-found');\n        } else {\n          this.matOption._getHostElement().classList.remove('mat-select-search-no-entries-found');\n        }\n      }\n    });\n    // resize the input width when the viewport is resized, i.e. the trigger width could potentially be resized\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      if (this.matSelect.panelOpen) {\n        this.updateInputWidth();\n      }\n    });\n    this.initMultipleHandling();\n    this.optionsList$.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      // update view when available options change\n      this.changeDetectorRef.markForCheck();\n    });\n  }\n  _emitSelectAllBooleanToParent(state) {\n    this.toggleAll.emit(state);\n  }\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  _isToggleAllCheckboxVisible() {\n    return this.matSelect.multiple && this.showToggleAllCheckbox;\n  }\n  /**\n   * Handles the key down event with MatSelect.\n   * Allows e.g. selecting with enter key, navigation with arrow keys, etc.\n   * @param event\n   */\n  _handleKeydown(event) {\n    // Prevent propagation for all alphanumeric characters in order to avoid selection issues\n    // Needed to avoid handling in https://github.com/angular/components/blob/5439460d1fe166f8ec34ab7d48f05e0dd7f6a946/src/material/select/select.ts#L965\n    if (event.key && event.key.length === 1 || this.preventHomeEndKeyPropagation && (event.key === 'Home' || event.key === 'End')) {\n      event.stopPropagation();\n    }\n    if (this.matSelect.multiple && event.key && event.key === 'Enter') {\n      // Regain focus after multiselect, so we can further type\n      setTimeout(() => this._focus());\n    }\n    // Special case if click Escape, if input is empty, close the dropdown, if not, empty out the search field\n    if (this.enableClearOnEscapePressed && event.key === 'Escape' && this.value) {\n      this._reset(true);\n      event.stopPropagation();\n    }\n  }\n  /**\n   * Handles the key up event with MatSelect.\n   * Allows e.g. the announcing of the currently activeDescendant by screen readers.\n   */\n  _handleKeyup(event) {\n    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {\n      const ariaActiveDescendantId = this.matSelect._getAriaActiveDescendant();\n      const index = this._options.toArray().findIndex(item => item.id === ariaActiveDescendantId);\n      if (index !== -1) {\n        this.unselectActiveDescendant();\n        this.activeDescendant = this._options.toArray()[index]._getHostElement();\n        this.activeDescendant.setAttribute('aria-selected', 'true');\n        this.searchSelectInput.nativeElement.setAttribute('aria-activedescendant', ariaActiveDescendantId);\n      }\n    }\n  }\n  writeValue(value) {\n    this._lastExternalInputValue = value;\n    this._formControl.setValue(value);\n    this.changeDetectorRef.markForCheck();\n  }\n  onBlur() {\n    this.unselectActiveDescendant();\n    this.onTouched();\n  }\n  registerOnChange(fn) {\n    this._formControl.valueChanges.pipe(filter(value => value !== this._lastExternalInputValue), tap(() => this._lastExternalInputValue = undefined), takeUntil(this._onDestroy)).subscribe(fn);\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Focuses the search input field\n   */\n  _focus() {\n    if (!this.searchSelectInput || !this.matSelect.panel) {\n      return;\n    }\n    // save and restore scrollTop of panel, since it will be reset by focus()\n    // note: this is hacky\n    const panel = this.matSelect.panel.nativeElement;\n    const scrollTop = panel.scrollTop;\n    // focus\n    this.searchSelectInput.nativeElement.focus();\n    panel.scrollTop = scrollTop;\n  }\n  /**\n   * Resets the current search value\n   * @param focus whether to focus after resetting\n   */\n  _reset(focus) {\n    this._formControl.setValue('');\n    if (focus) {\n      this._focus();\n    }\n  }\n  /**\n   * Initializes handling <mat-select [multiple]=\"true\">\n   * Note: to improve this code, mat-select should be extended to allow disabling resetting the selection while filtering.\n   */\n  initMultipleHandling() {\n    if (!this.matSelect.ngControl) {\n      if (this.matSelect.multiple) {\n        // note: the access to matSelect.ngControl (instead of matSelect.value / matSelect.valueChanges)\n        // is necessary to properly work in multi-selection mode.\n        console.error('the mat-select containing ngx-mat-select-search must have a ngModel or formControl directive when multiple=true');\n      }\n      return;\n    }\n    // if <mat-select [multiple]=\"true\">\n    // store previously selected values and restore them when they are deselected\n    // because the option is not available while we are currently filtering\n    this.previousSelectedValues = this.matSelect.ngControl.value;\n    if (!this.matSelect.ngControl.valueChanges) {\n      return;\n    }\n    this.matSelect.ngControl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(values => {\n      let restoreSelectedValues = false;\n      if (this.matSelect.multiple) {\n        if ((this.alwaysRestoreSelectedOptionsMulti || this._formControl.value && this._formControl.value.length) && this.previousSelectedValues && Array.isArray(this.previousSelectedValues)) {\n          if (!values || !Array.isArray(values)) {\n            values = [];\n          }\n          const optionValues = this.matSelect.options.map(option => option.value);\n          this.previousSelectedValues.forEach(previousValue => {\n            if (!values.some(v => this.matSelect.compareWith(v, previousValue)) && !optionValues.some(v => this.matSelect.compareWith(v, previousValue))) {\n              // if a value that was selected before is deselected and not found in the options, it was deselected\n              // due to the filtering, so we restore it.\n              if (this.recreateValuesArray) {\n                values = [...values, previousValue];\n              } else {\n                values.push(previousValue);\n              }\n              restoreSelectedValues = true;\n            }\n          });\n        }\n      }\n      this.previousSelectedValues = values;\n      if (restoreSelectedValues) {\n        this.matSelect._onChange(values);\n      }\n    });\n  }\n  /**\n   *  Set the width of the innerSelectSearch to fit even custom scrollbars\n   *  And support all Operating Systems\n   */\n  updateInputWidth() {\n    if (!this.innerSelectSearch || !this.innerSelectSearch.nativeElement) {\n      return;\n    }\n    let element = this.innerSelectSearch.nativeElement;\n    let panelElement = null;\n    while (element && element.parentElement) {\n      element = element.parentElement;\n      if (element.classList.contains('mat-select-panel')) {\n        panelElement = element;\n        break;\n      }\n    }\n    if (panelElement) {\n      this.innerSelectSearch.nativeElement.style.width = panelElement.clientWidth + 'px';\n    }\n  }\n  /**\n   * Determine the offset to length that can be caused by the optional matOption used as a search input.\n   */\n  getOptionsLengthOffset() {\n    if (this.matOption) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n  unselectActiveDescendant() {\n    this.activeDescendant?.removeAttribute('aria-selected');\n    this.searchSelectInput.nativeElement.removeAttribute('aria-activedescendant');\n  }\n  static ɵfac = function MatSelectSearchComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectSearchComponent)(i0.ɵɵdirectiveInject(MatSelect), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MatOption, 8), i0.ɵɵdirectiveInject(MatFormField, 8), i0.ɵɵdirectiveInject(MAT_SELECTSEARCH_DEFAULT_OPTIONS, 8));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSelectSearchComponent,\n    selectors: [[\"ngx-mat-select-search\"]],\n    contentQueries: function MatSelectSearchComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatSelectSearchClearDirective, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatSelectNoEntriesFoundDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEntriesFound = _t.first);\n      }\n    },\n    viewQuery: function MatSelectSearchComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7, ElementRef);\n        i0.ɵɵviewQuery(_c1, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchSelectInput = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.innerSelectSearch = _t.first);\n      }\n    },\n    inputs: {\n      placeholderLabel: \"placeholderLabel\",\n      type: \"type\",\n      closeIcon: \"closeIcon\",\n      closeSvgIcon: \"closeSvgIcon\",\n      noEntriesFoundLabel: \"noEntriesFoundLabel\",\n      clearSearchInput: \"clearSearchInput\",\n      searching: \"searching\",\n      disableInitialFocus: \"disableInitialFocus\",\n      enableClearOnEscapePressed: \"enableClearOnEscapePressed\",\n      preventHomeEndKeyPropagation: \"preventHomeEndKeyPropagation\",\n      disableScrollToActiveOnOptionsChanged: \"disableScrollToActiveOnOptionsChanged\",\n      ariaLabel: \"ariaLabel\",\n      showToggleAllCheckbox: \"showToggleAllCheckbox\",\n      toggleAllCheckboxChecked: \"toggleAllCheckboxChecked\",\n      toggleAllCheckboxIndeterminate: \"toggleAllCheckboxIndeterminate\",\n      toggleAllCheckboxTooltipMessage: \"toggleAllCheckboxTooltipMessage\",\n      toggleAllCheckboxTooltipPosition: \"toggleAllCheckboxTooltipPosition\",\n      hideClearSearchButton: \"hideClearSearchButton\",\n      alwaysRestoreSelectedOptionsMulti: \"alwaysRestoreSelectedOptionsMulti\",\n      recreateValuesArray: \"recreateValuesArray\"\n    },\n    outputs: {\n      toggleAll: \"toggleAll\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => MatSelectSearchComponent),\n      multi: true\n    }])],\n    ngContentSelectors: _c3,\n    decls: 13,\n    vars: 14,\n    consts: [[\"innerSelectSearch\", \"\"], [\"searchSelectInput\", \"\"], [\"matInput\", \"\", 1, \"mat-select-search-input\", \"mat-select-search-hidden\"], [1, \"mat-select-search-inner\", \"mat-typography\", \"mat-datepicker-content\", \"mat-tab-header\"], [1, \"mat-select-search-inner-row\"], [\"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 1, \"mat-select-search-toggle-all-checkbox\", 3, \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\"], [\"autocomplete\", \"off\", 1, \"mat-select-search-input\", 3, \"keydown\", \"keyup\", \"blur\", \"type\", \"formControl\", \"placeholder\"], [\"diameter\", \"16\", 1, \"mat-select-search-spinner\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 1, \"mat-select-search-clear\"], [1, \"mat-select-search-no-entries-found\"], [\"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 1, \"mat-select-search-toggle-all-checkbox\", 3, \"change\", \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 1, \"mat-select-search-clear\", 3, \"click\"], [3, \"svgIcon\"]],\n    template: function MatSelectSearchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelement(0, \"input\", 2);\n        i0.ɵɵelementStart(1, \"div\", 3, 0)(3, \"div\", 4);\n        i0.ɵɵtemplate(4, MatSelectSearchComponent_Conditional_4_Template, 1, 5, \"mat-checkbox\", 5);\n        i0.ɵɵelementStart(5, \"input\", 6, 1);\n        i0.ɵɵlistener(\"keydown\", function MatSelectSearchComponent_Template_input_keydown_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        })(\"keyup\", function MatSelectSearchComponent_Template_input_keyup_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeyup($event));\n        })(\"blur\", function MatSelectSearchComponent_Template_input_blur_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MatSelectSearchComponent_Conditional_7_Template, 1, 0, \"mat-spinner\", 7)(8, MatSelectSearchComponent_Conditional_8_Template, 4, 1, \"button\", 8);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(10, \"mat-divider\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, MatSelectSearchComponent_Conditional_11_Template, 3, 1, \"div\", 9);\n        i0.ɵɵpipe(12, \"async\");\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"mat-select-search-inner-multiple\", ctx.matSelect.multiple)(\"mat-select-search-inner-toggle-all\", ctx._isToggleAllCheckboxVisible());\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx._isToggleAllCheckboxVisible() ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"type\", ctx.type)(\"formControl\", ctx._formControl)(\"placeholder\", ctx.placeholderLabel);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.searching ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(!ctx.hideClearSearchButton && ctx.value && !ctx.searching ? 8 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(i0.ɵɵpipeBind1(12, 12, ctx._showNoEntriesFound$) ? 11 : -1);\n      }\n    },\n    dependencies: [AsyncPipe, ReactiveFormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlDirective, MatCheckbox, MatDivider, MatTooltip, MatProgressSpinner, MatIcon, MatButtonModule, i3.MatIconButton],\n    styles: [\".mat-select-search-hidden[_ngcontent-%COMP%]{visibility:hidden}.mat-select-search-inner[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none;background-color:var(--mat-sys-surface-container, var(--mat-select-panel-background-color, white))}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]   .mat-select-search-inner-row[_ngcontent-%COMP%]{display:flex;align-items:center}.mat-select-search-input[_ngcontent-%COMP%]{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background-color:var(--mat-sys-surface-container, var(--mat-select-panel-background-color, white));padding:0 44px 0 16px;height:47px;line-height:47px}[dir=rtl][_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-right:16px;padding-left:44px}.mat-select-search-input[_ngcontent-%COMP%]::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-left:5px}.mat-select-search-no-entries-found[_ngcontent-%COMP%]{padding-top:8px}.mat-select-search-clear[_ngcontent-%COMP%]{position:absolute;right:4px;top:0}[dir=rtl][_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%]{right:auto;left:4px}.mat-select-search-spinner[_ngcontent-%COMP%]{position:absolute;right:16px;top:calc(50% - 8px)}[dir=rtl][_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%]{right:auto;left:16px}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:-8px;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:5px}[dir=rtl][_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:0;padding-right:5px}\"],\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-mat-select-search',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatSelectSearchComponent),\n        multi: true\n      }],\n      imports: [AsyncPipe, ReactiveFormsModule, MatCheckbox, MatDivider, MatTooltip, MatProgressSpinner, MatIcon, MatButtonModule],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<!--\\nCopyright (c) 2018 Bithost GmbH All Rights Reserved.\\n\\nUse of this source code is governed by an MIT-style license that can be\\nfound in the LICENSE file at https://angular.io/license\\n-->\\n<!-- Placeholder to adjust vertical offset of the mat-option elements -->\\n<input matInput class=\\\"mat-select-search-input mat-select-search-hidden\\\"/>\\n\\n<!-- Note: the  mat-datepicker-content mat-tab-header are needed to inherit the material theme colors, see PR #22 -->\\n<div\\n      #innerSelectSearch\\n      class=\\\"mat-select-search-inner mat-typography mat-datepicker-content mat-tab-header\\\"\\n      [class.mat-select-search-inner-multiple]=\\\"matSelect.multiple\\\"\\n      [class.mat-select-search-inner-toggle-all]=\\\"_isToggleAllCheckboxVisible()\\\">\\n\\n  <div class=\\\"mat-select-search-inner-row\\\">\\n    @if(_isToggleAllCheckboxVisible())\\n    {\\n      <mat-checkbox [color]=\\\"matFormField?.color\\\"\\n                    class=\\\"mat-select-search-toggle-all-checkbox\\\"\\n                    [checked]=\\\"toggleAllCheckboxChecked\\\"\\n                    [indeterminate]=\\\"toggleAllCheckboxIndeterminate\\\"\\n                    [matTooltip]=\\\"toggleAllCheckboxTooltipMessage\\\"\\n                    matTooltipClass=\\\"ngx-mat-select-search-toggle-all-tooltip\\\"\\n                    [matTooltipPosition]=\\\"toggleAllCheckboxTooltipPosition\\\"\\n                    (change)=\\\"_emitSelectAllBooleanToParent($event.checked)\\\" />\\n    }\\n\\n    <input class=\\\"mat-select-search-input\\\"\\n           autocomplete=\\\"off\\\"\\n           [type]=\\\"type\\\"\\n           [formControl]=\\\"_formControl\\\"\\n           #searchSelectInput\\n           (keydown)=\\\"_handleKeydown($event)\\\"\\n           (keyup)=\\\"_handleKeyup($event)\\\"\\n           (blur)=\\\"onBlur()\\\"\\n           [placeholder]=\\\"placeholderLabel\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n    />\\n    @if(searching)\\n    {\\n      <mat-spinner class=\\\"mat-select-search-spinner\\\"\\n                   diameter=\\\"16\\\" />\\n    }\\n\\n    @if(!hideClearSearchButton && value && !searching)\\n    {\\n      <button mat-icon-button\\n              aria-label=\\\"Clear\\\"\\n              (click)=\\\"_reset(true)\\\"\\n              class=\\\"mat-select-search-clear\\\">\\n        @if(clearIcon)\\n        {\\n          <ng-content select=\\\"[ngxMatSelectSearchClear]\\\" />\\n        }\\n        @else if(closeSvgIcon)\\n        {\\n          <mat-icon [svgIcon]=\\\"closeSvgIcon\\\" />\\n        }\\n        @else\\n        {\\n          <mat-icon>\\n            {{closeIcon}}\\n          </mat-icon>\\n        }\\n      </button>\\n    }\\n\\n\\n    <ng-content select=\\\".mat-select-search-custom-header-content\\\" />\\n  </div>\\n\\n  <mat-divider />\\n</div>\\n\\n@if(_showNoEntriesFound$ | async)\\n{\\n  <div class=\\\"mat-select-search-no-entries-found\\\">\\n    @if(noEntriesFound)\\n    {\\n      <ng-content select=\\\"[ngxMatSelectNoEntriesFound]\\\" />\\n    }\\n    @else\\n    {\\n      {{noEntriesFoundLabel}}\\n    }\\n  </div>\\n}\\n\\n\",\n      styles: [\".mat-select-search-hidden{visibility:hidden}.mat-select-search-inner{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none;background-color:var(--mat-sys-surface-container, var(--mat-select-panel-background-color, white))}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all .mat-select-search-inner-row{display:flex;align-items:center}.mat-select-search-input{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background-color:var(--mat-sys-surface-container, var(--mat-select-panel-background-color, white));padding:0 44px 0 16px;height:47px;line-height:47px}:host-context([dir=rtl]) .mat-select-search-input{padding-right:16px;padding-left:44px}.mat-select-search-input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}.mat-select-search-inner-toggle-all .mat-select-search-input{padding-left:5px}.mat-select-search-no-entries-found{padding-top:8px}.mat-select-search-clear{position:absolute;right:4px;top:0}:host-context([dir=rtl]) .mat-select-search-clear{right:auto;left:4px}.mat-select-search-spinner{position:absolute;right:16px;top:calc(50% - 8px)}:host-context([dir=rtl]) .mat-select-search-spinner{right:auto;left:16px}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:-8px;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox{padding-left:5px}:host-context([dir=rtl]) .mat-select-search-toggle-all-checkbox{padding-left:0;padding-right:5px}\\n\"]\n    }]\n  }], () => [{\n    type: i4.MatSelect,\n    decorators: [{\n      type: Inject,\n      args: [MatSelect]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i5.MatOption,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatOption]\n    }]\n  }, {\n    type: i6.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatFormField]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SELECTSEARCH_DEFAULT_OPTIONS]\n    }]\n  }], {\n    placeholderLabel: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    closeSvgIcon: [{\n      type: Input\n    }],\n    noEntriesFoundLabel: [{\n      type: Input\n    }],\n    clearSearchInput: [{\n      type: Input\n    }],\n    searching: [{\n      type: Input\n    }],\n    disableInitialFocus: [{\n      type: Input\n    }],\n    enableClearOnEscapePressed: [{\n      type: Input\n    }],\n    preventHomeEndKeyPropagation: [{\n      type: Input\n    }],\n    disableScrollToActiveOnOptionsChanged: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    showToggleAllCheckbox: [{\n      type: Input\n    }],\n    toggleAllCheckboxChecked: [{\n      type: Input\n    }],\n    toggleAllCheckboxIndeterminate: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipMessage: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipPosition: [{\n      type: Input\n    }],\n    hideClearSearchButton: [{\n      type: Input\n    }],\n    alwaysRestoreSelectedOptionsMulti: [{\n      type: Input\n    }],\n    recreateValuesArray: [{\n      type: Input\n    }],\n    toggleAll: [{\n      type: Output\n    }],\n    searchSelectInput: [{\n      type: ViewChild,\n      args: ['searchSelectInput', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    innerSelectSearch: [{\n      type: ViewChild,\n      args: ['innerSelectSearch', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    clearIcon: [{\n      type: ContentChild,\n      args: [MatSelectSearchClearDirective]\n    }],\n    noEntriesFound: [{\n      type: ContentChild,\n      args: [MatSelectNoEntriesFoundDirective]\n    }]\n  });\n})();\n\n/**\n * Copyright (c) 2018 Bithost GmbH All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MatSelectSearchVersion = '8.0.2';\nclass NgxMatSelectSearchModule {\n  static ɵfac = function NgxMatSelectSearchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgxMatSelectSearchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxMatSelectSearchModule,\n    imports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n    exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatSelectSearchComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMatSelectSearchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n      exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECTSEARCH_DEFAULT_OPTIONS, MatSelectNoEntriesFoundDirective, MatSelectSearchClearDirective, MatSelectSearchComponent, MatSelectSearchVersion, NgxMatSelectSearchModule, configurableDefaultOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,+BAA+B,IAAI,eAAe,gCAAgC;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uCAAuC;AAC9C,SAAO;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AACF;AAMA,IAAI;AAAA,CACH,SAAUA,uBAAsB;AAE/B,EAAAA,sBAAqBA,sBAAqB,MAAM,IAAI,CAAC,IAAI;AAEzD,EAAAA,sBAAqBA,sBAAqB,SAAS,IAAI,CAAC,IAAI;AAE5D,EAAAA,sBAAqBA,sBAAqB,WAAW,IAAI,CAAC,IAAI;AAE9D,EAAAA,sBAAqBA,sBAAqB,eAAe,IAAI,CAAC,IAAI;AACpE,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAKtD,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,WAAW;AAAA,EACzC,OAAO;AACT;AAEA,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AACF;AAEA,IAAM,WAAW,qCAAqC;AACtD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,MAAM;AAAA,EACvB,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,8BAA8B;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,QAAQ;AACN,SAAK,cAAc,cAAc,MAAM;AAAA,EACzC;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,UAAM,QAAQ,IAAI,kBAAkB;AACpC,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAClB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAAA,EACrC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,gBAAgB;AAAA;AAAA,EAEhB,OAAO;AAAA;AAAA,EAEP,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,yBAAyB;AAAA,EACzB,qBAAqB,qBAAqB;AAAA,EAC1C,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,qBAAqB,MAAM;AAAA,EAAC;AAAA,EAC5B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAC7C,SAAK,WAAW,YAAY,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC7D,SAAK,KAAK,KAAK,YAAY,OAAO,YAAY,EAAE,MAAM,mBAAmB;AACzE,SAAK,sBAAsB,KAAK,UAAU,uBAAuB;AAAA,EACnE;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB,KAAK,cAAc;AAAA,EAC7C;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,SAAS,KAAK,SAAS;AACzB,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,UAAU;AAC3B,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,UAAM,UAAU,SAAS,KAAK;AAC9B,SAAK,iBAAiB;AACtB,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB;AACvB,aAAK,sBAAsB,qBAAqB,aAAa;AAAA,MAC/D,OAAO;AACL,aAAK,sBAAsB,KAAK,UAAU,qBAAqB,UAAU,qBAAqB,SAAS;AAAA,MACzG;AACA,WAAK,oBAAoB,KAAK,KAAK,cAAc;AAAA,IACnD;AACA,SAAK,mBAAmB,KAAK,cAAc;AAAA,EAC7C;AAAA,EACA,iBAAiB;AAAA,EACjB,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,qBAAqB;AAMnB,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS,SAAS;AAChB,WAAO,KAAK,YAAY,QAAQ,UAAU,OAAO;AAAA,MAC/C,YAAY;AAAA,IACd,IAAI;AAAA,EACN;AAAA;AAAA,EAEA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,sBAAsB,UAAU;AAC9B,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU,KAAK,2BAA2B;AAC9C,QAAI,aAAa,YAAY,CAAC,SAAS;AACrC;AAAA,IACF;AACA,QAAI,KAAK,wBAAwB;AAC/B,cAAQ,UAAU,OAAO,KAAK,sBAAsB;AAAA,IACtD;AACA,SAAK,yBAAyB,KAAK,0CAA0C,UAAU,QAAQ;AAC/F,SAAK,qBAAqB;AAC1B,QAAI,KAAK,uBAAuB,SAAS,GAAG;AAC1C,cAAQ,UAAU,IAAI,KAAK,sBAAsB;AAEjD,YAAM,iBAAiB,KAAK;AAC5B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAW,MAAM;AACf,kBAAQ,UAAU,OAAO,cAAc;AAAA,QACzC,GAAG,GAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,8BAA8B,KAAK,OAAO;AAC/C,SAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,OAAO,CAAC;AAGtD,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,cAAc,UAAU,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,UAAU,CAAC,KAAK;AACrB,SAAK,8BAA8B,KAAK,OAAO;AAAA,EACjD;AAAA,EACA,oBAAoB;AAClB,UAAM,cAAc,KAAK,UAAU;AAEnC,QAAI,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAE5C,UAAI,KAAK,iBAAiB,gBAAgB,SAAS;AACjD,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,iBAAiB;AACtB,eAAK,oBAAoB,KAAK,KAAK,cAAc;AAAA,QACnD,CAAC;AAAA,MACH;AACA,WAAK,WAAW,CAAC,KAAK;AACtB,WAAK,sBAAsB,KAAK,WAAW,qBAAqB,UAAU,qBAAqB,SAAS;AAIxG,WAAK,iBAAiB;AAAA,IACxB,WAAW,KAAK,YAAY,KAAK,uBAAuB,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAGhG,WAAK,cAAc,cAAc,UAAU,KAAK;AAChD,WAAK,cAAc,cAAc,gBAAgB,KAAK;AAAA,IACxD;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AAIzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,UAAU;AAMR,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,0CAA0C,UAAU,UAAU;AAE5D,QAAI,KAAK,mBAAmB,kBAAkB;AAC5C,aAAO;AAAA,IACT;AACA,YAAQ,UAAU;AAAA,MAChB,KAAK,qBAAqB;AAGxB,YAAI,aAAa,qBAAqB,SAAS;AAC7C,iBAAO,KAAK,kBAAkB;AAAA,QAChC,WAAW,YAAY,qBAAqB,eAAe;AACzD,iBAAO,KAAK,WAAW,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,QAChG;AACA;AAAA,MACF,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MACxH,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,YAAY,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MAC1H,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,IAC9H;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,OAAO;AACxB,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,qBAAe,cAAc,gBAAgB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,SAAK,kBAAkB;AACvB,QAAI,CAAC,KAAK,UAAU;AAGlB,WAAK,cAAc,cAAc,MAAM;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,OAAO;AAC/B,QAAI,CAAC,CAAC,MAAM,UAAU,KAAK,cAAc,cAAc,SAAS,MAAM,MAAM,GAAG;AAC7E,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,mBAAmB,IAAI;AAC5E,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,YAAY;AAC3D,QAAG,YAAY,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,0BAA0B,IAAI,QAAQ,EAAE,6BAA6B,IAAI,QAAQ,EAAE,4BAA4B,IAAI,OAAO,EAAE,yCAAyC,IAAI,mBAAmB;AAAA,MACjR;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,MAC1D,cAAc,CAAC,GAAG,iBAAiB,gBAAgB,gBAAgB;AAAA,MACnE,cAAc,CAAC,GAAG,iBAAiB,cAAc;AAAA,MACjD,UAAU,CAAC,GAAG,aAAa,UAAU;AAAA,MACrC,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK,CAAC;AAAA,MACjG,OAAO;AAAA,MACP,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACvF,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACvE;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC,qCAAqC;AAAA,MACrE,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,2BAA2B,IAAI,GAAG,SAAS,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iCAAiC,GAAG,OAAO,GAAG,CAAC,QAAQ,YAAY,GAAG,gCAAgC,GAAG,QAAQ,SAAS,UAAU,WAAW,iBAAiB,YAAY,MAAM,YAAY,UAAU,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,aAAa,SAAS,WAAW,aAAa,eAAe,QAAQ,GAAG,yBAAyB,GAAG,CAAC,QAAQ,QAAQ,KAAK,oCAAoC,GAAG,8BAA8B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,IAAI,GAAG,2BAA2B,uBAAuB,GAAG,oBAAoB,qBAAqB,mBAAmB,GAAG,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAAA,IAChyB,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,0CAA0C,QAAQ;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,0BAA0B,MAAM,CAAC;AAAA,QAC7D,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,QAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,CAAC;AAAA,QACjD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,QAAQ,SAAS,6CAA6C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,8CAA8C;AACjE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC,EAAE,UAAU,SAAS,6CAA6C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,QACvD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,gBAAgB;AACnB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,SAAS,IAAI,CAAC;AACpC,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,cAAiB,YAAY,CAAC;AACpC,QAAG,WAAW,iBAAiB,IAAI,aAAa;AAChD,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,0BAA0B,IAAI,OAAO;AACpD,QAAG,WAAW,WAAW,IAAI,OAAO,EAAE,iBAAiB,IAAI,aAAa,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,MAAM,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AACrP,QAAG,YAAY,cAAc,IAAI,aAAa,IAAI,EAAE,mBAAmB,IAAI,cAAc,EAAE,oBAAoB,IAAI,eAAe,EAAE,gBAAgB,IAAI,gBAAgB,UAAU,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,OAAO,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,aAAa,IAAI,QAAQ,EAAE,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,KAAK;AACrY,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,WAAW,EAAE,qBAAqB,IAAI,iBAAiB,IAAI,QAAQ,EAAE,qBAAqB,IAAI;AAChI,QAAG,UAAU;AACb,QAAG,WAAW,OAAO,IAAI,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,qBAAqB;AAAA,IAC/C,QAAQ,CAAC,u6fAAy6f;AAAA,IACl7f,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,QAAQ;AAAA;AAAA,QAER,qCAAqC;AAAA,QACrC,oCAAoC;AAAA,QACpC,iDAAiD;AAAA,QACjD,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,qCAAqC;AAAA,QAC/C,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA,MACD,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,qBAAqB;AAAA,MAC1C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,u6fAAy6f;AAAA,IACp7f,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK;AAAA,MACvE,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kCAAkC;AAAA,EACtC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,4BAA4B;AAAA,EAC1D,OAAO;AACT;AASA,IAAM,+BAAN,MAAM,sCAAqC,0BAA0B;AAAA,EACnE,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qCAAqC,mBAAmB;AACtE,cAAQ,8CAA8C,4CAA+C,sBAAsB,6BAA4B,IAAI,qBAAqB,6BAA4B;AAAA,IAC9M;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,YAAY,IAAI,mBAAmB,EAAE,GAAG,CAAC,gBAAgB,YAAY,IAAI,eAAe,EAAE,GAAG,CAAC,gBAAgB,YAAY,IAAI,WAAW,EAAE,CAAC;AAAA,IACzK,UAAU,CAAI,mBAAmB,CAAC,+BAA+B,CAAC,GAAM,0BAA0B;AAAA,EACpG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,WAAW,CAAC,+BAA+B;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,OAAO,OAAO,SAAS,4CAA4C,mBAAmB;AACpF,WAAO,KAAK,qBAAqB,sCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,4BAA4B;AAAA,IACtC,SAAS,CAAC,4BAA4B;AAAA,EACxC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,4BAA4B;AAAA,MACtC,SAAS,CAAC,4BAA4B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,eAAe;AAAA,IACtC,SAAS,CAAC,aAAa,eAAe;AAAA,EACxC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,aAAa,iBAAiB,eAAe;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,eAAe;AAAA,MACtC,SAAS,CAAC,aAAa,eAAe;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvuBH,IAAMC,OAAM,CAAC,oBAAoB;AACjC,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,UAAU,EAAE;AAC5B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,WAAW,OAAO,SAAS,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,YAAY,oBAAoB,OAAO,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,OAAO,qBAAqB,IAAI,GAAG,IAAI,EAAE,gBAAgB,OAAO,mBAAmB,GAAG,GAAG;AACtL,IAAG,YAAY,KAAK,OAAO,cAAc,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,uCAAuC,IAAI,eAAe,wCAAwC;AAAA,EACtG,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,+CAA+C;AACtD,SAAO;AAAA,IACL,UAAU;AAAA,EACZ;AACF;AAIA,IAAM,YAAY;AAIlB,IAAM,oBAAoB;AAC1B,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB;AAAA,EACA,cAAc;AACZ,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,UAAMC,YAAW,OAAO,oCAAoC;AAC5D,SAAK,kBAAkB,kBAAkB,oBAAoB,CAAC,CAACA,aAAY,CAACA,UAAS;AACrF,SAAK,OAAO,KAAK,YAAY,cAAc,SAAS,YAAY,MAAM,gBAAgB,kBAAkB;AACxG,QAAIA,WAAU;AACZ,UAAIA,UAAS,OAAO;AAClB,aAAK,QAAQ,KAAK,gBAAgBA,UAAS;AAAA,MAC7C;AACA,UAAIA,UAAS,UAAU;AACrB,aAAK,WAAWA,UAAS;AAAA,MAC3B;AACA,UAAIA,UAAS,aAAa;AACxB,aAAK,cAAcA,UAAS;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AAAA,EACrD;AAAA,EACA,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,SAAS;AAAA;AAAA,EAET,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,MAAM;AACjB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,WAAW;AAAA,EAC9C;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA,EAEA,gBAAgB;AACd,YAAQ,KAAK,WAAW,qBAAqB;AAAA,EAC/C;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,cAAc,IAAI,IAAI,KAAK;AAChD,WAAO,OAAO,OAAO,IAAI,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,IAAI,KAAK,KAAK,KAAK,cAAc;AAAA,EAC1C;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,SAAS,eAAe;AAC/B,aAAO,KAAK,qBAAqB,KAAK,MAAM,KAAK,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,GAAG,CAAC,aAAa,CAAC;AAAA,IACrD,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAYD,MAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,eAAe,YAAY,MAAM,GAAG,4BAA4B,uBAAuB;AAAA,IAC3G,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,IAAI,SAAS,gBAAgB,IAAI,QAAQ,IAAI,EAAE,QAAQ,IAAI,IAAI;AACzI,QAAG,WAAW,SAAS,IAAI,KAAK;AAChC,QAAG,YAAY,SAAS,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI,EAAE,gCAAgC,IAAI,WAAW,IAAI,EAAE,kDAAkD,IAAI,WAAW,IAAI;AACpM,QAAG,YAAY,2BAA2B,IAAI,eAAe,EAAE,wCAAwC,IAAI,SAAS,eAAe;AAAA,MACrI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,IAChE;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,8CAA8C,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,mDAAmD,GAAG,CAAC,MAAM,OAAO,MAAM,OAAO,GAAG,2CAA2C,GAAG,CAAC,eAAe,QAAQ,GAAG,gDAAgD,GAAG,CAAC,GAAG,sCAAsC,GAAG,CAAC,GAAG,yCAAyC,oCAAoC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,yCAAyC,qCAAqC,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,qDAAqD,GAAG,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IAC7zB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACnH,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa,EAAE;AAClB,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,QAAG,mBAAmB,GAAG,CAAC;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,WAAW,IAAI,SAAS,CAAC;AACxC,QAAG,UAAU;AACb,QAAG,YAAY,oBAAoB,IAAI,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,IAAI,kBAAkB,GAAG,IAAI,EAAE,gBAAgB,IAAI,mBAAmB,GAAG,GAAG;AACtK,QAAG,YAAY,KAAK,IAAI,cAAc,CAAC;AACvC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,QAAQ,CAAC,mrIAAmrI;AAAA,IAC5rI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,QAGT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,mCAAmC;AAAA,QACnC,gDAAgD;AAAA,QAChD,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,wCAAwC;AAAA,QACxC,0DAA0D;AAAA,QAC1D,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,eAAe;AAAA,MACjB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,mrIAAmrI;AAAA,IAC9rI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa;AACnB,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,UAAU;AAAA,IACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,EAC3D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,UAAU;AAAA,MACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvRH,IAAME,OAAM,CAAC,mBAAmB;AAChC,IAAMC,OAAM,CAAC,mBAAmB;AAChC,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,GAAG,yCAAyC,CAAC,GAAG,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;AAClJ,IAAM,MAAM,CAAC,4CAA4C,6BAA6B,8BAA8B;AACpH,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,gBAAgB,EAAE;AACvC,IAAG,WAAW,UAAU,SAAS,+EAA+E,QAAQ;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,8BAA8B,OAAO,OAAO,CAAC;AAAA,IAC5E,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,gBAAgB,OAAO,OAAO,OAAO,aAAa,KAAK,EAAE,WAAW,OAAO,wBAAwB,EAAE,iBAAiB,OAAO,8BAA8B,EAAE,cAAc,OAAO,+BAA+B,EAAE,sBAAsB,OAAO,gCAAgC;AAAA,EAChT;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,CAAC;AAAA,EAClC;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY;AAAA,EAC9C;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,WAAW,GAAG;AAAA,EAClD;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,0EAA0E;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,IAAI,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,WAAW,GAAG,+DAA+D,GAAG,CAAC,EAAE,GAAG,+DAA+D,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,+DAA+D,GAAG,GAAG,UAAU;AAChQ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,YAAY,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,EACrE;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,mBAAmB,KAAK,OAAO,qBAAqB,GAAG;AAAA,EAC5D;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,gEAAgE,GAAG,CAAC,EAAE,GAAG,gEAAgE,GAAG,CAAC;AAC9J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,iBAAiB,IAAI,CAAC;AAAA,EAChD;AACF;AACA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAA+B;AAAA,EAClE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,EACjD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,6BAA6B,CAAC,aAAa,oBAAoB,aAAa,gBAAgB,uBAAuB,yCAAyC,8BAA8B,yBAAyB,uBAAuB,oBAAoB,gCAAgC,WAAW;AAkB/S,IAAM,mCAAmC,IAAI,eAAe,kCAAkC;AAW9F,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,OAAO,OAAO,SAAS,yCAAyC,mBAAmB;AACjF,WAAO,KAAK,qBAAqB,mCAAkC;AAAA,EACrE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAgFH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,mBAAmB;AAAA;AAAA,EAEnB,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,mBAAmB;AAAA;AAAA,EAEnB,YAAY;AAAA;AAAA,EAEZ,sBAAsB;AAAA;AAAA,EAEtB,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,+BAA+B;AAAA;AAAA,EAE/B,wCAAwC;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,wBAAwB;AAAA;AAAA,EAExB,2BAA2B;AAAA;AAAA,EAE3B,iCAAiC;AAAA;AAAA,EAEjC,kCAAkC;AAAA;AAAA,EAElC,mCAAmC;AAAA;AAAA,EAEnC,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,oCAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,sBAAsB;AAAA;AAAA,EAEtB,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AAAA,EAElB;AAAA;AAAA,EAEA,IAAI,SAAS,UAAU;AACrB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA;AAAA,EAEA,YAAY,IAAI,gBAAgB,IAAI;AAAA,EACpC,eAAe,KAAK,UAAU,KAAK,UAAU,cAAY,WAAW,SAAS,QAAQ,KAAK,IAAI,aAAW,QAAQ,QAAQ,CAAC,GAAG,UAAU,SAAS,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,EACvK,iBAAiB,KAAK,aAAa,KAAK,IAAI,aAAW,UAAU,QAAQ,SAAS,CAAC,CAAC;AAAA;AAAA;AAAA,EAGpF;AAAA,EACA,eAAe,IAAI,YAAY,IAAI;AAAA,IACjC,aAAa;AAAA,EACf,CAAC;AAAA;AAAA,EAED,uBAAuB,cAAc,CAAC,KAAK,aAAa,cAAc,KAAK,cAAc,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,OAAO,aAAa,MAAM,CAAC,EAAE,KAAK,uBAAuB,SAAS,kBAAkB,KAAK,uBAAuB,EAAE,CAAC;AAAA;AAAA,EAE1N,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB;AAAA,EACA,YAAY,WAAW,mBAAmB,gBAAgB,WAAW,cAAc,gBAAgB;AACjG,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,oBAAoB,cAAc;AAAA,EACzC;AAAA,EACA,oBAAoB,gBAAgB;AAClC,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,eAAW,OAAO,4BAA4B;AAC5C,UAAI,OAAO,UAAU,eAAe,KAAK,gBAAgB,GAAG,GAAG;AAE7D,aAAK,GAAG,IAAI,eAAe,GAAG;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAET,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,WAAW;AAC1B,WAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,4BAA4B;AAC3E,WAAK,UAAU,gBAAgB,EAAE,aAAa,QAAQ,cAAc;AAAA,IACtE,OAAO;AACL,cAAQ,MAAM,sEAAsE;AAAA,IACtF;AAEA,SAAK,UAAU,aAAa,KAAK,MAAM,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzF,UAAI,QAAQ;AACV,aAAK,iBAAiB;AAEtB,YAAI,CAAC,KAAK,qBAAqB;AAC7B,eAAK,OAAO;AAAA,QACd;AAAA,MACF,OAAO;AAEL,YAAI,KAAK,kBAAkB;AACzB,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,UAAU,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,MAAM;AACxD,WAAK,WAAW,KAAK,UAAU;AAW/B,UAAI,sBAAsB,KAAK,SAAS,QAAQ,EAAE,KAAK,uBAAuB,CAAC;AAC/E,aAAO,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM;AAE1C,mBAAW,MAAM;AAEf,gBAAM,UAAU,KAAK,SAAS,QAAQ;AAEtC,gBAAM,qBAAqB,QAAQ,KAAK,uBAAuB,CAAC;AAChE,gBAAM,aAAa,KAAK,UAAU;AAClC,cAAI,cAAc,KAAK,UAAU,aAAa,oBAAoB;AAGhE,kBAAM,uBAAuB,CAAC,uBAAuB,CAAC,KAAK,UAAU,YAAY,oBAAoB,OAAO,mBAAmB,KAAK;AAGpI,gBAAI,wBAAwB,CAAC,WAAW,cAAc,CAAC,QAAQ,KAAK,YAAU,KAAK,UAAU,YAAY,OAAO,OAAO,WAAW,YAAY,KAAK,CAAC,GAAG;AACrJ,yBAAW,cAAc,KAAK,uBAAuB,CAAC;AAAA,YACxD;AAEA,uBAAW,MAAM;AACf,mBAAK,iBAAiB;AAAA,YACxB,CAAC;AAAA,UACH;AAEA,gCAAsB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU;AAG/C,SAAK,qBAAqB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,wBAAsB;AAEzF,UAAI,KAAK,WAAW;AAClB,YAAI,oBAAoB;AACtB,eAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,oCAAoC;AAAA,QACrF,OAAO;AACL,eAAK,UAAU,gBAAgB,EAAE,UAAU,OAAO,oCAAoC;AAAA,QACxF;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC5E,UAAI,KAAK,UAAU,WAAW;AAC5B,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB;AAC1B,SAAK,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAEjE,WAAK,kBAAkB,aAAa;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,8BAA8B,OAAO;AACnC,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,8BAA8B;AAC5B,WAAO,KAAK,UAAU,YAAY,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AAGpB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,KAAK,iCAAiC,MAAM,QAAQ,UAAU,MAAM,QAAQ,QAAQ;AAC7H,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,KAAK,UAAU,YAAY,MAAM,OAAO,MAAM,QAAQ,SAAS;AAEjE,iBAAW,MAAM,KAAK,OAAO,CAAC;AAAA,IAChC;AAEA,QAAI,KAAK,8BAA8B,MAAM,QAAQ,YAAY,KAAK,OAAO;AAC3E,WAAK,OAAO,IAAI;AAChB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,QAAI,MAAM,QAAQ,aAAa,MAAM,QAAQ,aAAa;AACxD,YAAM,yBAAyB,KAAK,UAAU,yBAAyB;AACvE,YAAM,QAAQ,KAAK,SAAS,QAAQ,EAAE,UAAU,UAAQ,KAAK,OAAO,sBAAsB;AAC1F,UAAI,UAAU,IAAI;AAChB,aAAK,yBAAyB;AAC9B,aAAK,mBAAmB,KAAK,SAAS,QAAQ,EAAE,KAAK,EAAE,gBAAgB;AACvE,aAAK,iBAAiB,aAAa,iBAAiB,MAAM;AAC1D,aAAK,kBAAkB,cAAc,aAAa,yBAAyB,sBAAsB;AAAA,MACnG;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,0BAA0B;AAC/B,SAAK,aAAa,SAAS,KAAK;AAChC,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,SAAS;AACP,SAAK,yBAAyB;AAC9B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,aAAa,aAAa,KAAK,OAAO,WAAS,UAAU,KAAK,uBAAuB,GAAG,IAAI,MAAM,KAAK,0BAA0B,MAAS,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,EAAE;AAAA,EAC5L;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,UAAU,OAAO;AACpD;AAAA,IACF;AAGA,UAAM,QAAQ,KAAK,UAAU,MAAM;AACnC,UAAM,YAAY,MAAM;AAExB,SAAK,kBAAkB,cAAc,MAAM;AAC3C,UAAM,YAAY;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,SAAK,aAAa,SAAS,EAAE;AAC7B,QAAI,OAAO;AACT,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,UAAI,KAAK,UAAU,UAAU;AAG3B,gBAAQ,MAAM,iHAAiH;AAAA,MACjI;AACA;AAAA,IACF;AAIA,SAAK,yBAAyB,KAAK,UAAU,UAAU;AACvD,QAAI,CAAC,KAAK,UAAU,UAAU,cAAc;AAC1C;AAAA,IACF;AACA,SAAK,UAAU,UAAU,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzF,UAAI,wBAAwB;AAC5B,UAAI,KAAK,UAAU,UAAU;AAC3B,aAAK,KAAK,qCAAqC,KAAK,aAAa,SAAS,KAAK,aAAa,MAAM,WAAW,KAAK,0BAA0B,MAAM,QAAQ,KAAK,sBAAsB,GAAG;AACtL,cAAI,CAAC,UAAU,CAAC,MAAM,QAAQ,MAAM,GAAG;AACrC,qBAAS,CAAC;AAAA,UACZ;AACA,gBAAM,eAAe,KAAK,UAAU,QAAQ,IAAI,YAAU,OAAO,KAAK;AACtE,eAAK,uBAAuB,QAAQ,mBAAiB;AACnD,gBAAI,CAAC,OAAO,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,GAAG;AAG5I,kBAAI,KAAK,qBAAqB;AAC5B,yBAAS,CAAC,GAAG,QAAQ,aAAa;AAAA,cACpC,OAAO;AACL,uBAAO,KAAK,aAAa;AAAA,cAC3B;AACA,sCAAwB;AAAA,YAC1B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,yBAAyB;AAC9B,UAAI,uBAAuB;AACzB,aAAK,UAAU,UAAU,MAAM;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,kBAAkB,eAAe;AACpE;AAAA,IACF;AACA,QAAI,UAAU,KAAK,kBAAkB;AACrC,QAAI,eAAe;AACnB,WAAO,WAAW,QAAQ,eAAe;AACvC,gBAAU,QAAQ;AAClB,UAAI,QAAQ,UAAU,SAAS,kBAAkB,GAAG;AAClD,uBAAe;AACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,cAAc;AAChB,WAAK,kBAAkB,cAAc,MAAM,QAAQ,aAAa,cAAc;AAAA,IAChF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACvB,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,SAAK,kBAAkB,gBAAgB,eAAe;AACtD,SAAK,kBAAkB,cAAc,gBAAgB,uBAAuB;AAAA,EAC9E;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA6B,kBAAkB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,aAAa,GAAM,kBAAkB,WAAW,CAAC,GAAM,kBAAkB,cAAc,CAAC,GAAM,kBAAkB,kCAAkC,CAAC,CAAC;AAAA,EACtT;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,+BAA+B,CAAC;AAC5D,QAAG,eAAe,UAAU,kCAAkC,CAAC;AAAA,MACjE;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,MACvE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,YAAYF,MAAK,GAAG,UAAU;AACjC,QAAG,YAAYC,MAAK,GAAG,UAAU;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,uCAAuC;AAAA,MACvC,WAAW;AAAA,MACX,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,gCAAgC;AAAA,MAChC,iCAAiC;AAAA,MACjC,kCAAkC;AAAA,MAClC,uBAAuB;AAAA,MACvB,mCAAmC;AAAA,MACnC,qBAAqB;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,MACtD,OAAO;AAAA,IACT,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,YAAY,IAAI,GAAG,2BAA2B,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,kBAAkB,0BAA0B,gBAAgB,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,mBAAmB,4CAA4C,GAAG,yCAAyC,GAAG,SAAS,WAAW,iBAAiB,cAAc,oBAAoB,GAAG,CAAC,gBAAgB,OAAO,GAAG,2BAA2B,GAAG,WAAW,SAAS,QAAQ,QAAQ,eAAe,aAAa,GAAG,CAAC,YAAY,MAAM,GAAG,2BAA2B,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,GAAG,yBAAyB,GAAG,CAAC,GAAG,oCAAoC,GAAG,CAAC,mBAAmB,4CAA4C,GAAG,yCAAyC,GAAG,UAAU,SAAS,WAAW,iBAAiB,cAAc,oBAAoB,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,GAAG,2BAA2B,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;AAAA,IAC1hC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgBC,IAAG;AACtB,QAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AACzF,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,WAAW,SAAS,2DAA2D,QAAQ;AACnG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,SAAS,SAAS,yDAAyD,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,QAAQ,SAAS,0DAA0D;AAC5E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,QACpC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,UAAU,CAAC;AAC/J,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAChB,QAAG,UAAU,IAAI,aAAa;AAC9B,QAAG,aAAa;AAChB,QAAG,WAAW,IAAI,kDAAkD,GAAG,GAAG,OAAO,CAAC;AAClF,QAAG,OAAO,IAAI,OAAO;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,YAAY,oCAAoC,IAAI,UAAU,QAAQ,EAAE,sCAAsC,IAAI,4BAA4B,CAAC;AAClJ,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,4BAA4B,IAAI,IAAI,EAAE;AAC3D,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,IAAI,EAAE,eAAe,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB;AACpG,QAAG,YAAY,cAAc,IAAI,SAAS;AAC1C,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AACvC,QAAG,UAAU;AACb,QAAG,cAAc,CAAC,IAAI,yBAAyB,IAAI,SAAS,CAAC,IAAI,YAAY,IAAI,EAAE;AACnF,QAAG,UAAU,CAAC;AACd,QAAG,cAAiB,YAAY,IAAI,IAAI,IAAI,oBAAoB,IAAI,KAAK,EAAE;AAAA,MAC7E;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,qBAAwB,sBAAyB,iBAAoB,sBAAsB,aAAa,YAAY,YAAY,oBAAoB,SAAS,iBAAoB,aAAa;AAAA,IACxN,QAAQ,CAAC,++EAA++E;AAAA,IACx/E,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,CAAC,WAAW,qBAAqB,aAAa,YAAY,YAAY,oBAAoB,SAAS,eAAe;AAAA,MAC3H,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,i5DAAi5D;AAAA,IAC55D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,yBAAyB;AAC/B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACnG,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,EACrG,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,wBAAwB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,MACnG,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACrG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["TransitionCheckState", "_c0", "defaults", "_c0", "_c1", "_c2"]}