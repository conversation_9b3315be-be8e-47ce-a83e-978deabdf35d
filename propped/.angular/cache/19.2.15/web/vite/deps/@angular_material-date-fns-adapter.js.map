{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+material-date-fns-_e379aa840d14dba98d552c37ea18fde2/node_modules/@angular/material-date-fns-adapter/fesm2022/material-date-fns-adapter.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, NgModule } from '@angular/core';\nimport { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { getYear, getMonth, getDate, getDay, getDaysInMonth, parseISO, parse, format, addYears, addMonths, addDays, formatISO, isDate, isValid, set, getHours, getMinutes, getSeconds, addSeconds } from 'date-fns';\n\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n// date-fns doesn't have a way to read/print month names or days of the week directly,\n// so we get them by formatting a date with a format that produces the desired month/day.\nconst MONTH_FORMATS = {\n  long: 'LLLL',\n  short: 'LLL',\n  narrow: 'LLLLL'\n};\nconst DAY_OF_WEEK_FORMATS = {\n  long: 'EEEE',\n  short: 'EEE',\n  narrow: 'EEEEE'\n};\n/** Adds date-fns support to Angular Material. */\nclass DateFnsAdapter extends DateAdapter {\n  constructor() {\n    super();\n    const matDateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    this.setLocale(matDateLocale);\n  }\n  getYear(date) {\n    return getYear(date);\n  }\n  getMonth(date) {\n    return getMonth(date);\n  }\n  getDate(date) {\n    return getDate(date);\n  }\n  getDayOfWeek(date) {\n    return getDay(date);\n  }\n  getMonthNames(style) {\n    const pattern = MONTH_FORMATS[style];\n    return range(12, i => this.format(new Date(2017, i, 1), pattern));\n  }\n  getDateNames() {\n    const dtf = typeof Intl !== 'undefined' ? new Intl.DateTimeFormat(this.locale.code, {\n      day: 'numeric',\n      timeZone: 'utc'\n    }) : null;\n    return range(31, i => {\n      if (dtf) {\n        // date-fns doesn't appear to support this functionality.\n        // Fall back to `Intl` on supported browsers.\n        const date = new Date();\n        date.setUTCFullYear(2017, 0, i + 1);\n        date.setUTCHours(0, 0, 0, 0);\n        return dtf.format(date).replace(/[\\u200e\\u200f]/g, '');\n      }\n      return i + '';\n    });\n  }\n  getDayOfWeekNames(style) {\n    const pattern = DAY_OF_WEEK_FORMATS[style];\n    return range(7, i => this.format(new Date(2017, 0, i + 1), pattern));\n  }\n  getYearName(date) {\n    return this.format(date, 'y');\n  }\n  getFirstDayOfWeek() {\n    return this.locale.options?.weekStartsOn ?? 0;\n  }\n  getNumDaysInMonth(date) {\n    return getDaysInMonth(date);\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const result = new Date();\n    result.setFullYear(year, month, date);\n    result.setHours(0, 0, 0, 0);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    if (typeof value == 'string' && value.length > 0) {\n      const iso8601Date = parseISO(value);\n      if (this.isValid(iso8601Date)) {\n        return iso8601Date;\n      }\n      const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];\n      if (!parseFormat.length) {\n        throw Error('Formats array must not be empty.');\n      }\n      for (const currentFormat of formats) {\n        const fromFormat = parse(value, currentFormat, new Date(), {\n          locale: this.locale\n        });\n        if (this.isValid(fromFormat)) {\n          return fromFormat;\n        }\n      }\n      return this.invalid();\n    } else if (typeof value === 'number') {\n      return new Date(value);\n    } else if (value instanceof Date) {\n      return this.clone(value);\n    }\n    return null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('DateFnsAdapter: Cannot format invalid date.');\n    }\n    return format(date, displayFormat, {\n      locale: this.locale\n    });\n  }\n  addCalendarYears(date, years) {\n    return addYears(date, years);\n  }\n  addCalendarMonths(date, months) {\n    return addMonths(date, months);\n  }\n  addCalendarDays(date, days) {\n    return addDays(date, days);\n  }\n  toIso8601(date) {\n    return formatISO(date, {\n      representation: 'date'\n    });\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      const date = parseISO(value);\n      if (this.isValid(date)) {\n        return date;\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return isDate(obj);\n  }\n  isValid(date) {\n    return isValid(date);\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  setTime(target, hours, minutes, seconds) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (hours < 0 || hours > 23) {\n        throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n      }\n      if (minutes < 0 || minutes > 59) {\n        throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n      }\n      if (seconds < 0 || seconds > 59) {\n        throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n      }\n    }\n    return set(this.clone(target), {\n      hours,\n      minutes,\n      seconds,\n      milliseconds: 0\n    });\n  }\n  getHours(date) {\n    return getHours(date);\n  }\n  getMinutes(date) {\n    return getMinutes(date);\n  }\n  getSeconds(date) {\n    return getSeconds(date);\n  }\n  parseTime(value, parseFormat) {\n    return this.parse(value, parseFormat);\n  }\n  addSeconds(date, amount) {\n    return addSeconds(date, amount);\n  }\n  static ɵfac = function DateFnsAdapter_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DateFnsAdapter)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DateFnsAdapter,\n    factory: DateFnsAdapter.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DateFnsAdapter, [{\n    type: Injectable\n  }], () => [], null);\n})();\nconst MAT_DATE_FNS_FORMATS = {\n  parse: {\n    dateInput: 'P',\n    timeInput: 'p'\n  },\n  display: {\n    dateInput: 'P',\n    timeInput: 'p',\n    monthYearLabel: 'LLL uuuu',\n    dateA11yLabel: 'PP',\n    monthYearA11yLabel: 'LLLL uuuu',\n    timeOptionLabel: 'p'\n  }\n};\nclass DateFnsModule {\n  static ɵfac = function DateFnsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DateFnsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DateFnsModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: DateAdapter,\n      useClass: DateFnsAdapter,\n      deps: [MAT_DATE_LOCALE]\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DateFnsModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: DateFnsAdapter,\n        deps: [MAT_DATE_LOCALE]\n      }]\n    }]\n  }], null, null);\n})();\nclass MatDateFnsModule {\n  static ɵfac = function MatDateFnsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDateFnsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDateFnsModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideDateFnsAdapter()]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDateFnsModule, [{\n    type: NgModule,\n    args: [{\n      providers: [provideDateFnsAdapter()]\n    }]\n  }], null, null);\n})();\nfunction provideDateFnsAdapter(formats = MAT_DATE_FNS_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: DateFnsAdapter,\n    deps: [MAT_DATE_LOCALE]\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\nexport { DateFnsAdapter, DateFnsModule, MAT_DATE_FNS_FORMATS, MatDateFnsModule, provideDateFnsAdapter };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,SAAS,MAAM,QAAQ,eAAe;AACpC,QAAM,cAAc,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAY,CAAC,IAAI,cAAc,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAGA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,sBAAsB;AAAA,EAC1B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV;AAEA,IAAM,iBAAN,MAAM,wBAAuB,YAAY;AAAA,EACvC,cAAc;AACZ,UAAM;AACN,UAAM,gBAAgB,OAAO,iBAAiB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,UAAU,aAAa;AAAA,EAC9B;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA,EACA,SAAS,MAAM;AACb,WAAO,SAAS,IAAI;AAAA,EACtB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,OAAO,IAAI;AAAA,EACpB;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,UAAU,cAAc,KAAK;AACnC,WAAO,MAAM,IAAI,OAAK,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC;AAAA,EAClE;AAAA,EACA,eAAe;AACb,UAAM,MAAM,OAAO,SAAS,cAAc,IAAI,KAAK,eAAe,KAAK,OAAO,MAAM;AAAA,MAClF,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,CAAC,IAAI;AACL,WAAO,MAAM,IAAI,OAAK;AACpB,UAAI,KAAK;AAGP,cAAM,OAAO,oBAAI,KAAK;AACtB,aAAK,eAAe,MAAM,GAAG,IAAI,CAAC;AAClC,aAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC3B,eAAO,IAAI,OAAO,IAAI,EAAE,QAAQ,mBAAmB,EAAE;AAAA,MACvD;AACA,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,UAAU,oBAAoB,KAAK;AACzC,WAAO,MAAM,GAAG,OAAK,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;AAAA,EACrE;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK,OAAO,MAAM,GAAG;AAAA,EAC9B;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,OAAO,SAAS,gBAAgB;AAAA,EAC9C;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,eAAe,IAAI;AAAA,EAC5B;AAAA,EACA,MAAM,MAAM;AACV,WAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAAA,EAChC;AAAA,EACA,WAAW,MAAM,OAAO,MAAM;AAC5B,QAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,cAAM,MAAM,wBAAwB,KAAK,4CAA4C;AAAA,MACvF;AACA,UAAI,OAAO,GAAG;AACZ,cAAM,MAAM,iBAAiB,IAAI,mCAAmC;AAAA,MACtE;AAAA,IACF;AAGA,UAAM,SAAS,oBAAI,KAAK;AACxB,WAAO,YAAY,MAAM,OAAO,IAAI;AACpC,WAAO,SAAS,GAAG,GAAG,GAAG,CAAC;AAE1B,QAAI,OAAO,SAAS,KAAK,UAAU,OAAO,cAAc,eAAe,YAAY;AACjF,YAAM,MAAM,iBAAiB,IAAI,2BAA2B,KAAK,IAAI;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,oBAAI,KAAK;AAAA,EAClB;AAAA,EACA,MAAM,OAAO,aAAa;AACxB,QAAI,OAAO,SAAS,YAAY,MAAM,SAAS,GAAG;AAChD,YAAM,cAAc,SAAS,KAAK;AAClC,UAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,eAAO;AAAA,MACT;AACA,YAAM,UAAU,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AACvE,UAAI,CAAC,YAAY,QAAQ;AACvB,cAAM,MAAM,kCAAkC;AAAA,MAChD;AACA,iBAAW,iBAAiB,SAAS;AACnC,cAAM,aAAa,MAAM,OAAO,eAAe,oBAAI,KAAK,GAAG;AAAA,UACzD,QAAQ,KAAK;AAAA,QACf,CAAC;AACD,YAAI,KAAK,QAAQ,UAAU,GAAG;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB,WAAW,iBAAiB,MAAM;AAChC,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM,eAAe;AAC1B,QAAI,CAAC,KAAK,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,6CAA6C;AAAA,IAC3D;AACA,WAAO,OAAO,MAAM,eAAe;AAAA,MACjC,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,MAAM,OAAO;AAC5B,WAAO,SAAS,MAAM,KAAK;AAAA,EAC7B;AAAA,EACA,kBAAkB,MAAM,QAAQ;AAC9B,WAAO,UAAU,MAAM,MAAM;AAAA,EAC/B;AAAA,EACA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU,MAAM;AACd,WAAO,UAAU,MAAM;AAAA,MACrB,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,YAAM,OAAO,SAAS,KAAK;AAC3B,UAAI,KAAK,QAAQ,IAAI,GAAG;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,OAAO,GAAG;AAAA,EACnB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA,EACA,UAAU;AACR,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAAA,EACA,QAAQ,QAAQ,OAAO,SAAS,SAAS;AACvC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,cAAM,MAAM,kBAAkB,KAAK,0CAA0C;AAAA,MAC/E;AACA,UAAI,UAAU,KAAK,UAAU,IAAI;AAC/B,cAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,MACrF;AACA,UAAI,UAAU,KAAK,UAAU,IAAI;AAC/B,cAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,MACrF;AAAA,IACF;AACA,WAAO,IAAI,KAAK,MAAM,MAAM,GAAG;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,SAAS,MAAM;AACb,WAAO,SAAS,IAAI;AAAA,EACtB;AAAA,EACA,WAAW,MAAM;AACf,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,MAAM;AACf,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,UAAU,OAAO,aAAa;AAC5B,WAAO,KAAK,MAAM,OAAO,WAAW;AAAA,EACtC;AAAA,EACA,WAAW,MAAM,QAAQ;AACvB,WAAO,WAAW,MAAM,MAAM;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AACF;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM,CAAC,eAAe;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,sBAAsB,CAAC;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,sBAAsB,CAAC;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,sBAAsB,UAAU,sBAAsB;AAC7D,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM,CAAC,eAAe;AAAA,EACxB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;", "names": []}