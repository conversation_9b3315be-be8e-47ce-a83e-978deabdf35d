{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/fuzzy-search@3.2.1/node_modules/fuzzy-search/src/Helper.js", "../../../../../../node_modules/.pnpm/fuzzy-search@3.2.1/node_modules/fuzzy-search/src/FuzzySearch.js"], "sourcesContent": ["export default class Helper {\n  static getDescendantProperty(object, path, list = []) {\n    let firstSegment;\n    let remaining;\n    let dotIndex;\n    let value;\n    let index;\n    let length;\n    if (path) {\n      dotIndex = path.indexOf('.');\n      if (dotIndex === -1) {\n        firstSegment = path;\n      } else {\n        firstSegment = path.slice(0, dotIndex);\n        remaining = path.slice(dotIndex + 1);\n      }\n      value = object[firstSegment];\n      if (value !== null && typeof value !== 'undefined') {\n        if (!remaining && (typeof value === 'string' || typeof value === 'number')) {\n          list.push(value);\n        } else if (Object.prototype.toString.call(value) === '[object Array]') {\n          for (index = 0, length = value.length; index < length; index++) {\n            Helper.getDescendantProperty(value[index], remaining, list);\n          }\n        } else if (remaining) {\n          Helper.getDescendantProperty(value, remaining, list);\n        }\n      }\n    } else {\n      list.push(object);\n    }\n    return list;\n  }\n}", "import Helper from './Helper';\nexport default class FuzzySearch {\n  constructor(haystack = [], keys = [], options = {}) {\n    if (!Array.isArray(keys)) {\n      options = keys;\n      keys = [];\n    }\n    this.haystack = haystack;\n    this.keys = keys;\n    this.options = Object.assign({\n      caseSensitive: false,\n      sort: false\n    }, options);\n  }\n  search(query = '') {\n    if (query === '') {\n      return this.haystack;\n    }\n    const results = [];\n    for (let i = 0; i < this.haystack.length; i++) {\n      const item = this.haystack[i];\n      if (this.keys.length === 0) {\n        const score = FuzzySearch.isMatch(item, query, this.options.caseSensitive);\n        if (score) {\n          results.push({\n            item,\n            score\n          });\n        }\n      } else {\n        for (let y = 0; y < this.keys.length; y++) {\n          const propertyValues = Helper.getDescendantProperty(item, this.keys[y]);\n          let found = false;\n          for (let z = 0; z < propertyValues.length; z++) {\n            const score = FuzzySearch.isMatch(propertyValues[z], query, this.options.caseSensitive);\n            if (score) {\n              found = true;\n              results.push({\n                item,\n                score\n              });\n              break;\n            }\n          }\n          if (found) {\n            break;\n          }\n        }\n      }\n    }\n    if (this.options.sort) {\n      results.sort((a, b) => a.score - b.score);\n    }\n    return results.map(result => result.item);\n  }\n  static isMatch(item, query, caseSensitive) {\n    item = String(item);\n    query = String(query);\n    if (!caseSensitive) {\n      item = item.toLocaleLowerCase();\n      query = query.toLocaleLowerCase();\n    }\n    const indexes = FuzzySearch.nearestIndexesFor(item, query);\n    if (!indexes) {\n      return false;\n    }\n\n    // Exact matches should be first.\n    if (item === query) {\n      return 1;\n    }\n\n    // If we have more than 2 letters, matches close to each other should be first.\n    if (indexes.length > 1) {\n      return 2 + (indexes[indexes.length - 1] - indexes[0]);\n    }\n\n    // Matches closest to the start of the string should be first.\n    return 2 + indexes[0];\n  }\n  static nearestIndexesFor(item, query) {\n    const letters = query.split('');\n    let indexes = [];\n    const indexesOfFirstLetter = FuzzySearch.indexesOfFirstLetter(item, query);\n    indexesOfFirstLetter.forEach((startingIndex, loopingIndex) => {\n      let index = startingIndex + 1;\n      indexes[loopingIndex] = [startingIndex];\n      for (let i = 1; i < letters.length; i++) {\n        const letter = letters[i];\n        index = item.indexOf(letter, index);\n        if (index === -1) {\n          indexes[loopingIndex] = false;\n          break;\n        }\n        indexes[loopingIndex].push(index);\n        index++;\n      }\n    });\n    indexes = indexes.filter(letterIndexes => letterIndexes !== false);\n    if (!indexes.length) {\n      return false;\n    }\n    return indexes.sort((a, b) => {\n      if (a.length === 1) {\n        return a[0] - b[0];\n      }\n      a = a[a.length - 1] - a[0];\n      b = b[b.length - 1] - b[0];\n      return a - b;\n    })[0];\n  }\n  static indexesOfFirstLetter(item, query) {\n    const match = query[0];\n    return item.split('').map((letter, index) => {\n      if (letter !== match) {\n        return false;\n      }\n      return index;\n    }).filter(index => index !== false);\n  }\n}"], "mappings": ";;;AAAA,IAAqB,SAArB,MAAqB,QAAO;AAAA,EAC1B,OAAO,sBAAsB,QAAQ,MAAM,OAAO,CAAC,GAAG;AACpD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM;AACR,iBAAW,KAAK,QAAQ,GAAG;AAC3B,UAAI,aAAa,IAAI;AACnB,uBAAe;AAAA,MACjB,OAAO;AACL,uBAAe,KAAK,MAAM,GAAG,QAAQ;AACrC,oBAAY,KAAK,MAAM,WAAW,CAAC;AAAA,MACrC;AACA,cAAQ,OAAO,YAAY;AAC3B,UAAI,UAAU,QAAQ,OAAO,UAAU,aAAa;AAClD,YAAI,CAAC,cAAc,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AAC1E,eAAK,KAAK,KAAK;AAAA,QACjB,WAAW,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,kBAAkB;AACrE,eAAK,QAAQ,GAAG,SAAS,MAAM,QAAQ,QAAQ,QAAQ,SAAS;AAC9D,oBAAO,sBAAsB,MAAM,KAAK,GAAG,WAAW,IAAI;AAAA,UAC5D;AAAA,QACF,WAAW,WAAW;AACpB,kBAAO,sBAAsB,OAAO,WAAW,IAAI;AAAA,QACrD;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,KAAK,MAAM;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACF;;;AChCA,IAAqB,cAArB,MAAqB,aAAY;AAAA,EAC/B,YAAY,WAAW,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;AAClD,QAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,gBAAU;AACV,aAAO,CAAC;AAAA,IACV;AACA,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU,OAAO,OAAO;AAAA,MAC3B,eAAe;AAAA,MACf,MAAM;AAAA,IACR,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,OAAO,QAAQ,IAAI;AACjB,QAAI,UAAU,IAAI;AAChB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,UAAI,KAAK,KAAK,WAAW,GAAG;AAC1B,cAAM,QAAQ,aAAY,QAAQ,MAAM,OAAO,KAAK,QAAQ,aAAa;AACzE,YAAI,OAAO;AACT,kBAAQ,KAAK;AAAA,YACX;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,gBAAM,iBAAiB,OAAO,sBAAsB,MAAM,KAAK,KAAK,CAAC,CAAC;AACtE,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,kBAAM,QAAQ,aAAY,QAAQ,eAAe,CAAC,GAAG,OAAO,KAAK,QAAQ,aAAa;AACtF,gBAAI,OAAO;AACT,sBAAQ;AACR,sBAAQ,KAAK;AAAA,gBACX;AAAA,gBACA;AAAA,cACF,CAAC;AACD;AAAA,YACF;AAAA,UACF;AACA,cAAI,OAAO;AACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,QAAQ,MAAM;AACrB,cAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAAA,IAC1C;AACA,WAAO,QAAQ,IAAI,YAAU,OAAO,IAAI;AAAA,EAC1C;AAAA,EACA,OAAO,QAAQ,MAAM,OAAO,eAAe;AACzC,WAAO,OAAO,IAAI;AAClB,YAAQ,OAAO,KAAK;AACpB,QAAI,CAAC,eAAe;AAClB,aAAO,KAAK,kBAAkB;AAC9B,cAAQ,MAAM,kBAAkB;AAAA,IAClC;AACA,UAAM,UAAU,aAAY,kBAAkB,MAAM,KAAK;AACzD,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,OAAO;AAClB,aAAO;AAAA,IACT;AAGA,QAAI,QAAQ,SAAS,GAAG;AACtB,aAAO,KAAK,QAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,CAAC;AAAA,IACrD;AAGA,WAAO,IAAI,QAAQ,CAAC;AAAA,EACtB;AAAA,EACA,OAAO,kBAAkB,MAAM,OAAO;AACpC,UAAM,UAAU,MAAM,MAAM,EAAE;AAC9B,QAAI,UAAU,CAAC;AACf,UAAM,uBAAuB,aAAY,qBAAqB,MAAM,KAAK;AACzE,yBAAqB,QAAQ,CAAC,eAAe,iBAAiB;AAC5D,UAAI,QAAQ,gBAAgB;AAC5B,cAAQ,YAAY,IAAI,CAAC,aAAa;AACtC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,SAAS,QAAQ,CAAC;AACxB,gBAAQ,KAAK,QAAQ,QAAQ,KAAK;AAClC,YAAI,UAAU,IAAI;AAChB,kBAAQ,YAAY,IAAI;AACxB;AAAA,QACF;AACA,gBAAQ,YAAY,EAAE,KAAK,KAAK;AAChC;AAAA,MACF;AAAA,IACF,CAAC;AACD,cAAU,QAAQ,OAAO,mBAAiB,kBAAkB,KAAK;AACjE,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,KAAK,CAAC,GAAG,MAAM;AAC5B,UAAI,EAAE,WAAW,GAAG;AAClB,eAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACnB;AACA,UAAI,EAAE,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;AACzB,UAAI,EAAE,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;AACzB,aAAO,IAAI;AAAA,IACb,CAAC,EAAE,CAAC;AAAA,EACN;AAAA,EACA,OAAO,qBAAqB,MAAM,OAAO;AACvC,UAAM,QAAQ,MAAM,CAAC;AACrB,WAAO,KAAK,MAAM,EAAE,EAAE,IAAI,CAAC,QAAQ,UAAU;AAC3C,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,OAAO,WAAS,UAAU,KAAK;AAAA,EACpC;AACF;", "names": []}