// node_modules/.pnpm/@angular+cdk@19.2.18_@angul_0ed178fe1a1f0f18d8ba5d05f1832738/node_modules/@angular/cdk/fesm2022/boolean-property-DaaVhX5A.mjs
function coerceBooleanProperty(value) {
  return value != null && `${value}` !== "false";
}

// node_modules/.pnpm/@angular+cdk@19.2.18_@angul_0ed178fe1a1f0f18d8ba5d05f1832738/node_modules/@angular/cdk/fesm2022/coercion.mjs
function coerceStringArray(value, separator = /\s+/) {
  const result = [];
  if (value != null) {
    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);
    for (const sourceValue of sourceValues) {
      const trimmedString = `${sourceValue}`.trim();
      if (trimmedString) {
        result.push(trimmedString);
      }
    }
  }
  return result;
}

export {
  coerceBooleanProperty,
  coerceStringArray
};
//# sourceMappingURL=chunk-T2PAMC2C.js.map
