{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+material@19.2.18_285bc3c224c839ebf1057ffc6879888d/node_modules/@angular/material/fesm2022/slider.mjs"], "sourcesContent": ["import { Directionality } from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, NgZone, Renderer2, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, signal, Directive, Output, NgModule } from '@angular/core';\nimport { b as RippleState, M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/coercion';\n\n/**\n * Thumb types: range slider has two thumbs (START, END) whereas single point\n * slider only has one thumb (END).\n */\nconst _c0 = [\"knob\"];\nconst _c1 = [\"valueIndicatorContainer\"];\nfunction MatSliderVisualThumb_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 1)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.valueIndicatorText);\n  }\n}\nconst _c2 = [\"trackActive\"];\nconst _c3 = [\"*\"];\nfunction MatSlider_Conditional_6_Conditional_2_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const tickMark_r1 = ctx.$implicit;\n    const ɵ$index_14_r2 = ctx.$index;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(tickMark_r1 === 0 ? \"mdc-slider__tick-mark--active\" : \"mdc-slider__tick-mark--inactive\");\n    i0.ɵɵstyleProp(\"transform\", ctx_r2._calcTickMarkTransform(ɵ$index_14_r2));\n  }\n}\nfunction MatSlider_Conditional_6_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, MatSlider_Conditional_6_Conditional_2_For_1_Template, 1, 4, \"div\", 8, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵrepeater(ctx_r2._tickMarks);\n  }\n}\nfunction MatSlider_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6, 1);\n    i0.ɵɵtemplate(2, MatSlider_Conditional_6_Conditional_2_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r2._cachedWidth ? 2 : -1);\n  }\n}\nfunction MatSlider_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-slider-visual-thumb\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"discrete\", ctx_r2.discrete)(\"thumbPosition\", 1)(\"valueIndicatorText\", ctx_r2.startValueIndicatorText);\n  }\n}\nvar _MatThumb;\n(function (_MatThumb) {\n  _MatThumb[_MatThumb[\"START\"] = 1] = \"START\";\n  _MatThumb[_MatThumb[\"END\"] = 2] = \"END\";\n})(_MatThumb || (_MatThumb = {}));\n/** Tick mark enum, for discrete sliders. */\nvar _MatTickMark;\n(function (_MatTickMark) {\n  _MatTickMark[_MatTickMark[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  _MatTickMark[_MatTickMark[\"INACTIVE\"] = 1] = \"INACTIVE\";\n})(_MatTickMark || (_MatTickMark = {}));\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER = new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://v17.material.angular.dev/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {\n  /** The MatSliderThumb that was interacted with. */\n  source;\n  /** The MatSlider that was interacted with. */\n  parent;\n  /** The new value of the source slider. */\n  value;\n}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nclass MatSliderVisualThumb {\n  _cdr = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _slider = inject(MAT_SLIDER);\n  _renderer = inject(Renderer2);\n  _listenerCleanups;\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  discrete;\n  /** Indicates which slider thumb this input corresponds to. */\n  thumbPosition;\n  /** The display value of the slider thumb. */\n  valueIndicatorText;\n  /** The MatRipple for this slider thumb. */\n  _ripple;\n  /** The slider thumb knob. */\n  _knob;\n  /** The slider thumb value indicator container. */\n  _valueIndicatorContainer;\n  /** The slider input corresponding to this slider thumb. */\n  _sliderInput;\n  /** The native html element of the slider input corresponding to this thumb. */\n  _sliderInputEl;\n  /** The RippleRef for the slider thumbs hover state. */\n  _hoverRippleRef;\n  /** The RippleRef for the slider thumbs focus state. */\n  _focusRippleRef;\n  /** The RippleRef for the slider thumbs active state. */\n  _activeRippleRef;\n  /** Whether the slider thumb is currently being hovered. */\n  _isHovered = false;\n  /** Whether the slider thumb is currently being pressed. */\n  _isActive = false;\n  /** Whether the value indicator tooltip is visible. */\n  _isValueIndicatorVisible = false;\n  /** The host native HTML input element. */\n  _hostElement = inject(ElementRef).nativeElement;\n  _platform = inject(Platform);\n  constructor() {}\n  ngAfterViewInit() {\n    const sliderInput = this._slider._getInput(this.thumbPosition);\n    // No-op if the slider isn't configured properly. `MatSlider` will\n    // throw an error instructing the user how to set up the slider.\n    if (!sliderInput) {\n      return;\n    }\n    this._ripple.radius = 24;\n    this._sliderInput = sliderInput;\n    this._sliderInputEl = this._sliderInput._hostElement;\n    // These listeners don't update any data bindings so we bind them outside\n    // of the NgZone to prevent Angular from needlessly running change detection.\n    this._ngZone.runOutsideAngular(() => {\n      const input = this._sliderInputEl;\n      const renderer = this._renderer;\n      this._listenerCleanups = [renderer.listen(input, 'pointermove', this._onPointerMove), renderer.listen(input, 'pointerdown', this._onDragStart), renderer.listen(input, 'pointerup', this._onDragEnd), renderer.listen(input, 'pointerleave', this._onMouseLeave), renderer.listen(input, 'focus', this._onFocus), renderer.listen(input, 'blur', this._onBlur)];\n    });\n  }\n  ngOnDestroy() {\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  _onPointerMove = event => {\n    if (this._sliderInput._isFocused) {\n      return;\n    }\n    const rect = this._hostElement.getBoundingClientRect();\n    const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n    this._isHovered = isHovered;\n    if (isHovered) {\n      this._showHoverRipple();\n    } else {\n      this._hideRipple(this._hoverRippleRef);\n    }\n  };\n  _onMouseLeave = () => {\n    this._isHovered = false;\n    this._hideRipple(this._hoverRippleRef);\n  };\n  _onFocus = () => {\n    // We don't want to show the hover ripple on top of the focus ripple.\n    // Happen when the users cursor is over a thumb and then the user tabs to it.\n    this._hideRipple(this._hoverRippleRef);\n    this._showFocusRipple();\n    this._hostElement.classList.add('mdc-slider__thumb--focused');\n  };\n  _onBlur = () => {\n    // Happens when the user tabs away while still dragging a thumb.\n    if (!this._isActive) {\n      this._hideRipple(this._focusRippleRef);\n    }\n    // Happens when the user tabs away from a thumb but their cursor is still over it.\n    if (this._isHovered) {\n      this._showHoverRipple();\n    }\n    this._hostElement.classList.remove('mdc-slider__thumb--focused');\n  };\n  _onDragStart = event => {\n    if (event.button !== 0) {\n      return;\n    }\n    this._isActive = true;\n    this._showActiveRipple();\n  };\n  _onDragEnd = () => {\n    this._isActive = false;\n    this._hideRipple(this._activeRippleRef);\n    // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n    if (!this._sliderInput._isFocused) {\n      this._hideRipple(this._focusRippleRef);\n    }\n    // On Safari we need to immediately re-show the hover ripple because\n    // sliders do not retain focus from pointer events on that platform.\n    if (this._platform.SAFARI) {\n      this._showHoverRipple();\n    }\n  };\n  /** Handles displaying the hover ripple. */\n  _showHoverRipple() {\n    if (!this._isShowingRipple(this._hoverRippleRef)) {\n      this._hoverRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      });\n      this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n    }\n  }\n  /** Handles displaying the focus ripple. */\n  _showFocusRipple() {\n    // Show the focus ripple event if noop animations are enabled.\n    if (!this._isShowingRipple(this._focusRippleRef)) {\n      this._focusRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      }, true);\n      this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n    }\n  }\n  /** Handles displaying the active ripple. */\n  _showActiveRipple() {\n    if (!this._isShowingRipple(this._activeRippleRef)) {\n      this._activeRippleRef = this._showRipple({\n        enterDuration: 225,\n        exitDuration: 400\n      });\n      this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n    }\n  }\n  /** Whether the given rippleRef is currently fading in or visible. */\n  _isShowingRipple(rippleRef) {\n    return rippleRef?.state === RippleState.FADING_IN || rippleRef?.state === RippleState.VISIBLE;\n  }\n  /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n  _showRipple(animation, ignoreGlobalRippleConfig) {\n    if (this._slider.disabled) {\n      return;\n    }\n    this._showValueIndicator();\n    if (this._slider._isRange) {\n      const sibling = this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n      sibling._showValueIndicator();\n    }\n    if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n      return;\n    }\n    return this._ripple.launch({\n      animation: this._slider._noopAnimations ? {\n        enterDuration: 0,\n        exitDuration: 0\n      } : animation,\n      centered: true,\n      persistent: true\n    });\n  }\n  /**\n   * Fades out the given ripple.\n   * Also hides the value indicator if no ripple is showing.\n   */\n  _hideRipple(rippleRef) {\n    rippleRef?.fadeOut();\n    if (this._isShowingAnyRipple()) {\n      return;\n    }\n    if (!this._slider._isRange) {\n      this._hideValueIndicator();\n    }\n    const sibling = this._getSibling();\n    if (!sibling._isShowingAnyRipple()) {\n      this._hideValueIndicator();\n      sibling._hideValueIndicator();\n    }\n  }\n  /** Shows the value indicator ui. */\n  _showValueIndicator() {\n    this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n  }\n  /** Hides the value indicator ui. */\n  _hideValueIndicator() {\n    this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n  }\n  _getSibling() {\n    return this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n  }\n  /** Gets the value indicator container's native HTML element. */\n  _getValueIndicatorContainer() {\n    return this._valueIndicatorContainer?.nativeElement;\n  }\n  /** Gets the native HTML element of the slider thumb knob. */\n  _getKnob() {\n    return this._knob.nativeElement;\n  }\n  _isShowingAnyRipple() {\n    return this._isShowingRipple(this._hoverRippleRef) || this._isShowingRipple(this._focusRippleRef) || this._isShowingRipple(this._activeRippleRef);\n  }\n  static ɵfac = function MatSliderVisualThumb_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSliderVisualThumb)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSliderVisualThumb,\n    selectors: [[\"mat-slider-visual-thumb\"]],\n    viewQuery: function MatSliderVisualThumb_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatRipple, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._ripple = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._knob = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._valueIndicatorContainer = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-slider__thumb\", \"mat-mdc-slider-visual-thumb\"],\n    inputs: {\n      discrete: \"discrete\",\n      thumbPosition: \"thumbPosition\",\n      valueIndicatorText: \"valueIndicatorText\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SLIDER_VISUAL_THUMB,\n      useExisting: MatSliderVisualThumb\n    }])],\n    decls: 4,\n    vars: 2,\n    consts: [[\"knob\", \"\"], [\"valueIndicatorContainer\", \"\"], [1, \"mdc-slider__value-indicator-container\"], [1, \"mdc-slider__thumb-knob\"], [\"matRipple\", \"\", 1, \"mat-focus-indicator\", 3, \"matRippleDisabled\"], [1, \"mdc-slider__value-indicator\"], [1, \"mdc-slider__value-indicator-text\"]],\n    template: function MatSliderVisualThumb_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatSliderVisualThumb_Conditional_0_Template, 5, 1, \"div\", 2);\n        i0.ɵɵelement(1, \"div\", 3, 0)(3, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.discrete ? 0 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"matRippleDisabled\", true);\n      }\n    },\n    dependencies: [MatRipple],\n    styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderVisualThumb, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider-visual-thumb',\n      host: {\n        'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_SLIDER_VISUAL_THUMB,\n        useExisting: MatSliderVisualThumb\n      }],\n      imports: [MatRipple],\n      template: \"@if (discrete) {\\n  <div class=\\\"mdc-slider__value-indicator-container\\\" #valueIndicatorContainer>\\n    <div class=\\\"mdc-slider__value-indicator\\\">\\n      <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n    </div>\\n  </div>\\n}\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\",\n      styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\\n\"]\n    }]\n  }], () => [], {\n    discrete: [{\n      type: Input\n    }],\n    thumbPosition: [{\n      type: Input\n    }],\n    valueIndicatorText: [{\n      type: Input\n    }],\n    _ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }],\n    _knob: [{\n      type: ViewChild,\n      args: ['knob']\n    }],\n    _valueIndicatorContainer: [{\n      type: ViewChild,\n      args: ['valueIndicatorContainer']\n    }]\n  });\n})();\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nclass MatSlider {\n  _ngZone = inject(NgZone);\n  _cdr = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  /** The active portion of the slider track. */\n  _trackActive;\n  /** The slider thumb(s). */\n  _thumbs;\n  /** The sliders hidden range input(s). */\n  _input;\n  /** The sliders hidden range input(s). */\n  _inputs;\n  /** Whether the slider is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(v) {\n    this._disabled = v;\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    if (endInput) {\n      endInput.disabled = this._disabled;\n    }\n    if (startInput) {\n      startInput.disabled = this._disabled;\n    }\n  }\n  _disabled = false;\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  get discrete() {\n    return this._discrete;\n  }\n  set discrete(v) {\n    this._discrete = v;\n    this._updateValueIndicatorUIs();\n  }\n  _discrete = false;\n  /** Whether the slider displays tick marks along the slider track. */\n  showTickMarks = false;\n  /** The minimum value that the slider can have. */\n  get min() {\n    return this._min;\n  }\n  set min(v) {\n    const min = isNaN(v) ? this._min : v;\n    if (this._min !== min) {\n      this._updateMin(min);\n    }\n  }\n  _min = 0;\n  /**\n   * Theme color of the slider. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/slider/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether ripples are disabled in the slider. */\n  disableRipple = false;\n  _updateMin(min) {\n    const prevMin = this._min;\n    this._min = min;\n    this._isRange ? this._updateMinRange({\n      old: prevMin,\n      new: min\n    }) : this._updateMinNonRange(min);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMinRange(min) {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    startInput.min = min.new;\n    endInput.min = Math.max(min.new, startInput.value);\n    startInput.max = Math.min(endInput.max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    min.new < min.old ? this._onTranslateXChangeBySideEffect(endInput, startInput) : this._onTranslateXChangeBySideEffect(startInput, endInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMinNonRange(min) {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.min = min;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The maximum value that the slider can have. */\n  get max() {\n    return this._max;\n  }\n  set max(v) {\n    const max = isNaN(v) ? this._max : v;\n    if (this._max !== max) {\n      this._updateMax(max);\n    }\n  }\n  _max = 100;\n  _updateMax(max) {\n    const prevMax = this._max;\n    this._max = max;\n    this._isRange ? this._updateMaxRange({\n      old: prevMax,\n      new: max\n    }) : this._updateMaxNonRange(max);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMaxRange(max) {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    endInput.max = max.new;\n    startInput.max = Math.min(max.new, endInput.value);\n    endInput.min = startInput.value;\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    max.new > max.old ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMaxNonRange(max) {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.max = max;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The values at which the thumb will snap. */\n  get step() {\n    return this._step;\n  }\n  set step(v) {\n    const step = isNaN(v) ? this._step : v;\n    if (this._step !== step) {\n      this._updateStep(step);\n    }\n  }\n  _step = 1;\n  _updateStep(step) {\n    this._step = step;\n    this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n    this._onMinMaxOrStepChange();\n  }\n  _updateStepRange() {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    const prevStartValue = startInput.value;\n    endInput.min = this._min;\n    startInput.max = this._max;\n    endInput.step = this._step;\n    startInput.step = this._step;\n    if (this._platform.SAFARI) {\n      endInput.value = endInput.value;\n      startInput.value = startInput.value;\n    }\n    endInput.min = Math.max(this._min, startInput.value);\n    startInput.max = Math.min(this._max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    endInput.value < prevStartValue ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateStepNonRange() {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.step = this._step;\n      if (this._platform.SAFARI) {\n        input.value = input.value;\n      }\n      input._updateThumbUIByValue();\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /**\n   * Function that will be used to format the value before it is displayed\n   * in the thumb label. Can be used to format very large number in order\n   * for them to fit into the slider thumb.\n   */\n  displayWith = value => `${value}`;\n  /** Used to keep track of & render the active & inactive tick marks on the slider track. */\n  _tickMarks;\n  /** Whether animations have been disabled. */\n  _noopAnimations;\n  /** Subscription to changes to the directionality (LTR / RTL) context for the application. */\n  _dirChangeSubscription;\n  /** Observer used to monitor size changes in the slider. */\n  _resizeObserver;\n  // Stored dimensions to avoid calling getBoundingClientRect redundantly.\n  _cachedWidth;\n  _cachedLeft;\n  _rippleRadius = 24;\n  // The value indicator tooltip text for the visual slider thumb(s).\n  /** @docs-private */\n  startValueIndicatorText = '';\n  /** @docs-private */\n  endValueIndicatorText = '';\n  // Used to control the translateX of the visual slider thumb(s).\n  _endThumbTransform;\n  _startThumbTransform;\n  _isRange = false;\n  /** Whether the slider is rtl. */\n  _isRtl = false;\n  _hasViewInitialized = false;\n  /**\n   * The width of the tick mark track.\n   * The tick mark track width is different from full track width\n   */\n  _tickMarkTrackWidth = 0;\n  _hasAnimation = false;\n  _resizeTimer = null;\n  _platform = inject(Platform);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    if (this._dir) {\n      this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n      this._isRtl = this._dir.value === 'rtl';\n    }\n  }\n  /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n  _knobRadius = 8;\n  _inputPadding;\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._updateDimensions();\n    }\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    this._isRange = !!eInput && !!sInput;\n    this._cdr.detectChanges();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      _validateInputs(this._isRange, this._getInput(_MatThumb.END), this._getInput(_MatThumb.START));\n    }\n    const thumb = this._getThumb(_MatThumb.END);\n    this._rippleRadius = thumb._ripple.radius;\n    this._inputPadding = this._rippleRadius - this._knobRadius;\n    this._isRange ? this._initUIRange(eInput, sInput) : this._initUINonRange(eInput);\n    this._updateTrackUI(eInput);\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._observeHostResize();\n    this._cdr.detectChanges();\n  }\n  _initUINonRange(eInput) {\n    eInput.initProps();\n    eInput.initUI();\n    this._updateValueIndicatorUI(eInput);\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n  }\n  _initUIRange(eInput, sInput) {\n    eInput.initProps();\n    eInput.initUI();\n    sInput.initProps();\n    sInput.initUI();\n    eInput._updateMinMax();\n    sInput._updateMinMax();\n    eInput._updateStaticStyles();\n    sInput._updateStaticStyles();\n    this._updateValueIndicatorUIs();\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n    sInput._updateThumbUIByValue();\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._resizeObserver?.disconnect();\n    this._resizeObserver = null;\n  }\n  /** Handles updating the slider ui after a dir change. */\n  _onDirChange() {\n    this._isRtl = this._dir?.value === 'rtl';\n    this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n    this._updateTickMarkUI();\n  }\n  _onDirChangeRange() {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    endInput._setIsLeftThumb();\n    startInput._setIsLeftThumb();\n    endInput.translateX = endInput._calcTranslateXByValue();\n    startInput.translateX = startInput._calcTranslateXByValue();\n    endInput._updateStaticStyles();\n    startInput._updateStaticStyles();\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    endInput._updateThumbUIByValue();\n    startInput._updateThumbUIByValue();\n  }\n  _onDirChangeNonRange() {\n    const input = this._getInput(_MatThumb.END);\n    input._updateThumbUIByValue();\n  }\n  /** Starts observing and updating the slider if the host changes its size. */\n  _observeHostResize() {\n    if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeObserver = new ResizeObserver(() => {\n        if (this._isActive()) {\n          return;\n        }\n        if (this._resizeTimer) {\n          clearTimeout(this._resizeTimer);\n        }\n        this._onResize();\n      });\n      this._resizeObserver.observe(this._elementRef.nativeElement);\n    });\n  }\n  /** Whether any of the thumbs are currently active. */\n  _isActive() {\n    return this._getThumb(_MatThumb.START)._isActive || this._getThumb(_MatThumb.END)._isActive;\n  }\n  _getValue(thumbPosition = _MatThumb.END) {\n    const input = this._getInput(thumbPosition);\n    if (!input) {\n      return this.min;\n    }\n    return input.value;\n  }\n  _skipUpdate() {\n    return !!(this._getInput(_MatThumb.START)?._skipUIUpdate || this._getInput(_MatThumb.END)?._skipUIUpdate);\n  }\n  /** Stores the slider dimensions. */\n  _updateDimensions() {\n    this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n    this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n  }\n  /** Sets the styles for the active portion of the track. */\n  _setTrackActiveStyles(styles) {\n    const trackStyle = this._trackActive.nativeElement.style;\n    trackStyle.left = styles.left;\n    trackStyle.right = styles.right;\n    trackStyle.transformOrigin = styles.transformOrigin;\n    trackStyle.transform = styles.transform;\n  }\n  /** Returns the translateX positioning for a tick mark based on it's index. */\n  _calcTickMarkTransform(index) {\n    // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n    const offset = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n    const translateX = this._isRtl ? this._cachedWidth - 6 - offset : offset;\n    return `translateX(${translateX}px`;\n  }\n  // Handlers for updating the slider ui.\n  _onTranslateXChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateThumbUI(source);\n    this._updateTrackUI(source);\n    this._updateOverlappingThumbUI(source);\n  }\n  _onTranslateXChangeBySideEffect(input1, input2) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    input1._updateThumbUIByValue();\n    input2._updateThumbUIByValue();\n  }\n  _onValueChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateValueIndicatorUI(source);\n    this._updateTickMarkUI();\n    this._cdr.detectChanges();\n  }\n  _onMinMaxOrStepChange() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.markForCheck();\n  }\n  _onResize() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateDimensions();\n    if (this._isRange) {\n      const eInput = this._getInput(_MatThumb.END);\n      const sInput = this._getInput(_MatThumb.START);\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n      eInput._updateWidthInactive();\n      sInput._updateWidthInactive();\n    } else {\n      const eInput = this._getInput(_MatThumb.END);\n      if (eInput) {\n        eInput._updateThumbUIByValue();\n      }\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.detectChanges();\n  }\n  /** Whether or not the slider thumbs overlap. */\n  _thumbsOverlap = false;\n  /** Returns true if the slider knobs are overlapping one another. */\n  _areThumbsOverlapping() {\n    const startInput = this._getInput(_MatThumb.START);\n    const endInput = this._getInput(_MatThumb.END);\n    if (!startInput || !endInput) {\n      return false;\n    }\n    return endInput.translateX - startInput.translateX < 20;\n  }\n  /**\n   * Updates the class names of overlapping slider thumbs so\n   * that the current active thumb is styled to be on \"top\".\n   */\n  _updateOverlappingThumbClassNames(source) {\n    const sibling = source.getSibling();\n    const sourceThumb = this._getThumb(source.thumbPosition);\n    const siblingThumb = this._getThumb(sibling.thumbPosition);\n    siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n    sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n  }\n  /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n  _updateOverlappingThumbUI(source) {\n    if (!this._isRange || this._skipUpdate()) {\n      return;\n    }\n    if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n      this._thumbsOverlap = !this._thumbsOverlap;\n      this._updateOverlappingThumbClassNames(source);\n    }\n  }\n  // _MatThumb styles update conditions\n  //\n  // 1. TranslateX, resize, or dir change\n  //    - Reason: The thumb styles need to be updated according to the new translateX.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the translateX of the given thumb. */\n  _updateThumbUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const thumb = this._getThumb(source.thumbPosition === _MatThumb.END ? _MatThumb.END : _MatThumb.START);\n    thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n  }\n  // Value indicator text update conditions\n  //\n  // 1. Value\n  //    - Reason: The value displayed needs to be updated.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the value indicator tooltip ui for the given thumb. */\n  _updateValueIndicatorUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const valuetext = this.displayWith(source.value);\n    this._hasViewInitialized ? source._valuetext.set(valuetext) : source._hostElement.setAttribute('aria-valuetext', valuetext);\n    if (this.discrete) {\n      source.thumbPosition === _MatThumb.START ? this.startValueIndicatorText = valuetext : this.endValueIndicatorText = valuetext;\n      const visualThumb = this._getThumb(source.thumbPosition);\n      valuetext.length < 3 ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value') : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n    }\n  }\n  /** Updates all value indicator UIs in the slider. */\n  _updateValueIndicatorUIs() {\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    if (eInput) {\n      this._updateValueIndicatorUI(eInput);\n    }\n    if (sInput) {\n      this._updateValueIndicatorUI(sInput);\n    }\n  }\n  // Update Tick Mark Track Width\n  //\n  // 1. Min, max, or step\n  //    - Reason: The maximum reachable value may have changed.\n  //    - Side note: The maximum reachable value is different from the maximum value set by the\n  //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n  //      reachable value of 95.\n  // 2. Resize\n  //    - Reason: The position for the maximum reachable value needs to be recalculated.\n  /** Updates the width of the tick mark track. */\n  _updateTickMarkTrackUI() {\n    if (!this.showTickMarks || this._skipUpdate()) {\n      return;\n    }\n    const step = this._step && this._step > 0 ? this._step : 1;\n    const maxValue = Math.floor(this.max / step) * step;\n    const percentage = (maxValue - this.min) / (this.max - this.min);\n    this._tickMarkTrackWidth = (this._cachedWidth - 6) * percentage;\n  }\n  // Track active update conditions\n  //\n  // 1. TranslateX\n  //    - Reason: The track active should line up with the new thumb position.\n  // 2. Min or max\n  //    - Reason #1: The 'active' percentage needs to be recalculated.\n  //    - Reason #2: The value may have silently changed.\n  // 3. Step\n  //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n  // 4. Dir change\n  //    - Reason: The track active will need to be updated according to the new thumb position(s).\n  // 5. Resize\n  //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    this._isRange ? this._updateTrackUIRange(source) : this._updateTrackUINonRange(source);\n  }\n  _updateTrackUIRange(source) {\n    const sibling = source.getSibling();\n    if (!sibling || !this._cachedWidth) {\n      return;\n    }\n    const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n    if (source._isLeftThumb && this._cachedWidth) {\n      this._setTrackActiveStyles({\n        left: 'auto',\n        right: `${this._cachedWidth - sibling.translateX}px`,\n        transformOrigin: 'right',\n        transform: `scaleX(${activePercentage})`\n      });\n    } else {\n      this._setTrackActiveStyles({\n        left: `${sibling.translateX}px`,\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${activePercentage})`\n      });\n    }\n  }\n  _updateTrackUINonRange(source) {\n    this._isRtl ? this._setTrackActiveStyles({\n      left: 'auto',\n      right: '0px',\n      transformOrigin: 'right',\n      transform: `scaleX(${1 - source.fillPercentage})`\n    }) : this._setTrackActiveStyles({\n      left: '0px',\n      right: 'auto',\n      transformOrigin: 'left',\n      transform: `scaleX(${source.fillPercentage})`\n    });\n  }\n  // Tick mark update conditions\n  //\n  // 1. Value\n  //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n  // 2. Min, max, or step\n  //    - Reason #1: the number of tick marks may have changed.\n  //    - Reason #2: The value may have silently changed.\n  /** Updates the dots along the slider track. */\n  _updateTickMarkUI() {\n    if (!this.showTickMarks || this.step === undefined || this.min === undefined || this.max === undefined) {\n      return;\n    }\n    const step = this.step > 0 ? this.step : 1;\n    this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n  }\n  _updateTickMarkUINonRange(step) {\n    const value = this._getValue();\n    let numActive = Math.max(Math.round((value - this.min) / step), 0) + 1;\n    let numInactive = Math.max(Math.round((this.max - value) / step), 0) - 1;\n    this._isRtl ? numActive++ : numInactive++;\n    this._tickMarks = Array(numActive).fill(_MatTickMark.ACTIVE).concat(Array(numInactive).fill(_MatTickMark.INACTIVE));\n  }\n  _updateTickMarkUIRange(step) {\n    const endValue = this._getValue();\n    const startValue = this._getValue(_MatThumb.START);\n    const numInactiveBeforeStartThumb = Math.max(Math.round((startValue - this.min) / step), 0);\n    const numActive = Math.max(Math.round((endValue - startValue) / step) + 1, 0);\n    const numInactiveAfterEndThumb = Math.max(Math.round((this.max - endValue) / step), 0);\n    this._tickMarks = Array(numInactiveBeforeStartThumb).fill(_MatTickMark.INACTIVE).concat(Array(numActive).fill(_MatTickMark.ACTIVE), Array(numInactiveAfterEndThumb).fill(_MatTickMark.INACTIVE));\n  }\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition) {\n    if (thumbPosition === _MatThumb.END && this._input) {\n      return this._input;\n    }\n    if (this._inputs?.length) {\n      return thumbPosition === _MatThumb.START ? this._inputs.first : this._inputs.last;\n    }\n    return;\n  }\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition) {\n    return thumbPosition === _MatThumb.END ? this._thumbs?.last : this._thumbs?.first;\n  }\n  _setTransition(withAnimation) {\n    this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n  }\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event, rect) {\n    const radius = rect.width / 2;\n    const centerX = rect.x + radius;\n    const centerY = rect.y + radius;\n    const dx = event.clientX - centerX;\n    const dy = event.clientY - centerY;\n    return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n  }\n  static ɵfac = function MatSlider_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSlider)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSlider,\n    selectors: [[\"mat-slider\"]],\n    contentQueries: function MatSlider_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_THUMB, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_RANGE_THUMB, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._input = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputs = _t);\n      }\n    },\n    viewQuery: function MatSlider_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(MAT_SLIDER_VISUAL_THUMB, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._trackActive = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbs = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-slider\", \"mdc-slider\"],\n    hostVars: 12,\n    hostBindings: function MatSlider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n        i0.ɵɵclassProp(\"mdc-slider--range\", ctx._isRange)(\"mdc-slider--disabled\", ctx.disabled)(\"mdc-slider--discrete\", ctx.discrete)(\"mdc-slider--tick-marks\", ctx.showTickMarks)(\"_mat-animation-noopable\", ctx._noopAnimations);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      discrete: [2, \"discrete\", \"discrete\", booleanAttribute],\n      showTickMarks: [2, \"showTickMarks\", \"showTickMarks\", booleanAttribute],\n      min: [2, \"min\", \"min\", numberAttribute],\n      color: \"color\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      max: [2, \"max\", \"max\", numberAttribute],\n      step: [2, \"step\", \"step\", numberAttribute],\n      displayWith: \"displayWith\"\n    },\n    exportAs: [\"matSlider\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SLIDER,\n      useExisting: MatSlider\n    }])],\n    ngContentSelectors: _c3,\n    decls: 9,\n    vars: 5,\n    consts: [[\"trackActive\", \"\"], [\"tickMarkContainer\", \"\"], [1, \"mdc-slider__track\"], [1, \"mdc-slider__track--inactive\"], [1, \"mdc-slider__track--active\"], [1, \"mdc-slider__track--active_fill\"], [1, \"mdc-slider__tick-marks\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\"], [3, \"class\", \"transform\"]],\n    template: function MatSlider_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 2);\n        i0.ɵɵelement(2, \"div\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4);\n        i0.ɵɵelement(4, \"div\", 5, 0);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, MatSlider_Conditional_6_Template, 3, 1, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MatSlider_Conditional_7_Template, 1, 3, \"mat-slider-visual-thumb\", 7);\n        i0.ɵɵelement(8, \"mat-slider-visual-thumb\", 7);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(ctx.showTickMarks ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._isRange ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"discrete\", ctx.discrete)(\"thumbPosition\", 2)(\"valueIndicatorText\", ctx.endValueIndicatorText);\n      }\n    },\n    dependencies: [MatSliderVisualThumb],\n    styles: [\".mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%;pointer-events:none;height:var(--mdc-slider-inactive-track-height, 4px)}.mdc-slider__track--active,.mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider__track--active{overflow:hidden;border-radius:var(--mdc-slider-active-track-shape, var(--mat-sys-corner-full));height:var(--mdc-slider-active-track-height, 4px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 4px))/2)}.mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;transform-origin:left;transition:transform 80ms ease;border-color:var(--mdc-slider-active-track-color, var(--mat-sys-primary));border-top-width:var(--mdc-slider-active-track-height, 4px)}.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mat-sys-on-surface))}[dir=rtl] .mdc-slider__track--active_fill{-webkit-transform-origin:right;transform-origin:right}.mdc-slider__track--inactive{left:0;top:0;opacity:.24;background-color:var(--mdc-slider-inactive-track-color, var(--mat-sys-surface-variant));height:var(--mdc-slider-inactive-track-height, 4px);border-radius:var(--mdc-slider-inactive-track-shape, var(--mat-sys-corner-full))}.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mat-sys-on-surface));opacity:.24}.mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media(forced-colors: active){.mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider__value-indicator-container{bottom:44px;left:50%;pointer-events:none;position:absolute;transform:translateX(-50%);transform:var(--mat-slider-value-indicator-container-transform, translateX(-50%) rotate(-45deg))}.mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider__value-indicator{display:flex;align-items:center;border-radius:4px;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom;opacity:1;transition:transform 100ms cubic-bezier(0.4, 0, 1, 1);word-break:normal;background-color:var(--mdc-slider-label-container-color, var(--mat-sys-primary));color:var(--mdc-slider-label-label-text-color, var(--mat-sys-on-primary));width:var(--mat-slider-value-indicator-width, 28px);height:var(--mat-slider-value-indicator-height, 28px);padding:var(--mat-slider-value-indicator-padding, 0);opacity:var(--mat-slider-value-indicator-opacity, 1);border-radius:var(--mat-slider-value-indicator-border-radius, 50% 50% 50% 0)}.mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}.mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;display:var(--mat-slider-value-indicator-caret-display, none);border-top-color:var(--mdc-slider-label-container-color, var(--mat-sys-primary))}.mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media(forced-colors: active){.mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider__value-indicator-text{text-align:center;width:var(--mat-slider-value-indicator-width, 28px);transform:var(--mat-slider-value-indicator-text-transform, rotate(45deg));font-family:var(--mdc-slider-label-label-text-font, var(--mat-sys-label-medium-font));font-size:var(--mdc-slider-label-label-text-size, var(--mat-sys-label-medium-size));font-weight:var(--mdc-slider-label-label-text-weight, var(--mat-sys-label-medium-weight));line-height:var(--mdc-slider-label-label-text-line-height, var(--mat-sys-label-medium-line-height));letter-spacing:var(--mdc-slider-label-label-text-tracking, var(--mat-sys-label-medium-tracking))}.mdc-slider__thumb{-webkit-user-select:none;user-select:none;display:flex;left:-24px;outline:none;position:absolute;height:48px;width:48px;pointer-events:none}.mdc-slider--discrete .mdc-slider__thumb{transition:transform 80ms ease}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__thumb--top{z-index:1}.mdc-slider__thumb-knob{position:absolute;box-sizing:border-box;left:50%;top:50%;transform:translate(-50%, -50%);border-style:solid;width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-width:calc(var(--mdc-slider-handle-height, 20px)/2) calc(var(--mdc-slider-handle-width, 20px)/2);box-shadow:var(--mdc-slider-handle-elevation, var(--mat-sys-level1));background-color:var(--mdc-slider-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-handle-color, var(--mat-sys-primary));border-radius:var(--mdc-slider-handle-shape, var(--mat-sys-corner-full))}.mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-hover-handle-color, var(--mat-sys-primary))}.mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-focus-handle-color, var(--mat-sys-primary))}.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mat-sys-on-surface));border-color:var(--mdc-slider-disabled-handle-color, var(--mat-sys-on-surface))}.mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border:solid 1px #fff;box-sizing:content-box;border-color:var(--mdc-slider-with-overlap-handle-outline-color, var(--mat-sys-on-primary));border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider__tick-mark--active,.mdc-slider__tick-mark--inactive{width:var(--mdc-slider-with-tick-marks-container-size, 2px);height:var(--mdc-slider-with-tick-marks-container-size, 2px);border-radius:var(--mdc-slider-with-tick-marks-container-shape, var(--mat-sys-corner-full))}.mdc-slider__tick-mark--inactive{opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mat-sys-on-surface-variant))}.mdc-slider--disabled .mdc-slider__tick-mark--inactive{opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mat-sys-on-surface))}.mdc-slider__tick-mark--active{opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mat-sys-on-primary))}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;position:absolute;top:2px;width:44px;box-sizing:content-box}.mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;cursor:pointer;height:48px;margin:0 8px;position:relative;touch-action:pan-y;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider.mdc-slider--disabled{cursor:auto;opacity:.38}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color, var(--mat-sys-primary))}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-primary) 5%, transparent))}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-primary) 20%, transparent))}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider',\n      host: {\n        'class': 'mat-mdc-slider mdc-slider',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mdc-slider--range]': '_isRange',\n        '[class.mdc-slider--disabled]': 'disabled',\n        '[class.mdc-slider--discrete]': 'discrete',\n        '[class.mdc-slider--tick-marks]': 'showTickMarks',\n        '[class._mat-animation-noopable]': '_noopAnimations'\n      },\n      exportAs: 'matSlider',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_SLIDER,\n        useExisting: MatSlider\n      }],\n      imports: [MatSliderVisualThumb],\n      template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  @if (showTickMarks) {\\n    <div class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n      @if (_cachedWidth) {\\n        @for (tickMark of _tickMarks; track i; let i = $index) {\\n          <div\\n            [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n            [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n        }\\n      }\\n    </div>\\n  }\\n</div>\\n\\n<!-- Thumbs -->\\n@if (_isRange) {\\n  <mat-slider-visual-thumb\\n    [discrete]=\\\"discrete\\\"\\n    [thumbPosition]=\\\"1\\\"\\n    [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n  </mat-slider-visual-thumb>\\n}\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\",\n      styles: [\".mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%;pointer-events:none;height:var(--mdc-slider-inactive-track-height, 4px)}.mdc-slider__track--active,.mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider__track--active{overflow:hidden;border-radius:var(--mdc-slider-active-track-shape, var(--mat-sys-corner-full));height:var(--mdc-slider-active-track-height, 4px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 4px))/2)}.mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;transform-origin:left;transition:transform 80ms ease;border-color:var(--mdc-slider-active-track-color, var(--mat-sys-primary));border-top-width:var(--mdc-slider-active-track-height, 4px)}.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mat-sys-on-surface))}[dir=rtl] .mdc-slider__track--active_fill{-webkit-transform-origin:right;transform-origin:right}.mdc-slider__track--inactive{left:0;top:0;opacity:.24;background-color:var(--mdc-slider-inactive-track-color, var(--mat-sys-surface-variant));height:var(--mdc-slider-inactive-track-height, 4px);border-radius:var(--mdc-slider-inactive-track-shape, var(--mat-sys-corner-full))}.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mat-sys-on-surface));opacity:.24}.mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media(forced-colors: active){.mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider__value-indicator-container{bottom:44px;left:50%;pointer-events:none;position:absolute;transform:translateX(-50%);transform:var(--mat-slider-value-indicator-container-transform, translateX(-50%) rotate(-45deg))}.mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider__value-indicator{display:flex;align-items:center;border-radius:4px;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom;opacity:1;transition:transform 100ms cubic-bezier(0.4, 0, 1, 1);word-break:normal;background-color:var(--mdc-slider-label-container-color, var(--mat-sys-primary));color:var(--mdc-slider-label-label-text-color, var(--mat-sys-on-primary));width:var(--mat-slider-value-indicator-width, 28px);height:var(--mat-slider-value-indicator-height, 28px);padding:var(--mat-slider-value-indicator-padding, 0);opacity:var(--mat-slider-value-indicator-opacity, 1);border-radius:var(--mat-slider-value-indicator-border-radius, 50% 50% 50% 0)}.mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}.mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;position:absolute;transform:translateX(-50%);width:0;display:var(--mat-slider-value-indicator-caret-display, none);border-top-color:var(--mdc-slider-label-container-color, var(--mat-sys-primary))}.mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media(forced-colors: active){.mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider__value-indicator-text{text-align:center;width:var(--mat-slider-value-indicator-width, 28px);transform:var(--mat-slider-value-indicator-text-transform, rotate(45deg));font-family:var(--mdc-slider-label-label-text-font, var(--mat-sys-label-medium-font));font-size:var(--mdc-slider-label-label-text-size, var(--mat-sys-label-medium-size));font-weight:var(--mdc-slider-label-label-text-weight, var(--mat-sys-label-medium-weight));line-height:var(--mdc-slider-label-label-text-line-height, var(--mat-sys-label-medium-line-height));letter-spacing:var(--mdc-slider-label-label-text-tracking, var(--mat-sys-label-medium-tracking))}.mdc-slider__thumb{-webkit-user-select:none;user-select:none;display:flex;left:-24px;outline:none;position:absolute;height:48px;width:48px;pointer-events:none}.mdc-slider--discrete .mdc-slider__thumb{transition:transform 80ms ease}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__thumb--top{z-index:1}.mdc-slider__thumb-knob{position:absolute;box-sizing:border-box;left:50%;top:50%;transform:translate(-50%, -50%);border-style:solid;width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-width:calc(var(--mdc-slider-handle-height, 20px)/2) calc(var(--mdc-slider-handle-width, 20px)/2);box-shadow:var(--mdc-slider-handle-elevation, var(--mat-sys-level1));background-color:var(--mdc-slider-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-handle-color, var(--mat-sys-primary));border-radius:var(--mdc-slider-handle-shape, var(--mat-sys-corner-full))}.mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-hover-handle-color, var(--mat-sys-primary))}.mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mat-sys-primary));border-color:var(--mdc-slider-focus-handle-color, var(--mat-sys-primary))}.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mat-sys-on-surface));border-color:var(--mdc-slider-disabled-handle-color, var(--mat-sys-on-surface))}.mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border:solid 1px #fff;box-sizing:content-box;border-color:var(--mdc-slider-with-overlap-handle-outline-color, var(--mat-sys-on-primary));border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider__tick-mark--active,.mdc-slider__tick-mark--inactive{width:var(--mdc-slider-with-tick-marks-container-size, 2px);height:var(--mdc-slider-with-tick-marks-container-size, 2px);border-radius:var(--mdc-slider-with-tick-marks-container-shape, var(--mat-sys-corner-full))}.mdc-slider__tick-mark--inactive{opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mat-sys-on-surface-variant))}.mdc-slider--disabled .mdc-slider__tick-mark--inactive{opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mat-sys-on-surface))}.mdc-slider__tick-mark--active{opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.38);background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mat-sys-on-primary))}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;position:absolute;top:2px;width:44px;box-sizing:content-box}.mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;cursor:pointer;height:48px;margin:0 8px;position:relative;touch-action:pan-y;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider.mdc-slider--disabled{cursor:auto;opacity:.38}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color, var(--mat-sys-primary))}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-primary) 5%, transparent))}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-primary) 20%, transparent))}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    _trackActive: [{\n      type: ViewChild,\n      args: ['trackActive']\n    }],\n    _thumbs: [{\n      type: ViewChildren,\n      args: [MAT_SLIDER_VISUAL_THUMB]\n    }],\n    _input: [{\n      type: ContentChild,\n      args: [MAT_SLIDER_THUMB]\n    }],\n    _inputs: [{\n      type: ContentChildren,\n      args: [MAT_SLIDER_RANGE_THUMB, {\n        descendants: false\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    discrete: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showTickMarks: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    min: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    max: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    step: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    displayWith: [{\n      type: Input\n    }]\n  });\n})();\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n  const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement?._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\nfunction _throwInvalidInputConfigurationError() {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderThumb),\n  multi: true\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderRangeThumb),\n  multi: true\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nclass MatSliderThumb {\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _cdr = inject(ChangeDetectorRef);\n  _slider = inject(MAT_SLIDER);\n  _platform = inject(Platform);\n  _listenerCleanups;\n  get value() {\n    return numberAttribute(this._hostElement.value, 0);\n  }\n  set value(value) {\n    value = isNaN(value) ? 0 : value;\n    const stringValue = value + '';\n    if (!this._hasSetInitialValue) {\n      this._initialValue = stringValue;\n      return;\n    }\n    if (this._isActive) {\n      return;\n    }\n    this._setValue(stringValue);\n  }\n  /**\n   * Handles programmatic value setting. This has been split out to\n   * allow the range thumb to override it and add additional necessary logic.\n   */\n  _setValue(value) {\n    this._hostElement.value = value;\n    this._updateThumbUIByValue();\n    this._slider._onValueChange(this);\n    this._cdr.detectChanges();\n    this._slider._cdr.markForCheck();\n  }\n  /** Event emitted when the `value` is changed. */\n  valueChange = new EventEmitter();\n  /** Event emitted when the slider thumb starts being dragged. */\n  dragStart = new EventEmitter();\n  /** Event emitted when the slider thumb stops being dragged. */\n  dragEnd = new EventEmitter();\n  /**\n   * The current translateX in px of the slider visual thumb.\n   * @docs-private\n   */\n  get translateX() {\n    if (this._slider.min >= this._slider.max) {\n      this._translateX = this._tickMarkOffset;\n      return this._translateX;\n    }\n    if (this._translateX === undefined) {\n      this._translateX = this._calcTranslateXByValue();\n    }\n    return this._translateX;\n  }\n  set translateX(v) {\n    this._translateX = v;\n  }\n  _translateX;\n  /**\n   * Indicates whether this thumb is the start or end thumb.\n   * @docs-private\n   */\n  thumbPosition = _MatThumb.END;\n  /** @docs-private */\n  get min() {\n    return numberAttribute(this._hostElement.min, 0);\n  }\n  set min(v) {\n    this._hostElement.min = v + '';\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get max() {\n    return numberAttribute(this._hostElement.max, 0);\n  }\n  set max(v) {\n    this._hostElement.max = v + '';\n    this._cdr.detectChanges();\n  }\n  get step() {\n    return numberAttribute(this._hostElement.step, 0);\n  }\n  set step(v) {\n    this._hostElement.step = v + '';\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get disabled() {\n    return booleanAttribute(this._hostElement.disabled);\n  }\n  set disabled(v) {\n    this._hostElement.disabled = v;\n    this._cdr.detectChanges();\n    if (this._slider.disabled !== this.disabled) {\n      this._slider.disabled = this.disabled;\n    }\n  }\n  /** The percentage of the slider that coincides with the value. */\n  get percentage() {\n    if (this._slider.min >= this._slider.max) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n  }\n  /** @docs-private */\n  get fillPercentage() {\n    if (!this._slider._cachedWidth) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    if (this._translateX === 0) {\n      return 0;\n    }\n    return this.translateX / this._slider._cachedWidth;\n  }\n  /** The host native HTML input element. */\n  _hostElement = this._elementRef.nativeElement;\n  /** The aria-valuetext string representation of the input's value. */\n  _valuetext = signal('');\n  /** The radius of a native html slider's knob. */\n  _knobRadius = 8;\n  /** The distance in px from the start of the slider track to the first tick mark. */\n  _tickMarkOffset = 3;\n  /** Whether user's cursor is currently in a mouse down state on the input. */\n  _isActive = false;\n  /** Whether the input is currently focused (either by tab or after clicking). */\n  _isFocused = false;\n  /** Used to relay updates to _isFocused to the slider visual thumbs. */\n  _setIsFocused(v) {\n    this._isFocused = v;\n  }\n  /**\n   * Whether the initial value has been set.\n   * This exists because the initial value cannot be immediately set because the min and max\n   * must first be relayed from the parent MatSlider component, which can only happen later\n   * in the component lifecycle.\n   */\n  _hasSetInitialValue = false;\n  /** The stored initial value. */\n  _initialValue;\n  /** Defined when a user is using a form control to manage slider value & validation. */\n  _formControl;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /**\n   * Indicates whether UI updates should be skipped.\n   *\n   * This flag is used to avoid flickering\n   * when correcting values on pointer up/down.\n   */\n  _skipUIUpdate = false;\n  /** Callback called when the slider input value changes. */\n  _onChangeFn;\n  /** Callback called when the slider input has been touched. */\n  _onTouchedFn = () => {};\n  /**\n   * Whether the NgModel has been initialized.\n   *\n   * This flag is used to ignore ghost null calls to\n   * writeValue which can break slider initialization.\n   *\n   * See https://github.com/angular/angular/issues/14988.\n   */\n  _isControlInitialized = false;\n  constructor() {\n    const renderer = inject(Renderer2);\n    this._ngZone.runOutsideAngular(() => {\n      this._listenerCleanups = [renderer.listen(this._hostElement, 'pointerdown', this._onPointerDown.bind(this)), renderer.listen(this._hostElement, 'pointermove', this._onPointerMove.bind(this)), renderer.listen(this._hostElement, 'pointerup', this._onPointerUp.bind(this))];\n    });\n  }\n  ngOnDestroy() {\n    this._listenerCleanups.forEach(cleanup => cleanup());\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.dragStart.complete();\n    this.dragEnd.complete();\n  }\n  /** @docs-private */\n  initProps() {\n    this._updateWidthInactive();\n    // If this or the parent slider is disabled, just make everything disabled.\n    if (this.disabled !== this._slider.disabled) {\n      // The MatSlider setter for disabled will relay this and disable both inputs.\n      this._slider.disabled = true;\n    }\n    this.step = this._slider.step;\n    this.min = this._slider.min;\n    this.max = this._slider.max;\n    this._initValue();\n  }\n  /** @docs-private */\n  initUI() {\n    this._updateThumbUIByValue();\n  }\n  _initValue() {\n    this._hasSetInitialValue = true;\n    if (this._initialValue === undefined) {\n      this.value = this._getDefaultValue();\n    } else {\n      this._hostElement.value = this._initialValue;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n    }\n  }\n  _getDefaultValue() {\n    return this.min;\n  }\n  _onBlur() {\n    this._setIsFocused(false);\n    this._onTouchedFn();\n  }\n  _onFocus() {\n    this._slider._setTransition(false);\n    this._slider._updateTrackUI(this);\n    this._setIsFocused(true);\n  }\n  _onChange() {\n    this.valueChange.emit(this.value);\n    // only used to handle the edge case where user\n    // mousedown on the slider then uses arrow keys.\n    if (this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n  }\n  _onInput() {\n    this._onChangeFn?.(this.value);\n    // handles arrowing and updating the value when\n    // a step is defined.\n    if (this._slider.step || !this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n    this._slider._onValueChange(this);\n  }\n  _onNgControlValueChange() {\n    // only used to handle when the value change\n    // originates outside of the slider.\n    if (!this._isActive || !this._isFocused) {\n      this._slider._onValueChange(this);\n      this._updateThumbUIByValue();\n    }\n    this._slider.disabled = this._formControl.disabled;\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    // On IOS, dragging only works if the pointer down happens on the\n    // slider thumb and the slider does not receive focus from pointer events.\n    if (this._platform.IOS) {\n      const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n      this._isActive = isCursorOnSliderThumb;\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      return;\n    }\n    this._isActive = true;\n    this._setIsFocused(true);\n    this._updateWidthActive();\n    this._slider._updateDimensions();\n    // Does nothing if a step is defined because we\n    // want the value to snap to the values on input.\n    if (!this._slider.step) {\n      this._updateThumbUIByPointerEvent(event, {\n        withAnimation: true\n      });\n    }\n    if (!this.disabled) {\n      this._handleValueCorrection(event);\n      this.dragStart.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n    }\n  }\n  /**\n   * Corrects the value of the slider on pointer up/down.\n   *\n   * Called on pointer down and up because the value is set based\n   * on the inactive width instead of the active width.\n   */\n  _handleValueCorrection(event) {\n    // Don't update the UI with the current value! The value on pointerdown\n    // and pointerup is calculated in the split second before the input(s)\n    // resize. See _updateWidthInactive() and _updateWidthActive() for more\n    // details.\n    this._skipUIUpdate = true;\n    // Note that this function gets triggered before the actual value of the\n    // slider is updated. This means if we were to set the value here, it\n    // would immediately be overwritten. Using setTimeout ensures the setting\n    // of the value happens after the value has been updated by the\n    // pointerdown event.\n    setTimeout(() => {\n      this._skipUIUpdate = false;\n      this._fixValue(event);\n    }, 0);\n  }\n  /** Corrects the value of the slider based on the pointer event's position. */\n  _fixValue(event) {\n    const xPos = event.clientX - this._slider._cachedLeft;\n    const width = this._slider._cachedWidth;\n    const step = this._slider.step === 0 ? 1 : this._slider.step;\n    const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n    const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n    // To ensure the percentage is rounded to the necessary number of decimals.\n    const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n    const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n    const value = Math.round(impreciseValue / step) * step;\n    const prevValue = this.value;\n    if (value === prevValue) {\n      // Because we prevented UI updates, if it turns out that the race\n      // condition didn't happen and the value is already correct, we\n      // have to apply the ui updates now.\n      this._slider._onValueChange(this);\n      this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n        withAnimation: this._slider._hasAnimation\n      });\n      return;\n    }\n    this.value = value;\n    this.valueChange.emit(this.value);\n    this._onChangeFn?.(this.value);\n    this._slider._onValueChange(this);\n    this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n      withAnimation: this._slider._hasAnimation\n    });\n  }\n  _onPointerMove(event) {\n    // Again, does nothing if a step is defined because\n    // we want the value to snap to the values on input.\n    if (!this._slider.step && this._isActive) {\n      this._updateThumbUIByPointerEvent(event);\n    }\n  }\n  _onPointerUp() {\n    if (this._isActive) {\n      this._isActive = false;\n      if (this._platform.SAFARI) {\n        this._setIsFocused(false);\n      }\n      this.dragEnd.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n      // This setTimeout is to prevent the pointerup from triggering a value\n      // change on the input based on the inactive width. It's not clear why\n      // but for some reason on IOS this race condition is even more common so\n      // the timeout needs to be increased.\n      setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n    }\n  }\n  _clamp(v) {\n    const min = this._tickMarkOffset;\n    const max = this._slider._cachedWidth - this._tickMarkOffset;\n    return Math.max(Math.min(v, max), min);\n  }\n  _calcTranslateXByValue() {\n    if (this._slider._isRtl) {\n      return (1 - this.percentage) * (this._slider._cachedWidth - this._tickMarkOffset * 2) + this._tickMarkOffset;\n    }\n    return this.percentage * (this._slider._cachedWidth - this._tickMarkOffset * 2) + this._tickMarkOffset;\n  }\n  _calcTranslateXByPointerEvent(event) {\n    return event.clientX - this._slider._cachedLeft;\n  }\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive() {}\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive() {\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding - this._tickMarkOffset * 2}px)`;\n    this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n  }\n  _updateThumbUIByValue(options) {\n    this.translateX = this._clamp(this._calcTranslateXByValue());\n    this._updateThumbUI(options);\n  }\n  _updateThumbUIByPointerEvent(event, options) {\n    this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n    this._updateThumbUI(options);\n  }\n  _updateThumbUI(options) {\n    this._slider._setTransition(!!options?.withAnimation);\n    this._slider._onTranslateXChange(this);\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n    }\n  }\n  /**\n   * Registers a callback to be invoked when the input's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChangeFn = fn;\n    this._isControlInitialized = true;\n  }\n  /**\n   * Registers a callback to be invoked when the input is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouchedFn = fn;\n  }\n  /**\n   * Sets the disabled state of the slider.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  focus() {\n    this._hostElement.focus();\n  }\n  blur() {\n    this._hostElement.blur();\n  }\n  static ɵfac = function MatSliderThumb_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSliderThumb)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSliderThumb,\n    selectors: [[\"input\", \"matSliderThumb\", \"\"]],\n    hostAttrs: [\"type\", \"range\", 1, \"mdc-slider__input\"],\n    hostVars: 1,\n    hostBindings: function MatSliderThumb_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function MatSliderThumb_change_HostBindingHandler() {\n          return ctx._onChange();\n        })(\"input\", function MatSliderThumb_input_HostBindingHandler() {\n          return ctx._onInput();\n        })(\"blur\", function MatSliderThumb_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        })(\"focus\", function MatSliderThumb_focus_HostBindingHandler() {\n          return ctx._onFocus();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuetext\", ctx._valuetext());\n      }\n    },\n    inputs: {\n      value: [2, \"value\", \"value\", numberAttribute]\n    },\n    outputs: {\n      valueChange: \"valueChange\",\n      dragStart: \"dragStart\",\n      dragEnd: \"dragEnd\"\n    },\n    exportAs: [\"matSliderThumb\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n      provide: MAT_SLIDER_THUMB,\n      useExisting: MatSliderThumb\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderThumb]',\n      exportAs: 'matSliderThumb',\n      host: {\n        'class': 'mdc-slider__input',\n        'type': 'range',\n        '[attr.aria-valuetext]': '_valuetext()',\n        '(change)': '_onChange()',\n        '(input)': '_onInput()',\n        // TODO(wagnermaciel): Consider using a global event listener instead.\n        // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n        '(blur)': '_onBlur()',\n        '(focus)': '_onFocus()'\n      },\n      providers: [MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_THUMB,\n        useExisting: MatSliderThumb\n      }]\n    }]\n  }], () => [], {\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSliderRangeThumb extends MatSliderThumb {\n  _cdr = inject(ChangeDetectorRef);\n  /** @docs-private */\n  getSibling() {\n    if (!this._sibling) {\n      this._sibling = this._slider._getInput(this._isEndThumb ? _MatThumb.START : _MatThumb.END);\n    }\n    return this._sibling;\n  }\n  _sibling;\n  /**\n   * Returns the minimum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMinPos() {\n    const sibling = this.getSibling();\n    if (!this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._tickMarkOffset;\n  }\n  /**\n   * Returns the maximum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMaxPos() {\n    const sibling = this.getSibling();\n    if (this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._slider._cachedWidth - this._tickMarkOffset;\n  }\n  _setIsLeftThumb() {\n    this._isLeftThumb = this._isEndThumb && this._slider._isRtl || !this._isEndThumb && !this._slider._isRtl;\n  }\n  /** Whether this slider corresponds to the input on the left hand side. */\n  _isLeftThumb;\n  /** Whether this slider corresponds to the input with greater value. */\n  _isEndThumb;\n  constructor() {\n    super();\n    this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n    this._setIsLeftThumb();\n    this.thumbPosition = this._isEndThumb ? _MatThumb.END : _MatThumb.START;\n  }\n  _getDefaultValue() {\n    return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n  }\n  _onInput() {\n    super._onInput();\n    this._updateSibling();\n    if (!this._isActive) {\n      this._updateWidthInactive();\n    }\n  }\n  _onNgControlValueChange() {\n    super._onNgControlValueChange();\n    this.getSibling()?._updateMinMax();\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    if (this._sibling) {\n      this._sibling._updateWidthActive();\n      this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n    }\n    super._onPointerDown(event);\n  }\n  _onPointerUp() {\n    super._onPointerUp();\n    if (this._sibling) {\n      setTimeout(() => {\n        this._sibling._updateWidthInactive();\n        this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n      });\n    }\n  }\n  _onPointerMove(event) {\n    super._onPointerMove(event);\n    if (!this._slider.step && this._isActive) {\n      this._updateSibling();\n    }\n  }\n  _fixValue(event) {\n    super._fixValue(event);\n    this._sibling?._updateMinMax();\n  }\n  _clamp(v) {\n    return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n  }\n  _updateMinMax() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    if (this._isEndThumb) {\n      this.min = Math.max(this._slider.min, sibling.value);\n      this.max = this._slider.max;\n    } else {\n      this.min = this._slider.min;\n      this.max = Math.min(this._slider.max, sibling.value);\n    }\n  }\n  _updateWidthActive() {\n    const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n    const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth - this._tickMarkOffset * 2;\n    const percentage = this._slider.min < this._slider.max ? (this.max - this.min) / (this._slider.max - this._slider.min) : 1;\n    const width = maxWidth * percentage + minWidth;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n  }\n  _updateWidthInactive() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    const maxWidth = this._slider._cachedWidth - this._tickMarkOffset * 2;\n    const midValue = this._isEndThumb ? this.value - (this.value - sibling.value) / 2 : this.value + (sibling.value - this.value) / 2;\n    const _percentage = this._isEndThumb ? (this.max - midValue) / (this._slider.max - this._slider.min) : (midValue - this.min) / (this._slider.max - this._slider.min);\n    const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n    // Extend the native input width by the radius of the ripple\n    let ripplePadding = this._slider._rippleRadius;\n    // If one of the inputs is maximally sized (the value of both thumbs is\n    // equal to the min or max), make that input take up all of the width and\n    // make the other unselectable.\n    if (percentage === 1) {\n      ripplePadding = 48;\n    } else if (percentage === 0) {\n      ripplePadding = 0;\n    }\n    const width = maxWidth * percentage + ripplePadding;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = '0px';\n    if (this._isLeftThumb) {\n      this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n      this._hostElement.style.right = 'auto';\n    } else {\n      this._hostElement.style.left = 'auto';\n      this._hostElement.style.right = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n    }\n  }\n  _updateStaticStyles() {\n    this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n  }\n  _updateSibling() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    sibling._updateMinMax();\n    if (this._isActive) {\n      sibling._updateWidthActive();\n    } else {\n      sibling._updateWidthInactive();\n    }\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n      this._updateWidthInactive();\n      this._updateSibling();\n    }\n  }\n  _setValue(value) {\n    super._setValue(value);\n    this._updateWidthInactive();\n    this._updateSibling();\n  }\n  static ɵfac = function MatSliderRangeThumb_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSliderRangeThumb)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSliderRangeThumb,\n    selectors: [[\"input\", \"matSliderStartThumb\", \"\"], [\"input\", \"matSliderEndThumb\", \"\"]],\n    exportAs: [\"matSliderRangeThumb\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n      provide: MAT_SLIDER_RANGE_THUMB,\n      useExisting: MatSliderRangeThumb\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderRangeThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n      exportAs: 'matSliderRangeThumb',\n      providers: [MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_RANGE_THUMB,\n        useExisting: MatSliderRangeThumb\n      }]\n    }]\n  }], () => [], null);\n})();\nclass MatSliderModule {\n  static ɵfac = function MatSliderModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSliderModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSliderModule,\n    imports: [MatCommonModule, MatRippleModule, MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb],\n    exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatRippleModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb],\n      exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb]\n    }]\n  }], null, null);\n})();\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,yBAAyB;AACtC,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC3D,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,kBAAkB;AAAA,EAChD;AACF;AACA,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK;AAAA,EACvB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,gBAAgB,IAAI;AAC1B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,gBAAgB,IAAI,kCAAkC,iCAAiC;AACrG,IAAG,YAAY,aAAa,OAAO,uBAAuB,aAAa,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,sDAAsD,GAAG,GAAG,OAAO,GAAM,sBAAsB;AAAA,EACxH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,UAAU;AAAA,EACjC;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,GAAG,gDAAgD,GAAG,CAAC;AACrE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,eAAe,IAAI,EAAE;AAAA,EAC/C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,2BAA2B,CAAC;AAAA,EAC9C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,iBAAiB,CAAC,EAAE,sBAAsB,OAAO,uBAAuB;AAAA,EACrH;AACF;AACA,IAAI;AAAA,CACH,SAAUA,YAAW;AACpB,EAAAA,WAAUA,WAAU,OAAO,IAAI,CAAC,IAAI;AACpC,EAAAA,WAAUA,WAAU,KAAK,IAAI,CAAC,IAAI;AACpC,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,IAAI;AAAA,CACH,SAAUC,eAAc;AACvB,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,UAAU,IAAI,CAAC,IAAI;AAC/C,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAOtC,IAAM,aAAa,IAAI,eAAe,YAAY;AAMlD,IAAM,mBAAmB,IAAI,eAAe,iBAAiB;AAM7D,IAAM,yBAAyB,IAAI,eAAe,sBAAsB;AAMxE,IAAM,0BAA0B,IAAI,eAAe,uBAAuB;AAM1E,IAAM,kBAAN,MAAsB;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AACF;AASA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,iBAAiB;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,UAAU,OAAO,UAAU;AAAA,EAC3B,YAAY,OAAO,SAAS;AAAA,EAC5B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb,YAAY;AAAA;AAAA,EAEZ,2BAA2B;AAAA;AAAA,EAE3B,eAAe,OAAO,UAAU,EAAE;AAAA,EAClC,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA,EACf,kBAAkB;AAChB,UAAM,cAAc,KAAK,QAAQ,UAAU,KAAK,aAAa;AAG7D,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,SAAK,QAAQ,SAAS;AACtB,SAAK,eAAe;AACpB,SAAK,iBAAiB,KAAK,aAAa;AAGxC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW,KAAK;AACtB,WAAK,oBAAoB,CAAC,SAAS,OAAO,OAAO,eAAe,KAAK,cAAc,GAAG,SAAS,OAAO,OAAO,eAAe,KAAK,YAAY,GAAG,SAAS,OAAO,OAAO,aAAa,KAAK,UAAU,GAAG,SAAS,OAAO,OAAO,gBAAgB,KAAK,aAAa,GAAG,SAAS,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,OAAO,OAAO,QAAQ,KAAK,OAAO,CAAC;AAAA,IAChW,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,iBAAiB,WAAS;AACxB,QAAI,KAAK,aAAa,YAAY;AAChC;AAAA,IACF;AACA,UAAM,OAAO,KAAK,aAAa,sBAAsB;AACrD,UAAM,YAAY,KAAK,QAAQ,uBAAuB,OAAO,IAAI;AACjE,SAAK,aAAa;AAClB,QAAI,WAAW;AACb,WAAK,iBAAiB;AAAA,IACxB,OAAO;AACL,WAAK,YAAY,KAAK,eAAe;AAAA,IACvC;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM;AACpB,SAAK,aAAa;AAClB,SAAK,YAAY,KAAK,eAAe;AAAA,EACvC;AAAA,EACA,WAAW,MAAM;AAGf,SAAK,YAAY,KAAK,eAAe;AACrC,SAAK,iBAAiB;AACtB,SAAK,aAAa,UAAU,IAAI,4BAA4B;AAAA,EAC9D;AAAA,EACA,UAAU,MAAM;AAEd,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,KAAK,eAAe;AAAA,IACvC;AAEA,QAAI,KAAK,YAAY;AACnB,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,aAAa,UAAU,OAAO,4BAA4B;AAAA,EACjE;AAAA,EACA,eAAe,WAAS;AACtB,QAAI,MAAM,WAAW,GAAG;AACtB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,aAAa,MAAM;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,gBAAgB;AAEtC,QAAI,CAAC,KAAK,aAAa,YAAY;AACjC,WAAK,YAAY,KAAK,eAAe;AAAA,IACvC;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,CAAC,KAAK,iBAAiB,KAAK,eAAe,GAAG;AAChD,WAAK,kBAAkB,KAAK,YAAY;AAAA,QACtC,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,CAAC;AACD,WAAK,iBAAiB,QAAQ,UAAU,IAAI,6BAA6B;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AAEjB,QAAI,CAAC,KAAK,iBAAiB,KAAK,eAAe,GAAG;AAChD,WAAK,kBAAkB,KAAK,YAAY;AAAA,QACtC,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,GAAG,IAAI;AACP,WAAK,iBAAiB,QAAQ,UAAU,IAAI,6BAA6B;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,CAAC,KAAK,iBAAiB,KAAK,gBAAgB,GAAG;AACjD,WAAK,mBAAmB,KAAK,YAAY;AAAA,QACvC,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,CAAC;AACD,WAAK,kBAAkB,QAAQ,UAAU,IAAI,8BAA8B;AAAA,IAC7E;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,WAAW;AAC1B,WAAO,WAAW,UAAU,YAAY,aAAa,WAAW,UAAU,YAAY;AAAA,EACxF;AAAA;AAAA,EAEA,YAAY,WAAW,0BAA0B;AAC/C,QAAI,KAAK,QAAQ,UAAU;AACzB;AAAA,IACF;AACA,SAAK,oBAAoB;AACzB,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,UAAU,KAAK,QAAQ,UAAU,KAAK,kBAAkB,UAAU,QAAQ,UAAU,MAAM,UAAU,KAAK;AAC/G,cAAQ,oBAAoB;AAAA,IAC9B;AACA,QAAI,KAAK,QAAQ,sBAAsB,YAAY,CAAC,0BAA0B;AAC5E;AAAA,IACF;AACA,WAAO,KAAK,QAAQ,OAAO;AAAA,MACzB,WAAW,KAAK,QAAQ,kBAAkB;AAAA,QACxC,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,WAAW;AACrB,eAAW,QAAQ;AACnB,QAAI,KAAK,oBAAoB,GAAG;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,WAAK,oBAAoB;AAAA,IAC3B;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,CAAC,QAAQ,oBAAoB,GAAG;AAClC,WAAK,oBAAoB;AACzB,cAAQ,oBAAoB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,aAAa,UAAU,IAAI,mCAAmC;AAAA,EACrE;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,aAAa,UAAU,OAAO,mCAAmC;AAAA,EACxE;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,QAAQ,UAAU,KAAK,kBAAkB,UAAU,QAAQ,UAAU,MAAM,UAAU,KAAK;AAAA,EACxG;AAAA;AAAA,EAEA,8BAA8B;AAC5B,WAAO,KAAK,0BAA0B;AAAA,EACxC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,iBAAiB,KAAK,gBAAgB;AAAA,EAClJ;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,IACvC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,WAAW,CAAC;AAC3B,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,MACjF;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,qBAAqB,6BAA6B;AAAA,IACjE,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,eAAe;AAAA,MACf,oBAAoB;AAAA,IACtB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,aAAa,IAAI,GAAG,uBAAuB,GAAG,mBAAmB,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,kCAAkC,CAAC;AAAA,IACrR,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,QAAG,UAAU,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,qBAAqB,IAAI;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,SAAS;AAAA,IACxB,QAAQ,CAAC,6SAA6S;AAAA,IACtT,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,SAAS;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ,CAAC,6SAA6S;AAAA,IACxT,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAUH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,UAAU,OAAO,MAAM;AAAA,EACvB,OAAO,OAAO,iBAAiB;AAAA,EAC/B,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,GAAG;AACd,SAAK,YAAY;AACjB,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,QAAI,UAAU;AACZ,eAAS,WAAW,KAAK;AAAA,IAC3B;AACA,QAAI,YAAY;AACd,iBAAW,WAAW,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,GAAG;AACd,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,gBAAgB;AAAA;AAAA,EAEhB,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,GAAG;AACT,UAAM,MAAM,MAAM,CAAC,IAAI,KAAK,OAAO;AACnC,QAAI,KAAK,SAAS,KAAK;AACrB,WAAK,WAAW,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP;AAAA;AAAA,EAEA,gBAAgB;AAAA,EAChB,WAAW,KAAK;AACd,UAAM,UAAU,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK,gBAAgB;AAAA,MACnC,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC,IAAI,KAAK,mBAAmB,GAAG;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,gBAAgB,KAAK;AACnB,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,UAAM,cAAc,SAAS;AAC7B,UAAM,gBAAgB,WAAW;AACjC,eAAW,MAAM,IAAI;AACrB,aAAS,MAAM,KAAK,IAAI,IAAI,KAAK,WAAW,KAAK;AACjD,eAAW,MAAM,KAAK,IAAI,SAAS,KAAK,SAAS,KAAK;AACtD,eAAW,qBAAqB;AAChC,aAAS,qBAAqB;AAC9B,QAAI,MAAM,IAAI,MAAM,KAAK,gCAAgC,UAAU,UAAU,IAAI,KAAK,gCAAgC,YAAY,QAAQ;AAC1I,QAAI,gBAAgB,SAAS,OAAO;AAClC,WAAK,eAAe,QAAQ;AAAA,IAC9B;AACA,QAAI,kBAAkB,WAAW,OAAO;AACtC,WAAK,eAAe,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK;AACtB,UAAM,QAAQ,KAAK,UAAU,UAAU,GAAG;AAC1C,QAAI,OAAO;AACT,YAAM,WAAW,MAAM;AACvB,YAAM,MAAM;AACZ,YAAM,sBAAsB;AAC5B,WAAK,eAAe,KAAK;AACzB,UAAI,aAAa,MAAM,OAAO;AAC5B,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,GAAG;AACT,UAAM,MAAM,MAAM,CAAC,IAAI,KAAK,OAAO;AACnC,QAAI,KAAK,SAAS,KAAK;AACrB,WAAK,WAAW,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AAAA,EACP,WAAW,KAAK;AACd,UAAM,UAAU,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK,gBAAgB;AAAA,MACnC,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC,IAAI,KAAK,mBAAmB,GAAG;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,gBAAgB,KAAK;AACnB,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,UAAM,cAAc,SAAS;AAC7B,UAAM,gBAAgB,WAAW;AACjC,aAAS,MAAM,IAAI;AACnB,eAAW,MAAM,KAAK,IAAI,IAAI,KAAK,SAAS,KAAK;AACjD,aAAS,MAAM,WAAW;AAC1B,aAAS,qBAAqB;AAC9B,eAAW,qBAAqB;AAChC,QAAI,MAAM,IAAI,MAAM,KAAK,gCAAgC,YAAY,QAAQ,IAAI,KAAK,gCAAgC,UAAU,UAAU;AAC1I,QAAI,gBAAgB,SAAS,OAAO;AAClC,WAAK,eAAe,QAAQ;AAAA,IAC9B;AACA,QAAI,kBAAkB,WAAW,OAAO;AACtC,WAAK,eAAe,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK;AACtB,UAAM,QAAQ,KAAK,UAAU,UAAU,GAAG;AAC1C,QAAI,OAAO;AACT,YAAM,WAAW,MAAM;AACvB,YAAM,MAAM;AACZ,YAAM,sBAAsB;AAC5B,WAAK,eAAe,KAAK;AACzB,UAAI,aAAa,MAAM,OAAO;AAC5B,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,GAAG;AACV,UAAM,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;AACrC,QAAI,KAAK,UAAU,MAAM;AACvB,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,MAAM;AAChB,SAAK,QAAQ;AACb,SAAK,WAAW,KAAK,iBAAiB,IAAI,KAAK,oBAAoB;AACnE,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,UAAM,cAAc,SAAS;AAC7B,UAAM,gBAAgB,WAAW;AACjC,UAAM,iBAAiB,WAAW;AAClC,aAAS,MAAM,KAAK;AACpB,eAAW,MAAM,KAAK;AACtB,aAAS,OAAO,KAAK;AACrB,eAAW,OAAO,KAAK;AACvB,QAAI,KAAK,UAAU,QAAQ;AACzB,eAAS,QAAQ,SAAS;AAC1B,iBAAW,QAAQ,WAAW;AAAA,IAChC;AACA,aAAS,MAAM,KAAK,IAAI,KAAK,MAAM,WAAW,KAAK;AACnD,eAAW,MAAM,KAAK,IAAI,KAAK,MAAM,SAAS,KAAK;AACnD,eAAW,qBAAqB;AAChC,aAAS,qBAAqB;AAC9B,aAAS,QAAQ,iBAAiB,KAAK,gCAAgC,YAAY,QAAQ,IAAI,KAAK,gCAAgC,UAAU,UAAU;AACxJ,QAAI,gBAAgB,SAAS,OAAO;AAClC,WAAK,eAAe,QAAQ;AAAA,IAC9B;AACA,QAAI,kBAAkB,WAAW,OAAO;AACtC,WAAK,eAAe,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,UAAM,QAAQ,KAAK,UAAU,UAAU,GAAG;AAC1C,QAAI,OAAO;AACT,YAAM,WAAW,MAAM;AACvB,YAAM,OAAO,KAAK;AAClB,UAAI,KAAK,UAAU,QAAQ;AACzB,cAAM,QAAQ,MAAM;AAAA,MACtB;AACA,YAAM,sBAAsB;AAC5B,UAAI,aAAa,MAAM,OAAO;AAC5B,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,WAAS,GAAG,KAAK;AAAA;AAAA,EAE/B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA;AAAA,EAGhB,0BAA0B;AAAA;AAAA,EAE1B,wBAAwB;AAAA;AAAA,EAExB;AAAA,EACA;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,kBAAkB,kBAAkB;AACzC,QAAI,KAAK,MAAM;AACb,WAAK,yBAAyB,KAAK,KAAK,OAAO,UAAU,MAAM,KAAK,aAAa,CAAC;AAClF,WAAK,SAAS,KAAK,KAAK,UAAU;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,kBAAkB;AAAA,IACzB;AACA,UAAM,SAAS,KAAK,UAAU,UAAU,GAAG;AAC3C,UAAM,SAAS,KAAK,UAAU,UAAU,KAAK;AAC7C,SAAK,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC;AAC9B,SAAK,KAAK,cAAc;AACxB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,sBAAgB,KAAK,UAAU,KAAK,UAAU,UAAU,GAAG,GAAG,KAAK,UAAU,UAAU,KAAK,CAAC;AAAA,IAC/F;AACA,UAAM,QAAQ,KAAK,UAAU,UAAU,GAAG;AAC1C,SAAK,gBAAgB,MAAM,QAAQ;AACnC,SAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAC/C,SAAK,WAAW,KAAK,aAAa,QAAQ,MAAM,IAAI,KAAK,gBAAgB,MAAM;AAC/E,SAAK,eAAe,MAAM;AAC1B,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB;AACxB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA,EACA,gBAAgB,QAAQ;AACtB,WAAO,UAAU;AACjB,WAAO,OAAO;AACd,SAAK,wBAAwB,MAAM;AACnC,SAAK,sBAAsB;AAC3B,WAAO,sBAAsB;AAAA,EAC/B;AAAA,EACA,aAAa,QAAQ,QAAQ;AAC3B,WAAO,UAAU;AACjB,WAAO,OAAO;AACd,WAAO,UAAU;AACjB,WAAO,OAAO;AACd,WAAO,cAAc;AACrB,WAAO,cAAc;AACrB,WAAO,oBAAoB;AAC3B,WAAO,oBAAoB;AAC3B,SAAK,yBAAyB;AAC9B,SAAK,sBAAsB;AAC3B,WAAO,sBAAsB;AAC7B,WAAO,sBAAsB;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,uBAAuB,YAAY;AACxC,SAAK,iBAAiB,WAAW;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,eAAe;AACb,SAAK,SAAS,KAAK,MAAM,UAAU;AACnC,SAAK,WAAW,KAAK,kBAAkB,IAAI,KAAK,qBAAqB;AACrE,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,aAAS,gBAAgB;AACzB,eAAW,gBAAgB;AAC3B,aAAS,aAAa,SAAS,uBAAuB;AACtD,eAAW,aAAa,WAAW,uBAAuB;AAC1D,aAAS,oBAAoB;AAC7B,eAAW,oBAAoB;AAC/B,aAAS,qBAAqB;AAC9B,eAAW,qBAAqB;AAChC,aAAS,sBAAsB;AAC/B,eAAW,sBAAsB;AAAA,EACnC;AAAA,EACA,uBAAuB;AACrB,UAAM,QAAQ,KAAK,UAAU,UAAU,GAAG;AAC1C,UAAM,sBAAsB;AAAA,EAC9B;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,OAAO,mBAAmB,eAAe,CAAC,gBAAgB;AAC5D;AAAA,IACF;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,kBAAkB,IAAI,eAAe,MAAM;AAC9C,YAAI,KAAK,UAAU,GAAG;AACpB;AAAA,QACF;AACA,YAAI,KAAK,cAAc;AACrB,uBAAa,KAAK,YAAY;AAAA,QAChC;AACA,aAAK,UAAU;AAAA,MACjB,CAAC;AACD,WAAK,gBAAgB,QAAQ,KAAK,YAAY,aAAa;AAAA,IAC7D,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,UAAU,UAAU,KAAK,EAAE,aAAa,KAAK,UAAU,UAAU,GAAG,EAAE;AAAA,EACpF;AAAA,EACA,UAAU,gBAAgB,UAAU,KAAK;AACvC,UAAM,QAAQ,KAAK,UAAU,aAAa;AAC1C,QAAI,CAAC,OAAO;AACV,aAAO,KAAK;AAAA,IACd;AACA,WAAO,MAAM;AAAA,EACf;AAAA,EACA,cAAc;AACZ,WAAO,CAAC,EAAE,KAAK,UAAU,UAAU,KAAK,GAAG,iBAAiB,KAAK,UAAU,UAAU,GAAG,GAAG;AAAA,EAC7F;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,eAAe,KAAK,YAAY,cAAc;AACnD,SAAK,cAAc,KAAK,YAAY,cAAc,sBAAsB,EAAE;AAAA,EAC5E;AAAA;AAAA,EAEA,sBAAsB,QAAQ;AAC5B,UAAM,aAAa,KAAK,aAAa,cAAc;AACnD,eAAW,OAAO,OAAO;AACzB,eAAW,QAAQ,OAAO;AAC1B,eAAW,kBAAkB,OAAO;AACpC,eAAW,YAAY,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,uBAAuB,OAAO;AAE5B,UAAM,SAAS,SAAS,KAAK,uBAAuB,KAAK,WAAW,SAAS;AAC7E,UAAM,aAAa,KAAK,SAAS,KAAK,eAAe,IAAI,SAAS;AAClE,WAAO,cAAc,UAAU;AAAA,EACjC;AAAA;AAAA,EAEA,oBAAoB,QAAQ;AAC1B,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,eAAe,MAAM;AAC1B,SAAK,eAAe,MAAM;AAC1B,SAAK,0BAA0B,MAAM;AAAA,EACvC;AAAA,EACA,gCAAgC,QAAQ,QAAQ;AAC9C,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,WAAO,sBAAsB;AAC7B,WAAO,sBAAsB;AAAA,EAC/B;AAAA,EACA,eAAe,QAAQ;AACrB,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,wBAAwB,MAAM;AACnC,SAAK,kBAAkB;AACvB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA,EACA,wBAAwB;AACtB,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,qBAAqB;AAC7B;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,QAAI,KAAK,UAAU;AACjB,YAAM,SAAS,KAAK,UAAU,UAAU,GAAG;AAC3C,YAAM,SAAS,KAAK,UAAU,UAAU,KAAK;AAC7C,aAAO,sBAAsB;AAC7B,aAAO,sBAAsB;AAC7B,aAAO,oBAAoB;AAC3B,aAAO,oBAAoB;AAC3B,aAAO,cAAc;AACrB,aAAO,cAAc;AACrB,aAAO,qBAAqB;AAC5B,aAAO,qBAAqB;AAAA,IAC9B,OAAO;AACL,YAAM,SAAS,KAAK,UAAU,UAAU,GAAG;AAC3C,UAAI,QAAQ;AACV,eAAO,sBAAsB;AAAA,MAC/B;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB;AACtB,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,UAAM,WAAW,KAAK,UAAU,UAAU,GAAG;AAC7C,QAAI,CAAC,cAAc,CAAC,UAAU;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,SAAS,aAAa,WAAW,aAAa;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kCAAkC,QAAQ;AACxC,UAAM,UAAU,OAAO,WAAW;AAClC,UAAM,cAAc,KAAK,UAAU,OAAO,aAAa;AACvD,UAAM,eAAe,KAAK,UAAU,QAAQ,aAAa;AACzD,iBAAa,aAAa,UAAU,OAAO,wBAAwB;AACnE,gBAAY,aAAa,UAAU,OAAO,0BAA0B,KAAK,cAAc;AAAA,EACzF;AAAA;AAAA,EAEA,0BAA0B,QAAQ;AAChC,QAAI,CAAC,KAAK,YAAY,KAAK,YAAY,GAAG;AACxC;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,KAAK,sBAAsB,GAAG;AACxD,WAAK,iBAAiB,CAAC,KAAK;AAC5B,WAAK,kCAAkC,MAAM;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,QAAQ;AACrB,QAAI,KAAK,YAAY,GAAG;AACtB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,UAAU,OAAO,kBAAkB,UAAU,MAAM,UAAU,MAAM,UAAU,KAAK;AACrG,UAAM,aAAa,MAAM,YAAY,cAAc,OAAO,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,QAAQ;AAC9B,QAAI,KAAK,YAAY,GAAG;AACtB;AAAA,IACF;AACA,UAAM,YAAY,KAAK,YAAY,OAAO,KAAK;AAC/C,SAAK,sBAAsB,OAAO,WAAW,IAAI,SAAS,IAAI,OAAO,aAAa,aAAa,kBAAkB,SAAS;AAC1H,QAAI,KAAK,UAAU;AACjB,aAAO,kBAAkB,UAAU,QAAQ,KAAK,0BAA0B,YAAY,KAAK,wBAAwB;AACnH,YAAM,cAAc,KAAK,UAAU,OAAO,aAAa;AACvD,gBAAU,SAAS,IAAI,YAAY,aAAa,UAAU,IAAI,gCAAgC,IAAI,YAAY,aAAa,UAAU,OAAO,gCAAgC;AAAA,IAC9K;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,UAAM,SAAS,KAAK,UAAU,UAAU,GAAG;AAC3C,UAAM,SAAS,KAAK,UAAU,UAAU,KAAK;AAC7C,QAAI,QAAQ;AACV,WAAK,wBAAwB,MAAM;AAAA,IACrC;AACA,QAAI,QAAQ;AACV,WAAK,wBAAwB,MAAM;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,yBAAyB;AACvB,QAAI,CAAC,KAAK,iBAAiB,KAAK,YAAY,GAAG;AAC7C;AAAA,IACF;AACA,UAAM,OAAO,KAAK,SAAS,KAAK,QAAQ,IAAI,KAAK,QAAQ;AACzD,UAAM,WAAW,KAAK,MAAM,KAAK,MAAM,IAAI,IAAI;AAC/C,UAAM,cAAc,WAAW,KAAK,QAAQ,KAAK,MAAM,KAAK;AAC5D,SAAK,uBAAuB,KAAK,eAAe,KAAK;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,eAAe,QAAQ;AACrB,QAAI,KAAK,YAAY,GAAG;AACtB;AAAA,IACF;AACA,SAAK,WAAW,KAAK,oBAAoB,MAAM,IAAI,KAAK,uBAAuB,MAAM;AAAA,EACvF;AAAA,EACA,oBAAoB,QAAQ;AAC1B,UAAM,UAAU,OAAO,WAAW;AAClC,QAAI,CAAC,WAAW,CAAC,KAAK,cAAc;AAClC;AAAA,IACF;AACA,UAAM,mBAAmB,KAAK,IAAI,QAAQ,aAAa,OAAO,UAAU,IAAI,KAAK;AACjF,QAAI,OAAO,gBAAgB,KAAK,cAAc;AAC5C,WAAK,sBAAsB;AAAA,QACzB,MAAM;AAAA,QACN,OAAO,GAAG,KAAK,eAAe,QAAQ,UAAU;AAAA,QAChD,iBAAiB;AAAA,QACjB,WAAW,UAAU,gBAAgB;AAAA,MACvC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,sBAAsB;AAAA,QACzB,MAAM,GAAG,QAAQ,UAAU;AAAA,QAC3B,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,WAAW,UAAU,gBAAgB;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,QAAQ;AAC7B,SAAK,SAAS,KAAK,sBAAsB;AAAA,MACvC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAW,UAAU,IAAI,OAAO,cAAc;AAAA,IAChD,CAAC,IAAI,KAAK,sBAAsB;AAAA,MAC9B,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAW,UAAU,OAAO,cAAc;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB;AAClB,QAAI,CAAC,KAAK,iBAAiB,KAAK,SAAS,UAAa,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AACtG;AAAA,IACF;AACA,UAAM,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO;AACzC,SAAK,WAAW,KAAK,uBAAuB,IAAI,IAAI,KAAK,0BAA0B,IAAI;AAAA,EACzF;AAAA,EACA,0BAA0B,MAAM;AAC9B,UAAM,QAAQ,KAAK,UAAU;AAC7B,QAAI,YAAY,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,IAAI;AACrE,QAAI,cAAc,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,SAAS,IAAI,GAAG,CAAC,IAAI;AACvE,SAAK,SAAS,cAAc;AAC5B,SAAK,aAAa,MAAM,SAAS,EAAE,KAAK,aAAa,MAAM,EAAE,OAAO,MAAM,WAAW,EAAE,KAAK,aAAa,QAAQ,CAAC;AAAA,EACpH;AAAA,EACA,uBAAuB,MAAM;AAC3B,UAAM,WAAW,KAAK,UAAU;AAChC,UAAM,aAAa,KAAK,UAAU,UAAU,KAAK;AACjD,UAAM,8BAA8B,KAAK,IAAI,KAAK,OAAO,aAAa,KAAK,OAAO,IAAI,GAAG,CAAC;AAC1F,UAAM,YAAY,KAAK,IAAI,KAAK,OAAO,WAAW,cAAc,IAAI,IAAI,GAAG,CAAC;AAC5E,UAAM,2BAA2B,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,YAAY,IAAI,GAAG,CAAC;AACrF,SAAK,aAAa,MAAM,2BAA2B,EAAE,KAAK,aAAa,QAAQ,EAAE,OAAO,MAAM,SAAS,EAAE,KAAK,aAAa,MAAM,GAAG,MAAM,wBAAwB,EAAE,KAAK,aAAa,QAAQ,CAAC;AAAA,EACjM;AAAA;AAAA,EAEA,UAAU,eAAe;AACvB,QAAI,kBAAkB,UAAU,OAAO,KAAK,QAAQ;AAClD,aAAO,KAAK;AAAA,IACd;AACA,QAAI,KAAK,SAAS,QAAQ;AACxB,aAAO,kBAAkB,UAAU,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AAAA,IAC/E;AACA;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,eAAe;AACvB,WAAO,kBAAkB,UAAU,MAAM,KAAK,SAAS,OAAO,KAAK,SAAS;AAAA,EAC9E;AAAA,EACA,eAAe,eAAe;AAC5B,SAAK,gBAAgB,CAAC,KAAK,UAAU,OAAO,iBAAiB,CAAC,KAAK;AACnE,SAAK,YAAY,cAAc,UAAU,OAAO,iCAAiC,KAAK,aAAa;AAAA,EACrG;AAAA;AAAA,EAEA,uBAAuB,OAAO,MAAM;AAClC,UAAM,SAAS,KAAK,QAAQ;AAC5B,UAAM,UAAU,KAAK,IAAI;AACzB,UAAM,UAAU,KAAK,IAAI;AACzB,UAAM,KAAK,MAAM,UAAU;AAC3B,UAAM,KAAK,MAAM,UAAU;AAC3B,WAAO,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAC/C,QAAG,eAAe,UAAU,wBAAwB,CAAC;AAAA,MACvD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAC7D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,yBAAyB,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB,YAAY;AAAA,IAC7C,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,IAAI,SAAS,UAAU;AAC/C,QAAG,YAAY,qBAAqB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ,EAAE,0BAA0B,IAAI,aAAa,EAAE,2BAA2B,IAAI,eAAe;AAAA,MAC3N;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,KAAK,CAAC,GAAG,OAAO,OAAO,eAAe;AAAA,MACtC,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,KAAK,CAAC,GAAG,OAAO,OAAO,eAAe;AAAA,MACtC,MAAM,CAAC,GAAG,QAAQ,QAAQ,eAAe;AAAA,MACzC,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,YAAY,iBAAiB,oBAAoB,GAAG,CAAC,GAAG,SAAS,WAAW,CAAC;AAAA,IAChT,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,OAAO,GAAG,CAAC;AAC3B,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,OAAO,CAAC;AACjE,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,2BAA2B,CAAC;AACrF,QAAG,UAAU,GAAG,2BAA2B,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAC3C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,QAAG,UAAU;AACb,QAAG,WAAW,YAAY,IAAI,QAAQ,EAAE,iBAAiB,CAAC,EAAE,sBAAsB,IAAI,qBAAqB;AAAA,MAC7G;AAAA,IACF;AAAA,IACA,cAAc,CAAC,oBAAoB;AAAA,IACnC,QAAQ,CAAC,2oSAAmpS;AAAA,IAC5pS,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,MACrC;AAAA,MACA,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,oBAAoB;AAAA,MAC9B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,2oSAAmpS;AAAA,IAC9pS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,gBAAgB,SAAS,iBAAiB,mBAAmB;AACpE,QAAM,aAAa,CAAC,WAAW,mBAAmB,aAAa,aAAa,qBAAqB;AACjG,QAAM,WAAW,iBAAiB,aAAa,aAAa,UAAU,sBAAsB,gBAAgB;AAC5G,MAAI,CAAC,cAAc,CAAC,UAAU;AAC5B,yCAAqC;AAAA,EACvC;AACF;AACA,SAAS,uCAAuC;AAC9C,QAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcV;AACJ;AAMA,IAAM,kCAAkC;AAAA,EACtC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,cAAc;AAAA,EAC5C,OAAO;AACT;AAKA,IAAM,wCAAwC;AAAA,EAC5C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,EACjD,OAAO;AACT;AASA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,iBAAiB;AAAA,EAC/B,UAAU,OAAO,UAAU;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,gBAAgB,KAAK,aAAa,OAAO,CAAC;AAAA,EACnD;AAAA,EACA,IAAI,MAAM,OAAO;AACf,YAAQ,MAAM,KAAK,IAAI,IAAI;AAC3B,UAAM,cAAc,QAAQ;AAC5B,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,gBAAgB;AACrB;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,SAAK,aAAa,QAAQ;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,QAAQ,eAAe,IAAI;AAChC,SAAK,KAAK,cAAc;AACxB,SAAK,QAAQ,KAAK,aAAa;AAAA,EACjC;AAAA;AAAA,EAEA,cAAc,IAAI,aAAa;AAAA;AAAA,EAE/B,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,IAAI,aAAa;AACf,QAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,KAAK;AACxC,WAAK,cAAc,KAAK;AACxB,aAAO,KAAK;AAAA,IACd;AACA,QAAI,KAAK,gBAAgB,QAAW;AAClC,WAAK,cAAc,KAAK,uBAAuB;AAAA,IACjD;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,GAAG;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAAU;AAAA;AAAA,EAE1B,IAAI,MAAM;AACR,WAAO,gBAAgB,KAAK,aAAa,KAAK,CAAC;AAAA,EACjD;AAAA,EACA,IAAI,IAAI,GAAG;AACT,SAAK,aAAa,MAAM,IAAI;AAC5B,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,gBAAgB,KAAK,aAAa,KAAK,CAAC;AAAA,EACjD;AAAA,EACA,IAAI,IAAI,GAAG;AACT,SAAK,aAAa,MAAM,IAAI;AAC5B,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,gBAAgB,KAAK,aAAa,MAAM,CAAC;AAAA,EAClD;AAAA,EACA,IAAI,KAAK,GAAG;AACV,SAAK,aAAa,OAAO,IAAI;AAC7B,SAAK,KAAK,cAAc;AAAA,EAC1B;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,iBAAiB,KAAK,aAAa,QAAQ;AAAA,EACpD;AAAA,EACA,IAAI,SAAS,GAAG;AACd,SAAK,aAAa,WAAW;AAC7B,SAAK,KAAK,cAAc;AACxB,QAAI,KAAK,QAAQ,aAAa,KAAK,UAAU;AAC3C,WAAK,QAAQ,WAAW,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,QAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,KAAK;AACxC,aAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,IACnC;AACA,YAAQ,KAAK,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAAA,EAC5E;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,QAAI,CAAC,KAAK,QAAQ,cAAc;AAC9B,aAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,IACnC;AACA,QAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,aAAa,KAAK,QAAQ;AAAA,EACxC;AAAA;AAAA,EAEA,eAAe,KAAK,YAAY;AAAA;AAAA,EAEhC,aAAa,OAAO,EAAE;AAAA;AAAA,EAEtB,cAAc;AAAA;AAAA,EAEd,kBAAkB;AAAA;AAAA,EAElB,YAAY;AAAA;AAAA,EAEZ,aAAa;AAAA;AAAA,EAEb,cAAc,GAAG;AACf,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,gBAAgB;AAAA;AAAA,EAEhB;AAAA;AAAA,EAEA,eAAe,MAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStB,wBAAwB;AAAA,EACxB,cAAc;AACZ,UAAM,WAAW,OAAO,SAAS;AACjC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,oBAAoB,CAAC,SAAS,OAAO,KAAK,cAAc,eAAe,KAAK,eAAe,KAAK,IAAI,CAAC,GAAG,SAAS,OAAO,KAAK,cAAc,eAAe,KAAK,eAAe,KAAK,IAAI,CAAC,GAAG,SAAS,OAAO,KAAK,cAAc,aAAa,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC;AAAA,IAC/Q,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,QAAQ,aAAW,QAAQ,CAAC;AACnD,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,UAAU,SAAS;AACxB,SAAK,QAAQ,SAAS;AAAA,EACxB;AAAA;AAAA,EAEA,YAAY;AACV,SAAK,qBAAqB;AAE1B,QAAI,KAAK,aAAa,KAAK,QAAQ,UAAU;AAE3C,WAAK,QAAQ,WAAW;AAAA,IAC1B;AACA,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,MAAM,KAAK,QAAQ;AACxB,SAAK,MAAM,KAAK,QAAQ;AACxB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,aAAa;AACX,SAAK,sBAAsB;AAC3B,QAAI,KAAK,kBAAkB,QAAW;AACpC,WAAK,QAAQ,KAAK,iBAAiB;AAAA,IACrC,OAAO;AACL,WAAK,aAAa,QAAQ,KAAK;AAC/B,WAAK,sBAAsB;AAC3B,WAAK,QAAQ,eAAe,IAAI;AAChC,WAAK,KAAK,cAAc;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,SAAK,QAAQ,eAAe,KAAK;AACjC,SAAK,QAAQ,eAAe,IAAI;AAChC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA,EACA,YAAY;AACV,SAAK,YAAY,KAAK,KAAK,KAAK;AAGhC,QAAI,KAAK,WAAW;AAClB,WAAK,sBAAsB;AAAA,QACzB,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,cAAc,KAAK,KAAK;AAG7B,QAAI,KAAK,QAAQ,QAAQ,CAAC,KAAK,WAAW;AACxC,WAAK,sBAAsB;AAAA,QACzB,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AACA,SAAK,QAAQ,eAAe,IAAI;AAAA,EAClC;AAAA,EACA,0BAA0B;AAGxB,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY;AACvC,WAAK,QAAQ,eAAe,IAAI;AAChC,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,QAAQ,WAAW,KAAK,aAAa;AAAA,EAC5C;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,YAAY,MAAM,WAAW,GAAG;AACvC;AAAA,IACF;AAGA,QAAI,KAAK,UAAU,KAAK;AACtB,YAAM,wBAAwB,KAAK,QAAQ,uBAAuB,OAAO,KAAK,QAAQ,UAAU,KAAK,aAAa,EAAE,aAAa,sBAAsB,CAAC;AACxJ,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,QAAQ,kBAAkB;AAC/B;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,cAAc,IAAI;AACvB,SAAK,mBAAmB;AACxB,SAAK,QAAQ,kBAAkB;AAG/B,QAAI,CAAC,KAAK,QAAQ,MAAM;AACtB,WAAK,6BAA6B,OAAO;AAAA,QACvC,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,uBAAuB,KAAK;AACjC,WAAK,UAAU,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,OAAO;AAK5B,SAAK,gBAAgB;AAMrB,eAAW,MAAM;AACf,WAAK,gBAAgB;AACrB,WAAK,UAAU,KAAK;AAAA,IACtB,GAAG,CAAC;AAAA,EACN;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,UAAM,OAAO,MAAM,UAAU,KAAK,QAAQ;AAC1C,UAAM,QAAQ,KAAK,QAAQ;AAC3B,UAAM,OAAO,KAAK,QAAQ,SAAS,IAAI,IAAI,KAAK,QAAQ;AACxD,UAAM,WAAW,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,IAAI;AACxE,UAAM,aAAa,KAAK,QAAQ,SAAS,IAAI,OAAO,QAAQ,OAAO;AAEnE,UAAM,kBAAkB,KAAK,MAAM,aAAa,QAAQ,IAAI;AAC5D,UAAM,iBAAiB,mBAAmB,KAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,KAAK,QAAQ;AAC9F,UAAM,QAAQ,KAAK,MAAM,iBAAiB,IAAI,IAAI;AAClD,UAAM,YAAY,KAAK;AACvB,QAAI,UAAU,WAAW;AAIvB,WAAK,QAAQ,eAAe,IAAI;AAChC,WAAK,QAAQ,OAAO,IAAI,KAAK,sBAAsB,IAAI,KAAK,6BAA6B,OAAO;AAAA,QAC9F,eAAe,KAAK,QAAQ;AAAA,MAC9B,CAAC;AACD;AAAA,IACF;AACA,SAAK,QAAQ;AACb,SAAK,YAAY,KAAK,KAAK,KAAK;AAChC,SAAK,cAAc,KAAK,KAAK;AAC7B,SAAK,QAAQ,eAAe,IAAI;AAChC,SAAK,QAAQ,OAAO,IAAI,KAAK,sBAAsB,IAAI,KAAK,6BAA6B,OAAO;AAAA,MAC9F,eAAe,KAAK,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,eAAe,OAAO;AAGpB,QAAI,CAAC,KAAK,QAAQ,QAAQ,KAAK,WAAW;AACxC,WAAK,6BAA6B,KAAK;AAAA,IACzC;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY;AACjB,UAAI,KAAK,UAAU,QAAQ;AACzB,aAAK,cAAc,KAAK;AAAA,MAC1B;AACA,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,MACd,CAAC;AAKD,iBAAW,MAAM,KAAK,qBAAqB,GAAG,KAAK,UAAU,MAAM,KAAK,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO,GAAG;AACR,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK,QAAQ,eAAe,KAAK;AAC7C,WAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AAAA,EACvC;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,QAAQ,QAAQ;AACvB,cAAQ,IAAI,KAAK,eAAe,KAAK,QAAQ,eAAe,KAAK,kBAAkB,KAAK,KAAK;AAAA,IAC/F;AACA,WAAO,KAAK,cAAc,KAAK,QAAQ,eAAe,KAAK,kBAAkB,KAAK,KAAK;AAAA,EACzF;AAAA,EACA,8BAA8B,OAAO;AACnC,WAAO,MAAM,UAAU,KAAK,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,uBAAuB;AACrB,SAAK,aAAa,MAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AACjE,SAAK,aAAa,MAAM,QAAQ,eAAe,KAAK,QAAQ,gBAAgB,KAAK,kBAAkB,CAAC;AACpG,SAAK,aAAa,MAAM,OAAO,IAAI,KAAK,QAAQ,gBAAgB,KAAK,eAAe;AAAA,EACtF;AAAA,EACA,sBAAsB,SAAS;AAC7B,SAAK,aAAa,KAAK,OAAO,KAAK,uBAAuB,CAAC;AAC3D,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA,EACA,6BAA6B,OAAO,SAAS;AAC3C,SAAK,aAAa,KAAK,OAAO,KAAK,8BAA8B,KAAK,CAAC;AACvE,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA,EACA,eAAe,SAAS;AACtB,SAAK,QAAQ,eAAe,CAAC,CAAC,SAAS,aAAa;AACpD,SAAK,QAAQ,oBAAoB,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,QAAI,KAAK,yBAAyB,UAAU,MAAM;AAChD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,IAAI;AACnB,SAAK,cAAc;AACnB,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,kBAAkB,EAAE,CAAC;AAAA,IAC3C,WAAW,CAAC,QAAQ,SAAS,GAAG,mBAAmB;AAAA,IACnD,UAAU;AAAA,IACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,2CAA2C;AAC1E,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC,EAAE,SAAS,SAAS,0CAA0C;AAC7D,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,EAAE,QAAQ,SAAS,yCAAyC;AAC3D,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC,EAAE,SAAS,SAAS,0CAA0C;AAC7D,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,IAAI,WAAW,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,IAC9C;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,gBAAgB;AAAA,IAC3B,UAAU,CAAI,mBAAmB,CAAC,iCAAiC;AAAA,MACjE,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,yBAAyB;AAAA,QACzB,YAAY;AAAA,QACZ,WAAW;AAAA;AAAA;AAAA,QAGX,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,iCAAiC;AAAA,QAC3C,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,6BAA4B,eAAe;AAAA,EAC/C,OAAO,OAAO,iBAAiB;AAAA;AAAA,EAE/B,aAAa;AACX,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,cAAc,UAAU,QAAQ,UAAU,GAAG;AAAA,IAC3F;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,CAAC,KAAK,gBAAgB,SAAS;AACjC,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,KAAK,gBAAgB,SAAS;AAChC,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,KAAK,QAAQ,eAAe,KAAK;AAAA,EAC1C;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe,KAAK,eAAe,KAAK,QAAQ,UAAU,CAAC,KAAK,eAAe,CAAC,KAAK,QAAQ;AAAA,EACpG;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,cAAc,KAAK,aAAa,aAAa,mBAAmB;AACrE,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,KAAK,cAAc,UAAU,MAAM,UAAU;AAAA,EACpE;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,eAAe,KAAK,QAAQ,WAAW,KAAK,MAAM,KAAK;AAAA,EACrE;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,eAAe;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,UAAM,wBAAwB;AAC9B,SAAK,WAAW,GAAG,cAAc;AAAA,EACnC;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,YAAY,MAAM,WAAW,GAAG;AACvC;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,mBAAmB;AACjC,WAAK,SAAS,aAAa,UAAU,IAAI,wCAAwC;AAAA,IACnF;AACA,UAAM,eAAe,KAAK;AAAA,EAC5B;AAAA,EACA,eAAe;AACb,UAAM,aAAa;AACnB,QAAI,KAAK,UAAU;AACjB,iBAAW,MAAM;AACf,aAAK,SAAS,qBAAqB;AACnC,aAAK,SAAS,aAAa,UAAU,OAAO,wCAAwC;AAAA,MACtF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,eAAe,KAAK;AAC1B,QAAI,CAAC,KAAK,QAAQ,QAAQ,KAAK,WAAW;AACxC,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,KAAK;AACrB,SAAK,UAAU,cAAc;AAAA,EAC/B;AAAA,EACA,OAAO,GAAG;AACR,WAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC;AAAA,EACjE;AAAA,EACA,gBAAgB;AACd,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK;AACnD,WAAK,MAAM,KAAK,QAAQ;AAAA,IAC1B,OAAO;AACL,WAAK,MAAM,KAAK,QAAQ;AACxB,WAAK,MAAM,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,IACrD;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,WAAW,KAAK,QAAQ,gBAAgB,IAAI,KAAK,QAAQ,gBAAgB;AAC/E,UAAM,WAAW,KAAK,QAAQ,eAAe,KAAK,QAAQ,gBAAgB,WAAW,KAAK,kBAAkB;AAC5G,UAAM,aAAa,KAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO;AACzH,UAAM,QAAQ,WAAW,aAAa;AACtC,SAAK,aAAa,MAAM,QAAQ,GAAG,KAAK;AACxC,SAAK,aAAa,MAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAAA,EACnE;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,WAAW,KAAK,QAAQ,eAAe,KAAK,kBAAkB;AACpE,UAAM,WAAW,KAAK,cAAc,KAAK,SAAS,KAAK,QAAQ,QAAQ,SAAS,IAAI,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS;AAChI,UAAM,cAAc,KAAK,eAAe,KAAK,MAAM,aAAa,KAAK,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,KAAK,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAChK,UAAM,aAAa,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,cAAc;AAEvE,QAAI,gBAAgB,KAAK,QAAQ;AAIjC,QAAI,eAAe,GAAG;AACpB,sBAAgB;AAAA,IAClB,WAAW,eAAe,GAAG;AAC3B,sBAAgB;AAAA,IAClB;AACA,UAAM,QAAQ,WAAW,aAAa;AACtC,SAAK,aAAa,MAAM,QAAQ,GAAG,KAAK;AACxC,SAAK,aAAa,MAAM,UAAU;AAClC,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,MAAM,OAAO,IAAI,KAAK,QAAQ,gBAAgB,KAAK,eAAe;AACpF,WAAK,aAAa,MAAM,QAAQ;AAAA,IAClC,OAAO;AACL,WAAK,aAAa,MAAM,OAAO;AAC/B,WAAK,aAAa,MAAM,QAAQ,IAAI,KAAK,QAAQ,gBAAgB,KAAK,eAAe;AAAA,IACvF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,SAAK,aAAa,UAAU,OAAO,2BAA2B,CAAC,KAAK,YAAY;AAAA,EAClF;AAAA,EACA,iBAAiB;AACf,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,YAAQ,cAAc;AACtB,QAAI,KAAK,WAAW;AAClB,cAAQ,mBAAmB;AAAA,IAC7B,OAAO;AACL,cAAQ,qBAAqB;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,QAAI,KAAK,yBAAyB,UAAU,MAAM;AAChD,WAAK,QAAQ;AACb,WAAK,qBAAqB;AAC1B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,KAAK;AACrB,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,uBAAuB,EAAE,GAAG,CAAC,SAAS,qBAAqB,EAAE,CAAC;AAAA,IACpF,UAAU,CAAC,qBAAqB;AAAA,IAChC,UAAU,CAAI,mBAAmB,CAAC,uCAAuC;AAAA,MACvE,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,uCAAuC;AAAA,QACjD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,WAAW,gBAAgB,qBAAqB,oBAAoB;AAAA,IAChH,SAAS,CAAC,WAAW,gBAAgB,mBAAmB;AAAA,EAC1D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,WAAW,gBAAgB,qBAAqB,oBAAoB;AAAA,MAChH,SAAS,CAAC,WAAW,gBAAgB,mBAAmB;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["_MatThumb", "_MatTickMark"]}