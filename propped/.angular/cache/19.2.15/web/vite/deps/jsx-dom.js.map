{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/jsx-dom@8.1.6/node_modules/jsx-dom/index.js"], "sourcesContent": ["/* eslint-disable */\nconst keys = Object.keys;\nfunction identity(value) {\n  return value;\n}\nfunction isBoolean(val) {\n  return typeof val === \"boolean\";\n}\nfunction isElement(val) {\n  return val && typeof val.nodeType === \"number\";\n}\nfunction isString(val) {\n  return typeof val === \"string\";\n}\nfunction isNumber(val) {\n  return typeof val === \"number\";\n}\nfunction isObject(val) {\n  return typeof val === \"object\" ? val !== null : isFunction(val);\n}\nfunction isFunction(val) {\n  return typeof val === \"function\";\n}\nfunction isComponentClass(Component) {\n  return !!(Component && Component.isComponent);\n}\nfunction isArrayLike(obj) {\n  return isObject(obj) && typeof obj.length === \"number\" && typeof obj.nodeType !== \"number\";\n}\nfunction forEach(value, fn) {\n  if (!value) return;\n  for (const key of keys(value)) {\n    fn(value[key], key);\n  }\n}\nfunction createRef() {\n  return Object.seal({\n    current: null\n  });\n}\nfunction isRef(maybeRef) {\n  return isObject(maybeRef) && \"current\" in maybeRef;\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found on\n * https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/LICENSE\n */\n\n/**\n * CSS properties which accept numbers but are not in units of \"px\".\n */\nconst isUnitlessNumber = {\n  animationIterationCount: 0,\n  borderImageOutset: 0,\n  borderImageSlice: 0,\n  borderImageWidth: 0,\n  boxFlex: 0,\n  boxFlexGroup: 0,\n  boxOrdinalGroup: 0,\n  columnCount: 0,\n  columns: 0,\n  flex: 0,\n  flexGrow: 0,\n  flexPositive: 0,\n  flexShrink: 0,\n  flexNegative: 0,\n  flexOrder: 0,\n  gridArea: 0,\n  gridRow: 0,\n  gridRowEnd: 0,\n  gridRowSpan: 0,\n  gridRowStart: 0,\n  gridColumn: 0,\n  gridColumnEnd: 0,\n  gridColumnSpan: 0,\n  gridColumnStart: 0,\n  fontWeight: 0,\n  lineClamp: 0,\n  lineHeight: 0,\n  opacity: 0,\n  order: 0,\n  orphans: 0,\n  tabSize: 0,\n  widows: 0,\n  zIndex: 0,\n  zoom: 0,\n  // SVG-related properties\n  fillOpacity: 0,\n  floodOpacity: 0,\n  stopOpacity: 0,\n  strokeDasharray: 0,\n  strokeDashoffset: 0,\n  strokeMiterlimit: 0,\n  strokeOpacity: 0,\n  strokeWidth: 0\n};\n\n/**\n * @param prefix vendor-specific prefix, eg: Webkit\n * @param key style name, eg: transitionDuration\n * @return style name prefixed with `prefix`, properly camelCased, eg:\n * WebkitTransitionDuration\n */\nfunction prefixKey(prefix, key) {\n  return prefix + key.charAt(0).toUpperCase() + key.substring(1);\n}\n\n/**\n * Support style names that may come passed in prefixed by adding permutations\n * of vendor prefixes.\n */\nconst prefixes = [\"Webkit\", \"ms\", \"Moz\", \"O\"];\n// Using Object.keys here, or else the vanilla for-in loop makes IE8 go into an\n// infinite loop, because it iterates over the newly added props too.\nkeys(isUnitlessNumber).forEach(prop => {\n  prefixes.forEach(prefix => {\n    isUnitlessNumber[prefixKey(prefix, prop)] = 0; // isUnitlessNumber[prop]\n  });\n});\nconst jsxDomType = Symbol.for(\"jsx-dom:type\");\nvar JsxDomType = /*#__PURE__*/function (JsxDomType) {\n  JsxDomType[\"ShadowRoot\"] = \"ShadowRoot\";\n  return JsxDomType;\n}(JsxDomType || {});\nfunction ShadowRoot(_ref) {\n  let {\n    children,\n    ref,\n    ...attr\n  } = _ref;\n  return {\n    [jsxDomType]: JsxDomType.ShadowRoot,\n    ref,\n    attr,\n    children\n  };\n}\nfunction isShadowRoot(el) {\n  return el != null && el[jsxDomType] === JsxDomType.ShadowRoot;\n}\nconst SVGNamespace = \"http://www.w3.org/2000/svg\";\nconst XLinkNamespace = \"http://www.w3.org/1999/xlink\";\nconst XMLNamespace = \"http://www.w3.org/XML/1998/namespace\";\n\n// https://facebook.github.io/react/docs/jsx-in-depth.html#booleans-null-and-undefined-are-ignored\n// Emulate JSX Expression logic to ignore certain type of children or className.\nfunction isVisibleChild(value) {\n  return !isBoolean(value) && value != null;\n}\n\n/**\n * Convert a `value` to a className string.\n * `value` can be a string, an array or a `Dictionary<boolean>`.\n */\nfunction className(value) {\n  if (Array.isArray(value)) {\n    return value.map(className).filter(Boolean).join(\" \");\n  } else if (isObject(value)) {\n    if (Symbol.iterator in value) {\n      return className(Array.from(value));\n    }\n    return keys(value).filter(k => value[k]).join(\" \");\n  } else if (isVisibleChild(value)) {\n    return \"\" + value;\n  } else {\n    return \"\";\n  }\n}\nconst svg = {\n  animate: 0,\n  circle: 0,\n  clipPath: 0,\n  defs: 0,\n  desc: 0,\n  ellipse: 0,\n  feBlend: 0,\n  feColorMatrix: 0,\n  feComponentTransfer: 0,\n  feComposite: 0,\n  feConvolveMatrix: 0,\n  feDiffuseLighting: 0,\n  feDisplacementMap: 0,\n  feDistantLight: 0,\n  feFlood: 0,\n  feFuncA: 0,\n  feFuncB: 0,\n  feFuncG: 0,\n  feFuncR: 0,\n  feGaussianBlur: 0,\n  feImage: 0,\n  feMerge: 0,\n  feMergeNode: 0,\n  feMorphology: 0,\n  feOffset: 0,\n  fePointLight: 0,\n  feSpecularLighting: 0,\n  feSpotLight: 0,\n  feTile: 0,\n  feTurbulence: 0,\n  filter: 0,\n  foreignObject: 0,\n  g: 0,\n  image: 0,\n  line: 0,\n  linearGradient: 0,\n  marker: 0,\n  mask: 0,\n  metadata: 0,\n  path: 0,\n  pattern: 0,\n  polygon: 0,\n  polyline: 0,\n  radialGradient: 0,\n  rect: 0,\n  stop: 0,\n  svg: 0,\n  switch: 0,\n  symbol: 0,\n  text: 0,\n  textPath: 0,\n  tspan: 0,\n  use: 0,\n  view: 0\n};\nconst nonPresentationSVGAttributes = /^(a(ll|t|u)|base[FP]|c(al|lipPathU|on)|di|ed|ex|filter[RU]|g(lyphR|r)|ke|l(en|im)|ma(rker[HUW]|s)|n|pat|pr|point[^e]|re[^n]|s[puy]|st[^or]|ta|textL|vi|xC|y|z)/;\nfunction createFactory(tag) {\n  return createElement.bind(null, tag);\n}\nfunction Fragment(attr) {\n  const fragment = document.createDocumentFragment();\n  appendChild(attr.children, fragment);\n  return fragment;\n}\nclass Component {\n  static isComponent = true;\n  constructor(props) {\n    this.props = props;\n  }\n  render() {\n    return null;\n  }\n}\nfunction initComponentClass(Class, attr, children) {\n  attr = {\n    ...attr,\n    children\n  };\n  const instance = new Class(attr);\n  const node = instance.render();\n  if (\"ref\" in attr) {\n    attachRef(attr.ref, instance);\n  }\n  return node;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction jsx(tag, _ref) {\n  let {\n    children,\n    ...attr\n  } = _ref;\n  if (!attr.namespaceURI && svg[tag] === 0) {\n    attr = {\n      ...attr,\n      namespaceURI: SVGNamespace\n    };\n  }\n  let node;\n  if (isString(tag)) {\n    node = attr.namespaceURI ? document.createElementNS(attr.namespaceURI, tag) : document.createElement(tag);\n    attributes(attr, node);\n    appendChild(children, node);\n\n    // Select `option` elements in `select`\n    if (node instanceof window.HTMLSelectElement && attr.value != null) {\n      if (attr.multiple === true && Array.isArray(attr.value)) {\n        const values = attr.value.map(value => String(value));\n        node.querySelectorAll(\"option\").forEach(option => option.selected = values.includes(option.value));\n      } else {\n        node.value = attr.value;\n      }\n    }\n    attachRef(attr.ref, node);\n  } else if (isFunction(tag)) {\n    // Custom elements.\n    if (isObject(tag.defaultProps)) {\n      attr = {\n        ...tag.defaultProps,\n        ...attr\n      };\n    }\n    node = isComponentClass(tag) ? initComponentClass(tag, attr, children) : tag({\n      ...attr,\n      children\n    });\n  } else {\n    throw new TypeError(`Invalid JSX element type: ${tag}`);\n  }\n  return node;\n}\nfunction createElement(tag, attr) {\n  for (var _len = arguments.length, children = new Array(_len > 2 ? _len - 2 : 0), _key2 = 2; _key2 < _len; _key2++) {\n    children[_key2 - 2] = arguments[_key2];\n  }\n  if (isString(attr) || Array.isArray(attr)) {\n    children.unshift(attr);\n    attr = {};\n  }\n  attr = attr || {};\n  if (attr.children != null && !children.length) {\n    ({\n      children,\n      ...attr\n    } = attr);\n  }\n  return jsx(tag, {\n    ...attr,\n    children\n  }, attr.key);\n}\nfunction attachRef(ref, node) {\n  if (isRef(ref)) {\n    ref.current = node;\n  } else if (isFunction(ref)) {\n    ref(node);\n  }\n}\nfunction appendChild(child, node) {\n  if (isArrayLike(child)) {\n    appendChildren(child, node);\n  } else if (isString(child) || isNumber(child)) {\n    appendChildToNode(document.createTextNode(child), node);\n  } else if (child === null) {\n    appendChildToNode(document.createComment(\"\"), node);\n  } else if (isElement(child)) {\n    appendChildToNode(child, node);\n  } else if (isShadowRoot(child)) {\n    const shadowRoot = node.attachShadow(child.attr);\n    appendChild(child.children, shadowRoot);\n    attachRef(child.ref, shadowRoot);\n  }\n}\nfunction appendChildren(children, node) {\n  for (const child of [...children]) {\n    appendChild(child, node);\n  }\n  return node;\n}\nfunction appendChildToNode(child, node) {\n  if (node instanceof window.HTMLTemplateElement) {\n    node.content.appendChild(child);\n  } else {\n    node.appendChild(child);\n  }\n}\nfunction normalizeAttribute(s, separator) {\n  return s.replace(/[A-Z]/g, match => separator + match.toLowerCase());\n}\nfunction style(node, value) {\n  if (value == null || value === false) ;else if (Array.isArray(value)) {\n    value.forEach(v => style(node, v));\n  } else if (isString(value)) {\n    node.setAttribute(\"style\", value);\n  } else if (isObject(value)) {\n    forEach(value, (val, key) => {\n      if (key.indexOf(\"-\") === 0) {\n        // CSS custom properties (variables) start with `-` (e.g. `--my-variable`)\n        // and must be assigned via `setProperty`.\n        node.style.setProperty(key, val);\n      } else if (isNumber(val) && isUnitlessNumber[key] !== 0) {\n        node.style[key] = val + \"px\";\n      } else {\n        node.style[key] = val;\n      }\n    });\n  }\n}\nfunction attribute(key, value, node) {\n  switch (key) {\n    case \"xlinkActuate\":\n    case \"xlinkArcrole\":\n    case \"xlinkHref\":\n    case \"xlinkRole\":\n    case \"xlinkShow\":\n    case \"xlinkTitle\":\n    case \"xlinkType\":\n      attrNS(node, XLinkNamespace, normalizeAttribute(key, \":\"), value);\n      return;\n    case \"xmlnsXlink\":\n      attr(node, normalizeAttribute(key, \":\"), value);\n      return;\n    case \"xmlBase\":\n    case \"xmlLang\":\n    case \"xmlSpace\":\n      attrNS(node, XMLNamespace, normalizeAttribute(key, \":\"), value);\n      return;\n  }\n  switch (key) {\n    case \"htmlFor\":\n      attr(node, \"for\", value);\n      return;\n    case \"dataset\":\n      forEach(value, (dataValue, dataKey) => {\n        if (dataValue != null) {\n          node.dataset[dataKey] = dataValue;\n        }\n      });\n      return;\n    case \"innerHTML\":\n    case \"innerText\":\n    case \"textContent\":\n      if (isVisibleChild(value)) {\n        node[key] = value;\n      }\n      return;\n    case \"dangerouslySetInnerHTML\":\n      if (isObject(value)) {\n        node.innerHTML = value[\"__html\"];\n      }\n      return;\n    case \"value\":\n      if (value == null || node instanceof window.HTMLSelectElement) {\n        // skip nullish values\n        // for `<select>` apply value after appending `<option>` elements\n        return;\n      } else if (node instanceof window.HTMLTextAreaElement) {\n        node.value = value;\n        return;\n      }\n      // use attribute for other elements\n      break;\n    case \"spellCheck\":\n      node.spellcheck = value;\n      return;\n    case \"class\":\n    case \"className\":\n      if (isFunction(value)) {\n        value(node);\n      } else {\n        attr(node, \"class\", className(value));\n      }\n      return;\n    case \"ref\":\n    case \"namespaceURI\":\n      return;\n    case \"style\":\n      style(node, value);\n      return;\n    case \"on\":\n    case \"onCapture\":\n      forEach(value, (eventHandler, eventName) => {\n        node.addEventListener(eventName, eventHandler, key === \"onCapture\");\n      });\n      return;\n    // fallthrough\n  }\n  if (isFunction(value)) {\n    if (key[0] === \"o\" && key[1] === \"n\") {\n      let attribute = key.toLowerCase();\n      const useCapture = attribute.endsWith(\"capture\");\n      if (attribute === \"ondoubleclick\") {\n        attribute = \"ondblclick\";\n      } else if (useCapture && attribute === \"ondoubleclickcapture\") {\n        attribute = \"ondblclickcapture\";\n      }\n      if (!useCapture && node[attribute] === null) {\n        // use property when possible PR #17\n        node[attribute] = value;\n      } else if (useCapture) {\n        node.addEventListener(attribute.substring(2, attribute.length - 7), value, true);\n      } else {\n        let eventName;\n        if (attribute in window) {\n          // standard event\n          // the JSX attribute could have been \"onMouseOver\" and the\n          // member name \"onmouseover\" is on the window's prototype\n          // so let's add the listener \"mouseover\", which is all lowercased\n          const standardEventName = attribute.substring(2);\n          eventName = standardEventName;\n        } else {\n          // custom event\n          // the JSX attribute could have been \"onMyCustomEvent\"\n          // so let's trim off the \"on\" prefix and lowercase the first character\n          // and add the listener \"myCustomEvent\"\n          // except for the first character, we keep the event name case\n          const customEventName = attribute[2] + key.slice(3);\n          eventName = customEventName;\n        }\n        node.addEventListener(eventName, value);\n      }\n    }\n  } else if (isObject(value)) {\n    node[key] = value;\n  } else if (value === true) {\n    attr(node, key, \"\");\n  } else if (value !== false && value != null) {\n    if (node instanceof SVGElement && !nonPresentationSVGAttributes.test(key)) {\n      attr(node, normalizeAttribute(key, \"-\"), value);\n    } else {\n      attr(node, key, value);\n    }\n  }\n}\nfunction attr(node, key, value) {\n  node.setAttribute(key, value);\n}\nfunction attrNS(node, namespace, key, value) {\n  node.setAttributeNS(namespace, key, value);\n}\nfunction attributes(attr, node) {\n  for (const key of keys(attr)) {\n    attribute(key, attr[key], node);\n  }\n  return node;\n}\nfunction useText(initialValue) {\n  const text = new Text();\n  Object.defineProperty(text, \"toString\", {\n    value() {\n      return this.textContent;\n    }\n  });\n  function setText(value) {\n    text.textContent = value;\n  }\n  if (initialValue != null) {\n    setText(initialValue);\n  }\n  return [text, setText];\n}\nfunction useClassList(initialValue) {\n  const div = document.createElement(\"div\");\n  if (initialValue != null) {\n    div.className = className(initialValue);\n  }\n  let list = div.classList;\n  function ClassList(value) {\n    value.setAttribute(\"class\", list.value);\n    list = value.classList;\n  }\n  Object.defineProperties(ClassList, Object.getOwnPropertyDescriptors({\n    get size() {\n      return list.length;\n    },\n    get value() {\n      return list.value;\n    },\n    add() {\n      list.add(...arguments);\n    },\n    remove() {\n      list.remove(...arguments);\n    },\n    toggle(token, force) {\n      list.toggle(token, force);\n    },\n    contains(token) {\n      return list.contains(token);\n    }\n  }));\n  return ClassList;\n}\nfunction useMemo(factory) {\n  return factory();\n}\nfunction forwardRef(render) {\n  return _ref => {\n    let {\n      ref,\n      ...props\n    } = _ref;\n    return render(props, ref ?? createRef());\n  };\n}\nfunction useImperativeHandle(ref, init) {\n  attachRef(ref, init());\n}\nconst cache = /* @__PURE__ */new Map();\nconst createStyledComponent = name => function (list) {\n  for (var _len = arguments.length, interpolations = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    interpolations[_key - 1] = arguments[_key];\n  }\n  return _ref => {\n    let {\n      style,\n      ...props\n    } = _ref;\n    const lastIndex = list.length - 1;\n    const css = list.slice(0, lastIndex).reduce((p, s, i) => {\n      const interpolation = interpolations[i];\n      const current = typeof interpolation === \"function\" ? interpolation(props) : interpolation;\n      return p + s + current;\n    }, \"\") + list[lastIndex];\n    return createElement(name, {\n      style: [css, style],\n      ...props\n    });\n  };\n};\nconst baseStyled = customComponent => createStyledComponent(customComponent);\nconst styled = /* @__PURE__ */new Proxy(baseStyled, {\n  get(_, name) {\n    return setIfAbsent(cache, name, () => createStyledComponent(name));\n  }\n});\nfunction setIfAbsent(map, key, getValue) {\n  if (map.has(key)) {\n    return map.get(key);\n  } else {\n    const value = getValue(key);\n    map.set(key, value);\n    return value;\n  }\n}\nvar index = {\n  Component,\n  PureComponent: Component,\n  createElement,\n  Fragment,\n  ShadowRoot\n};\nfunction preventDefault(event) {\n  event.preventDefault();\n  return event;\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n  return event;\n}\nexport { Component, Fragment, Component as PureComponent, SVGNamespace, ShadowRoot, Fragment as StrictMode, className, createElement, createFactory, createRef, index as default, forwardRef, createElement as h, isRef, jsx, jsx as jsxs, identity as memo, preventDefault, stopPropagation, styled, identity as useCallback, useClassList, useImperativeHandle, useMemo, createRef as useRef, useText };"], "mappings": ";;;;;;;AACA,IAAM,OAAO,OAAO;AACpB,SAAS,SAAS,OAAO;AACvB,SAAO;AACT;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,OAAO,OAAO,IAAI,aAAa;AACxC;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ,WAAW,QAAQ,OAAO,WAAW,GAAG;AAChE;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,iBAAiBA,YAAW;AACnC,SAAO,CAAC,EAAEA,cAAaA,WAAU;AACnC;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,SAAS,GAAG,KAAK,OAAO,IAAI,WAAW,YAAY,OAAO,IAAI,aAAa;AACpF;AACA,SAAS,QAAQ,OAAO,IAAI;AAC1B,MAAI,CAAC,MAAO;AACZ,aAAW,OAAO,KAAK,KAAK,GAAG;AAC7B,OAAG,MAAM,GAAG,GAAG,GAAG;AAAA,EACpB;AACF;AACA,SAAS,YAAY;AACnB,SAAO,OAAO,KAAK;AAAA,IACjB,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,MAAM,UAAU;AACvB,SAAO,SAAS,QAAQ,KAAK,aAAa;AAC5C;AAYA,IAAM,mBAAmB;AAAA,EACvB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AAQA,SAAS,UAAU,QAAQ,KAAK;AAC9B,SAAO,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AAC/D;AAMA,IAAM,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAG5C,KAAK,gBAAgB,EAAE,QAAQ,UAAQ;AACrC,WAAS,QAAQ,YAAU;AACzB,qBAAiB,UAAU,QAAQ,IAAI,CAAC,IAAI;AAAA,EAC9C,CAAC;AACH,CAAC;AACD,IAAM,aAAa,OAAO,IAAI,cAAc;AAC5C,IAAI,aAA0B,SAAUC,aAAY;AAClD,EAAAA,YAAW,YAAY,IAAI;AAC3B,SAAOA;AACT,EAAE,cAAc,CAAC,CAAC;AAClB,SAAS,WAAW,MAAM;AACxB,MAII,WAHF;AAAA;AAAA,IACA;AAAA,EAlIJ,IAoIM,IADCC,QAAA,UACD,IADC;AAAA,IAFH;AAAA,IACA;AAAA;AAGF,SAAO;AAAA,IACL,CAAC,UAAU,GAAG,WAAW;AAAA,IACzB;AAAA,IACA,MAAAA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,MAAM,QAAQ,GAAG,UAAU,MAAM,WAAW;AACrD;AACA,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AAIrB,SAAS,eAAe,OAAO;AAC7B,SAAO,CAAC,UAAU,KAAK,KAAK,SAAS;AACvC;AAMA,SAAS,UAAU,OAAO;AACxB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,EACtD,WAAW,SAAS,KAAK,GAAG;AAC1B,QAAI,OAAO,YAAY,OAAO;AAC5B,aAAO,UAAU,MAAM,KAAK,KAAK,CAAC;AAAA,IACpC;AACA,WAAO,KAAK,KAAK,EAAE,OAAO,OAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,EACnD,WAAW,eAAe,KAAK,GAAG;AAChC,WAAO,KAAK;AAAA,EACd,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,MAAM;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAM,+BAA+B;AACrC,SAAS,cAAc,KAAK;AAC1B,SAAO,cAAc,KAAK,MAAM,GAAG;AACrC;AACA,SAAS,SAASA,OAAM;AACtB,QAAM,WAAW,SAAS,uBAAuB;AACjD,cAAYA,MAAK,UAAU,QAAQ;AACnC,SAAO;AACT;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,OAAO,cAAc;AAAA,EACrB,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS;AACP,WAAO;AAAA,EACT;AACF;AACA,SAAS,mBAAmB,OAAOA,OAAM,UAAU;AACjD,EAAAA,QAAO,iCACFA,QADE;AAAA,IAEL;AAAA,EACF;AACA,QAAM,WAAW,IAAI,MAAMA,KAAI;AAC/B,QAAM,OAAO,SAAS,OAAO;AAC7B,MAAI,SAASA,OAAM;AACjB,cAAUA,MAAK,KAAK,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT;AAGA,SAAS,IAAI,KAAK,MAAM;AACtB,MAGI,WAFF;AAAA;AAAA,EArQJ,IAuQM,IADCA,QAAA,UACD,IADC;AAAA,IADH;AAAA;AAGF,MAAI,CAACA,MAAK,gBAAgB,IAAI,GAAG,MAAM,GAAG;AACxC,IAAAA,QAAO,iCACFA,QADE;AAAA,MAEL,cAAc;AAAA,IAChB;AAAA,EACF;AACA,MAAI;AACJ,MAAI,SAAS,GAAG,GAAG;AACjB,WAAOA,MAAK,eAAe,SAAS,gBAAgBA,MAAK,cAAc,GAAG,IAAI,SAAS,cAAc,GAAG;AACxG,eAAWA,OAAM,IAAI;AACrB,gBAAY,UAAU,IAAI;AAG1B,QAAI,gBAAgB,OAAO,qBAAqBA,MAAK,SAAS,MAAM;AAClE,UAAIA,MAAK,aAAa,QAAQ,MAAM,QAAQA,MAAK,KAAK,GAAG;AACvD,cAAM,SAASA,MAAK,MAAM,IAAI,WAAS,OAAO,KAAK,CAAC;AACpD,aAAK,iBAAiB,QAAQ,EAAE,QAAQ,YAAU,OAAO,WAAW,OAAO,SAAS,OAAO,KAAK,CAAC;AAAA,MACnG,OAAO;AACL,aAAK,QAAQA,MAAK;AAAA,MACpB;AAAA,IACF;AACA,cAAUA,MAAK,KAAK,IAAI;AAAA,EAC1B,WAAW,WAAW,GAAG,GAAG;AAE1B,QAAI,SAAS,IAAI,YAAY,GAAG;AAC9B,MAAAA,QAAO,kCACF,IAAI,eACJA;AAAA,IAEP;AACA,WAAO,iBAAiB,GAAG,IAAI,mBAAmB,KAAKA,OAAM,QAAQ,IAAI,IAAI,iCACxEA,QADwE;AAAA,MAE3E;AAAA,IACF,EAAC;AAAA,EACH,OAAO;AACL,UAAM,IAAI,UAAU,6BAA6B,GAAG,EAAE;AAAA,EACxD;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAKA,OAAM;AA/SlC;AAgTE,WAAS,OAAO,UAAU,QAAQ,WAAW,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,MAAM,SAAS;AACjH,aAAS,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,EACvC;AACA,MAAI,SAASA,KAAI,KAAK,MAAM,QAAQA,KAAI,GAAG;AACzC,aAAS,QAAQA,KAAI;AACrB,IAAAA,QAAO,CAAC;AAAA,EACV;AACA,EAAAA,QAAOA,SAAQ,CAAC;AAChB,MAAIA,MAAK,YAAY,QAAQ,CAAC,SAAS,QAAQ;AAC7C,IAGI,KAAAA,OAFF;AAAA;AAAA,QAEE,IADCA,QAAA,UACD,IADC;AAAA,MADH;AAAA;AAAA,EAGJ;AACA,SAAO,IAAI,KAAK,iCACXA,QADW;AAAA,IAEd;AAAA,EACF,IAAGA,MAAK,GAAG;AACb;AACA,SAAS,UAAU,KAAK,MAAM;AAC5B,MAAI,MAAM,GAAG,GAAG;AACd,QAAI,UAAU;AAAA,EAChB,WAAW,WAAW,GAAG,GAAG;AAC1B,QAAI,IAAI;AAAA,EACV;AACF;AACA,SAAS,YAAY,OAAO,MAAM;AAChC,MAAI,YAAY,KAAK,GAAG;AACtB,mBAAe,OAAO,IAAI;AAAA,EAC5B,WAAW,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AAC7C,sBAAkB,SAAS,eAAe,KAAK,GAAG,IAAI;AAAA,EACxD,WAAW,UAAU,MAAM;AACzB,sBAAkB,SAAS,cAAc,EAAE,GAAG,IAAI;AAAA,EACpD,WAAW,UAAU,KAAK,GAAG;AAC3B,sBAAkB,OAAO,IAAI;AAAA,EAC/B,WAAW,aAAa,KAAK,GAAG;AAC9B,UAAM,aAAa,KAAK,aAAa,MAAM,IAAI;AAC/C,gBAAY,MAAM,UAAU,UAAU;AACtC,cAAU,MAAM,KAAK,UAAU;AAAA,EACjC;AACF;AACA,SAAS,eAAe,UAAU,MAAM;AACtC,aAAW,SAAS,CAAC,GAAG,QAAQ,GAAG;AACjC,gBAAY,OAAO,IAAI;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,OAAO,MAAM;AACtC,MAAI,gBAAgB,OAAO,qBAAqB;AAC9C,SAAK,QAAQ,YAAY,KAAK;AAAA,EAChC,OAAO;AACL,SAAK,YAAY,KAAK;AAAA,EACxB;AACF;AACA,SAAS,mBAAmB,GAAG,WAAW;AACxC,SAAO,EAAE,QAAQ,UAAU,WAAS,YAAY,MAAM,YAAY,CAAC;AACrE;AACA,SAAS,MAAM,MAAM,OAAO;AAC1B,MAAI,SAAS,QAAQ,UAAU,MAAO;AAAA,WAAU,MAAM,QAAQ,KAAK,GAAG;AACpE,UAAM,QAAQ,OAAK,MAAM,MAAM,CAAC,CAAC;AAAA,EACnC,WAAW,SAAS,KAAK,GAAG;AAC1B,SAAK,aAAa,SAAS,KAAK;AAAA,EAClC,WAAW,SAAS,KAAK,GAAG;AAC1B,YAAQ,OAAO,CAAC,KAAK,QAAQ;AAC3B,UAAI,IAAI,QAAQ,GAAG,MAAM,GAAG;AAG1B,aAAK,MAAM,YAAY,KAAK,GAAG;AAAA,MACjC,WAAW,SAAS,GAAG,KAAK,iBAAiB,GAAG,MAAM,GAAG;AACvD,aAAK,MAAM,GAAG,IAAI,MAAM;AAAA,MAC1B,OAAO;AACL,aAAK,MAAM,GAAG,IAAI;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,UAAU,KAAK,OAAO,MAAM;AACnC,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,MAAM,gBAAgB,mBAAmB,KAAK,GAAG,GAAG,KAAK;AAChE;AAAA,IACF,KAAK;AACH,WAAK,MAAM,mBAAmB,KAAK,GAAG,GAAG,KAAK;AAC9C;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,MAAM,cAAc,mBAAmB,KAAK,GAAG,GAAG,KAAK;AAC9D;AAAA,EACJ;AACA,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,WAAK,MAAM,OAAO,KAAK;AACvB;AAAA,IACF,KAAK;AACH,cAAQ,OAAO,CAAC,WAAW,YAAY;AACrC,YAAI,aAAa,MAAM;AACrB,eAAK,QAAQ,OAAO,IAAI;AAAA,QAC1B;AAAA,MACF,CAAC;AACD;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,UAAI,eAAe,KAAK,GAAG;AACzB,aAAK,GAAG,IAAI;AAAA,MACd;AACA;AAAA,IACF,KAAK;AACH,UAAI,SAAS,KAAK,GAAG;AACnB,aAAK,YAAY,MAAM,QAAQ;AAAA,MACjC;AACA;AAAA,IACF,KAAK;AACH,UAAI,SAAS,QAAQ,gBAAgB,OAAO,mBAAmB;AAG7D;AAAA,MACF,WAAW,gBAAgB,OAAO,qBAAqB;AACrD,aAAK,QAAQ;AACb;AAAA,MACF;AAEA;AAAA,IACF,KAAK;AACH,WAAK,aAAa;AAClB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,UAAI,WAAW,KAAK,GAAG;AACrB,cAAM,IAAI;AAAA,MACZ,OAAO;AACL,aAAK,MAAM,SAAS,UAAU,KAAK,CAAC;AAAA,MACtC;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH;AAAA,IACF,KAAK;AACH,YAAM,MAAM,KAAK;AACjB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,cAAQ,OAAO,CAAC,cAAc,cAAc;AAC1C,aAAK,iBAAiB,WAAW,cAAc,QAAQ,WAAW;AAAA,MACpE,CAAC;AACD;AAAA,EAEJ;AACA,MAAI,WAAW,KAAK,GAAG;AACrB,QAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;AACpC,UAAIC,aAAY,IAAI,YAAY;AAChC,YAAM,aAAaA,WAAU,SAAS,SAAS;AAC/C,UAAIA,eAAc,iBAAiB;AACjC,QAAAA,aAAY;AAAA,MACd,WAAW,cAAcA,eAAc,wBAAwB;AAC7D,QAAAA,aAAY;AAAA,MACd;AACA,UAAI,CAAC,cAAc,KAAKA,UAAS,MAAM,MAAM;AAE3C,aAAKA,UAAS,IAAI;AAAA,MACpB,WAAW,YAAY;AACrB,aAAK,iBAAiBA,WAAU,UAAU,GAAGA,WAAU,SAAS,CAAC,GAAG,OAAO,IAAI;AAAA,MACjF,OAAO;AACL,YAAI;AACJ,YAAIA,cAAa,QAAQ;AAKvB,gBAAM,oBAAoBA,WAAU,UAAU,CAAC;AAC/C,sBAAY;AAAA,QACd,OAAO;AAML,gBAAM,kBAAkBA,WAAU,CAAC,IAAI,IAAI,MAAM,CAAC;AAClD,sBAAY;AAAA,QACd;AACA,aAAK,iBAAiB,WAAW,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF,WAAW,SAAS,KAAK,GAAG;AAC1B,SAAK,GAAG,IAAI;AAAA,EACd,WAAW,UAAU,MAAM;AACzB,SAAK,MAAM,KAAK,EAAE;AAAA,EACpB,WAAW,UAAU,SAAS,SAAS,MAAM;AAC3C,QAAI,gBAAgB,cAAc,CAAC,6BAA6B,KAAK,GAAG,GAAG;AACzE,WAAK,MAAM,mBAAmB,KAAK,GAAG,GAAG,KAAK;AAAA,IAChD,OAAO;AACL,WAAK,MAAM,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AACF;AACA,SAAS,KAAK,MAAM,KAAK,OAAO;AAC9B,OAAK,aAAa,KAAK,KAAK;AAC9B;AACA,SAAS,OAAO,MAAM,WAAW,KAAK,OAAO;AAC3C,OAAK,eAAe,WAAW,KAAK,KAAK;AAC3C;AACA,SAAS,WAAWD,OAAM,MAAM;AAC9B,aAAW,OAAO,KAAKA,KAAI,GAAG;AAC5B,cAAU,KAAKA,MAAK,GAAG,GAAG,IAAI;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,QAAQ,cAAc;AAC7B,QAAM,OAAO,IAAI,KAAK;AACtB,SAAO,eAAe,MAAM,YAAY;AAAA,IACtC,QAAQ;AACN,aAAO,KAAK;AAAA,IACd;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,OAAO;AACtB,SAAK,cAAc;AAAA,EACrB;AACA,MAAI,gBAAgB,MAAM;AACxB,YAAQ,YAAY;AAAA,EACtB;AACA,SAAO,CAAC,MAAM,OAAO;AACvB;AACA,SAAS,aAAa,cAAc;AAClC,QAAM,MAAM,SAAS,cAAc,KAAK;AACxC,MAAI,gBAAgB,MAAM;AACxB,QAAI,YAAY,UAAU,YAAY;AAAA,EACxC;AACA,MAAI,OAAO,IAAI;AACf,WAAS,UAAU,OAAO;AACxB,UAAM,aAAa,SAAS,KAAK,KAAK;AACtC,WAAO,MAAM;AAAA,EACf;AACA,SAAO,iBAAiB,WAAW,OAAO,0BAA0B;AAAA,IAClE,IAAI,OAAO;AACT,aAAO,KAAK;AAAA,IACd;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,IACA,MAAM;AACJ,WAAK,IAAI,GAAG,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AACP,WAAK,OAAO,GAAG,SAAS;AAAA,IAC1B;AAAA,IACA,OAAO,OAAO,OAAO;AACnB,WAAK,OAAO,OAAO,KAAK;AAAA,IAC1B;AAAA,IACA,SAAS,OAAO;AACd,aAAO,KAAK,SAAS,KAAK;AAAA,IAC5B;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT;AACA,SAAS,QAAQ,SAAS;AACxB,SAAO,QAAQ;AACjB;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,UAAQ;AACb,QAGI,WAFF;AAAA;AAAA,IA3jBN,IA6jBQ,IADC,kBACD,IADC;AAAA,MADH;AAAA;AAGF,WAAO,OAAO,OAAO,OAAO,UAAU,CAAC;AAAA,EACzC;AACF;AACA,SAAS,oBAAoB,KAAK,MAAM;AACtC,YAAU,KAAK,KAAK,CAAC;AACvB;AACA,IAAM,QAAuB,oBAAI,IAAI;AACrC,IAAM,wBAAwB,UAAQ,SAAU,MAAM;AACpD,WAAS,OAAO,UAAU,QAAQ,iBAAiB,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACpH,mBAAe,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EAC3C;AACA,SAAO,UAAQ;AACb,QAGI,WAFF;AAAA,aAAAE;AAAA,IA3kBN,IA6kBQ,IADC,kBACD,IADC;AAAA,MADH;AAAA;AAGF,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,MAAM,KAAK,MAAM,GAAG,SAAS,EAAE,OAAO,CAAC,GAAG,GAAG,MAAM;AACvD,YAAM,gBAAgB,eAAe,CAAC;AACtC,YAAM,UAAU,OAAO,kBAAkB,aAAa,cAAc,KAAK,IAAI;AAC7E,aAAO,IAAI,IAAI;AAAA,IACjB,GAAG,EAAE,IAAI,KAAK,SAAS;AACvB,WAAO,cAAc,MAAM;AAAA,MACzB,OAAO,CAAC,KAAKA,MAAK;AAAA,OACf,MACJ;AAAA,EACH;AACF;AACA,IAAM,aAAa,qBAAmB,sBAAsB,eAAe;AAC3E,IAAM,SAAwB,IAAI,MAAM,YAAY;AAAA,EAClD,IAAI,GAAG,MAAM;AACX,WAAO,YAAY,OAAO,MAAM,MAAM,sBAAsB,IAAI,CAAC;AAAA,EACnE;AACF,CAAC;AACD,SAAS,YAAY,KAAK,KAAK,UAAU;AACvC,MAAI,IAAI,IAAI,GAAG,GAAG;AAChB,WAAO,IAAI,IAAI,GAAG;AAAA,EACpB,OAAO;AACL,UAAM,QAAQ,SAAS,GAAG;AAC1B,QAAI,IAAI,KAAK,KAAK;AAClB,WAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,eAAe;AACrB,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,gBAAgB;AACtB,SAAO;AACT;", "names": ["Component", "JsxDomType", "attr", "attribute", "style"]}