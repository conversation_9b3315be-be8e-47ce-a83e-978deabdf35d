{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/can-promise.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/utils.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/error-correction-level.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/bit-buffer.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/bit-matrix.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/alignment-pattern.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/finder-pattern.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/mask-pattern.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/error-correction-code.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/galois-field.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/polynomial.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/reed-solomon-encoder.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/version-check.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/regex.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/mode.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/version.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/format-info.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/numeric-data.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/alphanumeric-data.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/byte-data.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/kanji-data.js", "../../../../../../node_modules/.pnpm/dijkstrajs@1.0.3/node_modules/dijkstrajs/dijkstra.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/segments.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/core/qrcode.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/renderer/utils.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/renderer/canvas.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/renderer/svg-tag.js", "../../../../../../node_modules/.pnpm/qrcode@1.5.4/node_modules/qrcode/lib/browser.js"], "sourcesContent": ["// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then;\n};", "let toSJISFunction;\nconst CODEWORDS_COUNT = [0,\n// Not used\n26, 44, 70, 100, 134, 172, 196, 242, 292, 346, 404, 466, 532, 581, 655, 733, 815, 901, 991, 1085, 1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185, 2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706];\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize(version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined');\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40');\n  return version * 4 + 17;\n};\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords(version) {\n  return CODEWORDS_COUNT[version];\n};\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0;\n  while (data !== 0) {\n    digit++;\n    data >>>= 1;\n  }\n  return digit;\n};\nexports.setToSJISFunction = function setToSJISFunction(f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.');\n  }\n  toSJISFunction = f;\n};\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined';\n};\nexports.toSJIS = function toSJIS(kanji) {\n  return toSJISFunction(kanji);\n};", "exports.L = {\n  bit: 1\n};\nexports.M = {\n  bit: 0\n};\nexports.Q = {\n  bit: 3\n};\nexports.H = {\n  bit: 2\n};\nfunction fromString(string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string');\n  }\n  const lcStr = string.toLowerCase();\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L;\n    case 'm':\n    case 'medium':\n      return exports.M;\n    case 'q':\n    case 'quartile':\n      return exports.Q;\n    case 'h':\n    case 'high':\n      return exports.H;\n    default:\n      throw new Error('Unknown EC Level: ' + string);\n  }\n}\nexports.isValid = function isValid(level) {\n  return level && typeof level.bit !== 'undefined' && level.bit >= 0 && level.bit < 4;\n};\nexports.from = function from(value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value;\n  }\n  try {\n    return fromString(value);\n  } catch (e) {\n    return defaultValue;\n  }\n};", "function BitBuffer() {\n  this.buffer = [];\n  this.length = 0;\n}\nBitBuffer.prototype = {\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8);\n    return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;\n  },\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit((num >>> length - i - 1 & 1) === 1);\n    }\n  },\n  getLengthInBits: function () {\n    return this.length;\n  },\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8);\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0);\n    }\n    if (bit) {\n      this.buffer[bufIndex] |= 0x80 >>> this.length % 8;\n    }\n    this.length++;\n  }\n};\nmodule.exports = BitBuffer;", "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix(size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0');\n  }\n  this.size = size;\n  this.data = new Uint8Array(size * size);\n  this.reservedBit = new Uint8Array(size * size);\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col;\n  this.data[index] = value;\n  if (reserved) this.reservedBit[index] = true;\n};\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col];\n};\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value;\n};\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col];\n};\nmodule.exports = BitMatrix;", "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize;\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords(version) {\n  if (version === 1) return [];\n  const posCount = Math.floor(version / 7) + 2;\n  const size = getSymbolSize(version);\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;\n  const positions = [size - 7]; // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals;\n  }\n  positions.push(6); // First coord is always 6\n\n  return positions.reverse();\n};\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions(version) {\n  const coords = [];\n  const pos = exports.getRowColCoords(version);\n  const posLength = pos.length;\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if (i === 0 && j === 0 ||\n      // top-left\n      i === 0 && j === posLength - 1 ||\n      // bottom-left\n      i === posLength - 1 && j === 0) {\n        // top-right\n        continue;\n      }\n      coords.push([pos[i], pos[j]]);\n    }\n  }\n  return coords;\n};", "const getSymbolSize = require('./utils').getSymbolSize;\nconst FINDER_PATTERN_SIZE = 7;\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions(version) {\n  const size = getSymbolSize(version);\n  return [\n  // top-left\n  [0, 0],\n  // top-right\n  [size - FINDER_PATTERN_SIZE, 0],\n  // bottom-left\n  [0, size - FINDER_PATTERN_SIZE]];\n};", "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n};\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n};\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid(mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7;\n};\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from(value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined;\n};\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1(data) {\n  const size = data.size;\n  let points = 0;\n  let sameCountCol = 0;\n  let sameCountRow = 0;\n  let lastCol = null;\n  let lastRow = null;\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0;\n    lastCol = lastRow = null;\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col);\n      if (module === lastCol) {\n        sameCountCol++;\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n        lastCol = module;\n        sameCountCol = 1;\n      }\n      module = data.get(col, row);\n      if (module === lastRow) {\n        sameCountRow++;\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n        lastRow = module;\n        sameCountRow = 1;\n      }\n    }\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n  }\n  return points;\n};\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2(data) {\n  const size = data.size;\n  let points = 0;\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);\n      if (last === 4 || last === 0) points++;\n    }\n  }\n  return points * PenaltyScores.N2;\n};\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3(data) {\n  const size = data.size;\n  let points = 0;\n  let bitsCol = 0;\n  let bitsRow = 0;\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0;\n    for (let col = 0; col < size; col++) {\n      bitsCol = bitsCol << 1 & 0x7FF | data.get(row, col);\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++;\n      bitsRow = bitsRow << 1 & 0x7FF | data.get(col, row);\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++;\n    }\n  }\n  return points * PenaltyScores.N3;\n};\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4(data) {\n  let darkCount = 0;\n  const modulesCount = data.data.length;\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i];\n  const k = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);\n  return k * PenaltyScores.N4;\n};\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt(maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000:\n      return (i + j) % 2 === 0;\n    case exports.Patterns.PATTERN001:\n      return i % 2 === 0;\n    case exports.Patterns.PATTERN010:\n      return j % 3 === 0;\n    case exports.Patterns.PATTERN011:\n      return (i + j) % 3 === 0;\n    case exports.Patterns.PATTERN100:\n      return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0;\n    case exports.Patterns.PATTERN101:\n      return i * j % 2 + i * j % 3 === 0;\n    case exports.Patterns.PATTERN110:\n      return (i * j % 2 + i * j % 3) % 2 === 0;\n    case exports.Patterns.PATTERN111:\n      return (i * j % 3 + (i + j) % 2) % 2 === 0;\n    default:\n      throw new Error('bad maskPattern:' + maskPattern);\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask(pattern, data) {\n  const size = data.size;\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue;\n      data.xor(row, col, getMaskAt(pattern, row, col));\n    }\n  }\n};\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask(data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length;\n  let bestPattern = 0;\n  let lowerPenalty = Infinity;\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p);\n    exports.applyMask(p, data);\n\n    // Calculate penalty\n    const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);\n\n    // Undo previously applied mask\n    exports.applyMask(p, data);\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty;\n      bestPattern = p;\n    }\n  }\n  return bestPattern;\n};", "const ECLevel = require('./error-correction-level');\nconst EC_BLOCKS_TABLE = [\n// L  M  Q  H\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 2, 2, 4, 1, 2, 4, 4, 2, 4, 4, 4, 2, 4, 6, 5, 2, 4, 6, 6, 2, 5, 8, 8, 4, 5, 8, 8, 4, 5, 8, 11, 4, 8, 10, 11, 4, 9, 12, 16, 4, 9, 16, 16, 6, 10, 12, 18, 6, 10, 17, 16, 6, 11, 16, 19, 6, 13, 18, 21, 7, 14, 21, 25, 8, 16, 20, 25, 8, 17, 23, 25, 9, 17, 23, 34, 9, 18, 25, 30, 10, 20, 27, 32, 12, 21, 29, 35, 12, 23, 34, 37, 12, 25, 34, 40, 13, 26, 35, 42, 14, 28, 38, 45, 15, 29, 40, 48, 16, 31, 43, 51, 17, 33, 45, 54, 18, 35, 48, 57, 19, 37, 51, 60, 19, 38, 53, 63, 20, 40, 56, 66, 21, 43, 59, 70, 22, 45, 62, 74, 24, 47, 65, 77, 25, 49, 68, 81];\nconst EC_CODEWORDS_TABLE = [\n// L  M  Q  H\n7, 10, 13, 17, 10, 16, 22, 28, 15, 26, 36, 44, 20, 36, 52, 64, 26, 48, 72, 88, 36, 64, 96, 112, 40, 72, 108, 130, 48, 88, 132, 156, 60, 110, 160, 192, 72, 130, 192, 224, 80, 150, 224, 264, 96, 176, 260, 308, 104, 198, 288, 352, 120, 216, 320, 384, 132, 240, 360, 432, 144, 280, 408, 480, 168, 308, 448, 532, 180, 338, 504, 588, 196, 364, 546, 650, 224, 416, 600, 700, 224, 442, 644, 750, 252, 476, 690, 816, 270, 504, 750, 900, 300, 560, 810, 960, 312, 588, 870, 1050, 336, 644, 952, 1110, 360, 700, 1020, 1200, 390, 728, 1050, 1260, 420, 784, 1140, 1350, 450, 812, 1200, 1440, 480, 868, 1290, 1530, 510, 924, 1350, 1620, 540, 980, 1440, 1710, 570, 1036, 1530, 1800, 570, 1064, 1590, 1890, 600, 1120, 1680, 1980, 630, 1204, 1770, 2100, 660, 1260, 1860, 2220, 720, 1316, 1950, 2310, 750, 1372, 2040, 2430];\n\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\nexports.getBlocksCount = function getBlocksCount(version, errorCorrectionLevel) {\n  switch (errorCorrectionLevel) {\n    case ECLevel.L:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];\n    case ECLevel.M:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];\n    case ECLevel.Q:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];\n    case ECLevel.H:\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];\n    default:\n      return undefined;\n  }\n};\n\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\nexports.getTotalCodewordsCount = function getTotalCodewordsCount(version, errorCorrectionLevel) {\n  switch (errorCorrectionLevel) {\n    case ECLevel.L:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];\n    case ECLevel.M:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];\n    case ECLevel.Q:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];\n    case ECLevel.H:\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];\n    default:\n      return undefined;\n  }\n};", "const EXP_TABLE = new Uint8Array(512);\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */;\n(function initTables() {\n  let x = 1;\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x;\n    LOG_TABLE[x] = i;\n    x <<= 1; // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) {\n      // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D;\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255];\n  }\n})();\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log(n) {\n  if (n < 1) throw new Error('log(' + n + ')');\n  return LOG_TABLE[n];\n};\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp(n) {\n  return EXP_TABLE[n];\n};\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul(x, y) {\n  if (x === 0 || y === 0) return 0;\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]];\n};", "const GF = require('./galois-field');\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul(p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1);\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j]);\n    }\n  }\n  return coeff;\n};\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod(divident, divisor) {\n  let result = new Uint8Array(divident);\n  while (result.length - divisor.length >= 0) {\n    const coeff = result[0];\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff);\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0;\n    while (offset < result.length && result[offset] === 0) offset++;\n    result = result.slice(offset);\n  }\n  return result;\n};\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial(degree) {\n  let poly = new Uint8Array([1]);\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]));\n  }\n  return poly;\n};", "const Polynomial = require('./polynomial');\nfunction ReedSolomonEncoder(degree) {\n  this.genPoly = undefined;\n  this.degree = degree;\n  if (this.degree) this.initialize(this.degree);\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize(degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree;\n  this.genPoly = Polynomial.generateECPolynomial(this.degree);\n};\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode(data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized');\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree);\n  paddedData.set(data);\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly);\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length;\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree);\n    buff.set(remainder, start);\n    return buff;\n  }\n  return remainder;\n};\nmodule.exports = ReedSolomonEncoder;", "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid(version) {\n  return !isNaN(version) && version >= 1 && version <= 40;\n};", "const numeric = '[0-9]+';\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+';\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' + '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' + '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' + '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+';\nkanji = kanji.replace(/u/g, '\\\\u');\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+';\nexports.KANJI = new RegExp(kanji, 'g');\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g');\nexports.BYTE = new RegExp(byte, 'g');\nexports.NUMERIC = new RegExp(numeric, 'g');\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g');\nconst TEST_KANJI = new RegExp('^' + kanji + '$');\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$');\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$');\nexports.testKanji = function testKanji(str) {\n  return TEST_KANJI.test(str);\n};\nexports.testNumeric = function testNumeric(str) {\n  return TEST_NUMERIC.test(str);\n};\nexports.testAlphanumeric = function testAlphanumeric(str) {\n  return TEST_ALPHANUMERIC.test(str);\n};", "const VersionCheck = require('./version-check');\nconst Regex = require('./regex');\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n};\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n};\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n};\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n};\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n};\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator(mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode);\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version);\n  }\n  if (version >= 1 && version < 10) return mode.ccBits[0];else if (version < 27) return mode.ccBits[1];\n  return mode.ccBits[2];\n};\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData(dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC;else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC;else if (Regex.testKanji(dataStr)) return exports.KANJI;else return exports.BYTE;\n};\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString(mode) {\n  if (mode && mode.id) return mode.id;\n  throw new Error('Invalid mode');\n};\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid(mode) {\n  return mode && mode.bit && mode.ccBits;\n};\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString(string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string');\n  }\n  const lcStr = string.toLowerCase();\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC;\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC;\n    case 'kanji':\n      return exports.KANJI;\n    case 'byte':\n      return exports.BYTE;\n    default:\n      throw new Error('Unknown mode: ' + string);\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from(value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value;\n  }\n  try {\n    return fromString(value);\n  } catch (e) {\n    return defaultValue;\n  }\n};", "const Utils = require('./utils');\nconst ECCode = require('./error-correction-code');\nconst ECLevel = require('./error-correction-level');\nconst Mode = require('./mode');\nconst VersionCheck = require('./version-check');\n\n// Generator polynomial used to encode version information\nconst G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;\nconst G18_BCH = Utils.getBCHDigit(G18);\nfunction getBestVersionForDataLength(mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion;\n    }\n  }\n  return undefined;\n}\nfunction getReservedBitsCount(mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4;\n}\nfunction getTotalBitsFromDataArray(segments, version) {\n  let totalBits = 0;\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version);\n    totalBits += reservedBits + data.getBitsLength();\n  });\n  return totalBits;\n}\nfunction getBestVersionForMixedData(segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion);\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion;\n    }\n  }\n  return undefined;\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from(value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10);\n  }\n  return defaultValue;\n};\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity(version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version');\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE;\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits;\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor(usableBits / 10 * 3);\n    case Mode.ALPHANUMERIC:\n      return Math.floor(usableBits / 11 * 2);\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13);\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8);\n  }\n};\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData(data, errorCorrectionLevel) {\n  let seg;\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl);\n    }\n    if (data.length === 0) {\n      return 1;\n    }\n    seg = data[0];\n  } else {\n    seg = data;\n  }\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);\n};\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits(version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version');\n  }\n  let d = version << 12;\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;\n  }\n  return version << 12 | d;\n};", "const Utils = require('./utils');\nconst G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;\nconst G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;\nconst G15_BCH = Utils.getBCHDigit(G15);\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits(errorCorrectionLevel, mask) {\n  const data = errorCorrectionLevel.bit << 3 | mask;\n  let d = data << 10;\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return (data << 10 | d) ^ G15_MASK;\n};", "const Mode = require('./mode');\nfunction NumericData(data) {\n  this.mode = Mode.NUMERIC;\n  this.data = data.toString();\n}\nNumericData.getBitsLength = function getBitsLength(length) {\n  return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);\n};\nNumericData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nNumericData.prototype.getBitsLength = function getBitsLength() {\n  return NumericData.getBitsLength(this.data.length);\n};\nNumericData.prototype.write = function write(bitBuffer) {\n  let i, group, value;\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3);\n    value = parseInt(group, 10);\n    bitBuffer.put(value, 10);\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i;\n  if (remainingNum > 0) {\n    group = this.data.substr(i);\n    value = parseInt(group, 10);\n    bitBuffer.put(value, remainingNum * 3 + 1);\n  }\n};\nmodule.exports = NumericData;", "const Mode = require('./mode');\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', ' ', '$', '%', '*', '+', '-', '.', '/', ':'];\nfunction AlphanumericData(data) {\n  this.mode = Mode.ALPHANUMERIC;\n  this.data = data;\n}\nAlphanumericData.getBitsLength = function getBitsLength(length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2);\n};\nAlphanumericData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nAlphanumericData.prototype.getBitsLength = function getBitsLength() {\n  return AlphanumericData.getBitsLength(this.data.length);\n};\nAlphanumericData.prototype.write = function write(bitBuffer) {\n  let i;\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45;\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1]);\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11);\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6);\n  }\n};\nmodule.exports = AlphanumericData;", "const Mode = require('./mode');\nfunction ByteData(data) {\n  this.mode = Mode.BYTE;\n  if (typeof data === 'string') {\n    this.data = new TextEncoder().encode(data);\n  } else {\n    this.data = new Uint8Array(data);\n  }\n}\nByteData.getBitsLength = function getBitsLength(length) {\n  return length * 8;\n};\nByteData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nByteData.prototype.getBitsLength = function getBitsLength() {\n  return ByteData.getBitsLength(this.data.length);\n};\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8);\n  }\n};\nmodule.exports = ByteData;", "const Mode = require('./mode');\nconst Utils = require('./utils');\nfunction KanjiData(data) {\n  this.mode = Mode.KANJI;\n  this.data = data;\n}\nKanjiData.getBitsLength = function getBitsLength(length) {\n  return length * 13;\n};\nKanjiData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nKanjiData.prototype.getBitsLength = function getBitsLength() {\n  return KanjiData.getBitsLength(this.data.length);\n};\nKanjiData.prototype.write = function (bitBuffer) {\n  let i;\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i]);\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140;\n\n      // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140;\n    } else {\n      throw new Error('Invalid SJIS character: ' + this.data[i] + '\\n' + 'Make sure your charset is UTF-8');\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (value >>> 8 & 0xff) * 0xC0 + (value & 0xff);\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13);\n  }\n};\nmodule.exports = KanjiData;", "'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function (graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n    var closest, u, v, cost_of_s_to_u, adjacent_nodes, cost_of_e, cost_of_s_to_u_plus_cost_of_e, cost_of_s_to_v, first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = typeof costs[v] === 'undefined';\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n    return predecessors;\n  },\n  extract_shortest_path_from_predecessor_list: function (predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n  find_path: function (graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(predecessors, d);\n  },\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n        t = {},\n        key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {\n        value: value,\n        cost: cost\n      };\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}", "const Mode = require('./mode');\nconst NumericData = require('./numeric-data');\nconst AlphanumericData = require('./alphanumeric-data');\nconst ByteData = require('./byte-data');\nconst KanjiData = require('./kanji-data');\nconst Regex = require('./regex');\nconst Utils = require('./utils');\nconst dijkstra = require('dijkstrajs');\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength(str) {\n  return unescape(encodeURIComponent(str)).length;\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments(regex, mode, str) {\n  const segments = [];\n  let result;\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    });\n  }\n  return segments;\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString(dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);\n  let byteSegs;\n  let kanjiSegs;\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);\n    kanjiSegs = [];\n  }\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);\n  return segs.sort(function (s1, s2) {\n    return s1.index - s2.index;\n  }).map(function (obj) {\n    return {\n      data: obj.data,\n      mode: obj.mode,\n      length: obj.length\n    };\n  });\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength(length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length);\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length);\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length);\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length);\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments(segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data;\n      return acc;\n    }\n    acc.push(curr);\n    return acc;\n  }, []);\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes(segs) {\n  const nodes = [];\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i];\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.ALPHANUMERIC,\n          length: seg.length\n        }, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: seg.length\n        }]);\n        break;\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: seg.length\n        }]);\n        break;\n      case Mode.KANJI:\n        nodes.push([seg, {\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: getStringByteLength(seg.data)\n        }]);\n        break;\n      case Mode.BYTE:\n        nodes.push([{\n          data: seg.data,\n          mode: Mode.BYTE,\n          length: getStringByteLength(seg.data)\n        }]);\n    }\n  }\n  return nodes;\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph(nodes, version) {\n  const table = {};\n  const graph = {\n    start: {}\n  };\n  let prevNodeIds = ['start'];\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i];\n    const currentNodeIds = [];\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j];\n      const key = '' + i + j;\n      currentNodeIds.push(key);\n      table[key] = {\n        node: node,\n        lastCount: 0\n      };\n      graph[key] = {};\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n];\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);\n          table[prevNodeId].lastCount += node.length;\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length;\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version); // switch cost\n        }\n      }\n    }\n    prevNodeIds = currentNodeIds;\n  }\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0;\n  }\n  return {\n    map: graph,\n    table: table\n  };\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment(data, modesHint) {\n  let mode;\n  const bestMode = Mode.getBestModeForData(data);\n  mode = Mode.from(modesHint, bestMode);\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' + ' cannot be encoded with mode ' + Mode.toString(mode) + '.\\n Suggested mode is: ' + Mode.toString(bestMode));\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE;\n  }\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data);\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data);\n    case Mode.KANJI:\n      return new KanjiData(data);\n    case Mode.BYTE:\n      return new ByteData(data);\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray(array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null));\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode));\n    }\n    return acc;\n  }, []);\n};\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString(data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());\n  const nodes = buildNodes(segs);\n  const graph = buildGraph(nodes, version);\n  const path = dijkstra.find_path(graph.map, 'start', 'end');\n  const optimizedSegs = [];\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node);\n  }\n  return exports.fromArray(mergeSegments(optimizedSegs));\n};\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit(data) {\n  return exports.fromArray(getSegmentsFromString(data, Utils.isKanjiModeEnabled()));\n};", "const Utils = require('./utils');\nconst ECLevel = require('./error-correction-level');\nconst BitBuffer = require('./bit-buffer');\nconst BitMatrix = require('./bit-matrix');\nconst AlignmentPattern = require('./alignment-pattern');\nconst FinderPattern = require('./finder-pattern');\nconst MaskPattern = require('./mask-pattern');\nconst ECCode = require('./error-correction-code');\nconst ReedSolomonEncoder = require('./reed-solomon-encoder');\nconst Version = require('./version');\nconst FormatInfo = require('./format-info');\nconst Mode = require('./mode');\nconst Segments = require('./segments');\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 Kazuhiko Arase\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern(matrix, version) {\n  const size = matrix.size;\n  const pos = FinderPattern.getPositions(version);\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0];\n    const col = pos[i][1];\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue;\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue;\n        if (r >= 0 && r <= 6 && (c === 0 || c === 6) || c >= 0 && c <= 6 && (r === 0 || r === 6) || r >= 2 && r <= 4 && c >= 2 && c <= 4) {\n          matrix.set(row + r, col + c, true, true);\n        } else {\n          matrix.set(row + r, col + c, false, true);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern(matrix) {\n  const size = matrix.size;\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0;\n    matrix.set(r, 6, value, true);\n    matrix.set(6, r, value, true);\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern(matrix, version) {\n  const pos = AlignmentPattern.getPositions(version);\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0];\n    const col = pos[i][1];\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 || r === 0 && c === 0) {\n          matrix.set(row + r, col + c, true, true);\n        } else {\n          matrix.set(row + r, col + c, false, true);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo(matrix, version) {\n  const size = matrix.size;\n  const bits = Version.getEncodedBits(version);\n  let row, col, mod;\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3);\n    col = i % 3 + size - 8 - 3;\n    mod = (bits >> i & 1) === 1;\n    matrix.set(row, col, mod, true);\n    matrix.set(col, row, mod, true);\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size;\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);\n  let i, mod;\n  for (i = 0; i < 15; i++) {\n    mod = (bits >> i & 1) === 1;\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true);\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true);\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true);\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true);\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true);\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true);\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true);\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData(matrix, data) {\n  const size = matrix.size;\n  let inc = -1;\n  let row = size - 1;\n  let bitIndex = 7;\n  let byteIndex = 0;\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--;\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false;\n          if (byteIndex < data.length) {\n            dark = (data[byteIndex] >>> bitIndex & 1) === 1;\n          }\n          matrix.set(row, col - c, dark);\n          bitIndex--;\n          if (bitIndex === -1) {\n            byteIndex++;\n            bitIndex = 7;\n          }\n        }\n      }\n      row += inc;\n      if (row < 0 || size <= row) {\n        row -= inc;\n        inc = -inc;\n        break;\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData(version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer();\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4);\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));\n\n    // add binary data sequence to buffer\n    data.write(buffer);\n  });\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4);\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0);\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8);\n  }\n  return createCodewords(buffer, version, errorCorrectionLevel);\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords(bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version);\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords;\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks;\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount);\n  let offset = 0;\n  const dcData = new Array(ecTotalBlocks);\n  const ecData = new Array(ecTotalBlocks);\n  let maxDataSize = 0;\n  const buffer = new Uint8Array(bitBuffer.buffer);\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize);\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b]);\n    offset += dataSize;\n    maxDataSize = Math.max(maxDataSize, dataSize);\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords);\n  let index = 0;\n  let i, r;\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i];\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i];\n    }\n  }\n  return data;\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol(data, version, errorCorrectionLevel, maskPattern) {\n  let segments;\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data);\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version;\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data);\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40);\n  } else {\n    throw new Error('Invalid data');\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code');\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion;\n\n    // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' + 'The chosen QR Code version cannot contain this amount of data.\\n' + 'Minimum version required to store current data is: ' + bestVersion + '.\\n');\n  }\n  const dataBits = createData(version, errorCorrectionLevel, segments);\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version);\n  const modules = new BitMatrix(moduleCount);\n\n  // Add function modules\n  setupFinderPattern(modules, version);\n  setupTimingPattern(modules);\n  setupAlignmentPattern(modules, version);\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0);\n  if (version >= 7) {\n    setupVersionInfo(modules, version);\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits);\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules, setupFormatInfo.bind(null, modules, errorCorrectionLevel));\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules);\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern);\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  };\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create(data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text');\n  }\n  let errorCorrectionLevel = ECLevel.M;\n  let version;\n  let mask;\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);\n    version = Version.from(options.version);\n    mask = MaskPattern.from(options.maskPattern);\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc);\n    }\n  }\n  return createSymbol(data, version, errorCorrectionLevel, mask);\n};", "function hex2rgba(hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString();\n  }\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string');\n  }\n  let hexCode = hex.slice().replace('#', '').split('');\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex);\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c];\n    }));\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F');\n  const hexValue = parseInt(hexCode.join(''), 16);\n  return {\n    r: hexValue >> 24 & 255,\n    g: hexValue >> 16 & 255,\n    b: hexValue >> 8 & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  };\n}\nexports.getOptions = function getOptions(options) {\n  if (!options) options = {};\n  if (!options.color) options.color = {};\n  const margin = typeof options.margin === 'undefined' || options.margin === null || options.margin < 0 ? 4 : options.margin;\n  const width = options.width && options.width >= 21 ? options.width : undefined;\n  const scale = options.scale || 4;\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  };\n};\nexports.getScale = function getScale(qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;\n};\nexports.getImageWidth = function getImageWidth(qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts);\n  return Math.floor((qrSize + opts.margin * 2) * scale);\n};\nexports.qrToImageData = function qrToImageData(imgData, qr, opts) {\n  const size = qr.modules.size;\n  const data = qr.modules.data;\n  const scale = exports.getScale(size, opts);\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale);\n  const scaledMargin = opts.margin * scale;\n  const palette = [opts.color.light, opts.color.dark];\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4;\n      let pxColor = opts.color.light;\n      if (i >= scaledMargin && j >= scaledMargin && i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale);\n        const jSrc = Math.floor((j - scaledMargin) / scale);\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];\n      }\n      imgData[posDst++] = pxColor.r;\n      imgData[posDst++] = pxColor.g;\n      imgData[posDst++] = pxColor.b;\n      imgData[posDst] = pxColor.a;\n    }\n  }\n};", "const Utils = require('./utils');\nfunction clearCanvas(ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  if (!canvas.style) canvas.style = {};\n  canvas.height = size;\n  canvas.width = size;\n  canvas.style.height = size + 'px';\n  canvas.style.width = size + 'px';\n}\nfunction getCanvasElement() {\n  try {\n    return document.createElement('canvas');\n  } catch (e) {\n    throw new Error('You need to specify a canvas element');\n  }\n}\nexports.render = function render(qrData, canvas, options) {\n  let opts = options;\n  let canvasEl = canvas;\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas;\n    canvas = undefined;\n  }\n  if (!canvas) {\n    canvasEl = getCanvasElement();\n  }\n  opts = Utils.getOptions(opts);\n  const size = Utils.getImageWidth(qrData.modules.size, opts);\n  const ctx = canvasEl.getContext('2d');\n  const image = ctx.createImageData(size, size);\n  Utils.qrToImageData(image.data, qrData, opts);\n  clearCanvas(ctx, canvasEl, size);\n  ctx.putImageData(image, 0, 0);\n  return canvasEl;\n};\nexports.renderToDataURL = function renderToDataURL(qrData, canvas, options) {\n  let opts = options;\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas;\n    canvas = undefined;\n  }\n  if (!opts) opts = {};\n  const canvasEl = exports.render(qrData, canvas, opts);\n  const type = opts.type || 'image/png';\n  const rendererOpts = opts.rendererOpts || {};\n  return canvasEl.toDataURL(type, rendererOpts.quality);\n};", "const Utils = require('./utils');\nfunction getColorAttrib(color, attrib) {\n  const alpha = color.a / 255;\n  const str = attrib + '=\"' + color.hex + '\"';\n  return alpha < 1 ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"' : str;\n}\nfunction svgCmd(cmd, x, y) {\n  let str = cmd + x;\n  if (typeof y !== 'undefined') str += ' ' + y;\n  return str;\n}\nfunction qrToPath(data, size, margin) {\n  let path = '';\n  let moveBy = 0;\n  let newRow = false;\n  let lineLength = 0;\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size);\n    const row = Math.floor(i / size);\n    if (!col && !newRow) newRow = true;\n    if (data[i]) {\n      lineLength++;\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow ? svgCmd('M', col + margin, 0.5 + row + margin) : svgCmd('m', moveBy, 0);\n        moveBy = 0;\n        newRow = false;\n      }\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength);\n        lineLength = 0;\n      }\n    } else {\n      moveBy++;\n    }\n  }\n  return path;\n}\nexports.render = function render(qrData, options, cb) {\n  const opts = Utils.getOptions(options);\n  const size = qrData.modules.size;\n  const data = qrData.modules.data;\n  const qrcodesize = size + opts.margin * 2;\n  const bg = !opts.color.light.a ? '' : '<path ' + getColorAttrib(opts.color.light, 'fill') + ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>';\n  const path = '<path ' + getColorAttrib(opts.color.dark, 'stroke') + ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>';\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"';\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" ';\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n';\n  if (typeof cb === 'function') {\n    cb(null, svgTag);\n  }\n  return svgTag;\n};", "const canPromise = require('./can-promise');\nconst QRCode = require('./core/qrcode');\nconst CanvasRenderer = require('./renderer/canvas');\nconst SvgRenderer = require('./renderer/svg-tag.js');\nfunction renderCanvas(renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1);\n  const argsNum = args.length;\n  const isLastArgCb = typeof args[argsNum - 1] === 'function';\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument');\n  }\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided');\n    }\n    if (argsNum === 2) {\n      cb = text;\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts;\n        opts = undefined;\n      } else {\n        cb = opts;\n        opts = text;\n        text = canvas;\n        canvas = undefined;\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided');\n    }\n    if (argsNum === 1) {\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text;\n      text = canvas;\n      canvas = undefined;\n    }\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts);\n        resolve(renderFunc(data, canvas, opts));\n      } catch (e) {\n        reject(e);\n      }\n    });\n  }\n  try {\n    const data = QRCode.create(text, opts);\n    cb(null, renderFunc(data, canvas, opts));\n  } catch (e) {\n    cb(e);\n  }\n}\nexports.create = QRCode.create;\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts);\n});"], "mappings": ";;;;;AAAA;AAAA;AAIA,WAAO,UAAU,WAAY;AAC3B,aAAO,OAAO,YAAY,cAAc,QAAQ,aAAa,QAAQ,UAAU;AAAA,IACjF;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI;AACJ,QAAM,kBAAkB;AAAA,MAAC;AAAA;AAAA,MAEzB;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IAAI;AAQxN,YAAQ,gBAAgB,SAAS,cAAc,SAAS;AACtD,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,uCAAuC;AACrE,UAAI,UAAU,KAAK,UAAU,GAAI,OAAM,IAAI,MAAM,2CAA2C;AAC5F,aAAO,UAAU,IAAI;AAAA,IACvB;AAQA,YAAQ,0BAA0B,SAAS,wBAAwB,SAAS;AAC1E,aAAO,gBAAgB,OAAO;AAAA,IAChC;AAQA,YAAQ,cAAc,SAAU,MAAM;AACpC,UAAI,QAAQ;AACZ,aAAO,SAAS,GAAG;AACjB;AACA,kBAAU;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AACA,YAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AACxD,UAAI,OAAO,MAAM,YAAY;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AACA,uBAAiB;AAAA,IACnB;AACA,YAAQ,qBAAqB,WAAY;AACvC,aAAO,OAAO,mBAAmB;AAAA,IACnC;AACA,YAAQ,SAAS,SAAS,OAAO,OAAO;AACtC,aAAO,eAAe,KAAK;AAAA,IAC7B;AAAA;AAAA;;;ACpDA;AAAA;AAAA,YAAQ,IAAI;AAAA,MACV,KAAK;AAAA,IACP;AACA,YAAQ,IAAI;AAAA,MACV,KAAK;AAAA,IACP;AACA,YAAQ,IAAI;AAAA,MACV,KAAK;AAAA,IACP;AACA,YAAQ,IAAI;AAAA,MACV,KAAK;AAAA,IACP;AACA,aAAS,WAAW,QAAQ;AAC1B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AACA,YAAM,QAAQ,OAAO,YAAY;AACjC,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB;AACE,gBAAM,IAAI,MAAM,uBAAuB,MAAM;AAAA,MACjD;AAAA,IACF;AACA,YAAQ,UAAU,SAAS,QAAQ,OAAO;AACxC,aAAO,SAAS,OAAO,MAAM,QAAQ,eAAe,MAAM,OAAO,KAAK,MAAM,MAAM;AAAA,IACpF;AACA,YAAQ,OAAO,SAAS,KAAK,OAAO,cAAc;AAChD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AC9CA;AAAA;AAAA,aAAS,YAAY;AACnB,WAAK,SAAS,CAAC;AACf,WAAK,SAAS;AAAA,IAChB;AACA,cAAU,YAAY;AAAA,MACpB,KAAK,SAAU,OAAO;AACpB,cAAM,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrC,gBAAQ,KAAK,OAAO,QAAQ,MAAM,IAAI,QAAQ,IAAI,OAAO;AAAA,MAC3D;AAAA,MACA,KAAK,SAAU,KAAK,QAAQ;AAC1B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAK,QAAQ,QAAQ,SAAS,IAAI,IAAI,OAAO,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,MACA,iBAAiB,WAAY;AAC3B,eAAO,KAAK;AAAA,MACd;AAAA,MACA,QAAQ,SAAU,KAAK;AACrB,cAAM,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AAC3C,YAAI,KAAK,OAAO,UAAU,UAAU;AAClC,eAAK,OAAO,KAAK,CAAC;AAAA,QACpB;AACA,YAAI,KAAK;AACP,eAAK,OAAO,QAAQ,KAAK,QAAS,KAAK,SAAS;AAAA,QAClD;AACA,aAAK;AAAA,MACP;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAKA,aAAS,UAAU,MAAM;AACvB,UAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AACA,WAAK,OAAO;AACZ,WAAK,OAAO,IAAI,WAAW,OAAO,IAAI;AACtC,WAAK,cAAc,IAAI,WAAW,OAAO,IAAI;AAAA,IAC/C;AAWA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO,UAAU;AAC7D,YAAM,QAAQ,MAAM,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK,IAAI;AACnB,UAAI,SAAU,MAAK,YAAY,KAAK,IAAI;AAAA,IAC1C;AASA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK;AAC5C,aAAO,KAAK,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACxC;AAUA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO;AACnD,WAAK,KAAK,MAAM,KAAK,OAAO,GAAG,KAAK;AAAA,IACtC;AASA,cAAU,UAAU,aAAa,SAAU,KAAK,KAAK;AACnD,aAAO,KAAK,YAAY,MAAM,KAAK,OAAO,GAAG;AAAA,IAC/C;AACA,WAAO,UAAU;AAAA;AAAA;;;AC9DjB;AAAA;AAUA,QAAM,gBAAgB,gBAAmB;AAgBzC,YAAQ,kBAAkB,SAAS,gBAAgB,SAAS;AAC1D,UAAI,YAAY,EAAG,QAAO,CAAC;AAC3B,YAAM,WAAW,KAAK,MAAM,UAAU,CAAC,IAAI;AAC3C,YAAM,OAAO,cAAc,OAAO;AAClC,YAAM,YAAY,SAAS,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,WAAW,EAAE,IAAI;AACpF,YAAM,YAAY,CAAC,OAAO,CAAC;AAE3B,eAAS,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK;AACrC,kBAAU,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI;AAAA,MACpC;AACA,gBAAU,KAAK,CAAC;AAEhB,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAsBA,YAAQ,eAAe,SAAS,aAAa,SAAS;AACpD,YAAM,SAAS,CAAC;AAChB,YAAM,MAAM,QAAQ,gBAAgB,OAAO;AAC3C,YAAM,YAAY,IAAI;AACtB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAElC,cAAI,MAAM,KAAK,MAAM;AAAA,UAErB,MAAM,KAAK,MAAM,YAAY;AAAA,UAE7B,MAAM,YAAY,KAAK,MAAM,GAAG;AAE9B;AAAA,UACF;AACA,iBAAO,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChFA;AAAA;AAAA,QAAM,gBAAgB,gBAAmB;AACzC,QAAM,sBAAsB;AAS5B,YAAQ,eAAe,SAAS,aAAa,SAAS;AACpD,YAAM,OAAO,cAAc,OAAO;AAClC,aAAO;AAAA;AAAA,QAEP,CAAC,GAAG,CAAC;AAAA;AAAA,QAEL,CAAC,OAAO,qBAAqB,CAAC;AAAA;AAAA,QAE9B,CAAC,GAAG,OAAO,mBAAmB;AAAA,MAAC;AAAA,IACjC;AAAA;AAAA;;;ACnBA;AAAA;AAIA,YAAQ,WAAW;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAMA,QAAM,gBAAgB;AAAA,MACpB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAQA,YAAQ,UAAU,SAAS,QAAQ,MAAM;AACvC,aAAO,QAAQ,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAAA,IAC7E;AASA,YAAQ,OAAO,SAAS,KAAK,OAAO;AAClC,aAAO,QAAQ,QAAQ,KAAK,IAAI,SAAS,OAAO,EAAE,IAAI;AAAA,IACxD;AASA,YAAQ,eAAe,SAAS,aAAa,MAAM;AACjD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,uBAAe,eAAe;AAC9B,kBAAU,UAAU;AACpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAIA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC9B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AACA,UAAAA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC1B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAAA,QACF;AACA,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AAAA,MACtE;AACA,aAAO;AAAA,IACT;AAOA,YAAQ,eAAe,SAAS,aAAa,MAAM;AACjD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,eAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,iBAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,gBAAM,OAAO,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAC7G,cAAI,SAAS,KAAK,SAAS,EAAG;AAAA,QAChC;AAAA,MACF;AACA,aAAO,SAAS,cAAc;AAAA,IAChC;AAQA,YAAQ,eAAe,SAAS,aAAa,MAAM;AACjD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,UAAU;AACd,UAAI,UAAU;AACd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,kBAAU,UAAU;AACpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,oBAAU,WAAW,IAAI,OAAQ,KAAK,IAAI,KAAK,GAAG;AAClD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAC3D,oBAAU,WAAW,IAAI,OAAQ,KAAK,IAAI,KAAK,GAAG;AAClD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAAA,QAC7D;AAAA,MACF;AACA,aAAO,SAAS,cAAc;AAAA,IAChC;AAUA,YAAQ,eAAe,SAAS,aAAa,MAAM;AACjD,UAAI,YAAY;AAChB,YAAM,eAAe,KAAK,KAAK;AAC/B,eAAS,IAAI,GAAG,IAAI,cAAc,IAAK,cAAa,KAAK,KAAK,CAAC;AAC/D,YAAM,IAAI,KAAK,IAAI,KAAK,KAAK,YAAY,MAAM,eAAe,CAAC,IAAI,EAAE;AACrE,aAAO,IAAI,cAAc;AAAA,IAC3B;AAUA,aAAS,UAAU,aAAa,GAAG,GAAG;AACpC,cAAQ,aAAa;AAAA,QACnB,KAAK,QAAQ,SAAS;AACpB,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzB,KAAK,QAAQ,SAAS;AACpB,iBAAO,IAAI,MAAM;AAAA,QACnB,KAAK,QAAQ,SAAS;AACpB,iBAAO,IAAI,MAAM;AAAA,QACnB,KAAK,QAAQ,SAAS;AACpB,kBAAQ,IAAI,KAAK,MAAM;AAAA,QACzB,KAAK,QAAQ,SAAS;AACpB,kBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AACpB,iBAAO,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,QACnC,KAAK,QAAQ,SAAS;AACpB,kBAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM;AAAA,QACzC,KAAK,QAAQ,SAAS;AACpB,kBAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM;AAAA,QAC3C;AACE,gBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,MACpD;AAAA,IACF;AAQA,YAAQ,YAAY,SAAS,UAAU,SAAS,MAAM;AACpD,YAAM,OAAO,KAAK;AAClB,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAI,KAAK,WAAW,KAAK,GAAG,EAAG;AAC/B,eAAK,IAAI,KAAK,KAAK,UAAU,SAAS,KAAK,GAAG,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAQA,YAAQ,cAAc,SAAS,YAAY,MAAM,iBAAiB;AAChE,YAAM,cAAc,OAAO,KAAK,QAAQ,QAAQ,EAAE;AAClD,UAAI,cAAc;AAClB,UAAI,eAAe;AACnB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,wBAAgB,CAAC;AACjB,gBAAQ,UAAU,GAAG,IAAI;AAGzB,cAAM,UAAU,QAAQ,aAAa,IAAI,IAAI,QAAQ,aAAa,IAAI,IAAI,QAAQ,aAAa,IAAI,IAAI,QAAQ,aAAa,IAAI;AAGhI,gBAAQ,UAAU,GAAG,IAAI;AACzB,YAAI,UAAU,cAAc;AAC1B,yBAAe;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxNA;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,kBAAkB;AAAA;AAAA,MAExB;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,IAAE;AACpkB,QAAM,qBAAqB;AAAA;AAAA,MAE3B;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,IAAI;AAUnyB,YAAQ,iBAAiB,SAAS,eAAe,SAAS,sBAAsB;AAC9E,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,YAAQ,yBAAyB,SAAS,uBAAuB,SAAS,sBAAsB;AAC9F,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA;AAAA;;;ACpDA;AAAA;AAAA,QAAM,YAAY,IAAI,WAAW,GAAG;AACpC,QAAM,YAAY,IAAI,WAAW,GAAG;AASpC,KAAC,SAAS,aAAa;AACrB,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAI;AACf,cAAM;AAIN,YAAI,IAAI,KAAO;AAEb,eAAK;AAAA,QACP;AAAA,MACF;AAMA,eAAS,IAAI,KAAK,IAAI,KAAK,KAAK;AAC9B,kBAAU,CAAC,IAAI,UAAU,IAAI,GAAG;AAAA,MAClC;AAAA,IACF,GAAG;AAQH,YAAQ,MAAM,SAAS,IAAI,GAAG;AAC5B,UAAI,IAAI,EAAG,OAAM,IAAI,MAAM,SAAS,IAAI,GAAG;AAC3C,aAAO,UAAU,CAAC;AAAA,IACpB;AAQA,YAAQ,MAAM,SAAS,IAAI,GAAG;AAC5B,aAAO,UAAU,CAAC;AAAA,IACpB;AASA,YAAQ,MAAM,SAAS,IAAI,GAAG,GAAG;AAC/B,UAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAI/B,aAAO,UAAU,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC;AAAA,IAC9C;AAAA;AAAA;;;ACpEA;AAAA;AAAA,QAAM,KAAK;AASX,YAAQ,MAAM,SAAS,IAAI,IAAI,IAAI;AACjC,YAAM,QAAQ,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;AACtD,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,gBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QACrC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AASA,YAAQ,MAAM,SAAS,IAAI,UAAU,SAAS;AAC5C,UAAI,SAAS,IAAI,WAAW,QAAQ;AACpC,aAAO,OAAO,SAAS,QAAQ,UAAU,GAAG;AAC1C,cAAM,QAAQ,OAAO,CAAC;AACtB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,iBAAO,CAAC,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,KAAK;AAAA,QACvC;AAGA,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,UAAU,OAAO,MAAM,MAAM,EAAG;AACvD,iBAAS,OAAO,MAAM,MAAM;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AASA,YAAQ,uBAAuB,SAAS,qBAAqB,QAAQ;AACnE,UAAI,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACvDA;AAAA;AAAA,QAAM,aAAa;AACnB,aAAS,mBAAmB,QAAQ;AAClC,WAAK,UAAU;AACf,WAAK,SAAS;AACd,UAAI,KAAK,OAAQ,MAAK,WAAW,KAAK,MAAM;AAAA,IAC9C;AAQA,uBAAmB,UAAU,aAAa,SAAS,WAAW,QAAQ;AAEpE,WAAK,SAAS;AACd,WAAK,UAAU,WAAW,qBAAqB,KAAK,MAAM;AAAA,IAC5D;AAQA,uBAAmB,UAAU,SAAS,SAAS,OAAO,MAAM;AAC1D,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAIA,YAAM,aAAa,IAAI,WAAW,KAAK,SAAS,KAAK,MAAM;AAC3D,iBAAW,IAAI,IAAI;AAInB,YAAM,YAAY,WAAW,IAAI,YAAY,KAAK,OAAO;AAKzD,YAAM,QAAQ,KAAK,SAAS,UAAU;AACtC,UAAI,QAAQ,GAAG;AACb,cAAM,OAAO,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,IAAI,WAAW,KAAK;AACzB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA;AAAA;;;AClDjB;AAAA;AAMA,YAAQ,UAAU,SAAS,QAAQ,SAAS;AAC1C,aAAO,CAAC,MAAM,OAAO,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD;AAAA;AAAA;;;ACRA;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,eAAe;AACrB,QAAI,QAAQ;AACZ,YAAQ,MAAM,QAAQ,MAAM,KAAK;AACjC,QAAM,OAAO,+BAA+B,QAAQ;AACpD,YAAQ,QAAQ,IAAI,OAAO,OAAO,GAAG;AACrC,YAAQ,aAAa,IAAI,OAAO,yBAAyB,GAAG;AAC5D,YAAQ,OAAO,IAAI,OAAO,MAAM,GAAG;AACnC,YAAQ,UAAU,IAAI,OAAO,SAAS,GAAG;AACzC,YAAQ,eAAe,IAAI,OAAO,cAAc,GAAG;AACnD,QAAM,aAAa,IAAI,OAAO,MAAM,QAAQ,GAAG;AAC/C,QAAM,eAAe,IAAI,OAAO,MAAM,UAAU,GAAG;AACnD,QAAM,oBAAoB,IAAI,OAAO,wBAAwB;AAC7D,YAAQ,YAAY,SAAS,UAAU,KAAK;AAC1C,aAAO,WAAW,KAAK,GAAG;AAAA,IAC5B;AACA,YAAQ,cAAc,SAAS,YAAY,KAAK;AAC9C,aAAO,aAAa,KAAK,GAAG;AAAA,IAC9B;AACA,YAAQ,mBAAmB,SAAS,iBAAiB,KAAK;AACxD,aAAO,kBAAkB,KAAK,GAAG;AAAA,IACnC;AAAA;AAAA;;;ACrBA;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,QAAQ;AASd,YAAQ,UAAU;AAAA,MAChB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,IAAI,IAAI,EAAE;AAAA,IACrB;AAWA,YAAQ,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAOA,YAAQ,OAAO;AAAA,MACb,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAWA,YAAQ,QAAQ;AAAA,MACd,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAQA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,IACP;AAUA,YAAQ,wBAAwB,SAAS,sBAAsB,MAAM,SAAS;AAC5E,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzD,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,sBAAsB,OAAO;AAAA,MAC/C;AACA,UAAI,WAAW,KAAK,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAAA,eAAW,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AACnG,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAQA,YAAQ,qBAAqB,SAAS,mBAAmB,SAAS;AAChE,UAAI,MAAM,YAAY,OAAO,EAAG,QAAO,QAAQ;AAAA,eAAiB,MAAM,iBAAiB,OAAO,EAAG,QAAO,QAAQ;AAAA,eAAsB,MAAM,UAAU,OAAO,EAAG,QAAO,QAAQ;AAAA,UAAW,QAAO,QAAQ;AAAA,IAC3M;AAQA,YAAQ,WAAW,SAAS,SAAS,MAAM;AACzC,UAAI,QAAQ,KAAK,GAAI,QAAO,KAAK;AACjC,YAAM,IAAI,MAAM,cAAc;AAAA,IAChC;AAQA,YAAQ,UAAU,SAAS,QAAQ,MAAM;AACvC,aAAO,QAAQ,KAAK,OAAO,KAAK;AAAA,IAClC;AAQA,aAAS,WAAW,QAAQ;AAC1B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AACA,YAAM,QAAQ,OAAO,YAAY;AACjC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB;AACE,gBAAM,IAAI,MAAM,mBAAmB,MAAM;AAAA,MAC7C;AAAA,IACF;AAUA,YAAQ,OAAO,SAAS,KAAK,OAAO,cAAc;AAChD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AC7JA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,eAAe;AAGrB,QAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACnF,QAAM,UAAU,MAAM,YAAY,GAAG;AACrC,aAAS,4BAA4B,MAAM,QAAQ,sBAAsB;AACvE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,IAAI,GAAG;AAC7E,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,qBAAqB,MAAM,SAAS;AAE3C,aAAO,KAAK,sBAAsB,MAAM,OAAO,IAAI;AAAA,IACrD;AACA,aAAS,0BAA0B,UAAU,SAAS;AACpD,UAAI,YAAY;AAChB,eAAS,QAAQ,SAAU,MAAM;AAC/B,cAAM,eAAe,qBAAqB,KAAK,MAAM,OAAO;AAC5D,qBAAa,eAAe,KAAK,cAAc;AAAA,MACjD,CAAC;AACD,aAAO;AAAA,IACT;AACA,aAAS,2BAA2B,UAAU,sBAAsB;AAClE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,cAAM,SAAS,0BAA0B,UAAU,cAAc;AACjE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,KAAK,KAAK,GAAG;AACnF,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,YAAQ,OAAO,SAAS,KAAK,OAAO,cAAc;AAChD,UAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAWA,YAAQ,cAAc,SAAS,YAAY,SAAS,sBAAsB,MAAM;AAC9E,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAGA,UAAI,OAAO,SAAS,YAAa,QAAO,KAAK;AAG7C,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AACrE,UAAI,SAAS,KAAK,MAAO,QAAO;AAChC,YAAM,aAAa,yBAAyB,qBAAqB,MAAM,OAAO;AAG9E,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,KAAK,CAAC;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,KAAK,CAAC;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,EAAE;AAAA,QACnC,KAAK,KAAK;AAAA,QACV;AACE,iBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,MACpC;AAAA,IACF;AAUA,YAAQ,wBAAwB,SAAS,sBAAsB,MAAM,sBAAsB;AACzF,UAAI;AACJ,YAAM,MAAM,QAAQ,KAAK,sBAAsB,QAAQ,CAAC;AACxD,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,2BAA2B,MAAM,GAAG;AAAA,QAC7C;AACA,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO;AAAA,QACT;AACA,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,cAAM;AAAA,MACR;AACA,aAAO,4BAA4B,IAAI,MAAM,IAAI,UAAU,GAAG,GAAG;AAAA,IACnE;AAYA,YAAQ,iBAAiB,SAAS,eAAe,SAAS;AACxD,UAAI,CAAC,aAAa,QAAQ,OAAO,KAAK,UAAU,GAAG;AACjD,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,UAAI,IAAI,WAAW;AACnB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAK,OAAO,MAAM,YAAY,CAAC,IAAI;AAAA,MACrC;AACA,aAAO,WAAW,KAAK;AAAA,IACzB;AAAA;AAAA;;;AC5IA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACxE,QAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;AAC7D,QAAM,UAAU,MAAM,YAAY,GAAG;AAYrC,YAAQ,iBAAiB,SAAS,eAAe,sBAAsB,MAAM;AAC3E,YAAM,OAAO,qBAAqB,OAAO,IAAI;AAC7C,UAAI,IAAI,QAAQ;AAChB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAK,OAAO,MAAM,YAAY,CAAC,IAAI;AAAA,MACrC;AAKA,cAAQ,QAAQ,KAAK,KAAK;AAAA,IAC5B;AAAA;AAAA;;;AC1BA;AAAA;AAAA,QAAM,OAAO;AACb,aAAS,YAAY,MAAM;AACzB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,KAAK,SAAS;AAAA,IAC5B;AACA,gBAAY,gBAAgB,SAAS,cAAc,QAAQ;AACzD,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,KAAK,SAAS,IAAI,SAAS,IAAI,IAAI,IAAI;AAAA,IAC1E;AACA,gBAAY,UAAU,YAAY,SAAS,YAAY;AACrD,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,gBAAY,UAAU,gBAAgB,SAAS,gBAAgB;AAC7D,aAAO,YAAY,cAAc,KAAK,KAAK,MAAM;AAAA,IACnD;AACA,gBAAY,UAAU,QAAQ,SAAS,MAAM,WAAW;AACtD,UAAI,GAAG,OAAO;AAId,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC7C,gBAAQ,KAAK,KAAK,OAAO,GAAG,CAAC;AAC7B,gBAAQ,SAAS,OAAO,EAAE;AAC1B,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,YAAM,eAAe,KAAK,KAAK,SAAS;AACxC,UAAI,eAAe,GAAG;AACpB,gBAAQ,KAAK,KAAK,OAAO,CAAC;AAC1B,gBAAQ,SAAS,OAAO,EAAE;AAC1B,kBAAU,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,MAC3C;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAAA,QAAM,OAAO;AAWb,QAAM,kBAAkB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxP,aAAS,iBAAiB,MAAM;AAC9B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AACA,qBAAiB,gBAAgB,SAAS,cAAc,QAAQ;AAC9D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,SAAS;AAAA,IACrD;AACA,qBAAiB,UAAU,YAAY,SAAS,YAAY;AAC1D,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,qBAAiB,UAAU,gBAAgB,SAAS,gBAAgB;AAClE,aAAO,iBAAiB,cAAc,KAAK,KAAK,MAAM;AAAA,IACxD;AACA,qBAAiB,UAAU,QAAQ,SAAS,MAAM,WAAW;AAC3D,UAAI;AAIJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAE7C,YAAI,QAAQ,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,IAAI;AAGpD,iBAAS,gBAAgB,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC;AAGjD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,UAAI,KAAK,KAAK,SAAS,GAAG;AACxB,kBAAU,IAAI,gBAAgB,QAAQ,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,MACxD;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC/CjB;AAAA;AAAA,QAAM,OAAO;AACb,aAAS,SAAS,MAAM;AACtB,WAAK,OAAO,KAAK;AACjB,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,OAAO,IAAI,YAAY,EAAE,OAAO,IAAI;AAAA,MAC3C,OAAO;AACL,aAAK,OAAO,IAAI,WAAW,IAAI;AAAA,MACjC;AAAA,IACF;AACA,aAAS,gBAAgB,SAAS,cAAc,QAAQ;AACtD,aAAO,SAAS;AAAA,IAClB;AACA,aAAS,UAAU,YAAY,SAAS,YAAY;AAClD,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,aAAS,UAAU,gBAAgB,SAAS,gBAAgB;AAC1D,aAAO,SAAS,cAAc,KAAK,KAAK,MAAM;AAAA,IAChD;AACA,aAAS,UAAU,QAAQ,SAAU,WAAW;AAC9C,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,IAAI,GAAG,KAAK;AAChD,kBAAU,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;ACvBjB;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,QAAQ;AACd,aAAS,UAAU,MAAM;AACvB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AACA,cAAU,gBAAgB,SAAS,cAAc,QAAQ;AACvD,aAAO,SAAS;AAAA,IAClB;AACA,cAAU,UAAU,YAAY,SAAS,YAAY;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,cAAU,UAAU,gBAAgB,SAAS,gBAAgB;AAC3D,aAAO,UAAU,cAAc,KAAK,KAAK,MAAM;AAAA,IACjD;AACA,cAAU,UAAU,QAAQ,SAAU,WAAW;AAC/C,UAAI;AAKJ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAI,QAAQ,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC;AAGrC,YAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,mBAAS;AAAA,QAGX,WAAW,SAAS,SAAU,SAAS,OAAQ;AAE7C,mBAAS;AAAA,QACX,OAAO;AACL,gBAAM,IAAI,MAAM,6BAA6B,KAAK,KAAK,CAAC,IAAI,mCAAwC;AAAA,QACtG;AAIA,iBAAS,UAAU,IAAI,OAAQ,OAAQ,QAAQ;AAG/C,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC7CjB;AAAA;AAAA;AAuBA,QAAI,WAAW;AAAA,MACb,8BAA8B,SAAU,OAAO,GAAG,GAAG;AAGnD,YAAI,eAAe,CAAC;AAIpB,YAAI,QAAQ,CAAC;AACb,cAAM,CAAC,IAAI;AAMX,YAAI,OAAO,SAAS,cAAc,KAAK;AACvC,aAAK,KAAK,GAAG,CAAC;AACd,YAAI,SAAS,GAAG,GAAG,gBAAgB,gBAAgB,WAAW,+BAA+B,gBAAgB;AAC7G,eAAO,CAAC,KAAK,MAAM,GAAG;AAGpB,oBAAU,KAAK,IAAI;AACnB,cAAI,QAAQ;AACZ,2BAAiB,QAAQ;AAGzB,2BAAiB,MAAM,CAAC,KAAK,CAAC;AAK9B,eAAK,KAAK,gBAAgB;AACxB,gBAAI,eAAe,eAAe,CAAC,GAAG;AAEpC,0BAAY,eAAe,CAAC;AAK5B,8CAAgC,iBAAiB;AAMjD,+BAAiB,MAAM,CAAC;AACxB,4BAAc,OAAO,MAAM,CAAC,MAAM;AAClC,kBAAI,eAAe,iBAAiB,+BAA+B;AACjE,sBAAM,CAAC,IAAI;AACX,qBAAK,KAAK,GAAG,6BAA6B;AAC1C,6BAAa,CAAC,IAAI;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO,MAAM,eAAe,OAAO,MAAM,CAAC,MAAM,aAAa;AAC/D,cAAI,MAAM,CAAC,+BAA+B,GAAG,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE;AACpE,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAAA,MACA,6CAA6C,SAAU,cAAc,GAAG;AACtE,YAAI,QAAQ,CAAC;AACb,YAAI,IAAI;AACR,YAAI;AACJ,eAAO,GAAG;AACR,gBAAM,KAAK,CAAC;AACZ,wBAAc,aAAa,CAAC;AAC5B,cAAI,aAAa,CAAC;AAAA,QACpB;AACA,cAAM,QAAQ;AACd,eAAO;AAAA,MACT;AAAA,MACA,WAAW,SAAU,OAAO,GAAG,GAAG;AAChC,YAAI,eAAe,SAAS,6BAA6B,OAAO,GAAG,CAAC;AACpE,eAAO,SAAS,4CAA4C,cAAc,CAAC;AAAA,MAC7E;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe;AAAA,QACb,MAAM,SAAU,MAAM;AACpB,cAAI,IAAI,SAAS,eACf,IAAI,CAAC,GACL;AACF,iBAAO,QAAQ,CAAC;AAChB,eAAK,OAAO,GAAG;AACb,gBAAI,EAAE,eAAe,GAAG,GAAG;AACzB,gBAAE,GAAG,IAAI,EAAE,GAAG;AAAA,YAChB;AAAA,UACF;AACA,YAAE,QAAQ,CAAC;AACX,YAAE,SAAS,KAAK,UAAU,EAAE;AAC5B,iBAAO;AAAA,QACT;AAAA,QACA,gBAAgB,SAAU,GAAG,GAAG;AAC9B,iBAAO,EAAE,OAAO,EAAE;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,MAAM,SAAU,OAAO,MAAM;AAC3B,cAAI,OAAO;AAAA,YACT;AAAA,YACA;AAAA,UACF;AACA,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QAC7B;AAAA;AAAA;AAAA;AAAA,QAIA,KAAK,WAAY;AACf,iBAAO,KAAK,MAAM,MAAM;AAAA,QAC1B;AAAA,QACA,OAAO,WAAY;AACjB,iBAAO,KAAK,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAGA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACpJA;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,WAAW;AAQjB,aAAS,oBAAoB,KAAK;AAChC,aAAO,SAAS,mBAAmB,GAAG,CAAC,EAAE;AAAA,IAC3C;AAUA,aAAS,YAAY,OAAO,MAAM,KAAK;AACrC,YAAM,WAAW,CAAC;AAClB,UAAI;AACJ,cAAQ,SAAS,MAAM,KAAK,GAAG,OAAO,MAAM;AAC1C,iBAAS,KAAK;AAAA,UACZ,MAAM,OAAO,CAAC;AAAA,UACd,OAAO,OAAO;AAAA,UACd;AAAA,UACA,QAAQ,OAAO,CAAC,EAAE;AAAA,QACpB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AASA,aAAS,sBAAsB,SAAS;AACtC,YAAM,UAAU,YAAY,MAAM,SAAS,KAAK,SAAS,OAAO;AAChE,YAAM,eAAe,YAAY,MAAM,cAAc,KAAK,cAAc,OAAO;AAC/E,UAAI;AACJ,UAAI;AACJ,UAAI,MAAM,mBAAmB,GAAG;AAC9B,mBAAW,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,oBAAY,YAAY,MAAM,OAAO,KAAK,OAAO,OAAO;AAAA,MAC1D,OAAO;AACL,mBAAW,YAAY,MAAM,YAAY,KAAK,MAAM,OAAO;AAC3D,oBAAY,CAAC;AAAA,MACf;AACA,YAAM,OAAO,QAAQ,OAAO,cAAc,UAAU,SAAS;AAC7D,aAAO,KAAK,KAAK,SAAU,IAAI,IAAI;AACjC,eAAO,GAAG,QAAQ,GAAG;AAAA,MACvB,CAAC,EAAE,IAAI,SAAU,KAAK;AACpB,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,QAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AAUA,aAAS,qBAAqB,QAAQ,MAAM;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,YAAY,cAAc,MAAM;AAAA,QACzC,KAAK,KAAK;AACR,iBAAO,iBAAiB,cAAc,MAAM;AAAA,QAC9C,KAAK,KAAK;AACR,iBAAO,UAAU,cAAc,MAAM;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,SAAS,cAAc,MAAM;AAAA,MACxC;AAAA,IACF;AAQA,aAAS,cAAc,MAAM;AAC3B,aAAO,KAAK,OAAO,SAAU,KAAK,MAAM;AACtC,cAAM,UAAU,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AAC5D,YAAI,WAAW,QAAQ,SAAS,KAAK,MAAM;AACzC,cAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK;AACjC,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,IAAI;AACb,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAkBA,aAAS,WAAW,MAAM;AACxB,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAC;AAClB,gBAAQ,IAAI,MAAM;AAAA,UAChB,KAAK,KAAK;AACR,kBAAM,KAAK,CAAC,KAAK;AAAA,cACf,MAAM,IAAI;AAAA,cACV,MAAM,KAAK;AAAA,cACX,QAAQ,IAAI;AAAA,YACd,GAAG;AAAA,cACD,MAAM,IAAI;AAAA,cACV,MAAM,KAAK;AAAA,cACX,QAAQ,IAAI;AAAA,YACd,CAAC,CAAC;AACF;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK,CAAC,KAAK;AAAA,cACf,MAAM,IAAI;AAAA,cACV,MAAM,KAAK;AAAA,cACX,QAAQ,IAAI;AAAA,YACd,CAAC,CAAC;AACF;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK,CAAC,KAAK;AAAA,cACf,MAAM,IAAI;AAAA,cACV,MAAM,KAAK;AAAA,cACX,QAAQ,oBAAoB,IAAI,IAAI;AAAA,YACtC,CAAC,CAAC;AACF;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK,CAAC;AAAA,cACV,MAAM,IAAI;AAAA,cACV,MAAM,KAAK;AAAA,cACX,QAAQ,oBAAoB,IAAI,IAAI;AAAA,YACtC,CAAC,CAAC;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAcA,aAAS,WAAW,OAAO,SAAS;AAClC,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ;AAAA,QACZ,OAAO,CAAC;AAAA,MACV;AACA,UAAI,cAAc,CAAC,OAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,YAAY,MAAM,CAAC;AACzB,cAAM,iBAAiB,CAAC;AACxB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,OAAO,UAAU,CAAC;AACxB,gBAAM,MAAM,KAAK,IAAI;AACrB,yBAAe,KAAK,GAAG;AACvB,gBAAM,GAAG,IAAI;AAAA,YACX;AAAA,YACA,WAAW;AAAA,UACb;AACA,gBAAM,GAAG,IAAI,CAAC;AACd,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,kBAAM,aAAa,YAAY,CAAC;AAChC,gBAAI,MAAM,UAAU,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,KAAK,MAAM;AAClE,oBAAM,UAAU,EAAE,GAAG,IAAI,qBAAqB,MAAM,UAAU,EAAE,YAAY,KAAK,QAAQ,KAAK,IAAI,IAAI,qBAAqB,MAAM,UAAU,EAAE,WAAW,KAAK,IAAI;AACjK,oBAAM,UAAU,EAAE,aAAa,KAAK;AAAA,YACtC,OAAO;AACL,kBAAI,MAAM,UAAU,EAAG,OAAM,UAAU,EAAE,YAAY,KAAK;AAC1D,oBAAM,UAAU,EAAE,GAAG,IAAI,qBAAqB,KAAK,QAAQ,KAAK,IAAI,IAAI,IAAI,KAAK,sBAAsB,KAAK,MAAM,OAAO;AAAA,YAC3H;AAAA,UACF;AAAA,QACF;AACA,sBAAc;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,YAAY,CAAC,CAAC,EAAE,MAAM;AAAA,MAC9B;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAUA,aAAS,mBAAmB,MAAM,WAAW;AAC3C,UAAI;AACJ,YAAM,WAAW,KAAK,mBAAmB,IAAI;AAC7C,aAAO,KAAK,KAAK,WAAW,QAAQ;AAGpC,UAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK;AACjD,cAAM,IAAI,MAAM,MAAM,OAAO,mCAAwC,KAAK,SAAS,IAAI,IAAI,4BAA4B,KAAK,SAAS,QAAQ,CAAC;AAAA,MAChJ;AAGA,UAAI,SAAS,KAAK,SAAS,CAAC,MAAM,mBAAmB,GAAG;AACtD,eAAO,KAAK;AAAA,MACd;AACA,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,IAAI,YAAY,IAAI;AAAA,QAC7B,KAAK,KAAK;AACR,iBAAO,IAAI,iBAAiB,IAAI;AAAA,QAClC,KAAK,KAAK;AACR,iBAAO,IAAI,UAAU,IAAI;AAAA,QAC3B,KAAK,KAAK;AACR,iBAAO,IAAI,SAAS,IAAI;AAAA,MAC5B;AAAA,IACF;AAiBA,YAAQ,YAAY,SAAS,UAAU,OAAO;AAC5C,aAAO,MAAM,OAAO,SAAU,KAAK,KAAK;AACtC,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC;AAAA,QACxC,WAAW,IAAI,MAAM;AACnB,cAAI,KAAK,mBAAmB,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,QACjD;AACA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAUA,YAAQ,aAAa,SAAS,WAAW,MAAM,SAAS;AACtD,YAAM,OAAO,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AACnE,YAAM,QAAQ,WAAW,IAAI;AAC7B,YAAM,QAAQ,WAAW,OAAO,OAAO;AACvC,YAAM,OAAO,SAAS,UAAU,MAAM,KAAK,SAAS,KAAK;AACzD,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,sBAAc,KAAK,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI;AAAA,MAC9C;AACA,aAAO,QAAQ,UAAU,cAAc,aAAa,CAAC;AAAA,IACvD;AAYA,YAAQ,WAAW,SAAS,SAAS,MAAM;AACzC,aAAO,QAAQ,UAAU,sBAAsB,MAAM,MAAM,mBAAmB,CAAC,CAAC;AAAA,IAClF;AAAA;AAAA;;;ACxTA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,cAAc;AACpB,QAAM,SAAS;AACf,QAAM,qBAAqB;AAC3B,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,OAAO;AACb,QAAM,WAAW;AAkCjB,aAAS,mBAAmB,QAAQ,SAAS;AAC3C,YAAM,OAAO,OAAO;AACpB,YAAM,MAAM,cAAc,aAAa,OAAO;AAC9C,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,cAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AACtC,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,EAAG;AACtC,gBAAI,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAChI,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,aAAS,mBAAmB,QAAQ;AAClC,YAAM,OAAO,OAAO;AACpB,eAAS,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AACjC,cAAM,QAAQ,IAAI,MAAM;AACxB,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAC5B,eAAO,IAAI,GAAG,GAAG,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AAUA,aAAS,sBAAsB,QAAQ,SAAS;AAC9C,YAAM,MAAM,iBAAiB,aAAa,OAAO;AACjD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAI,CAAC,EAAE,CAAC;AACpB,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAC5B,gBAAI,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACpE,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,aAAS,iBAAiB,QAAQ,SAAS;AACzC,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,QAAQ,eAAe,OAAO;AAC3C,UAAI,KAAK,KAAK;AACd,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,KAAK,MAAM,IAAI,CAAC;AACtB,cAAM,IAAI,IAAI,OAAO,IAAI;AACzB,eAAO,QAAQ,IAAI,OAAO;AAC1B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAC9B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC;AAAA,IACF;AASA,aAAS,gBAAgB,QAAQ,sBAAsB,aAAa;AAClE,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,WAAW,eAAe,sBAAsB,WAAW;AACxE,UAAI,GAAG;AACP,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,eAAO,QAAQ,IAAI,OAAO;AAG1B,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAC5B,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,IAAI;AAAA,QACxC;AAGA,YAAI,IAAI,GAAG;AACT,iBAAO,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,IAAI;AAAA,QACvC,WAAW,IAAI,GAAG;AAChB,iBAAO,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI;AAAA,QACzC,OAAO;AACL,iBAAO,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAGA,aAAO,IAAI,OAAO,GAAG,GAAG,GAAG,IAAI;AAAA,IACjC;AAQA,aAAS,UAAU,QAAQ,MAAM;AAC/B,YAAM,OAAO,OAAO;AACpB,UAAI,MAAM;AACV,UAAI,MAAM,OAAO;AACjB,UAAI,WAAW;AACf,UAAI,YAAY;AAChB,eAAS,MAAM,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG;AAC1C,YAAI,QAAQ,EAAG;AACf,eAAO,MAAM;AACX,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAI,CAAC,OAAO,WAAW,KAAK,MAAM,CAAC,GAAG;AACpC,kBAAI,OAAO;AACX,kBAAI,YAAY,KAAK,QAAQ;AAC3B,wBAAQ,KAAK,SAAS,MAAM,WAAW,OAAO;AAAA,cAChD;AACA,qBAAO,IAAI,KAAK,MAAM,GAAG,IAAI;AAC7B;AACA,kBAAI,aAAa,IAAI;AACnB;AACA,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AACP,cAAI,MAAM,KAAK,QAAQ,KAAK;AAC1B,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAUA,aAAS,WAAW,SAAS,sBAAsB,UAAU;AAE3D,YAAM,SAAS,IAAI,UAAU;AAC7B,eAAS,QAAQ,SAAU,MAAM;AAE/B,eAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAS3B,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,sBAAsB,KAAK,MAAM,OAAO,CAAC;AAG3E,aAAK,MAAM,MAAM;AAAA,MACnB,CAAC;AAGD,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAC5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AACpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAOrE,UAAI,OAAO,gBAAgB,IAAI,KAAK,wBAAwB;AAC1D,eAAO,IAAI,GAAG,CAAC;AAAA,MACjB;AAOA,aAAO,OAAO,gBAAgB,IAAI,MAAM,GAAG;AACzC,eAAO,OAAO,CAAC;AAAA,MACjB;AAMA,YAAM,iBAAiB,yBAAyB,OAAO,gBAAgB,KAAK;AAC5E,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,eAAO,IAAI,IAAI,IAAI,KAAO,KAAM,CAAC;AAAA,MACnC;AACA,aAAO,gBAAgB,QAAQ,SAAS,oBAAoB;AAAA,IAC9D;AAWA,aAAS,gBAAgB,WAAW,SAAS,sBAAsB;AAEjE,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,qBAAqB,iBAAiB;AAG5C,YAAM,gBAAgB,OAAO,eAAe,SAAS,oBAAoB;AAGzE,YAAM,iBAAiB,iBAAiB;AACxC,YAAM,iBAAiB,gBAAgB;AACvC,YAAM,yBAAyB,KAAK,MAAM,iBAAiB,aAAa;AACxE,YAAM,wBAAwB,KAAK,MAAM,qBAAqB,aAAa;AAC3E,YAAM,wBAAwB,wBAAwB;AAGtD,YAAM,UAAU,yBAAyB;AAGzC,YAAM,KAAK,IAAI,mBAAmB,OAAO;AACzC,UAAI,SAAS;AACb,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,UAAI,cAAc;AAClB,YAAM,SAAS,IAAI,WAAW,UAAU,MAAM;AAG9C,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,cAAM,WAAW,IAAI,iBAAiB,wBAAwB;AAG9D,eAAO,CAAC,IAAI,OAAO,MAAM,QAAQ,SAAS,QAAQ;AAGlD,eAAO,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC;AAC/B,kBAAU;AACV,sBAAc,KAAK,IAAI,aAAa,QAAQ;AAAA,MAC9C;AAIA,YAAM,OAAO,IAAI,WAAW,cAAc;AAC1C,UAAI,QAAQ;AACZ,UAAI,GAAG;AAGP,WAAK,IAAI,GAAG,IAAI,aAAa,KAAK;AAChC,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACxB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAGA,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,aAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,eAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,QAC7B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAWA,aAAS,aAAa,MAAM,SAAS,sBAAsB,aAAa;AACtE,UAAI;AACJ,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,mBAAW,SAAS,UAAU,IAAI;AAAA,MACpC,WAAW,OAAO,SAAS,UAAU;AACnC,YAAI,mBAAmB;AACvB,YAAI,CAAC,kBAAkB;AACrB,gBAAM,cAAc,SAAS,SAAS,IAAI;AAG1C,6BAAmB,QAAQ,sBAAsB,aAAa,oBAAoB;AAAA,QACpF;AAIA,mBAAW,SAAS,WAAW,MAAM,oBAAoB,EAAE;AAAA,MAC7D,OAAO;AACL,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC;AAGA,YAAM,cAAc,QAAQ,sBAAsB,UAAU,oBAAoB;AAGhF,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAGA,UAAI,CAAC,SAAS;AACZ,kBAAU;AAAA,MAGZ,WAAW,UAAU,aAAa;AAChC,cAAM,IAAI,MAAM,0HAAoI,cAAc,KAAK;AAAA,MACzK;AACA,YAAM,WAAW,WAAW,SAAS,sBAAsB,QAAQ;AAGnE,YAAM,cAAc,MAAM,cAAc,OAAO;AAC/C,YAAM,UAAU,IAAI,UAAU,WAAW;AAGzC,yBAAmB,SAAS,OAAO;AACnC,yBAAmB,OAAO;AAC1B,4BAAsB,SAAS,OAAO;AAMtC,sBAAgB,SAAS,sBAAsB,CAAC;AAChD,UAAI,WAAW,GAAG;AAChB,yBAAiB,SAAS,OAAO;AAAA,MACnC;AAGA,gBAAU,SAAS,QAAQ;AAC3B,UAAI,MAAM,WAAW,GAAG;AAEtB,sBAAc,YAAY,YAAY,SAAS,gBAAgB,KAAK,MAAM,SAAS,oBAAoB,CAAC;AAAA,MAC1G;AAGA,kBAAY,UAAU,aAAa,OAAO;AAG1C,sBAAgB,SAAS,sBAAsB,WAAW;AAC1D,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAWA,YAAQ,SAAS,SAAS,OAAO,MAAM,SAAS;AAC9C,UAAI,OAAO,SAAS,eAAe,SAAS,IAAI;AAC9C,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AACA,UAAI,uBAAuB,QAAQ;AACnC,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,YAAY,aAAa;AAElC,+BAAuB,QAAQ,KAAK,QAAQ,sBAAsB,QAAQ,CAAC;AAC3E,kBAAU,QAAQ,KAAK,QAAQ,OAAO;AACtC,eAAO,YAAY,KAAK,QAAQ,WAAW;AAC3C,YAAI,QAAQ,YAAY;AACtB,gBAAM,kBAAkB,QAAQ,UAAU;AAAA,QAC5C;AAAA,MACF;AACA,aAAO,aAAa,MAAM,SAAS,sBAAsB,IAAI;AAAA,IAC/D;AAAA;AAAA;;;ACrcA,IAAAC,iBAAA;AAAA;AAAA,aAAS,SAAS,KAAK;AACrB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,SAAS;AAAA,MACrB;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AACA,UAAI,UAAU,IAAI,MAAM,EAAE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AACnD,UAAI,QAAQ,SAAS,KAAK,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG;AACpE,cAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,MAC7C;AAGA,UAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAChD,kBAAU,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAU,GAAG;AAClE,iBAAO,CAAC,GAAG,CAAC;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AAGA,UAAI,QAAQ,WAAW,EAAG,SAAQ,KAAK,KAAK,GAAG;AAC/C,YAAM,WAAW,SAAS,QAAQ,KAAK,EAAE,GAAG,EAAE;AAC9C,aAAO;AAAA,QACL,GAAG,YAAY,KAAK;AAAA,QACpB,GAAG,YAAY,KAAK;AAAA,QACpB,GAAG,YAAY,IAAI;AAAA,QACnB,GAAG,WAAW;AAAA,QACd,KAAK,MAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,MACxC;AAAA,IACF;AACA,YAAQ,aAAa,SAAS,WAAW,SAAS;AAChD,UAAI,CAAC,QAAS,WAAU,CAAC;AACzB,UAAI,CAAC,QAAQ,MAAO,SAAQ,QAAQ,CAAC;AACrC,YAAM,SAAS,OAAO,QAAQ,WAAW,eAAe,QAAQ,WAAW,QAAQ,QAAQ,SAAS,IAAI,IAAI,QAAQ;AACpH,YAAM,QAAQ,QAAQ,SAAS,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AACrE,YAAM,QAAQ,QAAQ,SAAS;AAC/B,aAAO;AAAA,QACL;AAAA,QACA,OAAO,QAAQ,IAAI;AAAA,QACnB;AAAA,QACA,OAAO;AAAA,UACL,MAAM,SAAS,QAAQ,MAAM,QAAQ,WAAW;AAAA,UAChD,OAAO,SAAS,QAAQ,MAAM,SAAS,WAAW;AAAA,QACpD;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,cAAc,QAAQ,gBAAgB,CAAC;AAAA,MACzC;AAAA,IACF;AACA,YAAQ,WAAW,SAAS,SAAS,QAAQ,MAAM;AACjD,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK,KAAK;AAAA,IAC/G;AACA,YAAQ,gBAAgB,SAAS,cAAc,QAAQ,MAAM;AAC3D,YAAM,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAC3C,aAAO,KAAK,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK;AAAA,IACtD;AACA,YAAQ,gBAAgB,SAAS,cAAc,SAAS,IAAI,MAAM;AAChE,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACzC,YAAM,aAAa,KAAK,OAAO,OAAO,KAAK,SAAS,KAAK,KAAK;AAC9D,YAAM,eAAe,KAAK,SAAS;AACnC,YAAM,UAAU,CAAC,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI;AAClD,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,UAAU,IAAI,aAAa,KAAK;AACpC,cAAI,UAAU,KAAK,MAAM;AACzB,cAAI,KAAK,gBAAgB,KAAK,gBAAgB,IAAI,aAAa,gBAAgB,IAAI,aAAa,cAAc;AAC5G,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,kBAAM,OAAO,KAAK,OAAO,IAAI,gBAAgB,KAAK;AAClD,sBAAU,QAAQ,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC;AAAA,UACpD;AACA,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,MAAM,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC7EA;AAAA;AAAA,QAAM,QAAQ;AACd,aAAS,YAAY,KAAK,QAAQ,MAAM;AACtC,UAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,UAAI,CAAC,OAAO,MAAO,QAAO,QAAQ,CAAC;AACnC,aAAO,SAAS;AAChB,aAAO,QAAQ;AACf,aAAO,MAAM,SAAS,OAAO;AAC7B,aAAO,MAAM,QAAQ,OAAO;AAAA,IAC9B;AACA,aAAS,mBAAmB;AAC1B,UAAI;AACF,eAAO,SAAS,cAAc,QAAQ;AAAA,MACxC,SAAS,GAAG;AACV,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAAA,IACF;AACA,YAAQ,SAAS,SAAS,OAAO,QAAQ,QAAQ,SAAS;AACxD,UAAI,OAAO;AACX,UAAI,WAAW;AACf,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AACA,UAAI,CAAC,QAAQ;AACX,mBAAW,iBAAiB;AAAA,MAC9B;AACA,aAAO,MAAM,WAAW,IAAI;AAC5B,YAAM,OAAO,MAAM,cAAc,OAAO,QAAQ,MAAM,IAAI;AAC1D,YAAM,MAAM,SAAS,WAAW,IAAI;AACpC,YAAM,QAAQ,IAAI,gBAAgB,MAAM,IAAI;AAC5C,YAAM,cAAc,MAAM,MAAM,QAAQ,IAAI;AAC5C,kBAAY,KAAK,UAAU,IAAI;AAC/B,UAAI,aAAa,OAAO,GAAG,CAAC;AAC5B,aAAO;AAAA,IACT;AACA,YAAQ,kBAAkB,SAAS,gBAAgB,QAAQ,QAAQ,SAAS;AAC1E,UAAI,OAAO;AACX,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AACA,UAAI,CAAC,KAAM,QAAO,CAAC;AACnB,YAAM,WAAW,QAAQ,OAAO,QAAQ,QAAQ,IAAI;AACpD,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,eAAe,KAAK,gBAAgB,CAAC;AAC3C,aAAO,SAAS,UAAU,MAAM,aAAa,OAAO;AAAA,IACtD;AAAA;AAAA;;;AC9CA;AAAA;AAAA,QAAM,QAAQ;AACd,aAAS,eAAe,OAAO,QAAQ;AACrC,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,MAAM,SAAS,OAAO,MAAM,MAAM;AACxC,aAAO,QAAQ,IAAI,MAAM,MAAM,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,IAAI,MAAM;AAAA,IAC3F;AACA,aAAS,OAAO,KAAK,GAAG,GAAG;AACzB,UAAI,MAAM,MAAM;AAChB,UAAI,OAAO,MAAM,YAAa,QAAO,MAAM;AAC3C,aAAO;AAAA,IACT;AACA,aAAS,SAAS,MAAM,MAAM,QAAQ;AACpC,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAC/B,cAAM,MAAM,KAAK,MAAM,IAAI,IAAI;AAC/B,YAAI,CAAC,OAAO,CAAC,OAAQ,UAAS;AAC9B,YAAI,KAAK,CAAC,GAAG;AACX;AACA,cAAI,EAAE,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;AACtC,oBAAQ,SAAS,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,IAAI,OAAO,KAAK,QAAQ,CAAC;AACtF,qBAAS;AACT,qBAAS;AAAA,UACX;AACA,cAAI,EAAE,MAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACpC,oBAAQ,OAAO,KAAK,UAAU;AAC9B,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,YAAQ,SAAS,SAAS,OAAO,QAAQ,SAAS,IAAI;AACpD,YAAM,OAAO,MAAM,WAAW,OAAO;AACrC,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,aAAa,OAAO,KAAK,SAAS;AACxC,YAAM,KAAK,CAAC,KAAK,MAAM,MAAM,IAAI,KAAK,WAAW,eAAe,KAAK,MAAM,OAAO,MAAM,IAAI,cAAc,aAAa,MAAM,aAAa;AAC1I,YAAM,OAAO,WAAW,eAAe,KAAK,MAAM,MAAM,QAAQ,IAAI,SAAS,SAAS,MAAM,MAAM,KAAK,MAAM,IAAI;AACjH,YAAM,UAAU,kBAAuB,aAAa,MAAM,aAAa;AACvE,YAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,KAAK,QAAQ;AACtF,YAAM,SAAS,6CAA6C,QAAQ,UAAU,mCAAmC,KAAK,OAAO;AAC7H,UAAI,OAAO,OAAO,YAAY;AAC5B,WAAG,MAAM,MAAM;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnDA;AAAA;AAAA,QAAM,aAAa;AACnB,QAAM,SAAS;AACf,QAAM,iBAAiB;AACvB,QAAM,cAAc;AACpB,aAAS,aAAa,YAAY,QAAQ,MAAM,MAAM,IAAI;AACxD,YAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACvC,YAAM,UAAU,KAAK;AACrB,YAAM,cAAc,OAAO,KAAK,UAAU,CAAC,MAAM;AACjD,UAAI,CAAC,eAAe,CAAC,WAAW,GAAG;AACjC,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AACA,UAAI,aAAa;AACf,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AACA,YAAI,YAAY,GAAG;AACjB,eAAK;AACL,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,GAAG;AACxB,cAAI,OAAO,cAAc,OAAO,OAAO,aAAa;AAClD,iBAAK;AACL,mBAAO;AAAA,UACT,OAAO;AACL,iBAAK;AACL,mBAAO;AACP,mBAAO;AACP,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AACA,YAAI,YAAY,GAAG;AACjB,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,KAAK,CAAC,OAAO,YAAY;AAC9C,iBAAO;AACP,iBAAO;AACP,mBAAS;AAAA,QACX;AACA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAI;AACF,kBAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,oBAAQ,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,UACxC,SAAS,GAAG;AACV,mBAAO,CAAC;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI;AACF,cAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,WAAG,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,MACzC,SAAS,GAAG;AACV,WAAG,CAAC;AAAA,MACN;AAAA,IACF;AACA,YAAQ,SAAS,OAAO;AACxB,YAAQ,WAAW,aAAa,KAAK,MAAM,eAAe,MAAM;AAChE,YAAQ,YAAY,aAAa,KAAK,MAAM,eAAe,eAAe;AAG1E,YAAQ,WAAW,aAAa,KAAK,MAAM,SAAU,MAAM,GAAG,MAAM;AAClE,aAAO,YAAY,OAAO,MAAM,IAAI;AAAA,IACtC,CAAC;AAAA;AAAA;", "names": ["module", "require_utils"]}