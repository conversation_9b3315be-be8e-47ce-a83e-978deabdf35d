import {
  require_mapbox_gl
} from "./chunk-E3WEYDOE.js";
import {
  NgTemplateOutlet
} from "./chunk-FRDDUA6J.js";
import "./chunk-U5HOQPPH.js";
import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Directive,
  EventEmitter,
  HostListener,
  Injectable,
  InjectionToken,
  Injector,
  NgZone,
  Output,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
  afterNextRender,
  afterRender,
  forwardRef,
  inject,
  input,
  model,
  setClassMetadata,
  signal,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵconditional,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵqueryRefresh,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresolveWindow,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵviewQuery
} from "./chunk-H4Q5M6BP.js";
import {
  AsyncSubject,
  Subject,
  Subscription,
  debounceTime,
  filter,
  fromEvent,
  lastValueFrom,
  map,
  merge,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap
} from "./chunk-YH7VELCU.js";
import {
  __async,
  __spreadProps,
  __spreadValues,
  __toESM
} from "./chunk-KBUIKKCC.js";

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/map/map.service.mjs
var import_mapbox_gl = __toESM(require_mapbox_gl(), 1);
var MAPBOX_API_KEY = new InjectionToken("MapboxApiKey");
var MapService = class _MapService {
  zone = inject(NgZone);
  MAPBOX_API_KEY = inject(MAPBOX_API_KEY, {
    optional: true
  });
  injector = inject(Injector);
  mapInstance;
  mapCreated$;
  mapLoaded$;
  mapEvents;
  mapCreated = new AsyncSubject();
  mapLoaded = new AsyncSubject();
  markersToRemove = [];
  popupsToRemove = [];
  imageIdsToRemove = [];
  subscription = new Subscription();
  constructor() {
    this.mapCreated$ = this.mapCreated.asObservable();
    this.mapLoaded$ = this.mapLoaded.asObservable();
  }
  setup(options) {
    const mapOptions = __spreadProps(__spreadValues({}, options.mapOptions), {
      bearing: options.mapOptions.bearing?.[0],
      zoom: options.mapOptions.zoom?.[0],
      pitch: options.mapOptions.pitch?.[0],
      accessToken: options.accessToken || this.MAPBOX_API_KEY || ""
    });
    this.createMap(mapOptions);
    this.hookEvents(options.mapEvents);
    this.mapEvents = options.mapEvents;
    this.mapCreated.next(void 0);
    this.mapCreated.complete();
    if (options.mapEvents.mapCreate.observed) {
      this.zone.run(() => {
        options.mapEvents.mapCreate.emit(this.mapInstance);
      });
    }
  }
  destroyMap() {
    if (this.mapInstance) {
      this.subscription.unsubscribe();
      this.mapInstance.remove();
    }
  }
  updateProjection(projection) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setProjection(projection);
    });
  }
  updateMinZoom(minZoom) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setMinZoom(minZoom);
    });
  }
  updateMaxZoom(maxZoom) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setMaxZoom(maxZoom);
    });
  }
  updateMinPitch(minPitch) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setMinPitch(minPitch);
    });
  }
  updateMaxPitch(maxPitch) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setMaxPitch(maxPitch);
    });
  }
  updateRenderWorldCopies(status) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setRenderWorldCopies(status);
    });
  }
  updateScrollZoom(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.scrollZoom.enable() : this.mapInstance.scrollZoom.disable();
    });
  }
  updateDragRotate(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.dragRotate.enable() : this.mapInstance.dragRotate.disable();
    });
  }
  updateTouchPitch(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.touchPitch.enable() : this.mapInstance.touchPitch.disable();
    });
  }
  updateTouchZoomRotate(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.touchZoomRotate.enable() : this.mapInstance.touchZoomRotate.disable();
    });
  }
  updateDoubleClickZoom(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.doubleClickZoom.enable() : this.mapInstance.doubleClickZoom.disable();
    });
  }
  updateKeyboard(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.keyboard.enable() : this.mapInstance.keyboard.disable();
    });
  }
  updateDragPan(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.dragPan.enable() : this.mapInstance.dragPan.disable();
    });
  }
  updateBoxZoom(status) {
    return this.zone.runOutsideAngular(() => {
      status ? this.mapInstance.boxZoom.enable() : this.mapInstance.boxZoom.disable();
    });
  }
  updateStyle(style) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setStyle(style);
    });
  }
  updateMaxBounds(maxBounds) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setMaxBounds(maxBounds);
    });
  }
  changeCanvasCursor(cursor) {
    const canvas = this.mapInstance.getCanvasContainer();
    canvas.style.cursor = cursor;
  }
  queryRenderedFeatures(pointOrBox, parameters) {
    return this.mapInstance.queryRenderedFeatures(pointOrBox, parameters);
  }
  panTo(center, options) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.panTo(center, options);
    });
  }
  move(movingMethod, movingOptions, zoom, center, bearing, pitch) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance[movingMethod](__spreadProps(__spreadValues({}, movingOptions), {
        zoom: zoom != null ? zoom : this.mapInstance.getZoom(),
        center: center != null ? center : this.mapInstance.getCenter(),
        bearing: bearing != null ? bearing : this.mapInstance.getBearing(),
        pitch: pitch != null ? pitch : this.mapInstance.getPitch()
      }));
    });
  }
  addLayer(layer, bindEvents, before) {
    this.zone.runOutsideAngular(() => {
      Object.keys(layer.layerOptions).forEach((key) => {
        const tkey = key;
        if (layer.layerOptions[tkey] === void 0) {
          delete layer.layerOptions[tkey];
        }
      });
      this.mapInstance.addLayer(layer.layerOptions, before);
      if (bindEvents) {
        if (layer.layerEvents.layerClick.observed) {
          this.mapInstance.on("click", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerClick.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerDblClick.observed) {
          this.mapInstance.on("dblclick", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerDblClick.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseDown.observed) {
          this.mapInstance.on("mousedown", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseDown.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseUp.observed) {
          this.mapInstance.on("mouseup", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseUp.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseEnter.observed) {
          this.mapInstance.on("mouseenter", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseEnter.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseLeave.observed) {
          this.mapInstance.on("mouseleave", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseLeave.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseMove.observed) {
          this.mapInstance.on("mousemove", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseMove.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseOver.observed) {
          this.mapInstance.on("mouseover", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseOver.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerMouseOut.observed) {
          this.mapInstance.on("mouseout", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerMouseOut.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerContextMenu.observed) {
          this.mapInstance.on("contextmenu", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerContextMenu.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerTouchStart.observed) {
          this.mapInstance.on("touchstart", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerTouchStart.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerTouchEnd.observed) {
          this.mapInstance.on("touchend", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerTouchEnd.emit(evt);
            });
          });
        }
        if (layer.layerEvents.layerTouchCancel.observed) {
          this.mapInstance.on("touchcancel", layer.layerOptions.id, (evt) => {
            this.zone.run(() => {
              layer.layerEvents.layerTouchCancel.emit(evt);
            });
          });
        }
      }
    });
  }
  removeLayer(layerId) {
    this.zone.runOutsideAngular(() => {
      if (this.mapInstance.getLayer(layerId) != null) {
        this.mapInstance.removeLayer(layerId);
      }
    });
  }
  addMarker(marker) {
    const options = {
      offset: marker.markersOptions.offset,
      anchor: marker.markersOptions.anchor,
      draggable: marker.markersOptions.draggable,
      rotationAlignment: marker.markersOptions.rotationAlignment,
      pitchAlignment: marker.markersOptions.pitchAlignment,
      clickTolerance: marker.markersOptions.clickTolerance
    };
    Object.keys(options).forEach((key) => {
      const tkey = key;
      if (options[tkey] === void 0) {
        delete options[tkey];
      }
    });
    if (marker.markersOptions.element.childNodes.length > 0) {
      options.element = marker.markersOptions.element;
    }
    const markerInstance = new import_mapbox_gl.Marker(options);
    if (marker.markersEvents.markerDragStart.observed) {
      markerInstance.on("dragstart", (event) => {
        if (event) {
          const {
            target
          } = event;
          this.zone.run(() => {
            marker.markersEvents.markerDragStart.emit(target);
          });
        }
      });
    }
    if (marker.markersEvents.markerDrag.observed) {
      markerInstance.on("drag", (event) => {
        if (event) {
          const {
            target
          } = event;
          this.zone.run(() => {
            marker.markersEvents.markerDrag.emit(target);
          });
        }
      });
    }
    if (marker.markersEvents.markerDragEnd.observed) {
      markerInstance.on("dragend", (event) => {
        if (event) {
          const {
            target
          } = event;
          this.zone.run(() => {
            marker.markersEvents.markerDragEnd.emit(target);
          });
        }
      });
    }
    const lngLat = marker.markersOptions.feature ? marker.markersOptions.feature.geometry.coordinates : marker.markersOptions.lngLat;
    markerInstance.setLngLat(lngLat);
    return this.zone.runOutsideAngular(() => {
      markerInstance.addTo(this.mapInstance);
      return markerInstance;
    });
  }
  removeMarker(marker) {
    this.markersToRemove.push(marker);
  }
  createPopup(popup, element) {
    return this.zone.runOutsideAngular(() => {
      Object.keys(popup.popupOptions).forEach((key) => {
        const tkey = key;
        return popup.popupOptions[tkey] === void 0 && delete popup.popupOptions[tkey];
      });
      const popupInstance = new import_mapbox_gl.Popup(popup.popupOptions);
      popupInstance.setDOMContent(element);
      if (popup.popupEvents.popupClose.observed) {
        popupInstance.on("close", () => {
          this.zone.run(() => {
            popup.popupEvents.popupClose.emit();
          });
        });
      }
      if (popup.popupEvents.popupOpen.observed) {
        popupInstance.on("open", () => {
          this.zone.run(() => {
            popup.popupEvents.popupOpen.emit();
          });
        });
      }
      return popupInstance;
    });
  }
  addPopupToMap(popup, lngLat, skipOpenEvent = false) {
    return this.zone.runOutsideAngular(() => {
      if (skipOpenEvent && popup._listeners) {
        delete popup._listeners["open"];
      }
      popup.setLngLat(lngLat);
      popup.addTo(this.mapInstance);
    });
  }
  addPopupToMarker(marker, popup) {
    return this.zone.runOutsideAngular(() => {
      marker.setPopup(popup);
    });
  }
  removePopupFromMap(popup, skipCloseEvent = false) {
    if (skipCloseEvent && popup._listeners) {
      delete popup._listeners["close"];
    }
    this.popupsToRemove.push(popup);
  }
  removePopupFromMarker(marker) {
    return this.zone.runOutsideAngular(() => {
      marker.setPopup(void 0);
    });
  }
  addControl(control, position) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.addControl(control, position);
    });
  }
  removeControl(control) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.removeControl(control);
    });
  }
  loadAndAddImage(imageId, url, options) {
    return __async(this, null, function* () {
      return this.zone.runOutsideAngular(() => new Promise((resolve, reject) => {
        this.mapInstance.loadImage(url, (error, image) => {
          if (error) {
            reject(error);
            return;
          }
          if (!image) {
            reject(new Error("Image not loaded"));
            return;
          }
          this.addImage(imageId, image, options);
          resolve();
        });
      }));
    });
  }
  addImage(imageId, data, options) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.addImage(imageId, data, options);
    });
  }
  removeImage(imageId) {
    this.imageIdsToRemove.push(imageId);
  }
  addSource(sourceId, source) {
    return this.zone.runOutsideAngular(() => {
      Object.keys(source).forEach((key) => {
        const tkey = key;
        return source[tkey] === void 0 && delete source[tkey];
      });
      this.mapInstance.addSource(sourceId, source);
    });
  }
  getSource(sourceId) {
    return this.mapInstance.getSource(sourceId);
  }
  removeSource(sourceId) {
    this.zone.runOutsideAngular(() => {
      this.findLayersBySourceId(sourceId).forEach((layer) => this.mapInstance.removeLayer(layer.id));
      this.mapInstance.removeSource(sourceId);
    });
  }
  setLayerAllPaintProperty(layerId, paint) {
    return this.zone.runOutsideAngular(() => {
      Object.keys(paint).forEach((key) => {
        const tKey = key;
        this.mapInstance.setPaintProperty(layerId, tKey, paint[tKey]);
      });
    });
  }
  setLayerAllLayoutProperty(layerId, layout) {
    return this.zone.runOutsideAngular(() => {
      Object.keys(layout).forEach((key) => {
        const tKey = key;
        this.mapInstance.setLayoutProperty(layerId, tKey, layout[tKey]);
      });
    });
  }
  setLayerFilter(layerId, filter2) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setFilter(layerId, filter2);
    });
  }
  setLayerBefore(layerId, beforeId) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.moveLayer(layerId, beforeId);
    });
  }
  setLayerZoomRange(layerId, minZoom, maxZoom) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.setLayerZoomRange(layerId, minZoom ? minZoom : 0, maxZoom ? maxZoom : 20);
    });
  }
  fitBounds(bounds, options) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.fitBounds(bounds, options);
    });
  }
  fitScreenCoordinates(points, bearing, options) {
    return this.zone.runOutsideAngular(() => {
      this.mapInstance.fitScreenCoordinates(points[0], points[1], bearing, options);
    });
  }
  applyChanges() {
    this.zone.runOutsideAngular(() => {
      this.removeMarkers();
      this.removePopups();
      this.removeImages();
    });
  }
  createMap(options) {
    NgZone.assertNotInAngularZone();
    Object.keys(options).forEach((key) => {
      const tkey = key;
      if (options[tkey] === void 0) {
        delete options[tkey];
      }
    });
    this.mapInstance = new import_mapbox_gl.Map(options);
    afterRender({
      write: () => {
        this.applyChanges();
      }
    }, {
      injector: this.injector
    });
  }
  removeMarkers() {
    for (const marker of this.markersToRemove) {
      marker.remove();
    }
    this.markersToRemove = [];
  }
  removePopups() {
    for (const popup of this.popupsToRemove) {
      popup.remove();
    }
    this.popupsToRemove = [];
  }
  removeImages() {
    for (const imageId of this.imageIdsToRemove) {
      this.mapInstance.removeImage(imageId);
    }
    this.imageIdsToRemove = [];
  }
  findLayersBySourceId(sourceId) {
    const layers = this.mapInstance.getStyle().layers;
    if (layers == null) {
      return [];
    }
    return layers.filter((l) => "source" in l ? l.source === sourceId : false);
  }
  hookEvents(events) {
    this.mapInstance.on("load", (evt) => {
      this.mapLoaded.next(void 0);
      this.mapLoaded.complete();
      this.zone.run(() => {
        events.mapLoad.emit(evt);
      });
    });
    if (events.mapResize.observed) {
      this.mapInstance.on("resize", (evt) => this.zone.run(() => {
        events.mapResize.emit(evt);
      }));
    }
    if (events.mapRemove.observed) {
      this.mapInstance.on("remove", (evt) => this.zone.run(() => {
        events.mapRemove.emit(evt);
      }));
    }
    if (events.mapMouseDown.observed) {
      this.mapInstance.on("mousedown", (evt) => this.zone.run(() => {
        events.mapMouseDown.emit(evt);
      }));
    }
    if (events.mapMouseUp.observed) {
      this.mapInstance.on("mouseup", (evt) => this.zone.run(() => {
        events.mapMouseUp.emit(evt);
      }));
    }
    if (events.mapMouseMove.observed) {
      this.mapInstance.on("mousemove", (evt) => this.zone.run(() => {
        events.mapMouseMove.emit(evt);
      }));
    }
    if (events.mapClick.observed) {
      this.mapInstance.on("click", (evt) => this.zone.run(() => {
        events.mapClick.emit(evt);
      }));
    }
    if (events.mapDblClick.observed) {
      this.mapInstance.on("dblclick", (evt) => this.zone.run(() => {
        events.mapDblClick.emit(evt);
      }));
    }
    if (events.mapMouseOver.observed) {
      this.mapInstance.on("mouseover", (evt) => this.zone.run(() => {
        events.mapMouseOver.emit(evt);
      }));
    }
    if (events.mapMouseOut.observed) {
      this.mapInstance.on("mouseout", (evt) => this.zone.run(() => {
        events.mapMouseOut.emit(evt);
      }));
    }
    if (events.mapContextMenu.observed) {
      this.mapInstance.on("contextmenu", (evt) => this.zone.run(() => {
        events.mapContextMenu.emit(evt);
      }));
    }
    if (events.mapTouchStart.observed) {
      this.mapInstance.on("touchstart", (evt) => this.zone.run(() => {
        events.mapTouchStart.emit(evt);
      }));
    }
    if (events.mapTouchEnd.observed) {
      this.mapInstance.on("touchend", (evt) => this.zone.run(() => {
        events.mapTouchEnd.emit(evt);
      }));
    }
    if (events.mapTouchMove.observed) {
      this.mapInstance.on("touchmove", (evt) => this.zone.run(() => {
        events.mapTouchMove.emit(evt);
      }));
    }
    if (events.mapTouchCancel.observed) {
      this.mapInstance.on("touchcancel", (evt) => this.zone.run(() => {
        events.mapTouchCancel.emit(evt);
      }));
    }
    if (events.mapWheel.observed) {
      this.mapInstance.on("wheel", (evt) => this.zone.run(() => {
        events.mapWheel.emit(evt);
      }));
    }
    if (events.moveStart.observed) {
      this.mapInstance.on("movestart", (evt) => this.zone.run(() => events.moveStart.emit(evt)));
    }
    if (events.move.observed) {
      this.mapInstance.on("move", (evt) => this.zone.run(() => events.move.emit(evt)));
    }
    if (events.moveEnd.observed) {
      this.mapInstance.on("moveend", (evt) => this.zone.run(() => events.moveEnd.emit(evt)));
    }
    if (events.mapDragStart.observed) {
      this.mapInstance.on("dragstart", (evt) => this.zone.run(() => events.mapDragStart.emit(evt)));
    }
    if (events.mapDrag.observed) {
      this.mapInstance.on("drag", (evt) => this.zone.run(() => events.mapDrag.emit(evt)));
    }
    if (events.mapDragEnd.observed) {
      this.mapInstance.on("dragend", (evt) => this.zone.run(() => events.mapDragEnd.emit(evt)));
    }
    if (events.zoomStart.observed) {
      this.mapInstance.on("zoomstart", () => this.zone.run(() => events.zoomStart.emit()));
    }
    if (events.zoomEvt.observed) {
      this.mapInstance.on("zoom", () => this.zone.run(() => events.zoomEvt.emit()));
    }
    if (events.zoomEnd.observed) {
      this.mapInstance.on("zoomend", () => this.zone.run(() => events.zoomEnd.emit()));
    }
    if (events.rotateStart.observed) {
      this.mapInstance.on("rotatestart", (evt) => this.zone.run(() => events.rotateStart.emit(evt)));
    }
    if (events.rotate.observed) {
      this.mapInstance.on("rotate", (evt) => this.zone.run(() => events.rotate.emit(evt)));
    }
    if (events.rotateEnd.observed) {
      this.mapInstance.on("rotateend", (evt) => this.zone.run(() => events.rotateEnd.emit(evt)));
    }
    if (events.pitchStart.observed) {
      this.mapInstance.on("pitchstart", () => this.zone.run(() => events.pitchStart.emit()));
    }
    if (events.pitchEvt.observed) {
      this.mapInstance.on("pitch", () => this.zone.run(() => events.pitchEvt.emit()));
    }
    if (events.pitchEnd.observed) {
      this.mapInstance.on("pitchend", () => this.zone.run(() => events.pitchEnd.emit()));
    }
    if (events.boxZoomStart.observed) {
      this.mapInstance.on("boxzoomstart", (evt) => this.zone.run(() => events.boxZoomStart.emit(evt)));
    }
    if (events.boxZoomEnd.observed) {
      this.mapInstance.on("boxzoomend", (evt) => this.zone.run(() => events.boxZoomEnd.emit(evt)));
    }
    if (events.boxZoomCancel.observed) {
      this.mapInstance.on("boxzoomcancel", (evt) => this.zone.run(() => events.boxZoomCancel.emit(evt)));
    }
    if (events.webGlContextLost.observed) {
      this.mapInstance.on("webglcontextlost", (evt) => this.zone.run(() => events.webGlContextLost.emit(evt)));
    }
    if (events.webGlContextRestored.observed) {
      this.mapInstance.on("webglcontextrestored", (evt) => this.zone.run(() => events.webGlContextRestored.emit(evt)));
    }
    if (events.render.observed) {
      this.mapInstance.on("render", () => this.zone.run(() => events.render.emit()));
    }
    if (events.mapError.observed) {
      this.mapInstance.on("error", (evt) => this.zone.run(() => events.mapError.emit(evt.error)));
    }
    if (events.data.observed) {
      this.mapInstance.on("data", (evt) => this.zone.run(() => events.data.emit(evt)));
    }
    if (events.styleData.observed) {
      this.mapInstance.on("styledata", (evt) => this.zone.run(() => events.styleData.emit(evt)));
    }
    if (events.sourceData.observed) {
      this.mapInstance.on("sourcedata", (evt) => this.zone.run(() => events.sourceData.emit(evt)));
    }
    if (events.dataLoading.observed) {
      this.mapInstance.on("dataloading", (evt) => this.zone.run(() => events.dataLoading.emit(evt)));
    }
    if (events.styleDataLoading.observed) {
      this.mapInstance.on("styledataloading", (evt) => this.zone.run(() => events.styleDataLoading.emit(evt)));
    }
    if (events.sourceDataLoading.observed) {
      this.mapInstance.on("sourcedataloading", (evt) => this.zone.run(() => events.sourceDataLoading.emit(evt)));
    }
    if (events.styleImageMissing.observed) {
      this.mapInstance.on("styleimagemissing", (evt) => this.zone.run(() => events.styleImageMissing.emit(evt)));
    }
    if (events.idle.observed) {
      this.mapInstance.on("idle", () => this.zone.run(() => events.idle.emit()));
    }
  }
  static ɵfac = function MapService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MapService)();
  };
  static ɵprov = ɵɵdefineInjectable({
    token: _MapService,
    factory: _MapService.ɵfac
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MapService, [{
    type: Injectable
  }], () => [], null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/provide-mapbox-gl.mjs
function provideMapboxGL(config) {
  return {
    provide: MAPBOX_API_KEY,
    useValue: config.accessToken
  };
}

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/attribution-control.directive.mjs
var import_mapbox_gl2 = __toESM(require_mapbox_gl(), 1);

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/control.component.mjs
var _c0 = ["content"];
var _c1 = ["*"];
var CustomControl = class {
  container;
  constructor(container) {
    this.container = container;
  }
  onAdd() {
    return this.container;
  }
  onRemove() {
    return this.container.parentNode.removeChild(this.container);
  }
  getDefaultPosition() {
    return "top-right";
  }
};
var ControlComponent = class _ControlComponent {
  mapService = inject(MapService);
  /* Init inputs */
  position = input();
  content;
  control;
  controlAdded = false;
  ngAfterContentInit() {
    if (this.content.nativeElement.childNodes.length) {
      this.control = new CustomControl(this.content.nativeElement);
      this.mapService.mapCreated$.subscribe(() => {
        this.mapService.addControl(this.control, this.position());
        this.controlAdded = true;
      });
    }
  }
  ngOnDestroy() {
    if (this.controlAdded) {
      this.mapService.removeControl(this.control);
    }
  }
  static ɵfac = function ControlComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ControlComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _ControlComponent,
    selectors: [["mgl-control"]],
    viewQuery: function ControlComponent_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(_c0, 7);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
      }
    },
    inputs: {
      position: [1, "position"]
    },
    ngContentSelectors: _c1,
    decls: 3,
    vars: 0,
    consts: [["content", ""], [1, "mapboxgl-ctrl"]],
    template: function ControlComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵprojectionDef();
        ɵɵelementStart(0, "div", 1, 0);
        ɵɵprojection(2);
        ɵɵelementEnd();
      }
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ControlComponent, [{
    type: Component,
    args: [{
      selector: "mgl-control",
      template: `
    <div class="mapboxgl-ctrl" #content>
      <ng-content />
    </div>
  `,
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, {
    content: [{
      type: ViewChild,
      args: ["content", {
        static: true
      }]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/attribution-control.directive.mjs
var AttributionControlDirective = class _AttributionControlDirective {
  mapService = inject(MapService);
  controlComponent = inject(ControlComponent, {
    host: true
  });
  /* Init inputs */
  compact = input();
  customAttribution = input();
  ngAfterContentInit() {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.controlComponent.control) {
        throw new Error("Another control is already set for this control");
      }
      const options = {};
      const compact = this.compact();
      const customAttribution = this.customAttribution();
      if (compact !== void 0) {
        options.compact = compact;
      }
      if (customAttribution !== void 0) {
        options.customAttribution = customAttribution;
      }
      this.controlComponent.control = new import_mapbox_gl2.AttributionControl(options);
      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());
    });
  }
  static ɵfac = function AttributionControlDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AttributionControlDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _AttributionControlDirective,
    selectors: [["", "mglAttribution", ""]],
    inputs: {
      compact: [1, "compact"],
      customAttribution: [1, "customAttribution"]
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AttributionControlDirective, [{
    type: Directive,
    args: [{
      selector: "[mglAttribution]"
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/fullscreen-control.directive.mjs
var import_mapbox_gl3 = __toESM(require_mapbox_gl(), 1);
var FullscreenControlDirective = class _FullscreenControlDirective {
  mapService = inject(MapService);
  controlComponent = inject(ControlComponent, {
    host: true
  });
  /* Init inputs */
  container = input();
  onFullscreen() {
    this.mapService.mapInstance.resize();
  }
  ngAfterContentInit() {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.controlComponent.control) {
        throw new Error("Another control is already set for this control");
      }
      this.controlComponent.control = new import_mapbox_gl3.FullscreenControl({
        container: this.container()
      });
      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());
    });
  }
  static ɵfac = function FullscreenControlDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FullscreenControlDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _FullscreenControlDirective,
    selectors: [["", "mglFullscreen", ""]],
    hostBindings: function FullscreenControlDirective_HostBindings(rf, ctx) {
      if (rf & 1) {
        ɵɵlistener("webkitfullscreenchange", function FullscreenControlDirective_webkitfullscreenchange_HostBindingHandler($event) {
          return ctx.onFullscreen($event.target);
        }, false, ɵɵresolveWindow);
      }
    },
    inputs: {
      container: [1, "container"]
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FullscreenControlDirective, [{
    type: Directive,
    args: [{
      selector: "[mglFullscreen]"
    }]
  }], null, {
    onFullscreen: [{
      type: HostListener,
      args: ["window:webkitfullscreenchange", ["$event.target"]]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/geolocate-control.directive.mjs
var import_mapbox_gl4 = __toESM(require_mapbox_gl(), 1);
var GeolocateControlDirective = class _GeolocateControlDirective {
  mapService = inject(MapService);
  controlComponent = inject(ControlComponent, {
    host: true
  });
  /* Init inputs */
  positionOptions = input();
  fitBoundsOptions = input();
  trackUserLocation = input();
  showUserLocation = input();
  showUserHeading = input();
  geolocate = new EventEmitter();
  ngAfterContentInit() {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.controlComponent.control) {
        throw new Error("Another control is already set for this control");
      }
      const options = {
        positionOptions: this.positionOptions(),
        fitBoundsOptions: this.fitBoundsOptions(),
        trackUserLocation: this.trackUserLocation(),
        showUserLocation: this.showUserLocation(),
        showUserHeading: this.showUserHeading()
      };
      Object.keys(options).forEach((key) => {
        const tkey = key;
        if (options[tkey] === void 0) {
          delete options[tkey];
        }
      });
      this.controlComponent.control = new import_mapbox_gl4.GeolocateControl(options);
      this.controlComponent.control.on("geolocate", (data) => {
        this.geolocate.emit(data);
      });
      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());
    });
  }
  static ɵfac = function GeolocateControlDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _GeolocateControlDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _GeolocateControlDirective,
    selectors: [["", "mglGeolocate", ""]],
    inputs: {
      positionOptions: [1, "positionOptions"],
      fitBoundsOptions: [1, "fitBoundsOptions"],
      trackUserLocation: [1, "trackUserLocation"],
      showUserLocation: [1, "showUserLocation"],
      showUserHeading: [1, "showUserHeading"]
    },
    outputs: {
      geolocate: "geolocate"
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GeolocateControlDirective, [{
    type: Directive,
    args: [{
      selector: "[mglGeolocate]"
    }]
  }], null, {
    geolocate: [{
      type: Output
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/navigation-control.directive.mjs
var import_mapbox_gl5 = __toESM(require_mapbox_gl(), 1);
var NavigationControlDirective = class _NavigationControlDirective {
  mapService = inject(MapService);
  controlComponent = inject(ControlComponent, {
    host: true
  });
  /* Init inputs */
  showCompass = input();
  showZoom = input();
  ngAfterContentInit() {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.controlComponent.control) {
        throw new Error("Another control is already set for this control");
      }
      const options = {};
      const showCompass = this.showCompass();
      const showZoom = this.showZoom();
      if (showCompass !== void 0) {
        options.showCompass = showCompass;
      }
      if (showZoom !== void 0) {
        options.showZoom = showZoom;
      }
      this.controlComponent.control = new import_mapbox_gl5.NavigationControl(options);
      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());
    });
  }
  static ɵfac = function NavigationControlDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NavigationControlDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _NavigationControlDirective,
    selectors: [["", "mglNavigation", ""]],
    inputs: {
      showCompass: [1, "showCompass"],
      showZoom: [1, "showZoom"]
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NavigationControlDirective, [{
    type: Directive,
    args: [{
      selector: "[mglNavigation]"
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/scale-control.directive.mjs
var import_mapbox_gl6 = __toESM(require_mapbox_gl(), 1);
var ScaleControlDirective = class _ScaleControlDirective {
  mapService = inject(MapService);
  controlComponent = inject(ControlComponent, {
    host: true
  });
  /* Init inputs */
  maxWidth = input();
  /* Dynamic inputs */
  unit = input();
  ngOnChanges(changes) {
    if (changes["unit"] && !changes["unit"].isFirstChange()) {
      this.controlComponent.control.setUnit(changes["unit"].currentValue);
    }
  }
  ngAfterContentInit() {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.controlComponent.control) {
        throw new Error("Another control is already set for this control");
      }
      const options = {};
      const maxWidth = this.maxWidth();
      const unit = this.unit();
      if (maxWidth !== void 0) {
        options.maxWidth = maxWidth;
      }
      if (unit !== void 0) {
        options.unit = unit;
      }
      this.controlComponent.control = new import_mapbox_gl6.ScaleControl(options);
      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());
    });
  }
  static ɵfac = function ScaleControlDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ScaleControlDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _ScaleControlDirective,
    selectors: [["", "mglScale", ""]],
    inputs: {
      maxWidth: [1, "maxWidth"],
      unit: [1, "unit"]
    },
    features: [ɵɵNgOnChangesFeature]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ScaleControlDirective, [{
    type: Directive,
    args: [{
      selector: "[mglScale]"
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/geojson/geojson-source.component.mjs
var GeoJSONSourceComponent = class _GeoJSONSourceComponent {
  mapService = inject(MapService);
  zone = inject(NgZone);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  data = model();
  minzoom = input();
  maxzoom = input();
  attribution = input();
  buffer = input();
  tolerance = input();
  cluster = input();
  clusterRadius = input();
  clusterMaxZoom = input();
  clusterMinPoints = input();
  clusterProperties = input();
  lineMetrics = input();
  generateId = input();
  promoteId = input();
  filter = input();
  dynamic = input();
  updateFeatureData = new Subject();
  sub = new Subscription();
  sourceAdded = false;
  featureIdCounter = 0;
  ngOnInit() {
    if (!this.data()) {
      this.data.set({
        type: "FeatureCollection",
        features: []
      });
    }
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["minzoom"] && !changes["minzoom"].isFirstChange() || changes["maxzoom"] && !changes["maxzoom"].isFirstChange() || changes["attribution"] && !changes["attribution"].isFirstChange() || changes["buffer"] && !changes["buffer"].isFirstChange() || changes["tolerance"] && !changes["tolerance"].isFirstChange() || changes["cluster"] && !changes["cluster"].isFirstChange() || changes["clusterRadius"] && !changes["clusterRadius"].isFirstChange() || changes["clusterMaxZoom"] && !changes["clusterMaxZoom"].isFirstChange() || changes["clusterMinPoints"] && !changes["clusterMinPoints"].isFirstChange() || changes["clusterProperties"] && !changes["clusterProperties"].isFirstChange() || changes["lineMetrics"] && !changes["lineMetrics"].isFirstChange() || changes["generateId"] && !changes["generateId"].isFirstChange() || changes["promoteId"] && !changes["promoteId"].isFirstChange() || changes["filter"] && !changes["filter"].isFirstChange() || changes["dynamic"] && !changes["dynamic"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    }
    if (changes["data"] && !changes["data"].isFirstChange()) {
      const source = this.mapService.getSource(this.id());
      if (source === void 0) {
        return;
      }
      source.setData(this.data());
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  /**
   * For clustered sources, fetches the zoom at which the given cluster expands.
   *
   * @param clusterId The value of the cluster's cluster_id property.
   */
  getClusterExpansionZoom(clusterId) {
    return __async(this, null, function* () {
      const source = this.mapService.getSource(this.id());
      return this.zone.run(() => __async(this, null, function* () {
        return new Promise((resolve, reject) => {
          source.getClusterExpansionZoom(clusterId, (error, zoom) => {
            if (error) {
              reject(error);
            } else {
              resolve(zoom);
            }
          });
        });
      }));
    });
  }
  /**
   * For clustered sources, fetches the children of the given cluster on the next zoom level (as an array of GeoJSON features).
   *
   * @param clusterId The value of the cluster's cluster_id property.
   */
  getClusterChildren(clusterId) {
    return __async(this, null, function* () {
      const source = this.mapService.getSource(this.id());
      return this.zone.run(() => __async(this, null, function* () {
        return new Promise((resolve, reject) => {
          source.getClusterChildren(clusterId, (error, features) => {
            if (error) {
              reject(error);
            } else {
              resolve(features);
            }
          });
        });
      }));
    });
  }
  /**
   * For clustered sources, fetches the original points that belong to the cluster (as an array of GeoJSON features).
   *
   * @param clusterId The value of the cluster's cluster_id property.
   * @param limit The maximum number of features to return.
   * @param offset The number of features to skip (e.g. for pagination).
   */
  getClusterLeaves(clusterId, limit, offset) {
    return __async(this, null, function* () {
      const source = this.mapService.getSource(this.id());
      return this.zone.run(() => __async(this, null, function* () {
        return new Promise((resolve, reject) => {
          source.getClusterLeaves(clusterId, limit, offset, (error, features) => {
            if (error) {
              reject(error);
            } else {
              resolve(features || []);
            }
          });
        });
      }));
    });
  }
  _addFeature(feature) {
    const collection = this.data();
    collection.features.push(feature);
    this.updateFeatureData.next(null);
  }
  _removeFeature(feature) {
    const collection = this.data();
    const index = collection.features.indexOf(feature);
    if (index > -1) {
      collection.features.splice(index, 1);
    }
    this.updateFeatureData.next(null);
  }
  _getNewFeatureId() {
    return ++this.featureIdCounter;
  }
  init() {
    const source = {
      type: "geojson",
      data: this.data(),
      minzoom: this.minzoom(),
      maxzoom: this.maxzoom(),
      attribution: this.attribution(),
      buffer: this.buffer(),
      tolerance: this.tolerance(),
      cluster: this.cluster(),
      clusterRadius: this.clusterRadius(),
      clusterMaxZoom: this.clusterMaxZoom(),
      clusterMinPoints: this.clusterMinPoints(),
      clusterProperties: this.clusterProperties(),
      lineMetrics: this.lineMetrics(),
      generateId: this.generateId(),
      promoteId: this.promoteId(),
      filter: this.filter(),
      dynamic: this.dynamic()
    };
    this.mapService.addSource(this.id(), source);
    const sub = this.updateFeatureData.pipe(debounceTime(0)).subscribe(() => {
      const source2 = this.mapService.getSource(this.id());
      if (source2 === void 0) {
        return;
      }
      source2.setData(this.data());
    });
    this.sub.add(sub);
    this.sourceAdded = true;
  }
  static ɵfac = function GeoJSONSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _GeoJSONSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _GeoJSONSourceComponent,
    selectors: [["mgl-geojson-source"]],
    inputs: {
      id: [1, "id"],
      data: [1, "data"],
      minzoom: [1, "minzoom"],
      maxzoom: [1, "maxzoom"],
      attribution: [1, "attribution"],
      buffer: [1, "buffer"],
      tolerance: [1, "tolerance"],
      cluster: [1, "cluster"],
      clusterRadius: [1, "clusterRadius"],
      clusterMaxZoom: [1, "clusterMaxZoom"],
      clusterMinPoints: [1, "clusterMinPoints"],
      clusterProperties: [1, "clusterProperties"],
      lineMetrics: [1, "lineMetrics"],
      generateId: [1, "generateId"],
      promoteId: [1, "promoteId"],
      filter: [1, "filter"],
      dynamic: [1, "dynamic"]
    },
    outputs: {
      data: "dataChange"
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function GeoJSONSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GeoJSONSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-geojson-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/geojson/feature.component.mjs
var FeatureComponent = class _FeatureComponent {
  GeoJSONSourceComponent = inject(forwardRef(() => GeoJSONSourceComponent));
  /* Init inputs */
  id = model();
  // FIXME number only for now https://github.com/mapbox/mapbox-gl-js/issues/2716
  geometry = input.required();
  properties = input();
  type = "Feature";
  feature;
  ngOnInit() {
    if (!this.id()) {
      this.id.set(this.GeoJSONSourceComponent._getNewFeatureId());
    }
    this.feature = {
      type: this.type,
      geometry: this.geometry(),
      properties: this.properties() ? this.properties() : {}
    };
    this.feature.id = this.id();
    this.GeoJSONSourceComponent._addFeature(this.feature);
  }
  ngOnDestroy() {
    this.GeoJSONSourceComponent._removeFeature(this.feature);
  }
  updateCoordinates(coordinates) {
    this.feature.geometry.coordinates = coordinates;
    this.GeoJSONSourceComponent.updateFeatureData.next(null);
  }
  static ɵfac = function FeatureComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FeatureComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _FeatureComponent,
    selectors: [["mgl-feature"]],
    inputs: {
      id: [1, "id"],
      geometry: [1, "geometry"],
      properties: [1, "properties"]
    },
    outputs: {
      id: "idChange"
    },
    decls: 0,
    vars: 0,
    template: function FeatureComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FeatureComponent, [{
    type: Component,
    args: [{
      selector: "mgl-feature",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/draggable/draggable.directive.mjs
var DraggableDirective = class _DraggableDirective {
  mapService = inject(MapService);
  ngZone = inject(NgZone);
  featureComponent = inject(FeatureComponent, {
    optional: true,
    host: true
  });
  layer = input(void 0, {
    alias: "mglDraggable"
  });
  featureDragStart = new EventEmitter();
  featureDragEnd = new EventEmitter();
  featureDrag = new EventEmitter();
  sub = new Subscription();
  ngOnInit() {
    let enter$;
    let leave$;
    let updateCoords;
    const layer = this.layer();
    if (this.featureComponent && layer) {
      enter$ = layer.layerMouseEnter;
      leave$ = layer.layerMouseLeave;
      updateCoords = this.featureComponent.updateCoordinates.bind(this.featureComponent);
      if (this.featureComponent.geometry().type !== "Point") {
        throw new Error("mglDraggable only support point feature");
      }
    } else {
      throw new Error("mglDraggable can only be used on Feature (with a layer as input) or Marker");
    }
    this.handleDraggable(enter$, leave$, updateCoords);
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
  }
  handleDraggable(enter$, leave$, updateCoords) {
    let moving = false;
    let inside = false;
    this.mapService.mapCreated$.subscribe(() => {
      const mouseUp$ = fromEvent(this.mapService.mapInstance, "mouseup");
      const dragStart$ = enter$.pipe(filter(() => !moving), filter((evt) => this.filterFeature(evt)), tap(() => {
        inside = true;
        this.mapService.changeCanvasCursor("move");
        this.mapService.updateDragPan(false);
      }), switchMap(() => fromEvent(this.mapService.mapInstance, "mousedown").pipe(takeUntil(leave$))));
      const dragging$ = dragStart$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, "mousemove").pipe(takeUntil(mouseUp$))));
      const dragEnd$ = dragStart$.pipe(switchMap(() => mouseUp$.pipe(take(1))));
      this.sub.add(dragStart$.subscribe((evt) => {
        moving = true;
        if (this.featureDragStart.observed) {
          this.ngZone.run(() => {
            this.featureDragStart.emit(evt);
          });
        }
      }));
      this.sub.add(dragging$.subscribe((evt) => {
        updateCoords([evt.lngLat.lng, evt.lngLat.lat]);
        if (this.featureDrag.observed) {
          this.ngZone.run(() => {
            this.featureDrag.emit(evt);
          });
        }
      }));
      this.sub.add(dragEnd$.subscribe((evt) => {
        moving = false;
        if (this.featureDragEnd.observed) {
          this.ngZone.run(() => {
            this.featureDragEnd.emit(evt);
          });
        }
        if (!inside) {
          this.mapService.changeCanvasCursor("");
          this.mapService.updateDragPan(true);
        }
      }));
      this.sub.add(leave$.pipe(tap(() => inside = false), filter(() => !moving)).subscribe(() => {
        this.mapService.changeCanvasCursor("");
        this.mapService.updateDragPan(true);
      }));
    });
  }
  filterFeature(evt) {
    const layer = this.layer();
    if (this.featureComponent && layer) {
      const feature = this.mapService.queryRenderedFeatures(evt.point, {
        layers: [layer.id()],
        filter: ["all", ["==", "$type", "Point"], ["==", "$id", this.featureComponent.id()]]
      })[0];
      if (!feature) {
        return false;
      }
    }
    return true;
  }
  static ɵfac = function DraggableDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DraggableDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _DraggableDirective,
    selectors: [["", "mglDraggable", ""]],
    inputs: {
      layer: [1, "mglDraggable", "layer"]
    },
    outputs: {
      featureDragStart: "featureDragStart",
      featureDragEnd: "featureDragEnd",
      featureDrag: "featureDrag"
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DraggableDirective, [{
    type: Directive,
    args: [{
      selector: "[mglDraggable]"
    }]
  }], null, {
    featureDragStart: [{
      type: Output
    }],
    featureDragEnd: [{
      type: Output
    }],
    featureDrag: [{
      type: Output
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/image/image.component.mjs
var ImageComponent = class _ImageComponent {
  mapService = inject(MapService);
  zone = inject(NgZone);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  data = input();
  options = input();
  url = input();
  imageError = new EventEmitter();
  imageLoaded = new EventEmitter();
  isAdded = false;
  isAdding = false;
  sub;
  ngOnInit() {
    this.sub = this.mapService.mapLoaded$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, "styledata").pipe(startWith(void 0), filter(() => !this.isAdding && !this.mapService.mapInstance.hasImage(this.id()))))).subscribe(() => this.init());
  }
  ngOnChanges(changes) {
    if (changes["data"] && !changes["data"].isFirstChange() || changes["options"] && !changes["options"].isFirstChange() || changes["url"] && !changes["url"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    }
  }
  ngOnDestroy() {
    if (this.isAdded) {
      this.mapService.removeImage(this.id());
    }
    if (this.sub) {
      this.sub.unsubscribe();
    }
  }
  init() {
    return __async(this, null, function* () {
      this.isAdding = true;
      if (this.data()) {
        this.mapService.addImage(this.id(), this.data(), this.options());
        this.isAdded = true;
        this.isAdding = false;
      } else if (this.url()) {
        try {
          yield this.mapService.loadAndAddImage(this.id(), this.url(), this.options());
          this.isAdded = true;
          this.isAdding = false;
          this.zone.run(() => {
            this.imageLoaded.emit();
          });
        } catch (error) {
          this.zone.run(() => {
            this.imageError.emit(error);
          });
        }
      }
    });
  }
  static ɵfac = function ImageComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ImageComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _ImageComponent,
    selectors: [["mgl-image"]],
    inputs: {
      id: [1, "id"],
      data: [1, "data"],
      options: [1, "options"],
      url: [1, "url"]
    },
    outputs: {
      imageError: "imageError",
      imageLoaded: "imageLoaded"
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function ImageComponent_Template(rf, ctx) {
    },
    encapsulation: 2
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ImageComponent, [{
    type: Component,
    args: [{
      selector: "mgl-image",
      template: ""
    }]
  }], null, {
    imageError: [{
      type: Output
    }],
    imageLoaded: [{
      type: Output
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/layer/layer.component.mjs
var LayerComponent = class _LayerComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  source = input();
  type = input.required();
  metadata = input();
  sourceLayer = input();
  /* Dynamic inputs */
  filter = input();
  layout = input();
  paint = input();
  before = input();
  minzoom = input();
  maxzoom = input();
  layerClick = new EventEmitter();
  layerDblClick = new EventEmitter();
  layerMouseDown = new EventEmitter();
  layerMouseUp = new EventEmitter();
  layerMouseEnter = new EventEmitter();
  layerMouseLeave = new EventEmitter();
  layerMouseMove = new EventEmitter();
  layerMouseOver = new EventEmitter();
  layerMouseOut = new EventEmitter();
  layerContextMenu = new EventEmitter();
  layerTouchStart = new EventEmitter();
  layerTouchEnd = new EventEmitter();
  layerTouchCancel = new EventEmitter();
  layerAdded = false;
  sub;
  ngOnInit() {
    this.sub = this.mapService.mapLoaded$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, "styledata").pipe(map(() => false), filter(() => !this.mapService.mapInstance.getLayer(this.id())), startWith(true)))).subscribe((bindEvents) => this.init(bindEvents));
  }
  ngOnChanges(changes) {
    if (!this.layerAdded) {
      return;
    }
    if (changes["paint"] && !changes["paint"].isFirstChange()) {
      this.mapService.setLayerAllPaintProperty(this.id(), changes["paint"].currentValue);
    }
    if (changes["layout"] && !changes["layout"].isFirstChange()) {
      this.mapService.setLayerAllLayoutProperty(this.id(), changes["layout"].currentValue);
    }
    if (changes["filter"] && !changes["filter"].isFirstChange()) {
      this.mapService.setLayerFilter(this.id(), changes["filter"].currentValue);
    }
    if (changes["before"] && !changes["before"].isFirstChange()) {
      this.mapService.setLayerBefore(this.id(), changes["before"].currentValue);
    }
    if (changes["minzoom"] && !changes["minzoom"].isFirstChange() || changes["maxzoom"] && !changes["maxzoom"].isFirstChange()) {
      this.mapService.setLayerZoomRange(this.id(), this.minzoom(), this.maxzoom());
    }
  }
  ngOnDestroy() {
    if (this.layerAdded) {
      this.mapService.removeLayer(this.id());
    }
    if (this.sub) {
      this.sub.unsubscribe();
    }
  }
  init(bindEvents) {
    const layer = {
      layerOptions: {
        id: this.id(),
        type: this.type(),
        source: this.source(),
        metadata: this.metadata(),
        "source-layer": this.sourceLayer(),
        minzoom: this.minzoom(),
        maxzoom: this.maxzoom(),
        filter: this.filter(),
        layout: this.layout(),
        paint: this.paint()
      },
      layerEvents: {
        layerClick: this.layerClick,
        layerDblClick: this.layerDblClick,
        layerMouseDown: this.layerMouseDown,
        layerMouseUp: this.layerMouseUp,
        layerMouseEnter: this.layerMouseEnter,
        layerMouseLeave: this.layerMouseLeave,
        layerMouseMove: this.layerMouseMove,
        layerMouseOver: this.layerMouseOver,
        layerMouseOut: this.layerMouseOut,
        layerContextMenu: this.layerContextMenu,
        layerTouchStart: this.layerTouchStart,
        layerTouchEnd: this.layerTouchEnd,
        layerTouchCancel: this.layerTouchCancel
      }
    };
    this.mapService.addLayer(layer, bindEvents, this.before());
    this.layerAdded = true;
  }
  static ɵfac = function LayerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LayerComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _LayerComponent,
    selectors: [["mgl-layer"]],
    inputs: {
      id: [1, "id"],
      source: [1, "source"],
      type: [1, "type"],
      metadata: [1, "metadata"],
      sourceLayer: [1, "sourceLayer"],
      filter: [1, "filter"],
      layout: [1, "layout"],
      paint: [1, "paint"],
      before: [1, "before"],
      minzoom: [1, "minzoom"],
      maxzoom: [1, "maxzoom"]
    },
    outputs: {
      layerClick: "layerClick",
      layerDblClick: "layerDblClick",
      layerMouseDown: "layerMouseDown",
      layerMouseUp: "layerMouseUp",
      layerMouseEnter: "layerMouseEnter",
      layerMouseLeave: "layerMouseLeave",
      layerMouseMove: "layerMouseMove",
      layerMouseOver: "layerMouseOver",
      layerMouseOut: "layerMouseOut",
      layerContextMenu: "layerContextMenu",
      layerTouchStart: "layerTouchStart",
      layerTouchEnd: "layerTouchEnd",
      layerTouchCancel: "layerTouchCancel"
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function LayerComponent_Template(rf, ctx) {
    },
    encapsulation: 2
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LayerComponent, [{
    type: Component,
    args: [{
      selector: "mgl-layer",
      template: ""
    }]
  }], null, {
    layerClick: [{
      type: Output
    }],
    layerDblClick: [{
      type: Output
    }],
    layerMouseDown: [{
      type: Output
    }],
    layerMouseUp: [{
      type: Output
    }],
    layerMouseEnter: [{
      type: Output
    }],
    layerMouseLeave: [{
      type: Output
    }],
    layerMouseMove: [{
      type: Output
    }],
    layerMouseOver: [{
      type: Output
    }],
    layerMouseOut: [{
      type: Output
    }],
    layerContextMenu: [{
      type: Output
    }],
    layerTouchStart: [{
      type: Output
    }],
    layerTouchEnd: [{
      type: Output
    }],
    layerTouchCancel: [{
      type: Output
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/map/map.component.mjs
var _c02 = ["container"];
var MapComponent = class _MapComponent {
  mapService = inject(MapService);
  /* Init inputs */
  accessToken = input();
  collectResourceTiming = input();
  crossSourceCollisions = input();
  fadeDuration = input();
  hash = input();
  refreshExpiredTiles = input();
  failIfMajorPerformanceCaveat = input();
  bearingSnap = input();
  interactive = input();
  pitchWithRotate = input();
  clickTolerance = input();
  attributionControl = input();
  logoPosition = input();
  maxTileCacheSize = input();
  localIdeographFontFamily = input();
  preserveDrawingBuffer = input();
  trackResize = input();
  transformRequest = input();
  bounds = input();
  // Use fitBounds for dynamic input
  antialias = input();
  locale = input();
  cooperativeGestures = input();
  /* Dynamic inputs */
  minZoom = input();
  maxZoom = input();
  minPitch = input();
  maxPitch = input();
  scrollZoom = input();
  dragRotate = input();
  touchPitch = input();
  touchZoomRotate = input();
  doubleClickZoom = input();
  keyboard = input();
  dragPan = input();
  boxZoom = input();
  style = input();
  center = input();
  maxBounds = input();
  zoom = input();
  bearing = input();
  pitch = input();
  // First value goes to options.fitBoundsOptions. Subsequents changes are passed to fitBounds
  fitBoundsOptions = input();
  renderWorldCopies = input();
  projection = input();
  /* Added by ngx-mapbox-gl */
  movingMethod = input("flyTo");
  movingOptions = input();
  // => First value is a alias to bounds input (since mapbox 0.53.0). Subsequents changes are passed to fitBounds
  fitBounds = input();
  fitScreenCoordinates = input();
  centerWithPanTo = input();
  panToOptions = input();
  cursorStyle = input();
  // resizeEmitter = new Subject<MapEvent>();
  // mapResize = outputFromObservable(this.mapResizeEmitter);
  mapResize = new EventEmitter();
  mapRemove = new EventEmitter();
  mapMouseDown = new EventEmitter();
  mapMouseUp = new EventEmitter();
  mapMouseMove = new EventEmitter();
  mapClick = new EventEmitter();
  mapDblClick = new EventEmitter();
  mapMouseOver = new EventEmitter();
  mapMouseOut = new EventEmitter();
  mapContextMenu = new EventEmitter();
  mapTouchStart = new EventEmitter();
  mapTouchEnd = new EventEmitter();
  mapTouchMove = new EventEmitter();
  mapTouchCancel = new EventEmitter();
  mapWheel = new EventEmitter();
  moveStart = new EventEmitter();
  move = new EventEmitter();
  moveEnd = new EventEmitter();
  mapDragStart = new EventEmitter();
  mapDrag = new EventEmitter();
  mapDragEnd = new EventEmitter();
  zoomStart = new EventEmitter();
  zoomEvt = new EventEmitter();
  zoomEnd = new EventEmitter();
  rotateStart = new EventEmitter();
  rotate = new EventEmitter();
  rotateEnd = new EventEmitter();
  pitchStart = new EventEmitter();
  pitchEvt = new EventEmitter();
  pitchEnd = new EventEmitter();
  boxZoomStart = new EventEmitter();
  boxZoomEnd = new EventEmitter();
  boxZoomCancel = new EventEmitter();
  webGlContextLost = new EventEmitter();
  webGlContextRestored = new EventEmitter();
  mapLoad = new EventEmitter();
  mapCreate = new EventEmitter();
  idle = new EventEmitter();
  render = new EventEmitter();
  mapError = new EventEmitter();
  data = new EventEmitter();
  styleData = new EventEmitter();
  sourceData = new EventEmitter();
  dataLoading = new EventEmitter();
  styleDataLoading = new EventEmitter();
  sourceDataLoading = new EventEmitter();
  styleImageMissing = new EventEmitter();
  get mapInstance() {
    return this.mapService.mapInstance;
  }
  mapContainer;
  constructor() {
    afterNextRender(() => {
      this.mapService.setup({
        accessToken: this.accessToken(),
        mapOptions: {
          collectResourceTiming: this.collectResourceTiming(),
          container: this.mapContainer.nativeElement,
          crossSourceCollisions: this.crossSourceCollisions(),
          fadeDuration: this.fadeDuration(),
          minZoom: this.minZoom(),
          maxZoom: this.maxZoom(),
          minPitch: this.minPitch(),
          maxPitch: this.maxPitch(),
          style: this.style(),
          hash: this.hash(),
          interactive: this.interactive(),
          bearingSnap: this.bearingSnap(),
          pitchWithRotate: this.pitchWithRotate(),
          clickTolerance: this.clickTolerance(),
          attributionControl: this.attributionControl(),
          logoPosition: this.logoPosition(),
          failIfMajorPerformanceCaveat: this.failIfMajorPerformanceCaveat(),
          preserveDrawingBuffer: this.preserveDrawingBuffer(),
          refreshExpiredTiles: this.refreshExpiredTiles(),
          maxBounds: this.maxBounds(),
          scrollZoom: this.scrollZoom(),
          boxZoom: this.boxZoom(),
          dragRotate: this.dragRotate(),
          dragPan: this.dragPan(),
          keyboard: this.keyboard(),
          doubleClickZoom: this.doubleClickZoom(),
          touchPitch: this.touchPitch(),
          touchZoomRotate: this.touchZoomRotate(),
          trackResize: this.trackResize(),
          center: this.center(),
          zoom: this.zoom(),
          bearing: this.bearing(),
          pitch: this.pitch(),
          renderWorldCopies: this.renderWorldCopies(),
          maxTileCacheSize: this.maxTileCacheSize(),
          localIdeographFontFamily: this.localIdeographFontFamily(),
          transformRequest: this.transformRequest(),
          bounds: this.bounds() ? this.bounds() : this.fitBounds(),
          fitBoundsOptions: this.fitBoundsOptions(),
          antialias: this.antialias(),
          locale: this.locale(),
          cooperativeGestures: this.cooperativeGestures(),
          projection: this.projection()
        },
        mapEvents: this
      });
      if (this.cursorStyle()) {
        this.mapService.changeCanvasCursor(this.cursorStyle());
      }
    });
  }
  ngOnDestroy() {
    this.mapService.destroyMap();
  }
  ngOnChanges(changes) {
    return __async(this, null, function* () {
      yield lastValueFrom(this.mapService.mapCreated$);
      if (changes["cursorStyle"] && !changes["cursorStyle"].isFirstChange()) {
        this.mapService.changeCanvasCursor(changes["cursorStyle"].currentValue);
      }
      if (changes["projection"] && !changes["projection"].isFirstChange()) {
        this.mapService.updateProjection(changes["projection"].currentValue);
      }
      if (changes["minZoom"] && !changes["minZoom"].isFirstChange()) {
        this.mapService.updateMinZoom(changes["minZoom"].currentValue);
      }
      if (changes["maxZoom"] && !changes["maxZoom"].isFirstChange()) {
        this.mapService.updateMaxZoom(changes["maxZoom"].currentValue);
      }
      if (changes["minPitch"] && !changes["minPitch"].isFirstChange()) {
        this.mapService.updateMinPitch(changes["minPitch"].currentValue);
      }
      if (changes["maxPitch"] && !changes["maxPitch"].isFirstChange()) {
        this.mapService.updateMaxPitch(changes["maxPitch"].currentValue);
      }
      if (changes["renderWorldCopies"] && !changes["renderWorldCopies"].isFirstChange()) {
        this.mapService.updateRenderWorldCopies(changes["renderWorldCopies"].currentValue);
      }
      if (changes["scrollZoom"] && !changes["scrollZoom"].isFirstChange()) {
        this.mapService.updateScrollZoom(changes["scrollZoom"].currentValue);
      }
      if (changes["dragRotate"] && !changes["dragRotate"].isFirstChange()) {
        this.mapService.updateDragRotate(changes["dragRotate"].currentValue);
      }
      if (changes["touchPitch"] && !changes["touchPitch"].isFirstChange()) {
        this.mapService.updateTouchPitch(changes["touchPitch"].currentValue);
      }
      if (changes["touchZoomRotate"] && !changes["touchZoomRotate"].isFirstChange()) {
        this.mapService.updateTouchZoomRotate(changes["touchZoomRotate"].currentValue);
      }
      if (changes["doubleClickZoom"] && !changes["doubleClickZoom"].isFirstChange()) {
        this.mapService.updateDoubleClickZoom(changes["doubleClickZoom"].currentValue);
      }
      if (changes["keyboard"] && !changes["keyboard"].isFirstChange()) {
        this.mapService.updateKeyboard(changes["keyboard"].currentValue);
      }
      if (changes["dragPan"] && !changes["dragPan"].isFirstChange()) {
        this.mapService.updateDragPan(changes["dragPan"].currentValue);
      }
      if (changes["boxZoom"] && !changes["boxZoom"].isFirstChange()) {
        this.mapService.updateBoxZoom(changes["boxZoom"].currentValue);
      }
      if (changes["style"] && !changes["style"].isFirstChange()) {
        this.mapService.updateStyle(changes["style"].currentValue);
      }
      if (changes["maxBounds"] && !changes["maxBounds"].isFirstChange()) {
        this.mapService.updateMaxBounds(changes["maxBounds"].currentValue);
      }
      if (changes["fitBounds"] && changes["fitBounds"].currentValue && !changes["fitBounds"].isFirstChange()) {
        this.mapService.fitBounds(changes["fitBounds"].currentValue, this.fitBoundsOptions());
      }
      if (changes["fitScreenCoordinates"] && changes["fitScreenCoordinates"].currentValue) {
        if ((this.center() || this.zoom() || this.pitch() || this.fitBounds()) && changes["fitScreenCoordinates"].isFirstChange()) {
          console.warn("[ngx-mapbox-gl] center / zoom / pitch / fitBounds inputs are being overridden by fitScreenCoordinates input");
        }
        this.mapService.fitScreenCoordinates(changes["fitScreenCoordinates"].currentValue, this.bearing() ? this.bearing()[0] : 0, this.movingOptions());
      }
      if (this.centerWithPanTo() && changes["center"] && !changes["center"].isFirstChange() && !changes["zoom"] && !changes["bearing"] && !changes["pitch"]) {
        this.mapService.panTo(this.center(), this.panToOptions());
      } else if (changes["center"] && !changes["center"].isFirstChange() || changes["zoom"] && !changes["zoom"].isFirstChange() || changes["bearing"] && !changes["bearing"].isFirstChange() && !changes["fitScreenCoordinates"] || changes["pitch"] && !changes["pitch"].isFirstChange()) {
        this.mapService.move(this.movingMethod(), this.movingOptions(), changes["zoom"] && this.zoom() ? this.zoom()[0] : void 0, changes["center"] ? this.center() : void 0, changes["bearing"] && this.bearing() ? this.bearing()[0] : void 0, changes["pitch"] && this.pitch() ? this.pitch()[0] : void 0);
      }
    });
  }
  static ɵfac = function MapComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MapComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _MapComponent,
    selectors: [["mgl-map"]],
    viewQuery: function MapComponent_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(_c02, 7);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.mapContainer = _t.first);
      }
    },
    inputs: {
      accessToken: [1, "accessToken"],
      collectResourceTiming: [1, "collectResourceTiming"],
      crossSourceCollisions: [1, "crossSourceCollisions"],
      fadeDuration: [1, "fadeDuration"],
      hash: [1, "hash"],
      refreshExpiredTiles: [1, "refreshExpiredTiles"],
      failIfMajorPerformanceCaveat: [1, "failIfMajorPerformanceCaveat"],
      bearingSnap: [1, "bearingSnap"],
      interactive: [1, "interactive"],
      pitchWithRotate: [1, "pitchWithRotate"],
      clickTolerance: [1, "clickTolerance"],
      attributionControl: [1, "attributionControl"],
      logoPosition: [1, "logoPosition"],
      maxTileCacheSize: [1, "maxTileCacheSize"],
      localIdeographFontFamily: [1, "localIdeographFontFamily"],
      preserveDrawingBuffer: [1, "preserveDrawingBuffer"],
      trackResize: [1, "trackResize"],
      transformRequest: [1, "transformRequest"],
      bounds: [1, "bounds"],
      antialias: [1, "antialias"],
      locale: [1, "locale"],
      cooperativeGestures: [1, "cooperativeGestures"],
      minZoom: [1, "minZoom"],
      maxZoom: [1, "maxZoom"],
      minPitch: [1, "minPitch"],
      maxPitch: [1, "maxPitch"],
      scrollZoom: [1, "scrollZoom"],
      dragRotate: [1, "dragRotate"],
      touchPitch: [1, "touchPitch"],
      touchZoomRotate: [1, "touchZoomRotate"],
      doubleClickZoom: [1, "doubleClickZoom"],
      keyboard: [1, "keyboard"],
      dragPan: [1, "dragPan"],
      boxZoom: [1, "boxZoom"],
      style: [1, "style"],
      center: [1, "center"],
      maxBounds: [1, "maxBounds"],
      zoom: [1, "zoom"],
      bearing: [1, "bearing"],
      pitch: [1, "pitch"],
      fitBoundsOptions: [1, "fitBoundsOptions"],
      renderWorldCopies: [1, "renderWorldCopies"],
      projection: [1, "projection"],
      movingMethod: [1, "movingMethod"],
      movingOptions: [1, "movingOptions"],
      fitBounds: [1, "fitBounds"],
      fitScreenCoordinates: [1, "fitScreenCoordinates"],
      centerWithPanTo: [1, "centerWithPanTo"],
      panToOptions: [1, "panToOptions"],
      cursorStyle: [1, "cursorStyle"]
    },
    outputs: {
      mapResize: "mapResize",
      mapRemove: "mapRemove",
      mapMouseDown: "mapMouseDown",
      mapMouseUp: "mapMouseUp",
      mapMouseMove: "mapMouseMove",
      mapClick: "mapClick",
      mapDblClick: "mapDblClick",
      mapMouseOver: "mapMouseOver",
      mapMouseOut: "mapMouseOut",
      mapContextMenu: "mapContextMenu",
      mapTouchStart: "mapTouchStart",
      mapTouchEnd: "mapTouchEnd",
      mapTouchMove: "mapTouchMove",
      mapTouchCancel: "mapTouchCancel",
      mapWheel: "mapWheel",
      moveStart: "moveStart",
      move: "move",
      moveEnd: "moveEnd",
      mapDragStart: "mapDragStart",
      mapDrag: "mapDrag",
      mapDragEnd: "mapDragEnd",
      zoomStart: "zoomStart",
      zoomEvt: "zoomEvt",
      zoomEnd: "zoomEnd",
      rotateStart: "rotateStart",
      rotate: "rotate",
      rotateEnd: "rotateEnd",
      pitchStart: "pitchStart",
      pitchEvt: "pitchEvt",
      pitchEnd: "pitchEnd",
      boxZoomStart: "boxZoomStart",
      boxZoomEnd: "boxZoomEnd",
      boxZoomCancel: "boxZoomCancel",
      webGlContextLost: "webGlContextLost",
      webGlContextRestored: "webGlContextRestored",
      mapLoad: "mapLoad",
      mapCreate: "mapCreate",
      idle: "idle",
      render: "render",
      mapError: "mapError",
      data: "data",
      styleData: "styleData",
      sourceData: "sourceData",
      dataLoading: "dataLoading",
      styleDataLoading: "styleDataLoading",
      sourceDataLoading: "sourceDataLoading",
      styleImageMissing: "styleImageMissing"
    },
    features: [ɵɵProvidersFeature([MapService]), ɵɵNgOnChangesFeature],
    decls: 2,
    vars: 0,
    consts: [["container", ""]],
    template: function MapComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵelement(0, "div", null, 0);
      }
    },
    styles: ["[_nghost-%COMP%]{display:block}div[_ngcontent-%COMP%]{height:100%;width:100%}"],
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MapComponent, [{
    type: Component,
    args: [{
      selector: "mgl-map",
      template: "<div #container></div>",
      providers: [MapService],
      standalone: true,
      changeDetection: ChangeDetectionStrategy.OnPush,
      styles: [":host{display:block}div{height:100%;width:100%}\n"]
    }]
  }], () => [], {
    mapResize: [{
      type: Output
    }],
    mapRemove: [{
      type: Output
    }],
    mapMouseDown: [{
      type: Output
    }],
    mapMouseUp: [{
      type: Output
    }],
    mapMouseMove: [{
      type: Output
    }],
    mapClick: [{
      type: Output
    }],
    mapDblClick: [{
      type: Output
    }],
    mapMouseOver: [{
      type: Output
    }],
    mapMouseOut: [{
      type: Output
    }],
    mapContextMenu: [{
      type: Output
    }],
    mapTouchStart: [{
      type: Output
    }],
    mapTouchEnd: [{
      type: Output
    }],
    mapTouchMove: [{
      type: Output
    }],
    mapTouchCancel: [{
      type: Output
    }],
    mapWheel: [{
      type: Output
    }],
    moveStart: [{
      type: Output
    }],
    move: [{
      type: Output
    }],
    moveEnd: [{
      type: Output
    }],
    mapDragStart: [{
      type: Output
    }],
    mapDrag: [{
      type: Output
    }],
    mapDragEnd: [{
      type: Output
    }],
    zoomStart: [{
      type: Output
    }],
    zoomEvt: [{
      type: Output
    }],
    zoomEnd: [{
      type: Output
    }],
    rotateStart: [{
      type: Output
    }],
    rotate: [{
      type: Output
    }],
    rotateEnd: [{
      type: Output
    }],
    pitchStart: [{
      type: Output
    }],
    pitchEvt: [{
      type: Output
    }],
    pitchEnd: [{
      type: Output
    }],
    boxZoomStart: [{
      type: Output
    }],
    boxZoomEnd: [{
      type: Output
    }],
    boxZoomCancel: [{
      type: Output
    }],
    webGlContextLost: [{
      type: Output
    }],
    webGlContextRestored: [{
      type: Output
    }],
    mapLoad: [{
      type: Output
    }],
    mapCreate: [{
      type: Output
    }],
    idle: [{
      type: Output
    }],
    render: [{
      type: Output
    }],
    mapError: [{
      type: Output
    }],
    data: [{
      type: Output
    }],
    styleData: [{
      type: Output
    }],
    sourceData: [{
      type: Output
    }],
    dataLoading: [{
      type: Output
    }],
    styleDataLoading: [{
      type: Output
    }],
    sourceDataLoading: [{
      type: Output
    }],
    styleImageMissing: [{
      type: Output
    }],
    mapContainer: [{
      type: ViewChild,
      args: ["container", {
        static: true
      }]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/marker/marker.component.mjs
var _c03 = ["content"];
var _c12 = ["*"];
var MarkerComponent = class _MarkerComponent {
  mapService = inject(MapService);
  /* Init input */
  offset = input();
  anchor = input();
  clickTolerance = input();
  /* Dynamic input */
  feature = input();
  lngLat = input();
  draggable = input();
  popupShown = input();
  className = input();
  zIndex = input();
  pitchAlignment = input();
  rotationAlignment = input();
  markerDragStart = new EventEmitter();
  markerDragEnd = new EventEmitter();
  markerDrag = new EventEmitter();
  content;
  markerInstance;
  ngOnInit() {
    if (this.feature() && this.lngLat()) {
      throw new Error("feature and lngLat input are mutually exclusive");
    }
  }
  ngOnChanges(changes) {
    if (changes["lngLat"] && !changes["lngLat"].isFirstChange()) {
      this.markerInstance.setLngLat(this.lngLat());
    }
    if (changes["feature"] && !changes["feature"].isFirstChange()) {
      this.markerInstance.setLngLat(this.feature().geometry.coordinates);
    }
    if (changes["draggable"] && !changes["draggable"].isFirstChange()) {
      this.markerInstance.setDraggable(!!this.draggable());
    }
    if (changes["popupShown"] && !changes["popupShown"].isFirstChange()) {
      changes["popupShown"].currentValue ? this.markerInstance.getPopup()?.addTo(this.mapService.mapInstance) : this.markerInstance.getPopup()?.remove();
    }
    if (changes["pitchAlignment"] && !changes["pitchAlignment"].isFirstChange()) {
      this.markerInstance.setPitchAlignment(changes["pitchAlignment"].currentValue);
    }
    if (changes["rotationAlignment"] && !changes["rotationAlignment"].isFirstChange()) {
      this.markerInstance.setRotationAlignment(changes["rotationAlignment"].currentValue);
    }
  }
  ngAfterViewInit() {
    this.mapService.mapCreated$.subscribe(() => {
      this.markerInstance = this.mapService.addMarker({
        markersOptions: {
          offset: this.offset(),
          anchor: this.anchor(),
          pitchAlignment: this.pitchAlignment(),
          rotationAlignment: this.rotationAlignment(),
          draggable: this.draggable(),
          element: this.content.nativeElement,
          feature: this.feature(),
          lngLat: this.lngLat(),
          clickTolerance: this.clickTolerance()
        },
        markersEvents: {
          markerDragStart: this.markerDragStart,
          markerDrag: this.markerDrag,
          markerDragEnd: this.markerDragEnd
        }
      });
    });
  }
  ngOnDestroy() {
    this.mapService.removeMarker(this.markerInstance);
    this.markerInstance = void 0;
  }
  togglePopup() {
    this.markerInstance.togglePopup();
  }
  updateCoordinates(coordinates) {
    this.markerInstance.setLngLat(coordinates);
  }
  static ɵfac = function MarkerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MarkerComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _MarkerComponent,
    selectors: [["mgl-marker"]],
    viewQuery: function MarkerComponent_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(_c03, 7);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
      }
    },
    inputs: {
      offset: [1, "offset"],
      anchor: [1, "anchor"],
      clickTolerance: [1, "clickTolerance"],
      feature: [1, "feature"],
      lngLat: [1, "lngLat"],
      draggable: [1, "draggable"],
      popupShown: [1, "popupShown"],
      className: [1, "className"],
      zIndex: [1, "zIndex"],
      pitchAlignment: [1, "pitchAlignment"],
      rotationAlignment: [1, "rotationAlignment"]
    },
    outputs: {
      markerDragStart: "markerDragStart",
      markerDragEnd: "markerDragEnd",
      markerDrag: "markerDrag"
    },
    features: [ɵɵNgOnChangesFeature],
    ngContentSelectors: _c12,
    decls: 3,
    vars: 4,
    consts: [["content", ""]],
    template: function MarkerComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵprojectionDef();
        ɵɵelementStart(0, "div", null, 0);
        ɵɵprojection(2);
        ɵɵelementEnd();
      }
      if (rf & 2) {
        ɵɵclassMap(ctx.className());
        ɵɵstyleProp("z-index", ctx.zIndex());
      }
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MarkerComponent, [{
    type: Component,
    args: [{
      selector: "mgl-marker",
      template: `
    <div [class]="className()" [style.z-index]="zIndex()" #content>
      <ng-content />
    </div>
  `,
      encapsulation: ViewEncapsulation.None,
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, {
    markerDragStart: [{
      type: Output
    }],
    markerDragEnd: [{
      type: Output
    }],
    markerDrag: [{
      type: Output
    }],
    content: [{
      type: ViewChild,
      args: ["content", {
        static: true
      }]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/markers-for-clusters/markers-for-clusters.component.mjs
var _c04 = () => ({
  "circle-radius": 0
});
var _c13 = (a0) => ({
  $implicit: a0
});
function _forTrack0($index, $item) {
  return this.trackByFeature($item);
}
function MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {
}
function MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, "ng-template", 2);
  }
  if (rf & 2) {
    const feature_r1 = ɵɵnextContext(2).$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("ngTemplateOutlet", ctx_r1.clusterPointTpl)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c13, feature_r1));
  }
}
function MarkersForClustersComponent_For_2_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "mgl-marker", 1);
    ɵɵtemplate(1, MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_Template, 1, 4, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const feature_r1 = ɵɵnextContext().$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("feature", feature_r1);
    ɵɵadvance();
    ɵɵconditional(ctx_r1.clusterPointTpl ? 1 : -1);
  }
}
function MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_ng_template_0_Template(rf, ctx) {
}
function MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_ng_template_0_Template, 0, 0, "ng-template", 2);
  }
  if (rf & 2) {
    const feature_r1 = ɵɵnextContext(2).$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("ngTemplateOutlet", ctx_r1.pointTpl)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c13, feature_r1));
  }
}
function MarkersForClustersComponent_For_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "mgl-marker", 1);
    ɵɵtemplate(1, MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_Template, 1, 4, null, 2);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const feature_r1 = ɵɵnextContext().$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("feature", feature_r1);
    ɵɵadvance();
    ɵɵconditional(ctx_r1.pointTpl ? 1 : -1);
  }
}
function MarkersForClustersComponent_For_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_0_Template, 2, 2, "mgl-marker", 1)(1, MarkersForClustersComponent_For_2_Conditional_1_Template, 2, 2, "mgl-marker", 1);
  }
  if (rf & 2) {
    const feature_r1 = ctx.$implicit;
    ɵɵconditional(feature_r1.properties["cluster"] ? 0 : 1);
  }
}
var PointDirective = class _PointDirective {
  static ɵfac = function PointDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PointDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _PointDirective,
    selectors: [["ng-template", "mglPoint", ""]]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PointDirective, [{
    type: Directive,
    args: [{
      selector: "ng-template[mglPoint]"
    }]
  }], null, null);
})();
var ClusterPointDirective = class _ClusterPointDirective {
  static ɵfac = function ClusterPointDirective_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ClusterPointDirective)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _ClusterPointDirective,
    selectors: [["ng-template", "mglClusterPoint", ""]]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClusterPointDirective, [{
    type: Directive,
    args: [{
      selector: "ng-template[mglClusterPoint]"
    }]
  }], null, null);
})();
var uniqId = 0;
var MarkersForClustersComponent = class _MarkersForClustersComponent {
  mapService = inject(MapService);
  zone = inject(NgZone);
  /* Init input */
  source = input.required();
  /* Dynamic input */
  customPointIdKey = input();
  pointTpl;
  clusterPointTpl;
  clusterPoints = signal([]);
  layerId = `mgl-markers-for-clusters-${uniqId++}`;
  sub = new Subscription();
  ngAfterContentInit() {
    const clusterDataUpdate = () => fromEvent(this.mapService.mapInstance, "data").pipe(filter((e) => e.sourceId === this.source() && e.sourceDataType !== "metadata" && this.mapService.mapInstance.isSourceLoaded(this.source())));
    const sub = this.mapService.mapCreated$.pipe(switchMap(clusterDataUpdate), switchMap(() => merge(fromEvent(this.mapService.mapInstance, "move"), fromEvent(this.mapService.mapInstance, "moveend")).pipe(startWith(void 0)))).subscribe(() => {
      this.zone.run(() => {
        this.updateCluster();
      });
    });
    this.sub.add(sub);
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
  }
  trackByFeature(feature) {
    if (feature.id) {
      return feature.id;
    }
    const customPointIdKey = this.customPointIdKey();
    if (!customPointIdKey) {
      console.warn("[mgl-markers-for-clusters] feature.id is falsy, please provide a custom key");
      return "";
    }
    const id = feature.properties?.[customPointIdKey];
    if (!id) {
      console.warn(`[mgl-markers-for-clusters] Custom key [${customPointIdKey}], resolve to falsy for`, feature);
      return "";
    }
    return id;
  }
  updateCluster() {
    const params = {
      layers: [this.layerId]
    };
    if (!this.pointTpl) {
      params.filter = ["==", "cluster", true];
    }
    const clusterPoints = this.mapService.mapInstance.queryRenderedFeatures(params);
    const seen = /* @__PURE__ */ new Set();
    const unique = [];
    for (const feature of clusterPoints) {
      const id = this.trackByFeature(feature);
      if (!seen.has(id)) {
        seen.add(id);
        unique.push(feature);
      }
    }
    this.clusterPoints.set(unique);
  }
  static ɵfac = function MarkersForClustersComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MarkersForClustersComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _MarkersForClustersComponent,
    selectors: [["mgl-markers-for-clusters"]],
    contentQueries: function MarkersForClustersComponent_ContentQueries(rf, ctx, dirIndex) {
      if (rf & 1) {
        ɵɵcontentQuery(dirIndex, PointDirective, 5, TemplateRef);
        ɵɵcontentQuery(dirIndex, ClusterPointDirective, 5, TemplateRef);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.pointTpl = _t.first);
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.clusterPointTpl = _t.first);
      }
    },
    inputs: {
      source: [1, "source"],
      customPointIdKey: [1, "customPointIdKey"]
    },
    decls: 3,
    vars: 4,
    consts: [["type", "circle", 3, "id", "source", "paint"], [3, "feature"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"]],
    template: function MarkersForClustersComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵelement(0, "mgl-layer", 0);
        ɵɵrepeaterCreate(1, MarkersForClustersComponent_For_2_Template, 2, 1, null, null, _forTrack0, true);
      }
      if (rf & 2) {
        ɵɵproperty("id", ctx.layerId)("source", ctx.source())("paint", ɵɵpureFunction0(3, _c04));
        ɵɵadvance();
        ɵɵrepeater(ctx.clusterPoints());
      }
    },
    dependencies: [MarkerComponent, LayerComponent, NgTemplateOutlet],
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MarkersForClustersComponent, [{
    type: Component,
    args: [{
      selector: "mgl-markers-for-clusters",
      template: `
    <mgl-layer
      [id]="layerId"
      [source]="source()"
      type="circle"
      [paint]="{ 'circle-radius': 0 }"
    />
    @for (feature of clusterPoints(); track trackByFeature(feature)) {
      @if (feature.properties!['cluster']) {
        <mgl-marker [feature]="$any(feature)">
          @if (clusterPointTpl) {
            <ng-template
              [ngTemplateOutlet]="clusterPointTpl"
              [ngTemplateOutletContext]="{ $implicit: feature }"
            />
          }
        </mgl-marker>
      } @else {
        <mgl-marker [feature]="$any(feature)">
          @if (pointTpl) {
            <ng-template
              [ngTemplateOutlet]="pointTpl"
              [ngTemplateOutletContext]="{ $implicit: feature }"
            />
          }
        </mgl-marker>
      }
    }
  `,
      imports: [MarkerComponent, LayerComponent, NgTemplateOutlet],
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, {
    pointTpl: [{
      type: ContentChild,
      args: [PointDirective, {
        read: TemplateRef,
        static: false
      }]
    }],
    clusterPointTpl: [{
      type: ContentChild,
      args: [ClusterPointDirective, {
        read: TemplateRef,
        static: false
      }]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/popup/popup.component.mjs
var _c05 = ["content"];
var _c14 = ["*"];
var PopupComponent = class _PopupComponent {
  mapService = inject(MapService);
  /* Init input */
  closeButton = input();
  closeOnClick = input();
  closeOnMove = input();
  focusAfterOpen = input();
  anchor = input();
  className = input();
  maxWidth = input();
  /* Dynamic input */
  feature = input();
  lngLat = input();
  marker = input();
  offset = input();
  popupClose = new EventEmitter();
  popupOpen = new EventEmitter();
  content;
  popupInstance;
  ngOnInit() {
    if (this.lngLat() && this.marker() || this.feature() && this.lngLat() || this.feature() && this.marker()) {
      throw new Error("marker, lngLat, feature input are mutually exclusive");
    }
  }
  ngOnChanges(changes) {
    if (changes["lngLat"] && !changes["lngLat"].isFirstChange() || changes["feature"] && !changes["feature"].isFirstChange()) {
      const newlngLat = changes["lngLat"] ? this.lngLat() : this.feature().geometry.coordinates;
      this.mapService.removePopupFromMap(this.popupInstance, true);
      const popupInstanceTmp = this.createPopup();
      this.mapService.addPopupToMap(popupInstanceTmp, newlngLat, this.popupInstance.isOpen());
      this.popupInstance = popupInstanceTmp;
    }
    if (changes["marker"] && !changes["marker"].isFirstChange()) {
      const previousMarker = changes["marker"].previousValue;
      if (previousMarker.markerInstance) {
        this.mapService.removePopupFromMarker(previousMarker.markerInstance);
      }
      if (this.marker() && this.marker().markerInstance && this.popupInstance) {
        this.mapService.addPopupToMarker(this.marker().markerInstance, this.popupInstance);
      }
    }
    if (changes["offset"] && !changes["offset"].isFirstChange() && this.popupInstance) {
      this.popupInstance.setOffset(this.offset());
    }
  }
  ngAfterViewInit() {
    this.popupInstance = this.createPopup();
    this.addPopup(this.popupInstance);
  }
  ngOnDestroy() {
    if (this.popupInstance) {
      if (this.lngLat() || this.feature()) {
        this.mapService.removePopupFromMap(this.popupInstance);
      } else if (this.marker() && this.marker().markerInstance) {
        this.mapService.removePopupFromMarker(this.marker().markerInstance);
      }
    }
    this.popupInstance = void 0;
  }
  createPopup() {
    return this.mapService.createPopup({
      popupOptions: {
        closeButton: this.closeButton(),
        closeOnClick: this.closeOnClick(),
        closeOnMove: this.closeOnMove(),
        focusAfterOpen: this.focusAfterOpen(),
        anchor: this.anchor(),
        offset: this.offset(),
        className: this.className(),
        maxWidth: this.maxWidth()
      },
      popupEvents: {
        popupOpen: this.popupOpen,
        popupClose: this.popupClose
      }
    }, this.content.nativeElement);
  }
  addPopup(popup) {
    this.mapService.mapCreated$.subscribe(() => {
      if (this.lngLat() || this.feature()) {
        this.mapService.addPopupToMap(popup, this.lngLat() ? this.lngLat() : this.feature().geometry.coordinates);
      } else if (this.marker() && this.marker().markerInstance) {
        this.mapService.addPopupToMarker(this.marker().markerInstance, popup);
      } else {
        throw new Error("mgl-popup need either lngLat/marker/feature to be set");
      }
    });
  }
  static ɵfac = function PopupComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PopupComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _PopupComponent,
    selectors: [["mgl-popup"]],
    viewQuery: function PopupComponent_Query(rf, ctx) {
      if (rf & 1) {
        ɵɵviewQuery(_c05, 7);
      }
      if (rf & 2) {
        let _t;
        ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
      }
    },
    inputs: {
      closeButton: [1, "closeButton"],
      closeOnClick: [1, "closeOnClick"],
      closeOnMove: [1, "closeOnMove"],
      focusAfterOpen: [1, "focusAfterOpen"],
      anchor: [1, "anchor"],
      className: [1, "className"],
      maxWidth: [1, "maxWidth"],
      feature: [1, "feature"],
      lngLat: [1, "lngLat"],
      marker: [1, "marker"],
      offset: [1, "offset"]
    },
    outputs: {
      popupClose: "popupClose",
      popupOpen: "popupOpen"
    },
    features: [ɵɵNgOnChangesFeature],
    ngContentSelectors: _c14,
    decls: 3,
    vars: 0,
    consts: [["content", ""]],
    template: function PopupComponent_Template(rf, ctx) {
      if (rf & 1) {
        ɵɵprojectionDef();
        ɵɵelementStart(0, "div", null, 0);
        ɵɵprojection(2);
        ɵɵelementEnd();
      }
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PopupComponent, [{
    type: Component,
    args: [{
      selector: "mgl-popup",
      template: "<div #content><ng-content/></div>",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, {
    popupClose: [{
      type: Output
    }],
    popupOpen: [{
      type: Output
    }],
    content: [{
      type: ViewChild,
      args: ["content", {
        static: true
      }]
    }]
  });
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/canvas-source.component.mjs
var CanvasSourceComponent = class _CanvasSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  coordinates = input.required();
  canvas = input.required();
  animate = input();
  sourceAdded = false;
  sub = new Subscription();
  ngOnInit() {
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["canvas"] && !changes["canvas"].isFirstChange() || changes["animate"] && !changes["animate"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    } else if (changes["coordinates"] && !changes["coordinates"].isFirstChange()) {
      const source = this.mapService.getSource(this.id());
      if (source === void 0) {
        return;
      }
      source.setCoordinates(this.coordinates());
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  init() {
    const source = {
      type: "canvas",
      coordinates: this.coordinates(),
      canvas: this.canvas(),
      animate: this.animate()
    };
    this.mapService.addSource(this.id(), source);
    this.sourceAdded = true;
  }
  static ɵfac = function CanvasSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CanvasSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _CanvasSourceComponent,
    selectors: [["mgl-canvas-source"]],
    inputs: {
      id: [1, "id"],
      coordinates: [1, "coordinates"],
      canvas: [1, "canvas"],
      animate: [1, "animate"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function CanvasSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CanvasSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-canvas-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/image-source.component.mjs
var ImageSourceComponent = class _ImageSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  url = input();
  coordinates = input.required();
  sub;
  sourceId;
  ngOnInit() {
    this.sub = this.mapService.mapLoaded$.subscribe(() => this.init());
  }
  ngOnChanges(changes) {
    if (this.sourceId === void 0) {
      return;
    }
    const source = this.mapService.getSource(this.sourceId);
    if (source === void 0) {
      return;
    }
    source.updateImage({
      url: this.url(),
      coordinates: changes["coordinates"] === void 0 ? void 0 : this.coordinates()
    });
  }
  ngOnDestroy() {
    if (this.sub !== void 0) {
      this.sub.unsubscribe();
    }
    if (this.sourceId !== void 0) {
      this.mapService.removeSource(this.sourceId);
      this.sourceId = void 0;
    }
  }
  init() {
    const imageSource = {
      type: "image",
      url: this.url(),
      coordinates: this.coordinates()
    };
    this.mapService.addSource(this.id(), imageSource);
    this.sourceId = this.id();
  }
  static ɵfac = function ImageSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ImageSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _ImageSourceComponent,
    selectors: [["mgl-image-source"]],
    inputs: {
      id: [1, "id"],
      url: [1, "url"],
      coordinates: [1, "coordinates"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function ImageSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ImageSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-image-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/raster-dem-source.component.mjs
var RasterDemSourceComponent = class _RasterDemSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  url = input();
  tiles = input();
  bounds = input();
  minzoom = input();
  maxzoom = input();
  tileSize = input();
  attribution = input();
  encoding = input();
  volatile = input();
  sourceAdded = false;
  sub = new Subscription();
  ngOnInit() {
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["url"] && !changes["url"].isFirstChange() || changes["tiles"] && !changes["tiles"].isFirstChange() || changes["bounds"] && !changes["bounds"].isFirstChange() || changes["minzoom"] && !changes["minzoom"].isFirstChange() || changes["maxzoom"] && !changes["maxzoom"].isFirstChange() || changes["tileSize"] && !changes["tileSize"].isFirstChange() || changes["attribution"] && !changes["attribution"].isFirstChange() || changes["encoding"] && !changes["encoding"].isFirstChange() || changes["volatile"] && !changes["volatile"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  init() {
    const source = {
      type: "raster-dem",
      url: this.url(),
      tiles: this.tiles(),
      bounds: this.bounds(),
      minzoom: this.minzoom(),
      maxzoom: this.maxzoom(),
      tileSize: this.tileSize(),
      attribution: this.attribution(),
      encoding: this.encoding(),
      volatile: this.volatile()
    };
    this.mapService.addSource(this.id(), source);
    this.sourceAdded = true;
  }
  static ɵfac = function RasterDemSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RasterDemSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _RasterDemSourceComponent,
    selectors: [["mgl-raster-dem-source"]],
    inputs: {
      id: [1, "id"],
      url: [1, "url"],
      tiles: [1, "tiles"],
      bounds: [1, "bounds"],
      minzoom: [1, "minzoom"],
      maxzoom: [1, "maxzoom"],
      tileSize: [1, "tileSize"],
      attribution: [1, "attribution"],
      encoding: [1, "encoding"],
      volatile: [1, "volatile"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function RasterDemSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RasterDemSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-raster-dem-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/raster-source.component.mjs
var RasterSourceComponent = class _RasterSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  url = input();
  tiles = input();
  bounds = input();
  minzoom = input();
  maxzoom = input();
  tileSize = input();
  scheme = input();
  attribution = input();
  volatile = input();
  sourceAdded = false;
  sub = new Subscription();
  ngOnInit() {
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["url"] && !changes["url"].isFirstChange() || changes["tiles"] && !changes["tiles"].isFirstChange() || changes["bounds"] && !changes["bounds"].isFirstChange() || changes["minzoom"] && !changes["minzoom"].isFirstChange() || changes["maxzoom"] && !changes["maxzoom"].isFirstChange() || changes["tileSize"] && !changes["tileSize"].isFirstChange() || changes["scheme"] && !changes["scheme"].isFirstChange() || changes["attribution"] && !changes["attribution"].isFirstChange() || changes["volatile"] && !changes["volatile"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  init() {
    const source = {
      type: "raster",
      url: this.url(),
      tiles: this.tiles(),
      bounds: this.bounds(),
      minzoom: this.minzoom(),
      maxzoom: this.maxzoom(),
      tileSize: this.tileSize(),
      scheme: this.scheme(),
      attribution: this.attribution(),
      volatile: this.volatile()
    };
    this.mapService.addSource(this.id(), source);
    this.sourceAdded = true;
  }
  static ɵfac = function RasterSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RasterSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _RasterSourceComponent,
    selectors: [["mgl-raster-source"]],
    inputs: {
      id: [1, "id"],
      url: [1, "url"],
      tiles: [1, "tiles"],
      bounds: [1, "bounds"],
      minzoom: [1, "minzoom"],
      maxzoom: [1, "maxzoom"],
      tileSize: [1, "tileSize"],
      scheme: [1, "scheme"],
      attribution: [1, "attribution"],
      volatile: [1, "volatile"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function RasterSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RasterSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-raster-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/vector-source.component.mjs
var VectorSourceComponent = class _VectorSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  url = input();
  tiles = input();
  bounds = input();
  scheme = input();
  minzoom = input();
  maxzoom = input();
  attribution = input();
  promoteId = input();
  volatile = input();
  sourceAdded = false;
  sub = new Subscription();
  ngOnInit() {
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["bounds"] && !changes["bounds"].isFirstChange() || changes["scheme"] && !changes["scheme"].isFirstChange() || changes["minzoom"] && !changes["minzoom"].isFirstChange() || changes["maxzoom"] && !changes["maxzoom"].isFirstChange() || changes["attribution"] && !changes["attribution"].isFirstChange() || changes["promoteId"] && !changes["promoteId"].isFirstChange() || changes["volatile"] && !changes["volatile"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    } else if (changes["url"] && !changes["url"].isFirstChange() || changes["tiles"] && !changes["tiles"].isFirstChange()) {
      const source = this.mapService.getSource(this.id());
      if (source === void 0) {
        return;
      }
      if (changes["url"] && this.url()) {
        source.setUrl(this.url());
      }
      if (changes["tiles"] && this.tiles()) {
        source.setTiles(this.tiles());
      }
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  reload() {
    this.mapService.getSource(this.id())?.reload();
  }
  init() {
    const source = {
      type: "vector",
      url: this.url(),
      tiles: this.tiles(),
      bounds: this.bounds(),
      scheme: this.scheme(),
      minzoom: this.minzoom(),
      maxzoom: this.maxzoom(),
      attribution: this.attribution(),
      promoteId: this.promoteId(),
      volatile: this.volatile()
    };
    this.mapService.addSource(this.id(), source);
    this.sourceAdded = true;
  }
  static ɵfac = function VectorSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _VectorSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _VectorSourceComponent,
    selectors: [["mgl-vector-source"]],
    inputs: {
      id: [1, "id"],
      url: [1, "url"],
      tiles: [1, "tiles"],
      bounds: [1, "bounds"],
      scheme: [1, "scheme"],
      minzoom: [1, "minzoom"],
      maxzoom: [1, "maxzoom"],
      attribution: [1, "attribution"],
      promoteId: [1, "promoteId"],
      volatile: [1, "volatile"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function VectorSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VectorSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-vector-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();

// node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/video-source.component.mjs
var VideoSourceComponent = class _VideoSourceComponent {
  mapService = inject(MapService);
  /* Init inputs */
  id = input.required();
  /* Dynamic inputs */
  urls = input.required();
  coordinates = input.required();
  sourceAdded = false;
  sub = new Subscription();
  ngOnInit() {
    const sub1 = this.mapService.mapLoaded$.subscribe(() => {
      this.init();
      const sub = fromEvent(this.mapService.mapInstance, "styledata").pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {
        this.init();
      });
      this.sub.add(sub);
    });
    this.sub.add(sub1);
  }
  ngOnChanges(changes) {
    if (!this.sourceAdded) {
      return;
    }
    if (changes["urls"] && !changes["urls"].isFirstChange()) {
      this.ngOnDestroy();
      this.ngOnInit();
    } else if (changes["coordinates"] && !changes["coordinates"].isFirstChange()) {
      const source = this.mapService.getSource(this.id());
      if (source === void 0) {
        return;
      }
      source.setCoordinates(this.coordinates());
    }
  }
  ngOnDestroy() {
    this.sub.unsubscribe();
    if (this.sourceAdded) {
      this.mapService.removeSource(this.id());
      this.sourceAdded = false;
    }
  }
  pause() {
    this.mapService.getSource(this.id())?.pause();
  }
  play() {
    this.mapService.getSource(this.id())?.play();
  }
  getVideo() {
    return this.mapService.getSource(this.id())?.getVideo();
  }
  init() {
    const source = {
      type: "video",
      urls: this.urls(),
      coordinates: this.coordinates()
    };
    this.mapService.addSource(this.id(), source);
    this.sourceAdded = true;
  }
  static ɵfac = function VideoSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _VideoSourceComponent)();
  };
  static ɵcmp = ɵɵdefineComponent({
    type: _VideoSourceComponent,
    selectors: [["mgl-video-source"]],
    inputs: {
      id: [1, "id"],
      urls: [1, "urls"],
      coordinates: [1, "coordinates"]
    },
    features: [ɵɵNgOnChangesFeature],
    decls: 0,
    vars: 0,
    template: function VideoSourceComponent_Template(rf, ctx) {
    },
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VideoSourceComponent, [{
    type: Component,
    args: [{
      selector: "mgl-video-source",
      template: "",
      changeDetection: ChangeDetectionStrategy.OnPush
    }]
  }], null, null);
})();
export {
  AttributionControlDirective,
  CanvasSourceComponent,
  ClusterPointDirective,
  ControlComponent,
  CustomControl,
  DraggableDirective,
  FeatureComponent,
  FullscreenControlDirective,
  GeoJSONSourceComponent,
  GeolocateControlDirective,
  ImageComponent,
  ImageSourceComponent,
  LayerComponent,
  MAPBOX_API_KEY,
  MapComponent,
  MapService,
  MarkerComponent,
  MarkersForClustersComponent,
  NavigationControlDirective,
  PointDirective,
  PopupComponent,
  RasterDemSourceComponent,
  RasterSourceComponent,
  ScaleControlDirective,
  VectorSourceComponent,
  VideoSourceComponent,
  provideMapboxGL
};
//# sourceMappingURL=ngx-mapbox-gl.js.map
