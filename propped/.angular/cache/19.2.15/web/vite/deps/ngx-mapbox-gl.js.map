{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/map/map.service.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/provide-mapbox-gl.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/attribution-control.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/control.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/fullscreen-control.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/geolocate-control.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/navigation-control.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/control/scale-control.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/geojson/geojson-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/geojson/feature.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/draggable/draggable.directive.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/image/image.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/layer/layer.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/map/map.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/marker/marker.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/markers-for-clusters/markers-for-clusters.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/popup/popup.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/canvas-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/image-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/raster-dem-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/raster-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/vector-source.component.mjs", "../../../../../../node_modules/.pnpm/ngx-mapbox-gl@12.0.0_@angul_3fc0d9c7d9d6b78557f6a70b968f342c/node_modules/ngx-mapbox-gl/esm2022/lib/source/video-source.component.mjs"], "sourcesContent": ["import { Injectable, InjectionToken, Injector, NgZone, afterRender, inject } from '@angular/core';\nimport { Map, Marker, Popup } from 'mapbox-gl';\nimport { AsyncSubject, Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport const MAPBOX_API_KEY = new InjectionToken('MapboxApiKey');\nexport class MapService {\n  zone = inject(NgZone);\n  MAPBOX_API_KEY = inject(MAPBOX_API_KEY, {\n    optional: true\n  });\n  injector = inject(Injector);\n  mapInstance;\n  mapCreated$;\n  mapLoaded$;\n  mapEvents;\n  mapCreated = new AsyncSubject();\n  mapLoaded = new AsyncSubject();\n  markersToRemove = [];\n  popupsToRemove = [];\n  imageIdsToRemove = [];\n  subscription = new Subscription();\n  constructor() {\n    this.mapCreated$ = this.mapCreated.asObservable();\n    this.mapLoaded$ = this.mapLoaded.asObservable();\n  }\n  setup(options) {\n    const mapOptions = {\n      ...options.mapOptions,\n      bearing: options.mapOptions.bearing?.[0],\n      zoom: options.mapOptions.zoom?.[0],\n      pitch: options.mapOptions.pitch?.[0],\n      accessToken: options.accessToken || this.MAPBOX_API_KEY || ''\n    };\n    this.createMap(mapOptions);\n    this.hookEvents(options.mapEvents);\n    this.mapEvents = options.mapEvents;\n    this.mapCreated.next(undefined);\n    this.mapCreated.complete();\n    // Intentionally emit mapCreate after internal mapCreated event\n    if (options.mapEvents.mapCreate.observed) {\n      this.zone.run(() => {\n        options.mapEvents.mapCreate.emit(this.mapInstance);\n      });\n    }\n  }\n  destroyMap() {\n    if (this.mapInstance) {\n      this.subscription.unsubscribe();\n      this.mapInstance.remove();\n    }\n  }\n  updateProjection(projection) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setProjection(projection);\n    });\n  }\n  updateMinZoom(minZoom) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setMinZoom(minZoom);\n    });\n  }\n  updateMaxZoom(maxZoom) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setMaxZoom(maxZoom);\n    });\n  }\n  updateMinPitch(minPitch) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setMinPitch(minPitch);\n    });\n  }\n  updateMaxPitch(maxPitch) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setMaxPitch(maxPitch);\n    });\n  }\n  updateRenderWorldCopies(status) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setRenderWorldCopies(status);\n    });\n  }\n  updateScrollZoom(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.scrollZoom.enable() : this.mapInstance.scrollZoom.disable();\n    });\n  }\n  updateDragRotate(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.dragRotate.enable() : this.mapInstance.dragRotate.disable();\n    });\n  }\n  updateTouchPitch(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.touchPitch.enable() : this.mapInstance.touchPitch.disable();\n    });\n  }\n  updateTouchZoomRotate(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.touchZoomRotate.enable() : this.mapInstance.touchZoomRotate.disable();\n    });\n  }\n  updateDoubleClickZoom(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.doubleClickZoom.enable() : this.mapInstance.doubleClickZoom.disable();\n    });\n  }\n  updateKeyboard(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.keyboard.enable() : this.mapInstance.keyboard.disable();\n    });\n  }\n  updateDragPan(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.dragPan.enable() : this.mapInstance.dragPan.disable();\n    });\n  }\n  updateBoxZoom(status) {\n    return this.zone.runOutsideAngular(() => {\n      status ? this.mapInstance.boxZoom.enable() : this.mapInstance.boxZoom.disable();\n    });\n  }\n  updateStyle(style) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setStyle(style);\n    });\n  }\n  updateMaxBounds(maxBounds) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setMaxBounds(maxBounds);\n    });\n  }\n  changeCanvasCursor(cursor) {\n    const canvas = this.mapInstance.getCanvasContainer();\n    canvas.style.cursor = cursor;\n  }\n  queryRenderedFeatures(pointOrBox, parameters) {\n    return this.mapInstance.queryRenderedFeatures(pointOrBox, parameters);\n  }\n  panTo(center, options) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.panTo(center, options);\n    });\n  }\n  move(movingMethod, movingOptions, zoom, center, bearing, pitch) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance[movingMethod]({\n        ...movingOptions,\n        zoom: zoom != null ? zoom : this.mapInstance.getZoom(),\n        center: center != null ? center : this.mapInstance.getCenter(),\n        bearing: bearing != null ? bearing : this.mapInstance.getBearing(),\n        pitch: pitch != null ? pitch : this.mapInstance.getPitch()\n      });\n    });\n  }\n  addLayer(layer, bindEvents, before) {\n    this.zone.runOutsideAngular(() => {\n      Object.keys(layer.layerOptions).forEach(key => {\n        const tkey = key;\n        if (layer.layerOptions[tkey] === undefined) {\n          delete layer.layerOptions[tkey];\n        }\n      });\n      this.mapInstance.addLayer(layer.layerOptions, before);\n      if (bindEvents) {\n        if (layer.layerEvents.layerClick.observed) {\n          this.mapInstance.on('click', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerClick.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerDblClick.observed) {\n          this.mapInstance.on('dblclick', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerDblClick.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseDown.observed) {\n          this.mapInstance.on('mousedown', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseDown.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseUp.observed) {\n          this.mapInstance.on('mouseup', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseUp.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseEnter.observed) {\n          this.mapInstance.on('mouseenter', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseEnter.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseLeave.observed) {\n          this.mapInstance.on('mouseleave', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseLeave.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseMove.observed) {\n          this.mapInstance.on('mousemove', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseMove.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseOver.observed) {\n          this.mapInstance.on('mouseover', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseOver.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerMouseOut.observed) {\n          this.mapInstance.on('mouseout', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerMouseOut.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerContextMenu.observed) {\n          this.mapInstance.on('contextmenu', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerContextMenu.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerTouchStart.observed) {\n          this.mapInstance.on('touchstart', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerTouchStart.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerTouchEnd.observed) {\n          this.mapInstance.on('touchend', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerTouchEnd.emit(evt);\n            });\n          });\n        }\n        if (layer.layerEvents.layerTouchCancel.observed) {\n          this.mapInstance.on('touchcancel', layer.layerOptions.id, evt => {\n            this.zone.run(() => {\n              layer.layerEvents.layerTouchCancel.emit(evt);\n            });\n          });\n        }\n      }\n    });\n  }\n  removeLayer(layerId) {\n    this.zone.runOutsideAngular(() => {\n      if (this.mapInstance.getLayer(layerId) != null) {\n        this.mapInstance.removeLayer(layerId);\n      }\n    });\n  }\n  addMarker(marker) {\n    const options = {\n      offset: marker.markersOptions.offset,\n      anchor: marker.markersOptions.anchor,\n      draggable: marker.markersOptions.draggable,\n      rotationAlignment: marker.markersOptions.rotationAlignment,\n      pitchAlignment: marker.markersOptions.pitchAlignment,\n      clickTolerance: marker.markersOptions.clickTolerance\n    };\n    Object.keys(options).forEach(key => {\n      const tkey = key;\n      if (options[tkey] === undefined) {\n        delete options[tkey];\n      }\n    });\n    if (marker.markersOptions.element.childNodes.length > 0) {\n      options.element = marker.markersOptions.element;\n    }\n    const markerInstance = new Marker(options);\n    if (marker.markersEvents.markerDragStart.observed) {\n      markerInstance.on('dragstart', event => {\n        if (event) {\n          const {\n            target\n          } = event;\n          this.zone.run(() => {\n            marker.markersEvents.markerDragStart.emit(target);\n          });\n        }\n      });\n    }\n    /*\n          */\n    if (marker.markersEvents.markerDrag.observed) {\n      markerInstance.on('drag', event => {\n        if (event) {\n          const {\n            target\n          } = event;\n          this.zone.run(() => {\n            marker.markersEvents.markerDrag.emit(target);\n          });\n        }\n      });\n    }\n    if (marker.markersEvents.markerDragEnd.observed) {\n      markerInstance.on('dragend', event => {\n        if (event) {\n          const {\n            target\n          } = event;\n          this.zone.run(() => {\n            marker.markersEvents.markerDragEnd.emit(target);\n          });\n        }\n      });\n    }\n    const lngLat = marker.markersOptions.feature ? marker.markersOptions.feature.geometry.coordinates : marker.markersOptions.lngLat;\n    markerInstance.setLngLat(lngLat);\n    return this.zone.runOutsideAngular(() => {\n      markerInstance.addTo(this.mapInstance);\n      return markerInstance;\n    });\n  }\n  removeMarker(marker) {\n    this.markersToRemove.push(marker);\n  }\n  createPopup(popup, element) {\n    return this.zone.runOutsideAngular(() => {\n      Object.keys(popup.popupOptions).forEach(key => {\n        const tkey = key;\n        return popup.popupOptions[tkey] === undefined && delete popup.popupOptions[tkey];\n      });\n      const popupInstance = new Popup(popup.popupOptions);\n      popupInstance.setDOMContent(element);\n      if (popup.popupEvents.popupClose.observed) {\n        popupInstance.on('close', () => {\n          this.zone.run(() => {\n            popup.popupEvents.popupClose.emit();\n          });\n        });\n      }\n      if (popup.popupEvents.popupOpen.observed) {\n        popupInstance.on('open', () => {\n          this.zone.run(() => {\n            popup.popupEvents.popupOpen.emit();\n          });\n        });\n      }\n      return popupInstance;\n    });\n  }\n  addPopupToMap(popup, lngLat, skipOpenEvent = false) {\n    return this.zone.runOutsideAngular(() => {\n      if (skipOpenEvent && popup._listeners) {\n        delete popup._listeners['open'];\n      }\n      popup.setLngLat(lngLat);\n      popup.addTo(this.mapInstance);\n    });\n  }\n  addPopupToMarker(marker, popup) {\n    return this.zone.runOutsideAngular(() => {\n      marker.setPopup(popup);\n    });\n  }\n  removePopupFromMap(popup, skipCloseEvent = false) {\n    if (skipCloseEvent && popup._listeners) {\n      delete popup._listeners['close'];\n    }\n    this.popupsToRemove.push(popup);\n  }\n  removePopupFromMarker(marker) {\n    return this.zone.runOutsideAngular(() => {\n      marker.setPopup(undefined);\n    });\n  }\n  addControl(control, position) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.addControl(control, position);\n    });\n  }\n  removeControl(control) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.removeControl(control);\n    });\n  }\n  async loadAndAddImage(imageId, url, options) {\n    return this.zone.runOutsideAngular(() => new Promise((resolve, reject) => {\n      this.mapInstance.loadImage(url, (error, image) => {\n        if (error) {\n          reject(error);\n          return;\n        }\n        if (!image) {\n          reject(new Error('Image not loaded'));\n          return;\n        }\n        this.addImage(imageId, image, options);\n        resolve();\n      });\n    }));\n  }\n  addImage(imageId, data, options) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.addImage(imageId, data, options);\n    });\n  }\n  removeImage(imageId) {\n    this.imageIdsToRemove.push(imageId);\n  }\n  addSource(sourceId, source) {\n    return this.zone.runOutsideAngular(() => {\n      Object.keys(source).forEach(key => {\n        const tkey = key;\n        return source[tkey] === undefined && delete source[tkey];\n      });\n      this.mapInstance.addSource(sourceId, source);\n    });\n  }\n  getSource(sourceId) {\n    return this.mapInstance.getSource(sourceId);\n  }\n  removeSource(sourceId) {\n    this.zone.runOutsideAngular(() => {\n      this.findLayersBySourceId(sourceId).forEach(layer => this.mapInstance.removeLayer(layer.id));\n      this.mapInstance.removeSource(sourceId);\n    });\n  }\n  setLayerAllPaintProperty(layerId, paint) {\n    return this.zone.runOutsideAngular(() => {\n      Object.keys(paint).forEach(key => {\n        const tKey = key;\n        // TODO Check for perf, setPaintProperty only on changed paint props maybe\n        this.mapInstance.setPaintProperty(layerId, tKey, paint[tKey]);\n      });\n    });\n  }\n  setLayerAllLayoutProperty(layerId, layout) {\n    return this.zone.runOutsideAngular(() => {\n      Object.keys(layout).forEach(key => {\n        const tKey = key;\n        // TODO Check for perf, setLayoutProperty only on changed layout props maybe\n        this.mapInstance.setLayoutProperty(layerId, tKey, layout[tKey]);\n      });\n    });\n  }\n  setLayerFilter(layerId, filter) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setFilter(layerId, filter);\n    });\n  }\n  setLayerBefore(layerId, beforeId) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.moveLayer(layerId, beforeId);\n    });\n  }\n  setLayerZoomRange(layerId, minZoom, maxZoom) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.setLayerZoomRange(layerId, minZoom ? minZoom : 0, maxZoom ? maxZoom : 20);\n    });\n  }\n  fitBounds(bounds, options) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.fitBounds(bounds, options);\n    });\n  }\n  fitScreenCoordinates(points, bearing, options) {\n    return this.zone.runOutsideAngular(() => {\n      this.mapInstance.fitScreenCoordinates(points[0], points[1], bearing, options);\n    });\n  }\n  applyChanges() {\n    this.zone.runOutsideAngular(() => {\n      this.removeMarkers();\n      this.removePopups();\n      this.removeImages();\n    });\n  }\n  createMap(options) {\n    NgZone.assertNotInAngularZone();\n    Object.keys(options).forEach(key => {\n      const tkey = key;\n      if (options[tkey] === undefined) {\n        delete options[tkey];\n      }\n    });\n    this.mapInstance = new Map(options);\n    afterRender({\n      write: () => {\n        this.applyChanges();\n      }\n    }, {\n      injector: this.injector\n    });\n  }\n  removeMarkers() {\n    for (const marker of this.markersToRemove) {\n      marker.remove();\n    }\n    this.markersToRemove = [];\n  }\n  removePopups() {\n    for (const popup of this.popupsToRemove) {\n      popup.remove();\n    }\n    this.popupsToRemove = [];\n  }\n  removeImages() {\n    for (const imageId of this.imageIdsToRemove) {\n      this.mapInstance.removeImage(imageId);\n    }\n    this.imageIdsToRemove = [];\n  }\n  findLayersBySourceId(sourceId) {\n    const layers = this.mapInstance.getStyle().layers;\n    if (layers == null) {\n      return [];\n    }\n    return layers.filter(l => 'source' in l ? l.source === sourceId : false);\n  }\n  hookEvents(events) {\n    this.mapInstance.on('load', evt => {\n      this.mapLoaded.next(undefined);\n      this.mapLoaded.complete();\n      this.zone.run(() => {\n        events.mapLoad.emit(evt);\n      });\n    });\n    if (events.mapResize.observed) {\n      this.mapInstance.on('resize', evt => this.zone.run(() => {\n        events.mapResize.emit(evt);\n      }));\n    }\n    if (events.mapRemove.observed) {\n      this.mapInstance.on('remove', evt => this.zone.run(() => {\n        events.mapRemove.emit(evt);\n      }));\n    }\n    if (events.mapMouseDown.observed) {\n      this.mapInstance.on('mousedown', evt => this.zone.run(() => {\n        events.mapMouseDown.emit(evt);\n      }));\n    }\n    if (events.mapMouseUp.observed) {\n      this.mapInstance.on('mouseup', evt => this.zone.run(() => {\n        events.mapMouseUp.emit(evt);\n      }));\n    }\n    if (events.mapMouseMove.observed) {\n      this.mapInstance.on('mousemove', evt => this.zone.run(() => {\n        events.mapMouseMove.emit(evt);\n      }));\n    }\n    if (events.mapClick.observed) {\n      this.mapInstance.on('click', evt => this.zone.run(() => {\n        events.mapClick.emit(evt);\n      }));\n    }\n    if (events.mapDblClick.observed) {\n      this.mapInstance.on('dblclick', evt => this.zone.run(() => {\n        events.mapDblClick.emit(evt);\n      }));\n    }\n    if (events.mapMouseOver.observed) {\n      this.mapInstance.on('mouseover', evt => this.zone.run(() => {\n        events.mapMouseOver.emit(evt);\n      }));\n    }\n    if (events.mapMouseOut.observed) {\n      this.mapInstance.on('mouseout', evt => this.zone.run(() => {\n        events.mapMouseOut.emit(evt);\n      }));\n    }\n    if (events.mapContextMenu.observed) {\n      this.mapInstance.on('contextmenu', evt => this.zone.run(() => {\n        events.mapContextMenu.emit(evt);\n      }));\n    }\n    if (events.mapTouchStart.observed) {\n      this.mapInstance.on('touchstart', evt => this.zone.run(() => {\n        events.mapTouchStart.emit(evt);\n      }));\n    }\n    if (events.mapTouchEnd.observed) {\n      this.mapInstance.on('touchend', evt => this.zone.run(() => {\n        events.mapTouchEnd.emit(evt);\n      }));\n    }\n    if (events.mapTouchMove.observed) {\n      this.mapInstance.on('touchmove', evt => this.zone.run(() => {\n        events.mapTouchMove.emit(evt);\n      }));\n    }\n    if (events.mapTouchCancel.observed) {\n      this.mapInstance.on('touchcancel', evt => this.zone.run(() => {\n        events.mapTouchCancel.emit(evt);\n      }));\n    }\n    if (events.mapWheel.observed) {\n      this.mapInstance.on('wheel', evt => this.zone.run(() => {\n        events.mapWheel.emit(evt);\n      }));\n    }\n    if (events.moveStart.observed) {\n      this.mapInstance.on('movestart', evt => this.zone.run(() => events.moveStart.emit(evt)));\n    }\n    if (events.move.observed) {\n      this.mapInstance.on('move', evt => this.zone.run(() => events.move.emit(evt)));\n    }\n    if (events.moveEnd.observed) {\n      this.mapInstance.on('moveend', evt => this.zone.run(() => events.moveEnd.emit(evt)));\n    }\n    if (events.mapDragStart.observed) {\n      this.mapInstance.on('dragstart', evt => this.zone.run(() => events.mapDragStart.emit(evt)));\n    }\n    if (events.mapDrag.observed) {\n      this.mapInstance.on('drag', evt => this.zone.run(() => events.mapDrag.emit(evt)));\n    }\n    if (events.mapDragEnd.observed) {\n      this.mapInstance.on('dragend', evt => this.zone.run(() => events.mapDragEnd.emit(evt)));\n    }\n    if (events.zoomStart.observed) {\n      this.mapInstance.on('zoomstart', () => this.zone.run(() => events.zoomStart.emit()));\n    }\n    if (events.zoomEvt.observed) {\n      this.mapInstance.on('zoom', () => this.zone.run(() => events.zoomEvt.emit()));\n    }\n    if (events.zoomEnd.observed) {\n      this.mapInstance.on('zoomend', () => this.zone.run(() => events.zoomEnd.emit()));\n    }\n    if (events.rotateStart.observed) {\n      this.mapInstance.on('rotatestart', evt => this.zone.run(() => events.rotateStart.emit(evt)));\n    }\n    if (events.rotate.observed) {\n      this.mapInstance.on('rotate', evt => this.zone.run(() => events.rotate.emit(evt)));\n    }\n    if (events.rotateEnd.observed) {\n      this.mapInstance.on('rotateend', evt => this.zone.run(() => events.rotateEnd.emit(evt)));\n    }\n    if (events.pitchStart.observed) {\n      this.mapInstance.on('pitchstart', () => this.zone.run(() => events.pitchStart.emit()));\n    }\n    if (events.pitchEvt.observed) {\n      this.mapInstance.on('pitch', () => this.zone.run(() => events.pitchEvt.emit()));\n    }\n    if (events.pitchEnd.observed) {\n      this.mapInstance.on('pitchend', () => this.zone.run(() => events.pitchEnd.emit()));\n    }\n    if (events.boxZoomStart.observed) {\n      this.mapInstance.on('boxzoomstart', evt => this.zone.run(() => events.boxZoomStart.emit(evt)));\n    }\n    if (events.boxZoomEnd.observed) {\n      this.mapInstance.on('boxzoomend', evt => this.zone.run(() => events.boxZoomEnd.emit(evt)));\n    }\n    if (events.boxZoomCancel.observed) {\n      this.mapInstance.on('boxzoomcancel', evt => this.zone.run(() => events.boxZoomCancel.emit(evt)));\n    }\n    if (events.webGlContextLost.observed) {\n      this.mapInstance.on('webglcontextlost', evt => this.zone.run(() => events.webGlContextLost.emit(evt)));\n    }\n    if (events.webGlContextRestored.observed) {\n      this.mapInstance.on('webglcontextrestored', evt => this.zone.run(() => events.webGlContextRestored.emit(evt)));\n    }\n    if (events.render.observed) {\n      this.mapInstance.on('render', () => this.zone.run(() => events.render.emit()));\n    }\n    if (events.mapError.observed) {\n      this.mapInstance.on('error', evt => this.zone.run(() => events.mapError.emit(evt.error)));\n    }\n    if (events.data.observed) {\n      this.mapInstance.on('data', evt => this.zone.run(() => events.data.emit(evt)));\n    }\n    if (events.styleData.observed) {\n      this.mapInstance.on('styledata', evt => this.zone.run(() => events.styleData.emit(evt)));\n    }\n    if (events.sourceData.observed) {\n      this.mapInstance.on('sourcedata', evt => this.zone.run(() => events.sourceData.emit(evt)));\n    }\n    if (events.dataLoading.observed) {\n      this.mapInstance.on('dataloading', evt => this.zone.run(() => events.dataLoading.emit(evt)));\n    }\n    if (events.styleDataLoading.observed) {\n      this.mapInstance.on('styledataloading', evt => this.zone.run(() => events.styleDataLoading.emit(evt)));\n    }\n    if (events.sourceDataLoading.observed) {\n      this.mapInstance.on('sourcedataloading', evt => this.zone.run(() => events.sourceDataLoading.emit(evt)));\n    }\n    if (events.styleImageMissing.observed) {\n      this.mapInstance.on('styleimagemissing', evt => this.zone.run(() => events.styleImageMissing.emit(evt)));\n    }\n    if (events.idle.observed) {\n      this.mapInstance.on('idle', () => this.zone.run(() => events.idle.emit()));\n    }\n  }\n  static ɵfac = function MapService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MapService,\n    factory: MapService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n", "import { MAPBOX_API_KEY } from './map/map.service';\nexport function provideMapboxGL(config) {\n  return {\n    provide: MAPBOX_API_KEY,\n    useValue: config.accessToken\n  };\n}\n", "import { Directive, inject, input } from '@angular/core';\nimport { AttributionControl } from 'mapbox-gl';\nimport { MapService } from '../map/map.service';\nimport { ControlComponent } from './control.component';\nimport * as i0 from \"@angular/core\";\nexport class AttributionControlDirective {\n  mapService = inject(MapService);\n  controlComponent = inject(ControlComponent, {\n    host: true\n  });\n  /* Init inputs */\n  compact = input();\n  customAttribution = input();\n  ngAfterContentInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.controlComponent.control) {\n        throw new Error('Another control is already set for this control');\n      }\n      const options = {};\n      const compact = this.compact();\n      const customAttribution = this.customAttribution();\n      if (compact !== undefined) {\n        options.compact = compact;\n      }\n      if (customAttribution !== undefined) {\n        options.customAttribution = customAttribution;\n      }\n      this.controlComponent.control = new AttributionControl(options);\n      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());\n    });\n  }\n  static ɵfac = function AttributionControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AttributionControlDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AttributionControlDirective,\n    selectors: [[\"\", \"mglAttribution\", \"\"]],\n    inputs: {\n      compact: [1, \"compact\"],\n      customAttribution: [1, \"customAttribution\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AttributionControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglAttribution]'\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, ElementRef, ViewChild, inject, input } from '@angular/core';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"content\"];\nconst _c1 = [\"*\"];\nexport class CustomControl {\n  container;\n  constructor(container) {\n    this.container = container;\n  }\n  onAdd() {\n    return this.container;\n  }\n  onRemove() {\n    return this.container.parentNode.removeChild(this.container);\n  }\n  getDefaultPosition() {\n    return 'top-right';\n  }\n}\nexport class ControlComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  position = input();\n  content;\n  control;\n  controlAdded = false;\n  ngAfterContentInit() {\n    if (this.content.nativeElement.childNodes.length) {\n      this.control = new CustomControl(this.content.nativeElement);\n      this.mapService.mapCreated$.subscribe(() => {\n        this.mapService.addControl(this.control, this.position());\n        this.controlAdded = true;\n      });\n    }\n  }\n  ngOnD<PERSON>roy() {\n    if (this.controlAdded) {\n      this.mapService.removeControl(this.control);\n    }\n  }\n  static ɵfac = function ControlComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ControlComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ControlComponent,\n    selectors: [[\"mgl-control\"]],\n    viewQuery: function ControlComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n      }\n    },\n    inputs: {\n      position: [1, \"position\"]\n    },\n    ngContentSelectors: _c1,\n    decls: 3,\n    vars: 0,\n    consts: [[\"content\", \"\"], [1, \"mapboxgl-ctrl\"]],\n    template: function ControlComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-control',\n      template: `\n    <div class=\"mapboxgl-ctrl\" #content>\n      <ng-content />\n    </div>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    content: [{\n      type: ViewChild,\n      args: ['content', {\n        static: true\n      }]\n    }]\n  });\n})();\n", "import { Directive, HostListener, inject, input } from '@angular/core';\nimport { FullscreenControl } from 'mapbox-gl';\nimport { MapService } from '../map/map.service';\nimport { ControlComponent } from './control.component';\nimport * as i0 from \"@angular/core\";\nexport class FullscreenControlDirective {\n  mapService = inject(MapService);\n  controlComponent = inject(ControlComponent, {\n    host: true\n  });\n  /* Init inputs */\n  container = input();\n  onFullscreen() {\n    this.mapService.mapInstance.resize();\n  }\n  ngAfterContentInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.controlComponent.control) {\n        throw new Error('Another control is already set for this control');\n      }\n      this.controlComponent.control = new FullscreenControl({\n        container: this.container()\n      });\n      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());\n    });\n  }\n  static ɵfac = function FullscreenControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FullscreenControlDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FullscreenControlDirective,\n    selectors: [[\"\", \"mglFullscreen\", \"\"]],\n    hostBindings: function FullscreenControlDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"webkitfullscreenchange\", function FullscreenControlDirective_webkitfullscreenchange_HostBindingHandler($event) {\n          return ctx.onFullscreen($event.target);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      container: [1, \"container\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglFullscreen]'\n    }]\n  }], null, {\n    onFullscreen: [{\n      type: HostListener,\n      args: ['window:webkitfullscreenchange', ['$event.target']]\n    }]\n  });\n})();\n", "import { Directive, EventEmitter, Output, inject, input } from '@angular/core';\nimport { GeolocateControl } from 'mapbox-gl';\nimport { MapService } from '../map/map.service';\nimport { ControlComponent } from './control.component';\nimport * as i0 from \"@angular/core\";\nexport class GeolocateControlDirective {\n  mapService = inject(MapService);\n  controlComponent = inject(ControlComponent, {\n    host: true\n  });\n  /* Init inputs */\n  positionOptions = input();\n  fitBoundsOptions = input();\n  trackUserLocation = input();\n  showUserLocation = input();\n  showUserHeading = input();\n  geolocate = new EventEmitter();\n  ngAfterContentInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.controlComponent.control) {\n        throw new Error('Another control is already set for this control');\n      }\n      const options = {\n        positionOptions: this.positionOptions(),\n        fitBoundsOptions: this.fitBoundsOptions(),\n        trackUserLocation: this.trackUserLocation(),\n        showUserLocation: this.showUserLocation(),\n        showUserHeading: this.showUserHeading()\n      };\n      Object.keys(options).forEach(key => {\n        const tkey = key;\n        if (options[tkey] === undefined) {\n          delete options[tkey];\n        }\n      });\n      this.controlComponent.control = new GeolocateControl(options);\n      this.controlComponent.control.on('geolocate', data => {\n        this.geolocate.emit(data);\n      });\n      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());\n    });\n  }\n  static ɵfac = function GeolocateControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GeolocateControlDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: GeolocateControlDirective,\n    selectors: [[\"\", \"mglGeolocate\", \"\"]],\n    inputs: {\n      positionOptions: [1, \"positionOptions\"],\n      fitBoundsOptions: [1, \"fitBoundsOptions\"],\n      trackUserLocation: [1, \"trackUserLocation\"],\n      showUserLocation: [1, \"showUserLocation\"],\n      showUserHeading: [1, \"showUserHeading\"]\n    },\n    outputs: {\n      geolocate: \"geolocate\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GeolocateControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglGeolocate]'\n    }]\n  }], null, {\n    geolocate: [{\n      type: Output\n    }]\n  });\n})();\n", "import { Directive, inject, input } from '@angular/core';\nimport { NavigationControl } from 'mapbox-gl';\nimport { MapService } from '../map/map.service';\nimport { ControlComponent } from './control.component';\nimport * as i0 from \"@angular/core\";\nexport class NavigationControlDirective {\n  mapService = inject(MapService);\n  controlComponent = inject(ControlComponent, {\n    host: true\n  });\n  /* Init inputs */\n  showCompass = input();\n  showZoom = input();\n  ngAfterContentInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.controlComponent.control) {\n        throw new Error('Another control is already set for this control');\n      }\n      const options = {};\n      const showCompass = this.showCompass();\n      const showZoom = this.showZoom();\n      if (showCompass !== undefined) {\n        options.showCompass = showCompass;\n      }\n      if (showZoom !== undefined) {\n        options.showZoom = showZoom;\n      }\n      this.controlComponent.control = new NavigationControl(options);\n      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());\n    });\n  }\n  static ɵfac = function NavigationControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NavigationControlDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NavigationControlDirective,\n    selectors: [[\"\", \"mglNavigation\", \"\"]],\n    inputs: {\n      showCompass: [1, \"showCompass\"],\n      showZoom: [1, \"showZoom\"]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavigationControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglNavigation]'\n    }]\n  }], null, null);\n})();\n", "import { Directive, inject, input } from '@angular/core';\nimport { ScaleControl } from 'mapbox-gl';\nimport { MapService } from '../map/map.service';\nimport { ControlComponent } from './control.component';\nimport * as i0 from \"@angular/core\";\nexport class ScaleControlDirective {\n  mapService = inject(MapService);\n  controlComponent = inject(ControlComponent, {\n    host: true\n  });\n  /* Init inputs */\n  maxWidth = input();\n  /* Dynamic inputs */\n  unit = input();\n  ngOnChanges(changes) {\n    if (changes['unit'] && !changes['unit'].isFirstChange()) {\n      this.controlComponent.control.setUnit(changes['unit'].currentValue);\n    }\n  }\n  ngAfterContentInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.controlComponent.control) {\n        throw new Error('Another control is already set for this control');\n      }\n      const options = {};\n      const maxWidth = this.maxWidth();\n      const unit = this.unit();\n      if (maxWidth !== undefined) {\n        options.maxWidth = maxWidth;\n      }\n      if (unit !== undefined) {\n        options.unit = unit;\n      }\n      this.controlComponent.control = new ScaleControl(options);\n      this.mapService.addControl(this.controlComponent.control, this.controlComponent.position());\n    });\n  }\n  static ɵfac = function ScaleControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScaleControlDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ScaleControlDirective,\n    selectors: [[\"\", \"mglScale\", \"\"]],\n    inputs: {\n      maxWidth: [1, \"maxWidth\"],\n      unit: [1, \"unit\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScaleControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglScale]'\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, NgZone, inject, input, model } from '@angular/core';\nimport { fromEvent, Subject, Subscription } from 'rxjs';\nimport { debounceTime, filter } from 'rxjs/operators';\nimport { MapService } from '../../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class GeoJSONSourceComponent {\n  mapService = inject(MapService);\n  zone = inject(NgZone);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  data = model();\n  minzoom = input();\n  maxzoom = input();\n  attribution = input();\n  buffer = input();\n  tolerance = input();\n  cluster = input();\n  clusterRadius = input();\n  clusterMaxZoom = input();\n  clusterMinPoints = input();\n  clusterProperties = input();\n  lineMetrics = input();\n  generateId = input();\n  promoteId = input();\n  filter = input();\n  dynamic = input();\n  updateFeatureData = new Subject();\n  sub = new Subscription();\n  sourceAdded = false;\n  featureIdCounter = 0;\n  ngOnInit() {\n    if (!this.data()) {\n      this.data.set({\n        type: 'FeatureCollection',\n        features: []\n      });\n    }\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['minzoom'] && !changes['minzoom'].isFirstChange() || changes['maxzoom'] && !changes['maxzoom'].isFirstChange() || changes['attribution'] && !changes['attribution'].isFirstChange() || changes['buffer'] && !changes['buffer'].isFirstChange() || changes['tolerance'] && !changes['tolerance'].isFirstChange() || changes['cluster'] && !changes['cluster'].isFirstChange() || changes['clusterRadius'] && !changes['clusterRadius'].isFirstChange() || changes['clusterMaxZoom'] && !changes['clusterMaxZoom'].isFirstChange() || changes['clusterMinPoints'] && !changes['clusterMinPoints'].isFirstChange() || changes['clusterProperties'] && !changes['clusterProperties'].isFirstChange() || changes['lineMetrics'] && !changes['lineMetrics'].isFirstChange() || changes['generateId'] && !changes['generateId'].isFirstChange() || changes['promoteId'] && !changes['promoteId'].isFirstChange() || changes['filter'] && !changes['filter'].isFirstChange() || changes['dynamic'] && !changes['dynamic'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    }\n    if (changes['data'] && !changes['data'].isFirstChange()) {\n      const source = this.mapService.getSource(this.id());\n      if (source === undefined) {\n        return;\n      }\n      source.setData(this.data());\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  /**\n   * For clustered sources, fetches the zoom at which the given cluster expands.\n   *\n   * @param clusterId The value of the cluster's cluster_id property.\n   */\n  async getClusterExpansionZoom(clusterId) {\n    const source = this.mapService.getSource(this.id());\n    return this.zone.run(async () => new Promise((resolve, reject) => {\n      source.getClusterExpansionZoom(clusterId, (error, zoom) => {\n        if (error) {\n          reject(error);\n        } else {\n          resolve(zoom);\n        }\n      });\n    }));\n  }\n  /**\n   * For clustered sources, fetches the children of the given cluster on the next zoom level (as an array of GeoJSON features).\n   *\n   * @param clusterId The value of the cluster's cluster_id property.\n   */\n  async getClusterChildren(clusterId) {\n    const source = this.mapService.getSource(this.id());\n    return this.zone.run(async () => new Promise((resolve, reject) => {\n      source.getClusterChildren(clusterId, (error, features) => {\n        if (error) {\n          reject(error);\n        } else {\n          resolve(features);\n        }\n      });\n    }));\n  }\n  /**\n   * For clustered sources, fetches the original points that belong to the cluster (as an array of GeoJSON features).\n   *\n   * @param clusterId The value of the cluster's cluster_id property.\n   * @param limit The maximum number of features to return.\n   * @param offset The number of features to skip (e.g. for pagination).\n   */\n  async getClusterLeaves(clusterId, limit, offset) {\n    const source = this.mapService.getSource(this.id());\n    return this.zone.run(async () => new Promise((resolve, reject) => {\n      source.getClusterLeaves(clusterId, limit, offset, (error, features) => {\n        if (error) {\n          reject(error);\n        } else {\n          resolve(features || []);\n        }\n      });\n    }));\n  }\n  _addFeature(feature) {\n    const collection = this.data();\n    collection.features.push(feature);\n    this.updateFeatureData.next(null);\n  }\n  _removeFeature(feature) {\n    const collection = this.data();\n    const index = collection.features.indexOf(feature);\n    if (index > -1) {\n      collection.features.splice(index, 1);\n    }\n    this.updateFeatureData.next(null);\n  }\n  _getNewFeatureId() {\n    return ++this.featureIdCounter;\n  }\n  init() {\n    const source = {\n      type: 'geojson',\n      data: this.data(),\n      minzoom: this.minzoom(),\n      maxzoom: this.maxzoom(),\n      attribution: this.attribution(),\n      buffer: this.buffer(),\n      tolerance: this.tolerance(),\n      cluster: this.cluster(),\n      clusterRadius: this.clusterRadius(),\n      clusterMaxZoom: this.clusterMaxZoom(),\n      clusterMinPoints: this.clusterMinPoints(),\n      clusterProperties: this.clusterProperties(),\n      lineMetrics: this.lineMetrics(),\n      generateId: this.generateId(),\n      promoteId: this.promoteId(),\n      filter: this.filter(),\n      dynamic: this.dynamic()\n    };\n    this.mapService.addSource(this.id(), source);\n    const sub = this.updateFeatureData.pipe(debounceTime(0)).subscribe(() => {\n      const source = this.mapService.getSource(this.id());\n      if (source === undefined) {\n        return;\n      }\n      source.setData(this.data());\n    });\n    this.sub.add(sub);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function GeoJSONSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GeoJSONSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GeoJSONSourceComponent,\n    selectors: [[\"mgl-geojson-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      data: [1, \"data\"],\n      minzoom: [1, \"minzoom\"],\n      maxzoom: [1, \"maxzoom\"],\n      attribution: [1, \"attribution\"],\n      buffer: [1, \"buffer\"],\n      tolerance: [1, \"tolerance\"],\n      cluster: [1, \"cluster\"],\n      clusterRadius: [1, \"clusterRadius\"],\n      clusterMaxZoom: [1, \"clusterMaxZoom\"],\n      clusterMinPoints: [1, \"clusterMinPoints\"],\n      clusterProperties: [1, \"clusterProperties\"],\n      lineMetrics: [1, \"lineMetrics\"],\n      generateId: [1, \"generateId\"],\n      promoteId: [1, \"promoteId\"],\n      filter: [1, \"filter\"],\n      dynamic: [1, \"dynamic\"]\n    },\n    outputs: {\n      data: \"dataChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function GeoJSONSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GeoJSONSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-geojson-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, forwardRef, inject, input, model } from '@angular/core';\nimport { GeoJSONSourceComponent } from './geojson-source.component';\nimport * as i0 from \"@angular/core\";\nexport class FeatureComponent {\n  GeoJSONSourceComponent = inject(forwardRef(() => GeoJSONSourceComponent));\n  /* Init inputs */\n  id = model(); // FIXME number only for now https://github.com/mapbox/mapbox-gl-js/issues/2716\n  geometry = input.required();\n  properties = input();\n  type = 'Feature';\n  feature;\n  ngOnInit() {\n    if (!this.id()) {\n      this.id.set(this.GeoJSONSourceComponent._getNewFeatureId());\n    }\n    this.feature = {\n      type: this.type,\n      geometry: this.geometry(),\n      properties: this.properties() ? this.properties() : {}\n    };\n    this.feature.id = this.id();\n    this.GeoJSONSourceComponent._addFeature(this.feature);\n  }\n  ngOn<PERSON><PERSON>roy() {\n    this.GeoJSONSourceComponent._removeFeature(this.feature);\n  }\n  updateCoordinates(coordinates) {\n    this.feature.geometry.coordinates = coordinates;\n    this.GeoJSONSourceComponent.updateFeatureData.next(null);\n  }\n  static ɵfac = function FeatureComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FeatureComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: FeatureComponent,\n    selectors: [[\"mgl-feature\"]],\n    inputs: {\n      id: [1, \"id\"],\n      geometry: [1, \"geometry\"],\n      properties: [1, \"properties\"]\n    },\n    outputs: {\n      id: \"idChange\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function FeatureComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FeatureComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-feature',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { Directive, EventEmitter, NgZone, Output, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport { FeatureComponent } from '../source/geojson/feature.component';\nimport * as i0 from \"@angular/core\";\nexport class DraggableDirective {\n  mapService = inject(MapService);\n  ngZone = inject(NgZone);\n  featureComponent = inject(FeatureComponent, {\n    optional: true,\n    host: true\n  });\n  layer = input(undefined, {\n    alias: 'mglDraggable'\n  });\n  featureDragStart = new EventEmitter();\n  featureDragEnd = new EventEmitter();\n  featureDrag = new EventEmitter();\n  sub = new Subscription();\n  ngOnInit() {\n    let enter$;\n    let leave$;\n    let updateCoords;\n    const layer = this.layer();\n    if (this.featureComponent && layer) {\n      enter$ = layer.layerMouseEnter;\n      leave$ = layer.layerMouseLeave;\n      updateCoords = this.featureComponent.updateCoordinates.bind(this.featureComponent);\n      if (this.featureComponent.geometry().type !== 'Point') {\n        throw new Error('mglDraggable only support point feature');\n      }\n    } else {\n      throw new Error('mglDraggable can only be used on Feature (with a layer as input) or Marker');\n    }\n    this.handleDraggable(enter$, leave$, updateCoords);\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n  }\n  handleDraggable(enter$, leave$, updateCoords) {\n    let moving = false;\n    let inside = false;\n    this.mapService.mapCreated$.subscribe(() => {\n      const mouseUp$ = fromEvent(this.mapService.mapInstance, 'mouseup');\n      const dragStart$ = enter$.pipe(filter(() => !moving), filter(evt => this.filterFeature(evt)), tap(() => {\n        inside = true;\n        this.mapService.changeCanvasCursor('move');\n        this.mapService.updateDragPan(false);\n      }), switchMap(() => fromEvent(this.mapService.mapInstance, 'mousedown').pipe(takeUntil(leave$))));\n      const dragging$ = dragStart$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, 'mousemove').pipe(takeUntil(mouseUp$))));\n      const dragEnd$ = dragStart$.pipe(switchMap(() => mouseUp$.pipe(take(1))));\n      this.sub.add(dragStart$.subscribe(evt => {\n        moving = true;\n        if (this.featureDragStart.observed) {\n          this.ngZone.run(() => {\n            this.featureDragStart.emit(evt);\n          });\n        }\n      }));\n      this.sub.add(dragging$.subscribe(evt => {\n        updateCoords([evt.lngLat.lng, evt.lngLat.lat]);\n        if (this.featureDrag.observed) {\n          this.ngZone.run(() => {\n            this.featureDrag.emit(evt);\n          });\n        }\n      }));\n      this.sub.add(dragEnd$.subscribe(evt => {\n        moving = false;\n        if (this.featureDragEnd.observed) {\n          this.ngZone.run(() => {\n            this.featureDragEnd.emit(evt);\n          });\n        }\n        if (!inside) {\n          // It's possible to dragEnd outside the target (small input lag)\n          this.mapService.changeCanvasCursor('');\n          this.mapService.updateDragPan(true);\n        }\n      }));\n      this.sub.add(leave$.pipe(tap(() => inside = false), filter(() => !moving)).subscribe(() => {\n        this.mapService.changeCanvasCursor('');\n        this.mapService.updateDragPan(true);\n      }));\n    });\n  }\n  filterFeature(evt) {\n    const layer = this.layer();\n    if (this.featureComponent && layer) {\n      const feature = this.mapService.queryRenderedFeatures(evt.point, {\n        layers: [layer.id()],\n        filter: ['all', ['==', '$type', 'Point'], ['==', '$id', this.featureComponent.id()]]\n      })[0];\n      if (!feature) {\n        return false;\n      }\n    }\n    return true;\n  }\n  static ɵfac = function DraggableDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DraggableDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DraggableDirective,\n    selectors: [[\"\", \"mglDraggable\", \"\"]],\n    inputs: {\n      layer: [1, \"mglDraggable\", \"layer\"]\n    },\n    outputs: {\n      featureDragStart: \"featureDragStart\",\n      featureDragEnd: \"featureDragEnd\",\n      featureDrag: \"featureDrag\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mglDraggable]'\n    }]\n  }], null, {\n    featureDragStart: [{\n      type: Output\n    }],\n    featureDragEnd: [{\n      type: Output\n    }],\n    featureDrag: [{\n      type: Output\n    }]\n  });\n})();\n", "import { Component, EventEmitter, NgZone, Output, inject, input } from '@angular/core';\nimport { fromEvent } from 'rxjs';\nimport { filter, startWith, switchMap } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class ImageComponent {\n  mapService = inject(MapService);\n  zone = inject(NgZone);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  data = input();\n  options = input();\n  url = input();\n  imageError = new EventEmitter();\n  imageLoaded = new EventEmitter();\n  isAdded = false;\n  isAdding = false;\n  sub;\n  ngOnInit() {\n    this.sub = this.mapService.mapLoaded$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, 'styledata').pipe(startWith(undefined), filter(() => !this.isAdding && !this.mapService.mapInstance.hasImage(this.id()))))).subscribe(() => this.init());\n  }\n  ngOnChanges(changes) {\n    if (changes['data'] && !changes['data'].isFirstChange() || changes['options'] && !changes['options'].isFirstChange() || changes['url'] && !changes['url'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    }\n  }\n  ngOnDestroy() {\n    if (this.isAdded) {\n      this.mapService.removeImage(this.id());\n    }\n    if (this.sub) {\n      this.sub.unsubscribe();\n    }\n  }\n  async init() {\n    this.isAdding = true;\n    if (this.data()) {\n      this.mapService.addImage(this.id(), this.data(), this.options());\n      this.isAdded = true;\n      this.isAdding = false;\n    } else if (this.url()) {\n      try {\n        await this.mapService.loadAndAddImage(this.id(), this.url(), this.options());\n        this.isAdded = true;\n        this.isAdding = false;\n        this.zone.run(() => {\n          this.imageLoaded.emit();\n        });\n      } catch (error) {\n        this.zone.run(() => {\n          this.imageError.emit(error);\n        });\n      }\n    }\n  }\n  static ɵfac = function ImageComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ImageComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ImageComponent,\n    selectors: [[\"mgl-image\"]],\n    inputs: {\n      id: [1, \"id\"],\n      data: [1, \"data\"],\n      options: [1, \"options\"],\n      url: [1, \"url\"]\n    },\n    outputs: {\n      imageError: \"imageError\",\n      imageLoaded: \"imageLoaded\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function ImageComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImageComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-image',\n      template: ''\n    }]\n  }], null, {\n    imageError: [{\n      type: Output\n    }],\n    imageLoaded: [{\n      type: Output\n    }]\n  });\n})();\n", "import { Component, EventEmitter, Output, inject, input } from '@angular/core';\nimport { fromEvent } from 'rxjs';\nimport { filter, map, startWith, switchMap } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class LayerComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  source = input();\n  type = input.required();\n  metadata = input();\n  sourceLayer = input();\n  /* Dynamic inputs */\n  filter = input();\n  layout = input();\n  paint = input();\n  before = input();\n  minzoom = input();\n  maxzoom = input();\n  layerClick = new EventEmitter();\n  layerDblClick = new EventEmitter();\n  layerMouseDown = new EventEmitter();\n  layerMouseUp = new EventEmitter();\n  layerMouseEnter = new EventEmitter();\n  layerMouseLeave = new EventEmitter();\n  layerMouseMove = new EventEmitter();\n  layerMouseOver = new EventEmitter();\n  layerMouseOut = new EventEmitter();\n  layerContextMenu = new EventEmitter();\n  layerTouchStart = new EventEmitter();\n  layerTouchEnd = new EventEmitter();\n  layerTouchCancel = new EventEmitter();\n  layerAdded = false;\n  sub;\n  ngOnInit() {\n    this.sub = this.mapService.mapLoaded$.pipe(switchMap(() => fromEvent(this.mapService.mapInstance, 'styledata').pipe(map(() => false), filter(() => !this.mapService.mapInstance.getLayer(this.id())), startWith(true)))).subscribe(bindEvents => this.init(bindEvents));\n  }\n  ngOnChanges(changes) {\n    if (!this.layerAdded) {\n      return;\n    }\n    if (changes['paint'] && !changes['paint'].isFirstChange()) {\n      this.mapService.setLayerAllPaintProperty(this.id(), changes['paint'].currentValue);\n    }\n    if (changes['layout'] && !changes['layout'].isFirstChange()) {\n      this.mapService.setLayerAllLayoutProperty(this.id(), changes['layout'].currentValue);\n    }\n    if (changes['filter'] && !changes['filter'].isFirstChange()) {\n      this.mapService.setLayerFilter(this.id(), changes['filter'].currentValue);\n    }\n    if (changes['before'] && !changes['before'].isFirstChange()) {\n      this.mapService.setLayerBefore(this.id(), changes['before'].currentValue);\n    }\n    if (changes['minzoom'] && !changes['minzoom'].isFirstChange() || changes['maxzoom'] && !changes['maxzoom'].isFirstChange()) {\n      this.mapService.setLayerZoomRange(this.id(), this.minzoom(), this.maxzoom());\n    }\n  }\n  ngOnDestroy() {\n    if (this.layerAdded) {\n      this.mapService.removeLayer(this.id());\n    }\n    if (this.sub) {\n      this.sub.unsubscribe();\n    }\n  }\n  init(bindEvents) {\n    const layer = {\n      layerOptions: {\n        id: this.id(),\n        type: this.type(),\n        source: this.source(),\n        metadata: this.metadata(),\n        'source-layer': this.sourceLayer(),\n        minzoom: this.minzoom(),\n        maxzoom: this.maxzoom(),\n        filter: this.filter(),\n        layout: this.layout(),\n        paint: this.paint()\n      },\n      layerEvents: {\n        layerClick: this.layerClick,\n        layerDblClick: this.layerDblClick,\n        layerMouseDown: this.layerMouseDown,\n        layerMouseUp: this.layerMouseUp,\n        layerMouseEnter: this.layerMouseEnter,\n        layerMouseLeave: this.layerMouseLeave,\n        layerMouseMove: this.layerMouseMove,\n        layerMouseOver: this.layerMouseOver,\n        layerMouseOut: this.layerMouseOut,\n        layerContextMenu: this.layerContextMenu,\n        layerTouchStart: this.layerTouchStart,\n        layerTouchEnd: this.layerTouchEnd,\n        layerTouchCancel: this.layerTouchCancel\n      }\n    };\n    this.mapService.addLayer(layer, bindEvents, this.before());\n    this.layerAdded = true;\n  }\n  static ɵfac = function LayerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayerComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: LayerComponent,\n    selectors: [[\"mgl-layer\"]],\n    inputs: {\n      id: [1, \"id\"],\n      source: [1, \"source\"],\n      type: [1, \"type\"],\n      metadata: [1, \"metadata\"],\n      sourceLayer: [1, \"sourceLayer\"],\n      filter: [1, \"filter\"],\n      layout: [1, \"layout\"],\n      paint: [1, \"paint\"],\n      before: [1, \"before\"],\n      minzoom: [1, \"minzoom\"],\n      maxzoom: [1, \"maxzoom\"]\n    },\n    outputs: {\n      layerClick: \"layerClick\",\n      layerDblClick: \"layerDblClick\",\n      layerMouseDown: \"layerMouseDown\",\n      layerMouseUp: \"layerMouseUp\",\n      layerMouseEnter: \"layerMouseEnter\",\n      layerMouseLeave: \"layerMouseLeave\",\n      layerMouseMove: \"layerMouseMove\",\n      layerMouseOver: \"layerMouseOver\",\n      layerMouseOut: \"layerMouseOut\",\n      layerContextMenu: \"layerContextMenu\",\n      layerTouchStart: \"layerTouchStart\",\n      layerTouchEnd: \"layerTouchEnd\",\n      layerTouchCancel: \"layerTouchCancel\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function LayerComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-layer',\n      template: ''\n    }]\n  }], null, {\n    layerClick: [{\n      type: Output\n    }],\n    layerDblClick: [{\n      type: Output\n    }],\n    layerMouseDown: [{\n      type: Output\n    }],\n    layerMouseUp: [{\n      type: Output\n    }],\n    layerMouseEnter: [{\n      type: Output\n    }],\n    layerMouseLeave: [{\n      type: Output\n    }],\n    layerMouseMove: [{\n      type: Output\n    }],\n    layerMouseOver: [{\n      type: Output\n    }],\n    layerMouseOut: [{\n      type: Output\n    }],\n    layerContextMenu: [{\n      type: Output\n    }],\n    layerTouchStart: [{\n      type: Output\n    }],\n    layerTouchEnd: [{\n      type: Output\n    }],\n    layerTouchCancel: [{\n      type: Output\n    }]\n  });\n})();\n", "import { afterNextRender, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Output, ViewChild, inject, input } from '@angular/core';\nimport { lastValueFrom } from 'rxjs';\nimport { MapService } from './map.service';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"container\"];\nexport class MapComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  accessToken = input();\n  collectResourceTiming = input();\n  crossSourceCollisions = input();\n  fadeDuration = input();\n  hash = input();\n  refreshExpiredTiles = input();\n  failIfMajorPerformanceCaveat = input();\n  bearingSnap = input();\n  interactive = input();\n  pitchWithRotate = input();\n  clickTolerance = input();\n  attributionControl = input();\n  logoPosition = input();\n  maxTileCacheSize = input();\n  localIdeographFontFamily = input();\n  preserveDrawingBuffer = input();\n  trackResize = input();\n  transformRequest = input();\n  bounds = input(); // Use fitBounds for dynamic input\n  antialias = input();\n  locale = input();\n  cooperativeGestures = input();\n  /* Dynamic inputs */\n  minZoom = input();\n  maxZoom = input();\n  minPitch = input();\n  maxPitch = input();\n  scrollZoom = input();\n  dragRotate = input();\n  touchPitch = input();\n  touchZoomRotate = input();\n  doubleClickZoom = input();\n  keyboard = input();\n  dragPan = input();\n  boxZoom = input();\n  style = input();\n  center = input();\n  maxBounds = input();\n  zoom = input();\n  bearing = input();\n  pitch = input();\n  // First value goes to options.fitBoundsOptions. Subsequents changes are passed to fitBounds\n  fitBoundsOptions = input();\n  renderWorldCopies = input();\n  projection = input();\n  /* Added by ngx-mapbox-gl */\n  movingMethod = input('flyTo');\n  movingOptions = input();\n  // => First value is a alias to bounds input (since mapbox 0.53.0). Subsequents changes are passed to fitBounds\n  fitBounds = input();\n  fitScreenCoordinates = input();\n  centerWithPanTo = input();\n  panToOptions = input();\n  cursorStyle = input();\n  // resizeEmitter = new Subject<MapEvent>();\n  // mapResize = outputFromObservable(this.mapResizeEmitter);\n  mapResize = new EventEmitter();\n  mapRemove = new EventEmitter();\n  mapMouseDown = new EventEmitter();\n  mapMouseUp = new EventEmitter();\n  mapMouseMove = new EventEmitter();\n  mapClick = new EventEmitter();\n  mapDblClick = new EventEmitter();\n  mapMouseOver = new EventEmitter();\n  mapMouseOut = new EventEmitter();\n  mapContextMenu = new EventEmitter();\n  mapTouchStart = new EventEmitter();\n  mapTouchEnd = new EventEmitter();\n  mapTouchMove = new EventEmitter();\n  mapTouchCancel = new EventEmitter();\n  mapWheel = new EventEmitter();\n  moveStart = new EventEmitter();\n  move = new EventEmitter();\n  moveEnd = new EventEmitter();\n  mapDragStart = new EventEmitter();\n  mapDrag = new EventEmitter();\n  mapDragEnd = new EventEmitter();\n  zoomStart = new EventEmitter();\n  zoomEvt = new EventEmitter();\n  zoomEnd = new EventEmitter();\n  rotateStart = new EventEmitter();\n  rotate = new EventEmitter();\n  rotateEnd = new EventEmitter();\n  pitchStart = new EventEmitter();\n  pitchEvt = new EventEmitter();\n  pitchEnd = new EventEmitter();\n  boxZoomStart = new EventEmitter();\n  boxZoomEnd = new EventEmitter();\n  boxZoomCancel = new EventEmitter();\n  webGlContextLost = new EventEmitter();\n  webGlContextRestored = new EventEmitter();\n  mapLoad = new EventEmitter();\n  mapCreate = new EventEmitter();\n  idle = new EventEmitter();\n  render = new EventEmitter();\n  mapError = new EventEmitter();\n  data = new EventEmitter();\n  styleData = new EventEmitter();\n  sourceData = new EventEmitter();\n  dataLoading = new EventEmitter();\n  styleDataLoading = new EventEmitter();\n  sourceDataLoading = new EventEmitter();\n  styleImageMissing = new EventEmitter();\n  get mapInstance() {\n    return this.mapService.mapInstance;\n  }\n  mapContainer;\n  constructor() {\n    afterNextRender(() => {\n      this.mapService.setup({\n        accessToken: this.accessToken(),\n        mapOptions: {\n          collectResourceTiming: this.collectResourceTiming(),\n          container: this.mapContainer.nativeElement,\n          crossSourceCollisions: this.crossSourceCollisions(),\n          fadeDuration: this.fadeDuration(),\n          minZoom: this.minZoom(),\n          maxZoom: this.maxZoom(),\n          minPitch: this.minPitch(),\n          maxPitch: this.maxPitch(),\n          style: this.style(),\n          hash: this.hash(),\n          interactive: this.interactive(),\n          bearingSnap: this.bearingSnap(),\n          pitchWithRotate: this.pitchWithRotate(),\n          clickTolerance: this.clickTolerance(),\n          attributionControl: this.attributionControl(),\n          logoPosition: this.logoPosition(),\n          failIfMajorPerformanceCaveat: this.failIfMajorPerformanceCaveat(),\n          preserveDrawingBuffer: this.preserveDrawingBuffer(),\n          refreshExpiredTiles: this.refreshExpiredTiles(),\n          maxBounds: this.maxBounds(),\n          scrollZoom: this.scrollZoom(),\n          boxZoom: this.boxZoom(),\n          dragRotate: this.dragRotate(),\n          dragPan: this.dragPan(),\n          keyboard: this.keyboard(),\n          doubleClickZoom: this.doubleClickZoom(),\n          touchPitch: this.touchPitch(),\n          touchZoomRotate: this.touchZoomRotate(),\n          trackResize: this.trackResize(),\n          center: this.center(),\n          zoom: this.zoom(),\n          bearing: this.bearing(),\n          pitch: this.pitch(),\n          renderWorldCopies: this.renderWorldCopies(),\n          maxTileCacheSize: this.maxTileCacheSize(),\n          localIdeographFontFamily: this.localIdeographFontFamily(),\n          transformRequest: this.transformRequest(),\n          bounds: this.bounds() ? this.bounds() : this.fitBounds(),\n          fitBoundsOptions: this.fitBoundsOptions(),\n          antialias: this.antialias(),\n          locale: this.locale(),\n          cooperativeGestures: this.cooperativeGestures(),\n          projection: this.projection()\n        },\n        mapEvents: this\n      });\n      if (this.cursorStyle()) {\n        this.mapService.changeCanvasCursor(this.cursorStyle());\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.mapService.destroyMap();\n  }\n  async ngOnChanges(changes) {\n    await lastValueFrom(this.mapService.mapCreated$);\n    if (changes['cursorStyle'] && !changes['cursorStyle'].isFirstChange()) {\n      this.mapService.changeCanvasCursor(changes['cursorStyle'].currentValue);\n    }\n    if (changes['projection'] && !changes['projection'].isFirstChange()) {\n      this.mapService.updateProjection(changes['projection'].currentValue);\n    }\n    if (changes['minZoom'] && !changes['minZoom'].isFirstChange()) {\n      this.mapService.updateMinZoom(changes['minZoom'].currentValue);\n    }\n    if (changes['maxZoom'] && !changes['maxZoom'].isFirstChange()) {\n      this.mapService.updateMaxZoom(changes['maxZoom'].currentValue);\n    }\n    if (changes['minPitch'] && !changes['minPitch'].isFirstChange()) {\n      this.mapService.updateMinPitch(changes['minPitch'].currentValue);\n    }\n    if (changes['maxPitch'] && !changes['maxPitch'].isFirstChange()) {\n      this.mapService.updateMaxPitch(changes['maxPitch'].currentValue);\n    }\n    if (changes['renderWorldCopies'] && !changes['renderWorldCopies'].isFirstChange()) {\n      this.mapService.updateRenderWorldCopies(changes['renderWorldCopies'].currentValue);\n    }\n    if (changes['scrollZoom'] && !changes['scrollZoom'].isFirstChange()) {\n      this.mapService.updateScrollZoom(changes['scrollZoom'].currentValue);\n    }\n    if (changes['dragRotate'] && !changes['dragRotate'].isFirstChange()) {\n      this.mapService.updateDragRotate(changes['dragRotate'].currentValue);\n    }\n    if (changes['touchPitch'] && !changes['touchPitch'].isFirstChange()) {\n      this.mapService.updateTouchPitch(changes['touchPitch'].currentValue);\n    }\n    if (changes['touchZoomRotate'] && !changes['touchZoomRotate'].isFirstChange()) {\n      this.mapService.updateTouchZoomRotate(changes['touchZoomRotate'].currentValue);\n    }\n    if (changes['doubleClickZoom'] && !changes['doubleClickZoom'].isFirstChange()) {\n      this.mapService.updateDoubleClickZoom(changes['doubleClickZoom'].currentValue);\n    }\n    if (changes['keyboard'] && !changes['keyboard'].isFirstChange()) {\n      this.mapService.updateKeyboard(changes['keyboard'].currentValue);\n    }\n    if (changes['dragPan'] && !changes['dragPan'].isFirstChange()) {\n      this.mapService.updateDragPan(changes['dragPan'].currentValue);\n    }\n    if (changes['boxZoom'] && !changes['boxZoom'].isFirstChange()) {\n      this.mapService.updateBoxZoom(changes['boxZoom'].currentValue);\n    }\n    if (changes['style'] && !changes['style'].isFirstChange()) {\n      this.mapService.updateStyle(changes['style'].currentValue);\n    }\n    if (changes['maxBounds'] && !changes['maxBounds'].isFirstChange()) {\n      this.mapService.updateMaxBounds(changes['maxBounds'].currentValue);\n    }\n    if (changes['fitBounds'] && changes['fitBounds'].currentValue && !changes['fitBounds'].isFirstChange()) {\n      this.mapService.fitBounds(changes['fitBounds'].currentValue, this.fitBoundsOptions());\n    }\n    if (changes['fitScreenCoordinates'] && changes['fitScreenCoordinates'].currentValue) {\n      if ((this.center() || this.zoom() || this.pitch() || this.fitBounds()) && changes['fitScreenCoordinates'].isFirstChange()) {\n        console.warn('[ngx-mapbox-gl] center / zoom / pitch / fitBounds inputs are being overridden by fitScreenCoordinates input');\n      }\n      this.mapService.fitScreenCoordinates(changes['fitScreenCoordinates'].currentValue, this.bearing() ? this.bearing()[0] : 0, this.movingOptions());\n    }\n    if (this.centerWithPanTo() && changes['center'] && !changes['center'].isFirstChange() && !changes['zoom'] && !changes['bearing'] && !changes['pitch']) {\n      this.mapService.panTo(this.center(), this.panToOptions());\n    } else if (changes['center'] && !changes['center'].isFirstChange() || changes['zoom'] && !changes['zoom'].isFirstChange() || changes['bearing'] && !changes['bearing'].isFirstChange() && !changes['fitScreenCoordinates'] || changes['pitch'] && !changes['pitch'].isFirstChange()) {\n      this.mapService.move(this.movingMethod(), this.movingOptions(), changes['zoom'] && this.zoom() ? this.zoom()[0] : undefined, changes['center'] ? this.center() : undefined, changes['bearing'] && this.bearing() ? this.bearing()[0] : undefined, changes['pitch'] && this.pitch() ? this.pitch()[0] : undefined);\n    }\n  }\n  static ɵfac = function MapComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MapComponent,\n    selectors: [[\"mgl-map\"]],\n    viewQuery: function MapComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mapContainer = _t.first);\n      }\n    },\n    inputs: {\n      accessToken: [1, \"accessToken\"],\n      collectResourceTiming: [1, \"collectResourceTiming\"],\n      crossSourceCollisions: [1, \"crossSourceCollisions\"],\n      fadeDuration: [1, \"fadeDuration\"],\n      hash: [1, \"hash\"],\n      refreshExpiredTiles: [1, \"refreshExpiredTiles\"],\n      failIfMajorPerformanceCaveat: [1, \"failIfMajorPerformanceCaveat\"],\n      bearingSnap: [1, \"bearingSnap\"],\n      interactive: [1, \"interactive\"],\n      pitchWithRotate: [1, \"pitchWithRotate\"],\n      clickTolerance: [1, \"clickTolerance\"],\n      attributionControl: [1, \"attributionControl\"],\n      logoPosition: [1, \"logoPosition\"],\n      maxTileCacheSize: [1, \"maxTileCacheSize\"],\n      localIdeographFontFamily: [1, \"localIdeographFontFamily\"],\n      preserveDrawingBuffer: [1, \"preserveDrawingBuffer\"],\n      trackResize: [1, \"trackResize\"],\n      transformRequest: [1, \"transformRequest\"],\n      bounds: [1, \"bounds\"],\n      antialias: [1, \"antialias\"],\n      locale: [1, \"locale\"],\n      cooperativeGestures: [1, \"cooperativeGestures\"],\n      minZoom: [1, \"minZoom\"],\n      maxZoom: [1, \"maxZoom\"],\n      minPitch: [1, \"minPitch\"],\n      maxPitch: [1, \"maxPitch\"],\n      scrollZoom: [1, \"scrollZoom\"],\n      dragRotate: [1, \"dragRotate\"],\n      touchPitch: [1, \"touchPitch\"],\n      touchZoomRotate: [1, \"touchZoomRotate\"],\n      doubleClickZoom: [1, \"doubleClickZoom\"],\n      keyboard: [1, \"keyboard\"],\n      dragPan: [1, \"dragPan\"],\n      boxZoom: [1, \"boxZoom\"],\n      style: [1, \"style\"],\n      center: [1, \"center\"],\n      maxBounds: [1, \"maxBounds\"],\n      zoom: [1, \"zoom\"],\n      bearing: [1, \"bearing\"],\n      pitch: [1, \"pitch\"],\n      fitBoundsOptions: [1, \"fitBoundsOptions\"],\n      renderWorldCopies: [1, \"renderWorldCopies\"],\n      projection: [1, \"projection\"],\n      movingMethod: [1, \"movingMethod\"],\n      movingOptions: [1, \"movingOptions\"],\n      fitBounds: [1, \"fitBounds\"],\n      fitScreenCoordinates: [1, \"fitScreenCoordinates\"],\n      centerWithPanTo: [1, \"centerWithPanTo\"],\n      panToOptions: [1, \"panToOptions\"],\n      cursorStyle: [1, \"cursorStyle\"]\n    },\n    outputs: {\n      mapResize: \"mapResize\",\n      mapRemove: \"mapRemove\",\n      mapMouseDown: \"mapMouseDown\",\n      mapMouseUp: \"mapMouseUp\",\n      mapMouseMove: \"mapMouseMove\",\n      mapClick: \"mapClick\",\n      mapDblClick: \"mapDblClick\",\n      mapMouseOver: \"mapMouseOver\",\n      mapMouseOut: \"mapMouseOut\",\n      mapContextMenu: \"mapContextMenu\",\n      mapTouchStart: \"mapTouchStart\",\n      mapTouchEnd: \"mapTouchEnd\",\n      mapTouchMove: \"mapTouchMove\",\n      mapTouchCancel: \"mapTouchCancel\",\n      mapWheel: \"mapWheel\",\n      moveStart: \"moveStart\",\n      move: \"move\",\n      moveEnd: \"moveEnd\",\n      mapDragStart: \"mapDragStart\",\n      mapDrag: \"mapDrag\",\n      mapDragEnd: \"mapDragEnd\",\n      zoomStart: \"zoomStart\",\n      zoomEvt: \"zoomEvt\",\n      zoomEnd: \"zoomEnd\",\n      rotateStart: \"rotateStart\",\n      rotate: \"rotate\",\n      rotateEnd: \"rotateEnd\",\n      pitchStart: \"pitchStart\",\n      pitchEvt: \"pitchEvt\",\n      pitchEnd: \"pitchEnd\",\n      boxZoomStart: \"boxZoomStart\",\n      boxZoomEnd: \"boxZoomEnd\",\n      boxZoomCancel: \"boxZoomCancel\",\n      webGlContextLost: \"webGlContextLost\",\n      webGlContextRestored: \"webGlContextRestored\",\n      mapLoad: \"mapLoad\",\n      mapCreate: \"mapCreate\",\n      idle: \"idle\",\n      render: \"render\",\n      mapError: \"mapError\",\n      data: \"data\",\n      styleData: \"styleData\",\n      sourceData: \"sourceData\",\n      dataLoading: \"dataLoading\",\n      styleDataLoading: \"styleDataLoading\",\n      sourceDataLoading: \"sourceDataLoading\",\n      styleImageMissing: \"styleImageMissing\"\n    },\n    features: [i0.ɵɵProvidersFeature([MapService]), i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 0,\n    consts: [[\"container\", \"\"]],\n    template: function MapComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", null, 0);\n      }\n    },\n    styles: [\"[_nghost-%COMP%]{display:block}div[_ngcontent-%COMP%]{height:100%;width:100%}\"],\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-map',\n      template: '<div #container></div>',\n      providers: [MapService],\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\":host{display:block}div{height:100%;width:100%}\\n\"]\n    }]\n  }], () => [], {\n    mapResize: [{\n      type: Output\n    }],\n    mapRemove: [{\n      type: Output\n    }],\n    mapMouseDown: [{\n      type: Output\n    }],\n    mapMouseUp: [{\n      type: Output\n    }],\n    mapMouseMove: [{\n      type: Output\n    }],\n    mapClick: [{\n      type: Output\n    }],\n    mapDblClick: [{\n      type: Output\n    }],\n    mapMouseOver: [{\n      type: Output\n    }],\n    mapMouseOut: [{\n      type: Output\n    }],\n    mapContextMenu: [{\n      type: Output\n    }],\n    mapTouchStart: [{\n      type: Output\n    }],\n    mapTouchEnd: [{\n      type: Output\n    }],\n    mapTouchMove: [{\n      type: Output\n    }],\n    mapTouchCancel: [{\n      type: Output\n    }],\n    mapWheel: [{\n      type: Output\n    }],\n    moveStart: [{\n      type: Output\n    }],\n    move: [{\n      type: Output\n    }],\n    moveEnd: [{\n      type: Output\n    }],\n    mapDragStart: [{\n      type: Output\n    }],\n    mapDrag: [{\n      type: Output\n    }],\n    mapDragEnd: [{\n      type: Output\n    }],\n    zoomStart: [{\n      type: Output\n    }],\n    zoomEvt: [{\n      type: Output\n    }],\n    zoomEnd: [{\n      type: Output\n    }],\n    rotateStart: [{\n      type: Output\n    }],\n    rotate: [{\n      type: Output\n    }],\n    rotateEnd: [{\n      type: Output\n    }],\n    pitchStart: [{\n      type: Output\n    }],\n    pitchEvt: [{\n      type: Output\n    }],\n    pitchEnd: [{\n      type: Output\n    }],\n    boxZoomStart: [{\n      type: Output\n    }],\n    boxZoomEnd: [{\n      type: Output\n    }],\n    boxZoomCancel: [{\n      type: Output\n    }],\n    webGlContextLost: [{\n      type: Output\n    }],\n    webGlContextRestored: [{\n      type: Output\n    }],\n    mapLoad: [{\n      type: Output\n    }],\n    mapCreate: [{\n      type: Output\n    }],\n    idle: [{\n      type: Output\n    }],\n    render: [{\n      type: Output\n    }],\n    mapError: [{\n      type: Output\n    }],\n    data: [{\n      type: Output\n    }],\n    styleData: [{\n      type: Output\n    }],\n    sourceData: [{\n      type: Output\n    }],\n    dataLoading: [{\n      type: Output\n    }],\n    styleDataLoading: [{\n      type: Output\n    }],\n    sourceDataLoading: [{\n      type: Output\n    }],\n    styleImageMissing: [{\n      type: Output\n    }],\n    mapContainer: [{\n      type: ViewChild,\n      args: ['container', {\n        static: true\n      }]\n    }]\n  });\n})();\n", "import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Output, ViewChild, ViewEncapsulation, inject, input } from '@angular/core';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"content\"];\nconst _c1 = [\"*\"];\nexport class MarkerComponent {\n  mapService = inject(MapService);\n  /* Init input */\n  offset = input();\n  anchor = input();\n  clickTolerance = input();\n  /* Dynamic input */\n  feature = input();\n  lngLat = input();\n  draggable = input();\n  popupShown = input();\n  className = input();\n  zIndex = input();\n  pitchAlignment = input();\n  rotationAlignment = input();\n  markerDragStart = new EventEmitter();\n  markerDragEnd = new EventEmitter();\n  markerDrag = new EventEmitter();\n  content;\n  markerInstance;\n  ngOnInit() {\n    if (this.feature() && this.lngLat()) {\n      throw new Error('feature and lngLat input are mutually exclusive');\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['lngLat'] && !changes['lngLat'].isFirstChange()) {\n      this.markerInstance.setLngLat(this.lngLat());\n    }\n    if (changes['feature'] && !changes['feature'].isFirstChange()) {\n      this.markerInstance.setLngLat(this.feature().geometry.coordinates);\n    }\n    if (changes['draggable'] && !changes['draggable'].isFirstChange()) {\n      this.markerInstance.setDraggable(!!this.draggable());\n    }\n    if (changes['popupShown'] && !changes['popupShown'].isFirstChange()) {\n      changes['popupShown'].currentValue ? this.markerInstance.getPopup()?.addTo(this.mapService.mapInstance) : this.markerInstance.getPopup()?.remove();\n    }\n    if (changes['pitchAlignment'] && !changes['pitchAlignment'].isFirstChange()) {\n      this.markerInstance.setPitchAlignment(changes['pitchAlignment'].currentValue);\n    }\n    if (changes['rotationAlignment'] && !changes['rotationAlignment'].isFirstChange()) {\n      this.markerInstance.setRotationAlignment(changes['rotationAlignment'].currentValue);\n    }\n  }\n  ngAfterViewInit() {\n    this.mapService.mapCreated$.subscribe(() => {\n      this.markerInstance = this.mapService.addMarker({\n        markersOptions: {\n          offset: this.offset(),\n          anchor: this.anchor(),\n          pitchAlignment: this.pitchAlignment(),\n          rotationAlignment: this.rotationAlignment(),\n          draggable: this.draggable(),\n          element: this.content.nativeElement,\n          feature: this.feature(),\n          lngLat: this.lngLat(),\n          clickTolerance: this.clickTolerance()\n        },\n        markersEvents: {\n          markerDragStart: this.markerDragStart,\n          markerDrag: this.markerDrag,\n          markerDragEnd: this.markerDragEnd\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.mapService.removeMarker(this.markerInstance);\n    this.markerInstance = undefined;\n  }\n  togglePopup() {\n    this.markerInstance.togglePopup();\n  }\n  updateCoordinates(coordinates) {\n    this.markerInstance.setLngLat(coordinates);\n  }\n  static ɵfac = function MarkerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MarkerComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MarkerComponent,\n    selectors: [[\"mgl-marker\"]],\n    viewQuery: function MarkerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n      }\n    },\n    inputs: {\n      offset: [1, \"offset\"],\n      anchor: [1, \"anchor\"],\n      clickTolerance: [1, \"clickTolerance\"],\n      feature: [1, \"feature\"],\n      lngLat: [1, \"lngLat\"],\n      draggable: [1, \"draggable\"],\n      popupShown: [1, \"popupShown\"],\n      className: [1, \"className\"],\n      zIndex: [1, \"zIndex\"],\n      pitchAlignment: [1, \"pitchAlignment\"],\n      rotationAlignment: [1, \"rotationAlignment\"]\n    },\n    outputs: {\n      markerDragStart: \"markerDragStart\",\n      markerDragEnd: \"markerDragEnd\",\n      markerDrag: \"markerDrag\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c1,\n    decls: 3,\n    vars: 4,\n    consts: [[\"content\", \"\"]],\n    template: function MarkerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", null, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.className());\n        i0.ɵɵstyleProp(\"z-index\", ctx.zIndex());\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-marker',\n      template: `\n    <div [class]=\"className()\" [style.z-index]=\"zIndex()\" #content>\n      <ng-content />\n    </div>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    markerDragStart: [{\n      type: Output\n    }],\n    markerDragEnd: [{\n      type: Output\n    }],\n    markerDrag: [{\n      type: Output\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content', {\n        static: true\n      }]\n    }]\n  });\n})();\n", "import { ChangeDetectionStrategy, Component, ContentChild, Directive, NgZone, TemplateRef, inject, input, signal } from '@angular/core';\nimport { fromEvent, merge, Subscription } from 'rxjs';\nimport { filter, startWith, switchMap } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport { MarkerComponent } from '../marker/marker.component';\nimport { LayerComponent } from '../layer/layer.component';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nconst _c0 = () => ({\n  \"circle-radius\": 0\n});\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction _forTrack0($index, $item) {\n  return this.trackByFeature($item);\n}\nfunction MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const feature_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.clusterPointTpl)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, feature_r1));\n  }\n}\nfunction MarkersForClustersComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mgl-marker\", 1);\n    i0.ɵɵtemplate(1, MarkersForClustersComponent_For_2_Conditional_0_Conditional_1_Template, 1, 4, null, 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"feature\", feature_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.clusterPointTpl ? 1 : -1);\n  }\n}\nfunction MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const feature_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.pointTpl)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, feature_r1));\n  }\n}\nfunction MarkersForClustersComponent_For_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mgl-marker\", 1);\n    i0.ɵɵtemplate(1, MarkersForClustersComponent_For_2_Conditional_1_Conditional_1_Template, 1, 4, null, 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"feature\", feature_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.pointTpl ? 1 : -1);\n  }\n}\nfunction MarkersForClustersComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MarkersForClustersComponent_For_2_Conditional_0_Template, 2, 2, \"mgl-marker\", 1)(1, MarkersForClustersComponent_For_2_Conditional_1_Template, 2, 2, \"mgl-marker\", 1);\n  }\n  if (rf & 2) {\n    const feature_r1 = ctx.$implicit;\n    i0.ɵɵconditional(feature_r1.properties[\"cluster\"] ? 0 : 1);\n  }\n}\nexport class PointDirective {\n  static ɵfac = function PointDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PointDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PointDirective,\n    selectors: [[\"ng-template\", \"mglPoint\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PointDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[mglPoint]'\n    }]\n  }], null, null);\n})();\nexport class ClusterPointDirective {\n  static ɵfac = function ClusterPointDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClusterPointDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ClusterPointDirective,\n    selectors: [[\"ng-template\", \"mglClusterPoint\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClusterPointDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[mglClusterPoint]'\n    }]\n  }], null, null);\n})();\nlet uniqId = 0;\nexport class MarkersForClustersComponent {\n  mapService = inject(MapService);\n  zone = inject(NgZone);\n  /* Init input */\n  source = input.required();\n  /* Dynamic input */\n  customPointIdKey = input();\n  pointTpl;\n  clusterPointTpl;\n  clusterPoints = signal([]);\n  layerId = `mgl-markers-for-clusters-${uniqId++}`;\n  sub = new Subscription();\n  ngAfterContentInit() {\n    const clusterDataUpdate = () => fromEvent(this.mapService.mapInstance, 'data').pipe(filter(e => e.sourceId === this.source() && e.sourceDataType !== 'metadata' && this.mapService.mapInstance.isSourceLoaded(this.source())));\n    const sub = this.mapService.mapCreated$.pipe(switchMap(clusterDataUpdate), switchMap(() => merge(fromEvent(this.mapService.mapInstance, 'move'), fromEvent(this.mapService.mapInstance, 'moveend')).pipe(startWith(undefined)))).subscribe(() => {\n      this.zone.run(() => {\n        this.updateCluster();\n      });\n    });\n    this.sub.add(sub);\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n  }\n  trackByFeature(feature) {\n    if (feature.id) {\n      return feature.id;\n    }\n    const customPointIdKey = this.customPointIdKey();\n    if (!customPointIdKey) {\n      console.warn('[mgl-markers-for-clusters] feature.id is falsy, please provide a custom key');\n      return '';\n    }\n    const id = feature.properties?.[customPointIdKey];\n    if (!id) {\n      console.warn(`[mgl-markers-for-clusters] Custom key [${customPointIdKey}], resolve to falsy for`, feature);\n      return '';\n    }\n    return id;\n  }\n  updateCluster() {\n    const params = {\n      layers: [this.layerId]\n    };\n    if (!this.pointTpl) {\n      params.filter = ['==', 'cluster', true];\n    }\n    const clusterPoints = this.mapService.mapInstance.queryRenderedFeatures(params);\n    // Remove duplicates, because it seems that queryRenderedFeatures can return duplicates\n    const seen = new Set();\n    const unique = [];\n    for (const feature of clusterPoints) {\n      const id = this.trackByFeature(feature);\n      if (!seen.has(id)) {\n        seen.add(id);\n        unique.push(feature);\n      }\n    }\n    this.clusterPoints.set(unique);\n  }\n  static ɵfac = function MarkersForClustersComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MarkersForClustersComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MarkersForClustersComponent,\n    selectors: [[\"mgl-markers-for-clusters\"]],\n    contentQueries: function MarkersForClustersComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PointDirective, 5, TemplateRef);\n        i0.ɵɵcontentQuery(dirIndex, ClusterPointDirective, 5, TemplateRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pointTpl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clusterPointTpl = _t.first);\n      }\n    },\n    inputs: {\n      source: [1, \"source\"],\n      customPointIdKey: [1, \"customPointIdKey\"]\n    },\n    decls: 3,\n    vars: 4,\n    consts: [[\"type\", \"circle\", 3, \"id\", \"source\", \"paint\"], [3, \"feature\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function MarkersForClustersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"mgl-layer\", 0);\n        i0.ɵɵrepeaterCreate(1, MarkersForClustersComponent_For_2_Template, 2, 1, null, null, _forTrack0, true);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"id\", ctx.layerId)(\"source\", ctx.source())(\"paint\", i0.ɵɵpureFunction0(3, _c0));\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.clusterPoints());\n      }\n    },\n    dependencies: [MarkerComponent, LayerComponent, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkersForClustersComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-markers-for-clusters',\n      template: `\n    <mgl-layer\n      [id]=\"layerId\"\n      [source]=\"source()\"\n      type=\"circle\"\n      [paint]=\"{ 'circle-radius': 0 }\"\n    />\n    @for (feature of clusterPoints(); track trackByFeature(feature)) {\n      @if (feature.properties!['cluster']) {\n        <mgl-marker [feature]=\"$any(feature)\">\n          @if (clusterPointTpl) {\n            <ng-template\n              [ngTemplateOutlet]=\"clusterPointTpl\"\n              [ngTemplateOutletContext]=\"{ $implicit: feature }\"\n            />\n          }\n        </mgl-marker>\n      } @else {\n        <mgl-marker [feature]=\"$any(feature)\">\n          @if (pointTpl) {\n            <ng-template\n              [ngTemplateOutlet]=\"pointTpl\"\n              [ngTemplateOutletContext]=\"{ $implicit: feature }\"\n            />\n          }\n        </mgl-marker>\n      }\n    }\n  `,\n      imports: [MarkerComponent, LayerComponent, NgTemplateOutlet],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    pointTpl: [{\n      type: ContentChild,\n      args: [PointDirective, {\n        read: TemplateRef,\n        static: false\n      }]\n    }],\n    clusterPointTpl: [{\n      type: ContentChild,\n      args: [ClusterPointDirective, {\n        read: TemplateRef,\n        static: false\n      }]\n    }]\n  });\n})();\n", "import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Output, ViewChild, inject, input } from '@angular/core';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"content\"];\nconst _c1 = [\"*\"];\nexport class PopupComponent {\n  mapService = inject(MapService);\n  /* Init input */\n  closeButton = input();\n  closeOnClick = input();\n  closeOnMove = input();\n  focusAfterOpen = input();\n  anchor = input();\n  className = input();\n  maxWidth = input();\n  /* Dynamic input */\n  feature = input();\n  lngLat = input();\n  marker = input();\n  offset = input();\n  popupClose = new EventEmitter();\n  popupOpen = new EventEmitter();\n  content;\n  popupInstance;\n  ngOnInit() {\n    if (this.lngLat() && this.marker() || this.feature() && this.lngLat() || this.feature() && this.marker()) {\n      throw new Error('marker, lngLat, feature input are mutually exclusive');\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['lngLat'] && !changes['lngLat'].isFirstChange() || changes['feature'] && !changes['feature'].isFirstChange()) {\n      const newlngLat = changes['lngLat'] ? this.lngLat() : this.feature().geometry.coordinates;\n      this.mapService.removePopupFromMap(this.popupInstance, true);\n      const popupInstanceTmp = this.createPopup();\n      this.mapService.addPopupToMap(popupInstanceTmp, newlngLat, this.popupInstance.isOpen());\n      this.popupInstance = popupInstanceTmp;\n    }\n    if (changes['marker'] && !changes['marker'].isFirstChange()) {\n      const previousMarker = changes['marker'].previousValue;\n      if (previousMarker.markerInstance) {\n        this.mapService.removePopupFromMarker(previousMarker.markerInstance);\n      }\n      if (this.marker() && this.marker().markerInstance && this.popupInstance) {\n        this.mapService.addPopupToMarker(this.marker().markerInstance, this.popupInstance);\n      }\n    }\n    if (changes['offset'] && !changes['offset'].isFirstChange() && this.popupInstance) {\n      this.popupInstance.setOffset(this.offset());\n    }\n  }\n  ngAfterViewInit() {\n    this.popupInstance = this.createPopup();\n    this.addPopup(this.popupInstance);\n  }\n  ngOnDestroy() {\n    if (this.popupInstance) {\n      if (this.lngLat() || this.feature()) {\n        this.mapService.removePopupFromMap(this.popupInstance);\n      } else if (this.marker() && this.marker().markerInstance) {\n        this.mapService.removePopupFromMarker(this.marker().markerInstance);\n      }\n    }\n    this.popupInstance = undefined;\n  }\n  createPopup() {\n    return this.mapService.createPopup({\n      popupOptions: {\n        closeButton: this.closeButton(),\n        closeOnClick: this.closeOnClick(),\n        closeOnMove: this.closeOnMove(),\n        focusAfterOpen: this.focusAfterOpen(),\n        anchor: this.anchor(),\n        offset: this.offset(),\n        className: this.className(),\n        maxWidth: this.maxWidth()\n      },\n      popupEvents: {\n        popupOpen: this.popupOpen,\n        popupClose: this.popupClose\n      }\n    }, this.content.nativeElement);\n  }\n  addPopup(popup) {\n    this.mapService.mapCreated$.subscribe(() => {\n      if (this.lngLat() || this.feature()) {\n        this.mapService.addPopupToMap(popup, this.lngLat() ? this.lngLat() : this.feature().geometry.coordinates);\n      } else if (this.marker() && this.marker().markerInstance) {\n        this.mapService.addPopupToMarker(this.marker().markerInstance, popup);\n      } else {\n        throw new Error('mgl-popup need either lngLat/marker/feature to be set');\n      }\n    });\n  }\n  static ɵfac = function PopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PopupComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PopupComponent,\n    selectors: [[\"mgl-popup\"]],\n    viewQuery: function PopupComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n      }\n    },\n    inputs: {\n      closeButton: [1, \"closeButton\"],\n      closeOnClick: [1, \"closeOnClick\"],\n      closeOnMove: [1, \"closeOnMove\"],\n      focusAfterOpen: [1, \"focusAfterOpen\"],\n      anchor: [1, \"anchor\"],\n      className: [1, \"className\"],\n      maxWidth: [1, \"maxWidth\"],\n      feature: [1, \"feature\"],\n      lngLat: [1, \"lngLat\"],\n      marker: [1, \"marker\"],\n      offset: [1, \"offset\"]\n    },\n    outputs: {\n      popupClose: \"popupClose\",\n      popupOpen: \"popupOpen\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c1,\n    decls: 3,\n    vars: 0,\n    consts: [[\"content\", \"\"]],\n    template: function PopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", null, 0);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-popup',\n      template: '<div #content><ng-content/></div>',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    popupClose: [{\n      type: Output\n    }],\n    popupOpen: [{\n      type: Output\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content', {\n        static: true\n      }]\n    }]\n  });\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class CanvasSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  coordinates = input.required();\n  canvas = input.required();\n  animate = input();\n  sourceAdded = false;\n  sub = new Subscription();\n  ngOnInit() {\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['canvas'] && !changes['canvas'].isFirstChange() || changes['animate'] && !changes['animate'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    } else if (changes['coordinates'] && !changes['coordinates'].isFirstChange()) {\n      const source = this.mapService.getSource(this.id());\n      if (source === undefined) {\n        return;\n      }\n      source.setCoordinates(this.coordinates());\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  init() {\n    const source = {\n      type: 'canvas',\n      coordinates: this.coordinates(),\n      canvas: this.canvas(),\n      animate: this.animate()\n    };\n    this.mapService.addSource(this.id(), source);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function CanvasSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CanvasSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CanvasSourceComponent,\n    selectors: [[\"mgl-canvas-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      coordinates: [1, \"coordinates\"],\n      canvas: [1, \"canvas\"],\n      animate: [1, \"animate\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function CanvasSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CanvasSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-canvas-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class ImageSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  url = input();\n  coordinates = input.required();\n  sub;\n  sourceId;\n  ngOnInit() {\n    this.sub = this.mapService.mapLoaded$.subscribe(() => this.init());\n  }\n  ngOnChanges(changes) {\n    if (this.sourceId === undefined) {\n      return;\n    }\n    const source = this.mapService.getSource(this.sourceId);\n    if (source === undefined) {\n      return;\n    }\n    source.updateImage({\n      url: this.url(),\n      coordinates: changes['coordinates'] === undefined ? undefined : this.coordinates()\n    });\n  }\n  ngOnDestroy() {\n    if (this.sub !== undefined) {\n      this.sub.unsubscribe();\n    }\n    if (this.sourceId !== undefined) {\n      this.mapService.removeSource(this.sourceId);\n      this.sourceId = undefined;\n    }\n  }\n  init() {\n    const imageSource = {\n      type: 'image',\n      url: this.url(),\n      coordinates: this.coordinates()\n    };\n    this.mapService.addSource(this.id(), imageSource);\n    this.sourceId = this.id();\n  }\n  static ɵfac = function ImageSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ImageSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ImageSourceComponent,\n    selectors: [[\"mgl-image-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      url: [1, \"url\"],\n      coordinates: [1, \"coordinates\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function ImageSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImageSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-image-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\n// Typing issue in RasterDEMSourceSpecification\n// type RasterDemSourceInputs = {\n//   [K in keyof Omit<RasterDEMSourceSpecification, 'type'>]: InputSignal<\n//     Omit<RasterDEMSourceSpecification, 'type'>[K]\n//   >;\n// };\nexport class RasterDemSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  url = input();\n  tiles = input();\n  bounds = input();\n  minzoom = input();\n  maxzoom = input();\n  tileSize = input();\n  attribution = input();\n  encoding = input();\n  volatile = input();\n  sourceAdded = false;\n  sub = new Subscription();\n  ngOnInit() {\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['url'] && !changes['url'].isFirstChange() || changes['tiles'] && !changes['tiles'].isFirstChange() || changes['bounds'] && !changes['bounds'].isFirstChange() || changes['minzoom'] && !changes['minzoom'].isFirstChange() || changes['maxzoom'] && !changes['maxzoom'].isFirstChange() || changes['tileSize'] && !changes['tileSize'].isFirstChange() || changes['attribution'] && !changes['attribution'].isFirstChange() || changes['encoding'] && !changes['encoding'].isFirstChange() || changes['volatile'] && !changes['volatile'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  init() {\n    const source = {\n      type: 'raster-dem',\n      url: this.url(),\n      tiles: this.tiles(),\n      bounds: this.bounds(),\n      minzoom: this.minzoom(),\n      maxzoom: this.maxzoom(),\n      tileSize: this.tileSize(),\n      attribution: this.attribution(),\n      encoding: this.encoding(),\n      volatile: this.volatile()\n    };\n    this.mapService.addSource(this.id(), source);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function RasterDemSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RasterDemSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: RasterDemSourceComponent,\n    selectors: [[\"mgl-raster-dem-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      url: [1, \"url\"],\n      tiles: [1, \"tiles\"],\n      bounds: [1, \"bounds\"],\n      minzoom: [1, \"minzoom\"],\n      maxzoom: [1, \"maxzoom\"],\n      tileSize: [1, \"tileSize\"],\n      attribution: [1, \"attribution\"],\n      encoding: [1, \"encoding\"],\n      volatile: [1, \"volatile\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function RasterDemSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RasterDemSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-raster-dem-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class RasterSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  url = input();\n  tiles = input();\n  bounds = input();\n  minzoom = input();\n  maxzoom = input();\n  tileSize = input();\n  scheme = input();\n  attribution = input();\n  volatile = input();\n  sourceAdded = false;\n  sub = new Subscription();\n  ngOnInit() {\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['url'] && !changes['url'].isFirstChange() || changes['tiles'] && !changes['tiles'].isFirstChange() || changes['bounds'] && !changes['bounds'].isFirstChange() || changes['minzoom'] && !changes['minzoom'].isFirstChange() || changes['maxzoom'] && !changes['maxzoom'].isFirstChange() || changes['tileSize'] && !changes['tileSize'].isFirstChange() || changes['scheme'] && !changes['scheme'].isFirstChange() || changes['attribution'] && !changes['attribution'].isFirstChange() || changes['volatile'] && !changes['volatile'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  init() {\n    const source = {\n      type: 'raster',\n      url: this.url(),\n      tiles: this.tiles(),\n      bounds: this.bounds(),\n      minzoom: this.minzoom(),\n      maxzoom: this.maxzoom(),\n      tileSize: this.tileSize(),\n      scheme: this.scheme(),\n      attribution: this.attribution(),\n      volatile: this.volatile()\n    };\n    this.mapService.addSource(this.id(), source);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function RasterSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RasterSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: RasterSourceComponent,\n    selectors: [[\"mgl-raster-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      url: [1, \"url\"],\n      tiles: [1, \"tiles\"],\n      bounds: [1, \"bounds\"],\n      minzoom: [1, \"minzoom\"],\n      maxzoom: [1, \"maxzoom\"],\n      tileSize: [1, \"tileSize\"],\n      scheme: [1, \"scheme\"],\n      attribution: [1, \"attribution\"],\n      volatile: [1, \"volatile\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function RasterSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RasterSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-raster-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class VectorSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  url = input();\n  tiles = input();\n  bounds = input();\n  scheme = input();\n  minzoom = input();\n  maxzoom = input();\n  attribution = input();\n  promoteId = input();\n  volatile = input();\n  sourceAdded = false;\n  sub = new Subscription();\n  ngOnInit() {\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['bounds'] && !changes['bounds'].isFirstChange() || changes['scheme'] && !changes['scheme'].isFirstChange() || changes['minzoom'] && !changes['minzoom'].isFirstChange() || changes['maxzoom'] && !changes['maxzoom'].isFirstChange() || changes['attribution'] && !changes['attribution'].isFirstChange() || changes['promoteId'] && !changes['promoteId'].isFirstChange() || changes['volatile'] && !changes['volatile'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    } else if (changes['url'] && !changes['url'].isFirstChange() || changes['tiles'] && !changes['tiles'].isFirstChange()) {\n      const source = this.mapService.getSource(this.id());\n      if (source === undefined) {\n        return;\n      }\n      if (changes['url'] && this.url()) {\n        source.setUrl(this.url());\n      }\n      if (changes['tiles'] && this.tiles()) {\n        source.setTiles(this.tiles());\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  reload() {\n    this.mapService.getSource(this.id())?.reload();\n  }\n  init() {\n    const source = {\n      type: 'vector',\n      url: this.url(),\n      tiles: this.tiles(),\n      bounds: this.bounds(),\n      scheme: this.scheme(),\n      minzoom: this.minzoom(),\n      maxzoom: this.maxzoom(),\n      attribution: this.attribution(),\n      promoteId: this.promoteId(),\n      volatile: this.volatile()\n    };\n    this.mapService.addSource(this.id(), source);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function VectorSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || VectorSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: VectorSourceComponent,\n    selectors: [[\"mgl-vector-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      url: [1, \"url\"],\n      tiles: [1, \"tiles\"],\n      bounds: [1, \"bounds\"],\n      scheme: [1, \"scheme\"],\n      minzoom: [1, \"minzoom\"],\n      maxzoom: [1, \"maxzoom\"],\n      attribution: [1, \"attribution\"],\n      promoteId: [1, \"promoteId\"],\n      volatile: [1, \"volatile\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function VectorSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VectorSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-vector-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n", "import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';\nimport { fromEvent, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { MapService } from '../map/map.service';\nimport * as i0 from \"@angular/core\";\nexport class VideoSourceComponent {\n  mapService = inject(MapService);\n  /* Init inputs */\n  id = input.required();\n  /* Dynamic inputs */\n  urls = input.required();\n  coordinates = input.required();\n  sourceAdded = false;\n  sub = new Subscription();\n  ngOnInit() {\n    const sub1 = this.mapService.mapLoaded$.subscribe(() => {\n      this.init();\n      const sub = fromEvent(this.mapService.mapInstance, 'styledata').pipe(filter(() => !this.mapService.mapInstance.getSource(this.id()))).subscribe(() => {\n        this.init();\n      });\n      this.sub.add(sub);\n    });\n    this.sub.add(sub1);\n  }\n  ngOnChanges(changes) {\n    if (!this.sourceAdded) {\n      return;\n    }\n    if (changes['urls'] && !changes['urls'].isFirstChange()) {\n      this.ngOnDestroy();\n      this.ngOnInit();\n    } else if (changes['coordinates'] && !changes['coordinates'].isFirstChange()) {\n      const source = this.mapService.getSource(this.id());\n      if (source === undefined) {\n        return;\n      }\n      source.setCoordinates(this.coordinates());\n    }\n  }\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    if (this.sourceAdded) {\n      this.mapService.removeSource(this.id());\n      this.sourceAdded = false;\n    }\n  }\n  pause() {\n    this.mapService.getSource(this.id())?.pause();\n  }\n  play() {\n    this.mapService.getSource(this.id())?.play();\n  }\n  getVideo() {\n    return this.mapService.getSource(this.id())?.getVideo();\n  }\n  init() {\n    const source = {\n      type: 'video',\n      urls: this.urls(),\n      coordinates: this.coordinates()\n    };\n    this.mapService.addSource(this.id(), source);\n    this.sourceAdded = true;\n  }\n  static ɵfac = function VideoSourceComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || VideoSourceComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: VideoSourceComponent,\n    selectors: [[\"mgl-video-source\"]],\n    inputs: {\n      id: [1, \"id\"],\n      urls: [1, \"urls\"],\n      coordinates: [1, \"coordinates\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 0,\n    vars: 0,\n    template: function VideoSourceComponent_Template(rf, ctx) {},\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VideoSourceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mgl-video-source',\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uBAAmC;AAG5B,IAAM,iBAAiB,IAAI,eAAe,cAAc;AACxD,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,OAAO,OAAO,MAAM;AAAA,EACpB,iBAAiB,OAAO,gBAAgB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,QAAQ;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,IAAI,aAAa;AAAA,EAC9B,YAAY,IAAI,aAAa;AAAA,EAC7B,kBAAkB,CAAC;AAAA,EACnB,iBAAiB,CAAC;AAAA,EAClB,mBAAmB,CAAC;AAAA,EACpB,eAAe,IAAI,aAAa;AAAA,EAChC,cAAc;AACZ,SAAK,cAAc,KAAK,WAAW,aAAa;AAChD,SAAK,aAAa,KAAK,UAAU,aAAa;AAAA,EAChD;AAAA,EACA,MAAM,SAAS;AACb,UAAM,aAAa,iCACd,QAAQ,aADM;AAAA,MAEjB,SAAS,QAAQ,WAAW,UAAU,CAAC;AAAA,MACvC,MAAM,QAAQ,WAAW,OAAO,CAAC;AAAA,MACjC,OAAO,QAAQ,WAAW,QAAQ,CAAC;AAAA,MACnC,aAAa,QAAQ,eAAe,KAAK,kBAAkB;AAAA,IAC7D;AACA,SAAK,UAAU,UAAU;AACzB,SAAK,WAAW,QAAQ,SAAS;AACjC,SAAK,YAAY,QAAQ;AACzB,SAAK,WAAW,KAAK,MAAS;AAC9B,SAAK,WAAW,SAAS;AAEzB,QAAI,QAAQ,UAAU,UAAU,UAAU;AACxC,WAAK,KAAK,IAAI,MAAM;AAClB,gBAAQ,UAAU,UAAU,KAAK,KAAK,WAAW;AAAA,MACnD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,aAAa;AACpB,WAAK,aAAa,YAAY;AAC9B,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,iBAAiB,YAAY;AAC3B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,cAAc,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,WAAW,OAAO;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,WAAW,OAAO;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,YAAY,QAAQ;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,YAAY,QAAQ;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB,QAAQ;AAC9B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,qBAAqB,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,WAAW,OAAO,IAAI,KAAK,YAAY,WAAW,QAAQ;AAAA,IACtF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,WAAW,OAAO,IAAI,KAAK,YAAY,WAAW,QAAQ;AAAA,IACtF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,WAAW,OAAO,IAAI,KAAK,YAAY,WAAW,QAAQ;AAAA,IACtF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,QAAQ;AAC5B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,gBAAgB,OAAO,IAAI,KAAK,YAAY,gBAAgB,QAAQ;AAAA,IAChG,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,QAAQ;AAC5B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,gBAAgB,OAAO,IAAI,KAAK,YAAY,gBAAgB,QAAQ;AAAA,IAChG,CAAC;AAAA,EACH;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,SAAS,OAAO,IAAI,KAAK,YAAY,SAAS,QAAQ;AAAA,IAClF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,QAAQ;AACpB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,QAAQ,OAAO,IAAI,KAAK,YAAY,QAAQ,QAAQ;AAAA,IAChF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,QAAQ;AACpB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,eAAS,KAAK,YAAY,QAAQ,OAAO,IAAI,KAAK,YAAY,QAAQ,QAAQ;AAAA,IAChF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,SAAS,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,WAAW;AACzB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,aAAa,SAAS;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,SAAS,KAAK,YAAY,mBAAmB;AACnD,WAAO,MAAM,SAAS;AAAA,EACxB;AAAA,EACA,sBAAsB,YAAY,YAAY;AAC5C,WAAO,KAAK,YAAY,sBAAsB,YAAY,UAAU;AAAA,EACtE;AAAA,EACA,MAAM,QAAQ,SAAS;AACrB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,MAAM,QAAQ,OAAO;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,KAAK,cAAc,eAAe,MAAM,QAAQ,SAAS,OAAO;AAC9D,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,YAAY,EAAE,iCAC1B,gBAD0B;AAAA,QAE7B,MAAM,QAAQ,OAAO,OAAO,KAAK,YAAY,QAAQ;AAAA,QACrD,QAAQ,UAAU,OAAO,SAAS,KAAK,YAAY,UAAU;AAAA,QAC7D,SAAS,WAAW,OAAO,UAAU,KAAK,YAAY,WAAW;AAAA,QACjE,OAAO,SAAS,OAAO,QAAQ,KAAK,YAAY,SAAS;AAAA,MAC3D,EAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO,YAAY,QAAQ;AAClC,SAAK,KAAK,kBAAkB,MAAM;AAChC,aAAO,KAAK,MAAM,YAAY,EAAE,QAAQ,SAAO;AAC7C,cAAM,OAAO;AACb,YAAI,MAAM,aAAa,IAAI,MAAM,QAAW;AAC1C,iBAAO,MAAM,aAAa,IAAI;AAAA,QAChC;AAAA,MACF,CAAC;AACD,WAAK,YAAY,SAAS,MAAM,cAAc,MAAM;AACpD,UAAI,YAAY;AACd,YAAI,MAAM,YAAY,WAAW,UAAU;AACzC,eAAK,YAAY,GAAG,SAAS,MAAM,aAAa,IAAI,SAAO;AACzD,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,WAAW,KAAK,GAAG;AAAA,YACvC,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,cAAc,UAAU;AAC5C,eAAK,YAAY,GAAG,YAAY,MAAM,aAAa,IAAI,SAAO;AAC5D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,cAAc,KAAK,GAAG;AAAA,YAC1C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,eAAe,UAAU;AAC7C,eAAK,YAAY,GAAG,aAAa,MAAM,aAAa,IAAI,SAAO;AAC7D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,eAAe,KAAK,GAAG;AAAA,YAC3C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,aAAa,UAAU;AAC3C,eAAK,YAAY,GAAG,WAAW,MAAM,aAAa,IAAI,SAAO;AAC3D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,aAAa,KAAK,GAAG;AAAA,YACzC,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,gBAAgB,UAAU;AAC9C,eAAK,YAAY,GAAG,cAAc,MAAM,aAAa,IAAI,SAAO;AAC9D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,gBAAgB,KAAK,GAAG;AAAA,YAC5C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,gBAAgB,UAAU;AAC9C,eAAK,YAAY,GAAG,cAAc,MAAM,aAAa,IAAI,SAAO;AAC9D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,gBAAgB,KAAK,GAAG;AAAA,YAC5C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,eAAe,UAAU;AAC7C,eAAK,YAAY,GAAG,aAAa,MAAM,aAAa,IAAI,SAAO;AAC7D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,eAAe,KAAK,GAAG;AAAA,YAC3C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,eAAe,UAAU;AAC7C,eAAK,YAAY,GAAG,aAAa,MAAM,aAAa,IAAI,SAAO;AAC7D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,eAAe,KAAK,GAAG;AAAA,YAC3C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,cAAc,UAAU;AAC5C,eAAK,YAAY,GAAG,YAAY,MAAM,aAAa,IAAI,SAAO;AAC5D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,cAAc,KAAK,GAAG;AAAA,YAC1C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,iBAAiB,UAAU;AAC/C,eAAK,YAAY,GAAG,eAAe,MAAM,aAAa,IAAI,SAAO;AAC/D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,iBAAiB,KAAK,GAAG;AAAA,YAC7C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,gBAAgB,UAAU;AAC9C,eAAK,YAAY,GAAG,cAAc,MAAM,aAAa,IAAI,SAAO;AAC9D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,gBAAgB,KAAK,GAAG;AAAA,YAC5C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,cAAc,UAAU;AAC5C,eAAK,YAAY,GAAG,YAAY,MAAM,aAAa,IAAI,SAAO;AAC5D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,cAAc,KAAK,GAAG;AAAA,YAC1C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI,MAAM,YAAY,iBAAiB,UAAU;AAC/C,eAAK,YAAY,GAAG,eAAe,MAAM,aAAa,IAAI,SAAO;AAC/D,iBAAK,KAAK,IAAI,MAAM;AAClB,oBAAM,YAAY,iBAAiB,KAAK,GAAG;AAAA,YAC7C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,KAAK,kBAAkB,MAAM;AAChC,UAAI,KAAK,YAAY,SAAS,OAAO,KAAK,MAAM;AAC9C,aAAK,YAAY,YAAY,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,UAAU;AAAA,MACd,QAAQ,OAAO,eAAe;AAAA,MAC9B,QAAQ,OAAO,eAAe;AAAA,MAC9B,WAAW,OAAO,eAAe;AAAA,MACjC,mBAAmB,OAAO,eAAe;AAAA,MACzC,gBAAgB,OAAO,eAAe;AAAA,MACtC,gBAAgB,OAAO,eAAe;AAAA,IACxC;AACA,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,YAAM,OAAO;AACb,UAAI,QAAQ,IAAI,MAAM,QAAW;AAC/B,eAAO,QAAQ,IAAI;AAAA,MACrB;AAAA,IACF,CAAC;AACD,QAAI,OAAO,eAAe,QAAQ,WAAW,SAAS,GAAG;AACvD,cAAQ,UAAU,OAAO,eAAe;AAAA,IAC1C;AACA,UAAM,iBAAiB,IAAI,wBAAO,OAAO;AACzC,QAAI,OAAO,cAAc,gBAAgB,UAAU;AACjD,qBAAe,GAAG,aAAa,WAAS;AACtC,YAAI,OAAO;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,eAAK,KAAK,IAAI,MAAM;AAClB,mBAAO,cAAc,gBAAgB,KAAK,MAAM;AAAA,UAClD,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,cAAc,WAAW,UAAU;AAC5C,qBAAe,GAAG,QAAQ,WAAS;AACjC,YAAI,OAAO;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,eAAK,KAAK,IAAI,MAAM;AAClB,mBAAO,cAAc,WAAW,KAAK,MAAM;AAAA,UAC7C,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,OAAO,cAAc,cAAc,UAAU;AAC/C,qBAAe,GAAG,WAAW,WAAS;AACpC,YAAI,OAAO;AACT,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,eAAK,KAAK,IAAI,MAAM;AAClB,mBAAO,cAAc,cAAc,KAAK,MAAM;AAAA,UAChD,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,SAAS,OAAO,eAAe,UAAU,OAAO,eAAe,QAAQ,SAAS,cAAc,OAAO,eAAe;AAC1H,mBAAe,UAAU,MAAM;AAC/B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,qBAAe,MAAM,KAAK,WAAW;AACrC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,aAAa,QAAQ;AACnB,SAAK,gBAAgB,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,YAAY,OAAO,SAAS;AAC1B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,KAAK,MAAM,YAAY,EAAE,QAAQ,SAAO;AAC7C,cAAM,OAAO;AACb,eAAO,MAAM,aAAa,IAAI,MAAM,UAAa,OAAO,MAAM,aAAa,IAAI;AAAA,MACjF,CAAC;AACD,YAAM,gBAAgB,IAAI,uBAAM,MAAM,YAAY;AAClD,oBAAc,cAAc,OAAO;AACnC,UAAI,MAAM,YAAY,WAAW,UAAU;AACzC,sBAAc,GAAG,SAAS,MAAM;AAC9B,eAAK,KAAK,IAAI,MAAM;AAClB,kBAAM,YAAY,WAAW,KAAK;AAAA,UACpC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,UAAI,MAAM,YAAY,UAAU,UAAU;AACxC,sBAAc,GAAG,QAAQ,MAAM;AAC7B,eAAK,KAAK,IAAI,MAAM;AAClB,kBAAM,YAAY,UAAU,KAAK;AAAA,UACnC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,cAAc,OAAO,QAAQ,gBAAgB,OAAO;AAClD,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,UAAI,iBAAiB,MAAM,YAAY;AACrC,eAAO,MAAM,WAAW,MAAM;AAAA,MAChC;AACA,YAAM,UAAU,MAAM;AACtB,YAAM,MAAM,KAAK,WAAW;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,QAAQ,OAAO;AAC9B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,SAAS,KAAK;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,OAAO,iBAAiB,OAAO;AAChD,QAAI,kBAAkB,MAAM,YAAY;AACtC,aAAO,MAAM,WAAW,OAAO;AAAA,IACjC;AACA,SAAK,eAAe,KAAK,KAAK;AAAA,EAChC;AAAA,EACA,sBAAsB,QAAQ;AAC5B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,SAAS,MAAS;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,WAAW,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,WAAW,SAAS,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,cAAc,OAAO;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACM,gBAAgB,SAAS,KAAK,SAAS;AAAA;AAC3C,aAAO,KAAK,KAAK,kBAAkB,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxE,aAAK,YAAY,UAAU,KAAK,CAAC,OAAO,UAAU;AAChD,cAAI,OAAO;AACT,mBAAO,KAAK;AACZ;AAAA,UACF;AACA,cAAI,CAAC,OAAO;AACV,mBAAO,IAAI,MAAM,kBAAkB,CAAC;AACpC;AAAA,UACF;AACA,eAAK,SAAS,SAAS,OAAO,OAAO;AACrC,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA,EACA,SAAS,SAAS,MAAM,SAAS;AAC/B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,SAAS,SAAS,MAAM,OAAO;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,iBAAiB,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,UAAU,UAAU,QAAQ;AAC1B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,cAAM,OAAO;AACb,eAAO,OAAO,IAAI,MAAM,UAAa,OAAO,OAAO,IAAI;AAAA,MACzD,CAAC;AACD,WAAK,YAAY,UAAU,UAAU,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,UAAU,UAAU;AAClB,WAAO,KAAK,YAAY,UAAU,QAAQ;AAAA,EAC5C;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,qBAAqB,QAAQ,EAAE,QAAQ,WAAS,KAAK,YAAY,YAAY,MAAM,EAAE,CAAC;AAC3F,WAAK,YAAY,aAAa,QAAQ;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB,SAAS,OAAO;AACvC,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,KAAK,KAAK,EAAE,QAAQ,SAAO;AAChC,cAAM,OAAO;AAEb,aAAK,YAAY,iBAAiB,SAAS,MAAM,MAAM,IAAI,CAAC;AAAA,MAC9D,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B,SAAS,QAAQ;AACzC,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,aAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,cAAM,OAAO;AAEb,aAAK,YAAY,kBAAkB,SAAS,MAAM,OAAO,IAAI,CAAC;AAAA,MAChE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,eAAe,SAASA,SAAQ;AAC9B,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,UAAU,SAASA,OAAM;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,eAAe,SAAS,UAAU;AAChC,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,UAAU,SAAS,QAAQ;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,SAAS,SAAS,SAAS;AAC3C,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,kBAAkB,SAAS,UAAU,UAAU,GAAG,UAAU,UAAU,EAAE;AAAA,IAC3F,CAAC;AAAA,EACH;AAAA,EACA,UAAU,QAAQ,SAAS;AACzB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,UAAU,QAAQ,OAAO;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,QAAQ,SAAS,SAAS;AAC7C,WAAO,KAAK,KAAK,kBAAkB,MAAM;AACvC,WAAK,YAAY,qBAAqB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,SAAS,OAAO;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,cAAc;AACnB,WAAK,aAAa;AAClB,WAAK,aAAa;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,UAAU,SAAS;AACjB,WAAO,uBAAuB;AAC9B,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,YAAM,OAAO;AACb,UAAI,QAAQ,IAAI,MAAM,QAAW;AAC/B,eAAO,QAAQ,IAAI;AAAA,MACrB;AAAA,IACF,CAAC;AACD,SAAK,cAAc,IAAI,qBAAI,OAAO;AAClC,gBAAY;AAAA,MACV,OAAO,MAAM;AACX,aAAK,aAAa;AAAA,MACpB;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,eAAW,UAAU,KAAK,iBAAiB;AACzC,aAAO,OAAO;AAAA,IAChB;AACA,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,eAAe;AACb,eAAW,SAAS,KAAK,gBAAgB;AACvC,YAAM,OAAO;AAAA,IACf;AACA,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,eAAe;AACb,eAAW,WAAW,KAAK,kBAAkB;AAC3C,WAAK,YAAY,YAAY,OAAO;AAAA,IACtC;AACA,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,qBAAqB,UAAU;AAC7B,UAAM,SAAS,KAAK,YAAY,SAAS,EAAE;AAC3C,QAAI,UAAU,MAAM;AAClB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,OAAO,OAAK,YAAY,IAAI,EAAE,WAAW,WAAW,KAAK;AAAA,EACzE;AAAA,EACA,WAAW,QAAQ;AACjB,SAAK,YAAY,GAAG,QAAQ,SAAO;AACjC,WAAK,UAAU,KAAK,MAAS;AAC7B,WAAK,UAAU,SAAS;AACxB,WAAK,KAAK,IAAI,MAAM;AAClB,eAAO,QAAQ,KAAK,GAAG;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,UAAU,SAAO,KAAK,KAAK,IAAI,MAAM;AACvD,eAAO,UAAU,KAAK,GAAG;AAAA,MAC3B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,UAAU,SAAO,KAAK,KAAK,IAAI,MAAM;AACvD,eAAO,UAAU,KAAK,GAAG;AAAA,MAC3B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM;AAC1D,eAAO,aAAa,KAAK,GAAG;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,WAAK,YAAY,GAAG,WAAW,SAAO,KAAK,KAAK,IAAI,MAAM;AACxD,eAAO,WAAW,KAAK,GAAG;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM;AAC1D,eAAO,aAAa,KAAK,GAAG;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,YAAY,GAAG,SAAS,SAAO,KAAK,KAAK,IAAI,MAAM;AACtD,eAAO,SAAS,KAAK,GAAG;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,YAAY,GAAG,YAAY,SAAO,KAAK,KAAK,IAAI,MAAM;AACzD,eAAO,YAAY,KAAK,GAAG;AAAA,MAC7B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM;AAC1D,eAAO,aAAa,KAAK,GAAG;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,YAAY,GAAG,YAAY,SAAO,KAAK,KAAK,IAAI,MAAM;AACzD,eAAO,YAAY,KAAK,GAAG;AAAA,MAC7B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,eAAe,UAAU;AAClC,WAAK,YAAY,GAAG,eAAe,SAAO,KAAK,KAAK,IAAI,MAAM;AAC5D,eAAO,eAAe,KAAK,GAAG;AAAA,MAChC,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,cAAc,UAAU;AACjC,WAAK,YAAY,GAAG,cAAc,SAAO,KAAK,KAAK,IAAI,MAAM;AAC3D,eAAO,cAAc,KAAK,GAAG;AAAA,MAC/B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,YAAY,GAAG,YAAY,SAAO,KAAK,KAAK,IAAI,MAAM;AACzD,eAAO,YAAY,KAAK,GAAG;AAAA,MAC7B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM;AAC1D,eAAO,aAAa,KAAK,GAAG;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,eAAe,UAAU;AAClC,WAAK,YAAY,GAAG,eAAe,SAAO,KAAK,KAAK,IAAI,MAAM;AAC5D,eAAO,eAAe,KAAK,GAAG;AAAA,MAChC,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,YAAY,GAAG,SAAS,SAAO,KAAK,KAAK,IAAI,MAAM;AACtD,eAAO,SAAS,KAAK,GAAG;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,UAAU,KAAK,GAAG,CAAC,CAAC;AAAA,IACzF;AACA,QAAI,OAAO,KAAK,UAAU;AACxB,WAAK,YAAY,GAAG,QAAQ,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,IAC/E;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,YAAY,GAAG,WAAW,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,QAAQ,KAAK,GAAG,CAAC,CAAC;AAAA,IACrF;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,aAAa,KAAK,GAAG,CAAC,CAAC;AAAA,IAC5F;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,YAAY,GAAG,QAAQ,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,QAAQ,KAAK,GAAG,CAAC,CAAC;AAAA,IAClF;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,WAAK,YAAY,GAAG,WAAW,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,IACxF;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,aAAa,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,UAAU,KAAK,CAAC,CAAC;AAAA,IACrF;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,YAAY,GAAG,QAAQ,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC9E;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,YAAY,GAAG,WAAW,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,QAAQ,KAAK,CAAC,CAAC;AAAA,IACjF;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,YAAY,GAAG,eAAe,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,YAAY,KAAK,GAAG,CAAC,CAAC;AAAA,IAC7F;AACA,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,YAAY,GAAG,UAAU,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,IACnF;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,UAAU,KAAK,GAAG,CAAC,CAAC;AAAA,IACzF;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,WAAK,YAAY,GAAG,cAAc,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,WAAW,KAAK,CAAC,CAAC;AAAA,IACvF;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,YAAY,GAAG,SAAS,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,IAChF;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,YAAY,GAAG,YAAY,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,IACnF;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,GAAG,gBAAgB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,aAAa,KAAK,GAAG,CAAC,CAAC;AAAA,IAC/F;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,WAAK,YAAY,GAAG,cAAc,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,IAC3F;AACA,QAAI,OAAO,cAAc,UAAU;AACjC,WAAK,YAAY,GAAG,iBAAiB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,cAAc,KAAK,GAAG,CAAC,CAAC;AAAA,IACjG;AACA,QAAI,OAAO,iBAAiB,UAAU;AACpC,WAAK,YAAY,GAAG,oBAAoB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,iBAAiB,KAAK,GAAG,CAAC,CAAC;AAAA,IACvG;AACA,QAAI,OAAO,qBAAqB,UAAU;AACxC,WAAK,YAAY,GAAG,wBAAwB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,qBAAqB,KAAK,GAAG,CAAC,CAAC;AAAA,IAC/G;AACA,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,YAAY,GAAG,UAAU,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,IAC/E;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,YAAY,GAAG,SAAS,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,IAAI,KAAK,CAAC,CAAC;AAAA,IAC1F;AACA,QAAI,OAAO,KAAK,UAAU;AACxB,WAAK,YAAY,GAAG,QAAQ,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,IAC/E;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,YAAY,GAAG,aAAa,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,UAAU,KAAK,GAAG,CAAC,CAAC;AAAA,IACzF;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,WAAK,YAAY,GAAG,cAAc,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,IAC3F;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,WAAK,YAAY,GAAG,eAAe,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,YAAY,KAAK,GAAG,CAAC,CAAC;AAAA,IAC7F;AACA,QAAI,OAAO,iBAAiB,UAAU;AACpC,WAAK,YAAY,GAAG,oBAAoB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,iBAAiB,KAAK,GAAG,CAAC,CAAC;AAAA,IACvG;AACA,QAAI,OAAO,kBAAkB,UAAU;AACrC,WAAK,YAAY,GAAG,qBAAqB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,kBAAkB,KAAK,GAAG,CAAC,CAAC;AAAA,IACzG;AACA,QAAI,OAAO,kBAAkB,UAAU;AACrC,WAAK,YAAY,GAAG,qBAAqB,SAAO,KAAK,KAAK,IAAI,MAAM,OAAO,kBAAkB,KAAK,GAAG,CAAC,CAAC;AAAA,IACzG;AACA,QAAI,OAAO,KAAK,UAAU;AACxB,WAAK,YAAY,GAAG,QAAQ,MAAM,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,YAAW;AAAA,EACtB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACvsBI,SAAS,gBAAgB,QAAQ;AACtC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB;AACF;;;ACLA,IAAAC,oBAAmC;;;ACEnC,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AACT,IAAM,gBAAN,MAAoB;AAAA,EACzB;AAAA,EACA,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,WAAW,YAAY,KAAK,SAAS;AAAA,EAC7D;AAAA,EACA,qBAAqB;AACnB,WAAO;AAAA,EACT;AACF;AACO,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,WAAW,MAAM;AAAA,EACjB;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,qBAAqB;AACnB,QAAI,KAAK,QAAQ,cAAc,WAAW,QAAQ;AAChD,WAAK,UAAU,IAAI,cAAc,KAAK,QAAQ,aAAa;AAC3D,WAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,aAAK,WAAW,WAAW,KAAK,SAAS,KAAK,SAAS,CAAC;AACxD,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,WAAW,cAAc,KAAK,OAAO;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,eAAe,CAAC;AAAA,IAC9C,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AD1FI,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,UAAU,MAAM;AAAA,EAChB,oBAAoB,MAAM;AAAA,EAC1B,qBAAqB;AACnB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,iBAAiB,SAAS;AACjC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,YAAM,UAAU,CAAC;AACjB,YAAM,UAAU,KAAK,QAAQ;AAC7B,YAAM,oBAAoB,KAAK,kBAAkB;AACjD,UAAI,YAAY,QAAW;AACzB,gBAAQ,UAAU;AAAA,MACpB;AACA,UAAI,sBAAsB,QAAW;AACnC,gBAAQ,oBAAoB;AAAA,MAC9B;AACA,WAAK,iBAAiB,UAAU,IAAI,qCAAmB,OAAO;AAC9D,WAAK,WAAW,WAAW,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAA6B;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,IACtC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,IAC5C;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AEjDH,IAAAC,oBAAkC;AAI3B,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EACtC,aAAa,OAAO,UAAU;AAAA,EAC9B,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,YAAY,MAAM;AAAA,EAClB,eAAe;AACb,SAAK,WAAW,YAAY,OAAO;AAAA,EACrC;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,iBAAiB,SAAS;AACjC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,WAAK,iBAAiB,UAAU,IAAI,oCAAkB;AAAA,QACpD,WAAW,KAAK,UAAU;AAAA,MAC5B,CAAC;AACD,WAAK,WAAW,WAAW,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,0BAA0B,SAAS,qEAAqE,QAAQ;AAC5H,iBAAO,IAAI,aAAa,OAAO,MAAM;AAAA,QACvC,GAAG,OAAU,eAAe;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,WAAW;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC,CAAC,eAAe,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACvDH,IAAAC,oBAAiC;AAI1B,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EACrC,aAAa,OAAO,UAAU;AAAA,EAC9B,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,kBAAkB,MAAM;AAAA,EACxB,mBAAmB,MAAM;AAAA,EACzB,oBAAoB,MAAM;AAAA,EAC1B,mBAAmB,MAAM;AAAA,EACzB,kBAAkB,MAAM;AAAA,EACxB,YAAY,IAAI,aAAa;AAAA,EAC7B,qBAAqB;AACnB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,iBAAiB,SAAS;AACjC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,YAAM,UAAU;AAAA,QACd,iBAAiB,KAAK,gBAAgB;AAAA,QACtC,kBAAkB,KAAK,iBAAiB;AAAA,QACxC,mBAAmB,KAAK,kBAAkB;AAAA,QAC1C,kBAAkB,KAAK,iBAAiB;AAAA,QACxC,iBAAiB,KAAK,gBAAgB;AAAA,MACxC;AACA,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,cAAM,OAAO;AACb,YAAI,QAAQ,IAAI,MAAM,QAAW;AAC/B,iBAAO,QAAQ,IAAI;AAAA,QACrB;AAAA,MACF,CAAC;AACD,WAAK,iBAAiB,UAAU,IAAI,mCAAiB,OAAO;AAC5D,WAAK,iBAAiB,QAAQ,GAAG,aAAa,UAAQ;AACpD,aAAK,UAAU,KAAK,IAAI;AAAA,MAC1B,CAAC;AACD,WAAK,WAAW,WAAW,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,IACxC;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACtEH,IAAAC,oBAAkC;AAI3B,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EACtC,aAAa,OAAO,UAAU;AAAA,EAC9B,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,cAAc,MAAM;AAAA,EACpB,WAAW,MAAM;AAAA,EACjB,qBAAqB;AACnB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,iBAAiB,SAAS;AACjC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,YAAM,UAAU,CAAC;AACjB,YAAM,cAAc,KAAK,YAAY;AACrC,YAAM,WAAW,KAAK,SAAS;AAC/B,UAAI,gBAAgB,QAAW;AAC7B,gBAAQ,cAAc;AAAA,MACxB;AACA,UAAI,aAAa,QAAW;AAC1B,gBAAQ,WAAW;AAAA,MACrB;AACA,WAAK,iBAAiB,UAAU,IAAI,oCAAkB,OAAO;AAC7D,WAAK,WAAW,WAAW,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,QAAQ;AAAA,MACN,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjDH,IAAAC,oBAA6B;AAItB,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,aAAa,OAAO,UAAU;AAAA,EAC9B,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,WAAW,MAAM;AAAA;AAAA,EAEjB,OAAO,MAAM;AAAA,EACb,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,cAAc,GAAG;AACvD,WAAK,iBAAiB,QAAQ,QAAQ,QAAQ,MAAM,EAAE,YAAY;AAAA,IACpE;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,iBAAiB,SAAS;AACjC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,YAAM,UAAU,CAAC;AACjB,YAAM,WAAW,KAAK,SAAS;AAC/B,YAAM,OAAO,KAAK,KAAK;AACvB,UAAI,aAAa,QAAW;AAC1B,gBAAQ,WAAW;AAAA,MACrB;AACA,UAAI,SAAS,QAAW;AACtB,gBAAQ,OAAO;AAAA,MACjB;AACA,WAAK,iBAAiB,UAAU,IAAI,+BAAa,OAAO;AACxD,WAAK,WAAW,WAAW,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,IAChC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,MAAM,CAAC,GAAG,MAAM;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACpDI,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAClC,aAAa,OAAO,UAAU;AAAA,EAC9B,OAAO,OAAO,MAAM;AAAA;AAAA,EAEpB,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,OAAO,MAAM;AAAA,EACb,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,cAAc,MAAM;AAAA,EACpB,SAAS,MAAM;AAAA,EACf,YAAY,MAAM;AAAA,EAClB,UAAU,MAAM;AAAA,EAChB,gBAAgB,MAAM;AAAA,EACtB,iBAAiB,MAAM;AAAA,EACvB,mBAAmB,MAAM;AAAA,EACzB,oBAAoB,MAAM;AAAA,EAC1B,cAAc,MAAM;AAAA,EACpB,aAAa,MAAM;AAAA,EACnB,YAAY,MAAM;AAAA,EAClB,SAAS,MAAM;AAAA,EACf,UAAU,MAAM;AAAA,EAChB,oBAAoB,IAAI,QAAQ;AAAA,EAChC,MAAM,IAAI,aAAa;AAAA,EACvB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,WAAW;AACT,QAAI,CAAC,KAAK,KAAK,GAAG;AAChB,WAAK,KAAK,IAAI;AAAA,QACZ,MAAM;AAAA,QACN,UAAU,CAAC;AAAA,MACb,CAAC;AAAA,IACH;AACA,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,WAAW,KAAK,CAAC,QAAQ,WAAW,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,eAAe,KAAK,CAAC,QAAQ,eAAe,EAAE,cAAc,KAAK,QAAQ,gBAAgB,KAAK,CAAC,QAAQ,gBAAgB,EAAE,cAAc,KAAK,QAAQ,kBAAkB,KAAK,CAAC,QAAQ,kBAAkB,EAAE,cAAc,KAAK,QAAQ,mBAAmB,KAAK,CAAC,QAAQ,mBAAmB,EAAE,cAAc,KAAK,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,KAAK,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,KAAK,QAAQ,WAAW,KAAK,CAAC,QAAQ,WAAW,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7+B,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,cAAc,GAAG;AACvD,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,UAAI,WAAW,QAAW;AACxB;AAAA,MACF;AACA,aAAO,QAAQ,KAAK,KAAK,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,wBAAwB,WAAW;AAAA;AACvC,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,aAAO,KAAK,KAAK,IAAI,MAAS;AAAG,mBAAI,QAAQ,CAAC,SAAS,WAAW;AAChE,iBAAO,wBAAwB,WAAW,CAAC,OAAO,SAAS;AACzD,gBAAI,OAAO;AACT,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,QAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,mBAAmB,WAAW;AAAA;AAClC,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,aAAO,KAAK,KAAK,IAAI,MAAS;AAAG,mBAAI,QAAQ,CAAC,SAAS,WAAW;AAChE,iBAAO,mBAAmB,WAAW,CAAC,OAAO,aAAa;AACxD,gBAAI,OAAO;AACT,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ,QAAQ;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,QAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,iBAAiB,WAAW,OAAO,QAAQ;AAAA;AAC/C,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,aAAO,KAAK,KAAK,IAAI,MAAS;AAAG,mBAAI,QAAQ,CAAC,SAAS,WAAW;AAChE,iBAAO,iBAAiB,WAAW,OAAO,QAAQ,CAAC,OAAO,aAAa;AACrE,gBAAI,OAAO;AACT,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ,YAAY,CAAC,CAAC;AAAA,YACxB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,QAAC;AAAA,IACJ;AAAA;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,aAAa,KAAK,KAAK;AAC7B,eAAW,SAAS,KAAK,OAAO;AAChC,SAAK,kBAAkB,KAAK,IAAI;AAAA,EAClC;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,aAAa,KAAK,KAAK;AAC7B,UAAM,QAAQ,WAAW,SAAS,QAAQ,OAAO;AACjD,QAAI,QAAQ,IAAI;AACd,iBAAW,SAAS,OAAO,OAAO,CAAC;AAAA,IACrC;AACA,SAAK,kBAAkB,KAAK,IAAI;AAAA,EAClC;AAAA,EACA,mBAAmB;AACjB,WAAO,EAAE,KAAK;AAAA,EAChB;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,MAAM,KAAK,KAAK;AAAA,MAChB,SAAS,KAAK,QAAQ;AAAA,MACtB,SAAS,KAAK,QAAQ;AAAA,MACtB,aAAa,KAAK,YAAY;AAAA,MAC9B,QAAQ,KAAK,OAAO;AAAA,MACpB,WAAW,KAAK,UAAU;AAAA,MAC1B,SAAS,KAAK,QAAQ;AAAA,MACtB,eAAe,KAAK,cAAc;AAAA,MAClC,gBAAgB,KAAK,eAAe;AAAA,MACpC,kBAAkB,KAAK,iBAAiB;AAAA,MACxC,mBAAmB,KAAK,kBAAkB;AAAA,MAC1C,aAAa,KAAK,YAAY;AAAA,MAC9B,YAAY,KAAK,WAAW;AAAA,MAC5B,WAAW,KAAK,UAAU;AAAA,MAC1B,QAAQ,KAAK,OAAO;AAAA,MACpB,SAAS,KAAK,QAAQ;AAAA,IACxB;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,UAAM,MAAM,KAAK,kBAAkB,KAAK,aAAa,CAAC,CAAC,EAAE,UAAU,MAAM;AACvE,YAAMC,UAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,UAAIA,YAAW,QAAW;AACxB;AAAA,MACF;AACA,MAAAA,QAAO,QAAQ,KAAK,KAAK,CAAC;AAAA,IAC5B,CAAC;AACD,SAAK,IAAI,IAAI,GAAG;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,IACxB;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC7D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACpNI,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,yBAAyB,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAAA;AAAA,EAExE,KAAK,MAAM;AAAA;AAAA,EACX,WAAW,MAAM,SAAS;AAAA,EAC1B,aAAa,MAAM;AAAA,EACnB,OAAO;AAAA,EACP;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,GAAG,GAAG;AACd,WAAK,GAAG,IAAI,KAAK,uBAAuB,iBAAiB,CAAC;AAAA,IAC5D;AACA,SAAK,UAAU;AAAA,MACb,MAAM,KAAK;AAAA,MACX,UAAU,KAAK,SAAS;AAAA,MACxB,YAAY,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI,CAAC;AAAA,IACvD;AACA,SAAK,QAAQ,KAAK,KAAK,GAAG;AAC1B,SAAK,uBAAuB,YAAY,KAAK,OAAO;AAAA,EACtD;AAAA,EACA,cAAc;AACZ,SAAK,uBAAuB,eAAe,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,kBAAkB,aAAa;AAC7B,SAAK,QAAQ,SAAS,cAAc;AACpC,SAAK,uBAAuB,kBAAkB,KAAK,IAAI;AAAA,EACzD;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,YAAY,CAAC,GAAG,YAAY;AAAA,IAC9B;AAAA,IACA,SAAS;AAAA,MACP,IAAI;AAAA,IACN;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AAAA,IAAC;AAAA,IACvD,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACtDI,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EAC9B,aAAa,OAAO,UAAU;AAAA,EAC9B,SAAS,OAAO,MAAM;AAAA,EACtB,mBAAmB,OAAO,kBAAkB;AAAA,IAC1C,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,QAAQ,MAAM,QAAW;AAAA,IACvB,OAAO;AAAA,EACT,CAAC;AAAA,EACD,mBAAmB,IAAI,aAAa;AAAA,EACpC,iBAAiB,IAAI,aAAa;AAAA,EAClC,cAAc,IAAI,aAAa;AAAA,EAC/B,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,QAAQ,KAAK,MAAM;AACzB,QAAI,KAAK,oBAAoB,OAAO;AAClC,eAAS,MAAM;AACf,eAAS,MAAM;AACf,qBAAe,KAAK,iBAAiB,kBAAkB,KAAK,KAAK,gBAAgB;AACjF,UAAI,KAAK,iBAAiB,SAAS,EAAE,SAAS,SAAS;AACrD,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,4EAA4E;AAAA,IAC9F;AACA,SAAK,gBAAgB,QAAQ,QAAQ,YAAY;AAAA,EACnD;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AAAA,EACvB;AAAA,EACA,gBAAgB,QAAQ,QAAQ,cAAc;AAC5C,QAAI,SAAS;AACb,QAAI,SAAS;AACb,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,YAAM,WAAW,UAAU,KAAK,WAAW,aAAa,SAAS;AACjE,YAAM,aAAa,OAAO,KAAK,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,SAAO,KAAK,cAAc,GAAG,CAAC,GAAG,IAAI,MAAM;AACtG,iBAAS;AACT,aAAK,WAAW,mBAAmB,MAAM;AACzC,aAAK,WAAW,cAAc,KAAK;AAAA,MACrC,CAAC,GAAG,UAAU,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,UAAU,MAAM,CAAC,CAAC,CAAC;AAChG,YAAM,YAAY,WAAW,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,UAAU,QAAQ,CAAC,CAAC,CAAC;AAChI,YAAM,WAAW,WAAW,KAAK,UAAU,MAAM,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AACxE,WAAK,IAAI,IAAI,WAAW,UAAU,SAAO;AACvC,iBAAS;AACT,YAAI,KAAK,iBAAiB,UAAU;AAClC,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,iBAAiB,KAAK,GAAG;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF,CAAC,CAAC;AACF,WAAK,IAAI,IAAI,UAAU,UAAU,SAAO;AACtC,qBAAa,CAAC,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG,CAAC;AAC7C,YAAI,KAAK,YAAY,UAAU;AAC7B,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,YAAY,KAAK,GAAG;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF,CAAC,CAAC;AACF,WAAK,IAAI,IAAI,SAAS,UAAU,SAAO;AACrC,iBAAS;AACT,YAAI,KAAK,eAAe,UAAU;AAChC,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,eAAe,KAAK,GAAG;AAAA,UAC9B,CAAC;AAAA,QACH;AACA,YAAI,CAAC,QAAQ;AAEX,eAAK,WAAW,mBAAmB,EAAE;AACrC,eAAK,WAAW,cAAc,IAAI;AAAA,QACpC;AAAA,MACF,CAAC,CAAC;AACF,WAAK,IAAI,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,KAAK,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,UAAU,MAAM;AACzF,aAAK,WAAW,mBAAmB,EAAE;AACrC,aAAK,WAAW,cAAc,IAAI;AAAA,MACpC,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,cAAc,KAAK;AACjB,UAAM,QAAQ,KAAK,MAAM;AACzB,QAAI,KAAK,oBAAoB,OAAO;AAClC,YAAM,UAAU,KAAK,WAAW,sBAAsB,IAAI,OAAO;AAAA,QAC/D,QAAQ,CAAC,MAAM,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,OAAO,CAAC,MAAM,SAAS,OAAO,GAAG,CAAC,MAAM,OAAO,KAAK,iBAAiB,GAAG,CAAC,CAAC;AAAA,MACrF,CAAC,EAAE,CAAC;AACJ,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,IACpC;AAAA,IACA,SAAS;AAAA,MACP,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AChII,IAAM,iBAAN,MAAM,gBAAe;AAAA,EAC1B,aAAa,OAAO,UAAU;AAAA,EAC9B,OAAO,OAAO,MAAM;AAAA;AAAA,EAEpB,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,OAAO,MAAM;AAAA,EACb,UAAU,MAAM;AAAA,EAChB,MAAM,MAAM;AAAA,EACZ,aAAa,IAAI,aAAa;AAAA,EAC9B,cAAc,IAAI,aAAa;AAAA,EAC/B,UAAU;AAAA,EACV,WAAW;AAAA,EACX;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,WAAW,WAAW,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,UAAU,MAAS,GAAG,OAAO,MAAM,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW,YAAY,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,KAAK,CAAC;AAAA,EAC3P;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,EAAE,cAAc,GAAG;AACzK,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,WAAW,YAAY,KAAK,GAAG,CAAC;AAAA,IACvC;AACA,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,YAAY;AAAA,IACvB;AAAA,EACF;AAAA,EACM,OAAO;AAAA;AACX,WAAK,WAAW;AAChB,UAAI,KAAK,KAAK,GAAG;AACf,aAAK,WAAW,SAAS,KAAK,GAAG,GAAG,KAAK,KAAK,GAAG,KAAK,QAAQ,CAAC;AAC/D,aAAK,UAAU;AACf,aAAK,WAAW;AAAA,MAClB,WAAW,KAAK,IAAI,GAAG;AACrB,YAAI;AACF,gBAAM,KAAK,WAAW,gBAAgB,KAAK,GAAG,GAAG,KAAK,IAAI,GAAG,KAAK,QAAQ,CAAC;AAC3E,eAAK,UAAU;AACf,eAAK,WAAW;AAChB,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,YAAY,KAAK;AAAA,UACxB,CAAC;AAAA,QACH,SAAS,OAAO;AACd,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,WAAW,KAAK,KAAK;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,KAAK,CAAC,GAAG,KAAK;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAAA,IAAC;AAAA,IACrD,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AC1FI,IAAM,iBAAN,MAAM,gBAAe;AAAA,EAC1B,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA,EACpB,SAAS,MAAM;AAAA,EACf,OAAO,MAAM,SAAS;AAAA,EACtB,WAAW,MAAM;AAAA,EACjB,cAAc,MAAM;AAAA;AAAA,EAEpB,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,QAAQ,MAAM;AAAA,EACd,SAAS,MAAM;AAAA,EACf,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,aAAa,IAAI,aAAa;AAAA,EAC9B,gBAAgB,IAAI,aAAa;AAAA,EACjC,iBAAiB,IAAI,aAAa;AAAA,EAClC,eAAe,IAAI,aAAa;AAAA,EAChC,kBAAkB,IAAI,aAAa;AAAA,EACnC,kBAAkB,IAAI,aAAa;AAAA,EACnC,iBAAiB,IAAI,aAAa;AAAA,EAClC,iBAAiB,IAAI,aAAa;AAAA,EAClC,gBAAgB,IAAI,aAAa;AAAA,EACjC,mBAAmB,IAAI,aAAa;AAAA,EACpC,kBAAkB,IAAI,aAAa;AAAA,EACnC,gBAAgB,IAAI,aAAa;AAAA,EACjC,mBAAmB,IAAI,aAAa;AAAA,EACpC,aAAa;AAAA,EACb;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,WAAW,WAAW,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,IAAI,MAAM,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,SAAS,KAAK,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,gBAAc,KAAK,KAAK,UAAU,CAAC;AAAA,EACxQ;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,GAAG;AACzD,WAAK,WAAW,yBAAyB,KAAK,GAAG,GAAG,QAAQ,OAAO,EAAE,YAAY;AAAA,IACnF;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,GAAG;AAC3D,WAAK,WAAW,0BAA0B,KAAK,GAAG,GAAG,QAAQ,QAAQ,EAAE,YAAY;AAAA,IACrF;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,GAAG;AAC3D,WAAK,WAAW,eAAe,KAAK,GAAG,GAAG,QAAQ,QAAQ,EAAE,YAAY;AAAA,IAC1E;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,GAAG;AAC3D,WAAK,WAAW,eAAe,KAAK,GAAG,GAAG,QAAQ,QAAQ,EAAE,YAAY;AAAA,IAC1E;AACA,QAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC1H,WAAK,WAAW,kBAAkB,KAAK,GAAG,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,YAAY,KAAK,GAAG,CAAC;AAAA,IACvC;AACA,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,YAAY;AAAA,IACvB;AAAA,EACF;AAAA,EACA,KAAK,YAAY;AACf,UAAM,QAAQ;AAAA,MACZ,cAAc;AAAA,QACZ,IAAI,KAAK,GAAG;AAAA,QACZ,MAAM,KAAK,KAAK;AAAA,QAChB,QAAQ,KAAK,OAAO;AAAA,QACpB,UAAU,KAAK,SAAS;AAAA,QACxB,gBAAgB,KAAK,YAAY;AAAA,QACjC,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,KAAK,QAAQ;AAAA,QACtB,QAAQ,KAAK,OAAO;AAAA,QACpB,QAAQ,KAAK,OAAO;AAAA,QACpB,OAAO,KAAK,MAAM;AAAA,MACpB;AAAA,MACA,aAAa;AAAA,QACX,YAAY,KAAK;AAAA,QACjB,eAAe,KAAK;AAAA,QACpB,gBAAgB,KAAK;AAAA,QACrB,cAAc,KAAK;AAAA,QACnB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB,KAAK;AAAA,QACtB,gBAAgB,KAAK;AAAA,QACrB,gBAAgB,KAAK;AAAA,QACrB,eAAe,KAAK;AAAA,QACpB,kBAAkB,KAAK;AAAA,QACvB,iBAAiB,KAAK;AAAA,QACtB,eAAe,KAAK;AAAA,QACpB,kBAAkB,KAAK;AAAA,MACzB;AAAA,IACF;AACA,SAAK,WAAW,SAAS,OAAO,YAAY,KAAK,OAAO,CAAC;AACzD,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,IACxB;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAAA,IAAC;AAAA,IACrD,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACxLH,IAAMC,OAAM,CAAC,WAAW;AACjB,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,cAAc,MAAM;AAAA,EACpB,wBAAwB,MAAM;AAAA,EAC9B,wBAAwB,MAAM;AAAA,EAC9B,eAAe,MAAM;AAAA,EACrB,OAAO,MAAM;AAAA,EACb,sBAAsB,MAAM;AAAA,EAC5B,+BAA+B,MAAM;AAAA,EACrC,cAAc,MAAM;AAAA,EACpB,cAAc,MAAM;AAAA,EACpB,kBAAkB,MAAM;AAAA,EACxB,iBAAiB,MAAM;AAAA,EACvB,qBAAqB,MAAM;AAAA,EAC3B,eAAe,MAAM;AAAA,EACrB,mBAAmB,MAAM;AAAA,EACzB,2BAA2B,MAAM;AAAA,EACjC,wBAAwB,MAAM;AAAA,EAC9B,cAAc,MAAM;AAAA,EACpB,mBAAmB,MAAM;AAAA,EACzB,SAAS,MAAM;AAAA;AAAA,EACf,YAAY,MAAM;AAAA,EAClB,SAAS,MAAM;AAAA,EACf,sBAAsB,MAAM;AAAA;AAAA,EAE5B,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,WAAW,MAAM;AAAA,EACjB,WAAW,MAAM;AAAA,EACjB,aAAa,MAAM;AAAA,EACnB,aAAa,MAAM;AAAA,EACnB,aAAa,MAAM;AAAA,EACnB,kBAAkB,MAAM;AAAA,EACxB,kBAAkB,MAAM;AAAA,EACxB,WAAW,MAAM;AAAA,EACjB,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,QAAQ,MAAM;AAAA,EACd,SAAS,MAAM;AAAA,EACf,YAAY,MAAM;AAAA,EAClB,OAAO,MAAM;AAAA,EACb,UAAU,MAAM;AAAA,EAChB,QAAQ,MAAM;AAAA;AAAA,EAEd,mBAAmB,MAAM;AAAA,EACzB,oBAAoB,MAAM;AAAA,EAC1B,aAAa,MAAM;AAAA;AAAA,EAEnB,eAAe,MAAM,OAAO;AAAA,EAC5B,gBAAgB,MAAM;AAAA;AAAA,EAEtB,YAAY,MAAM;AAAA,EAClB,uBAAuB,MAAM;AAAA,EAC7B,kBAAkB,MAAM;AAAA,EACxB,eAAe,MAAM;AAAA,EACrB,cAAc,MAAM;AAAA;AAAA;AAAA,EAGpB,YAAY,IAAI,aAAa;AAAA,EAC7B,YAAY,IAAI,aAAa;AAAA,EAC7B,eAAe,IAAI,aAAa;AAAA,EAChC,aAAa,IAAI,aAAa;AAAA,EAC9B,eAAe,IAAI,aAAa;AAAA,EAChC,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B,eAAe,IAAI,aAAa;AAAA,EAChC,cAAc,IAAI,aAAa;AAAA,EAC/B,iBAAiB,IAAI,aAAa;AAAA,EAClC,gBAAgB,IAAI,aAAa;AAAA,EACjC,cAAc,IAAI,aAAa;AAAA,EAC/B,eAAe,IAAI,aAAa;AAAA,EAChC,iBAAiB,IAAI,aAAa;AAAA,EAClC,WAAW,IAAI,aAAa;AAAA,EAC5B,YAAY,IAAI,aAAa;AAAA,EAC7B,OAAO,IAAI,aAAa;AAAA,EACxB,UAAU,IAAI,aAAa;AAAA,EAC3B,eAAe,IAAI,aAAa;AAAA,EAChC,UAAU,IAAI,aAAa;AAAA,EAC3B,aAAa,IAAI,aAAa;AAAA,EAC9B,YAAY,IAAI,aAAa;AAAA,EAC7B,UAAU,IAAI,aAAa;AAAA,EAC3B,UAAU,IAAI,aAAa;AAAA,EAC3B,cAAc,IAAI,aAAa;AAAA,EAC/B,SAAS,IAAI,aAAa;AAAA,EAC1B,YAAY,IAAI,aAAa;AAAA,EAC7B,aAAa,IAAI,aAAa;AAAA,EAC9B,WAAW,IAAI,aAAa;AAAA,EAC5B,WAAW,IAAI,aAAa;AAAA,EAC5B,eAAe,IAAI,aAAa;AAAA,EAChC,aAAa,IAAI,aAAa;AAAA,EAC9B,gBAAgB,IAAI,aAAa;AAAA,EACjC,mBAAmB,IAAI,aAAa;AAAA,EACpC,uBAAuB,IAAI,aAAa;AAAA,EACxC,UAAU,IAAI,aAAa;AAAA,EAC3B,YAAY,IAAI,aAAa;AAAA,EAC7B,OAAO,IAAI,aAAa;AAAA,EACxB,SAAS,IAAI,aAAa;AAAA,EAC1B,WAAW,IAAI,aAAa;AAAA,EAC5B,OAAO,IAAI,aAAa;AAAA,EACxB,YAAY,IAAI,aAAa;AAAA,EAC7B,aAAa,IAAI,aAAa;AAAA,EAC9B,cAAc,IAAI,aAAa;AAAA,EAC/B,mBAAmB,IAAI,aAAa;AAAA,EACpC,oBAAoB,IAAI,aAAa;AAAA,EACrC,oBAAoB,IAAI,aAAa;AAAA,EACrC,IAAI,cAAc;AAChB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA,cAAc;AACZ,oBAAgB,MAAM;AACpB,WAAK,WAAW,MAAM;AAAA,QACpB,aAAa,KAAK,YAAY;AAAA,QAC9B,YAAY;AAAA,UACV,uBAAuB,KAAK,sBAAsB;AAAA,UAClD,WAAW,KAAK,aAAa;AAAA,UAC7B,uBAAuB,KAAK,sBAAsB;AAAA,UAClD,cAAc,KAAK,aAAa;AAAA,UAChC,SAAS,KAAK,QAAQ;AAAA,UACtB,SAAS,KAAK,QAAQ;AAAA,UACtB,UAAU,KAAK,SAAS;AAAA,UACxB,UAAU,KAAK,SAAS;AAAA,UACxB,OAAO,KAAK,MAAM;AAAA,UAClB,MAAM,KAAK,KAAK;AAAA,UAChB,aAAa,KAAK,YAAY;AAAA,UAC9B,aAAa,KAAK,YAAY;AAAA,UAC9B,iBAAiB,KAAK,gBAAgB;AAAA,UACtC,gBAAgB,KAAK,eAAe;AAAA,UACpC,oBAAoB,KAAK,mBAAmB;AAAA,UAC5C,cAAc,KAAK,aAAa;AAAA,UAChC,8BAA8B,KAAK,6BAA6B;AAAA,UAChE,uBAAuB,KAAK,sBAAsB;AAAA,UAClD,qBAAqB,KAAK,oBAAoB;AAAA,UAC9C,WAAW,KAAK,UAAU;AAAA,UAC1B,YAAY,KAAK,WAAW;AAAA,UAC5B,SAAS,KAAK,QAAQ;AAAA,UACtB,YAAY,KAAK,WAAW;AAAA,UAC5B,SAAS,KAAK,QAAQ;AAAA,UACtB,UAAU,KAAK,SAAS;AAAA,UACxB,iBAAiB,KAAK,gBAAgB;AAAA,UACtC,YAAY,KAAK,WAAW;AAAA,UAC5B,iBAAiB,KAAK,gBAAgB;AAAA,UACtC,aAAa,KAAK,YAAY;AAAA,UAC9B,QAAQ,KAAK,OAAO;AAAA,UACpB,MAAM,KAAK,KAAK;AAAA,UAChB,SAAS,KAAK,QAAQ;AAAA,UACtB,OAAO,KAAK,MAAM;AAAA,UAClB,mBAAmB,KAAK,kBAAkB;AAAA,UAC1C,kBAAkB,KAAK,iBAAiB;AAAA,UACxC,0BAA0B,KAAK,yBAAyB;AAAA,UACxD,kBAAkB,KAAK,iBAAiB;AAAA,UACxC,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,UAAU;AAAA,UACvD,kBAAkB,KAAK,iBAAiB;AAAA,UACxC,WAAW,KAAK,UAAU;AAAA,UAC1B,QAAQ,KAAK,OAAO;AAAA,UACpB,qBAAqB,KAAK,oBAAoB;AAAA,UAC9C,YAAY,KAAK,WAAW;AAAA,QAC9B;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AACD,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,WAAW,mBAAmB,KAAK,YAAY,CAAC;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA,EACM,YAAY,SAAS;AAAA;AACzB,YAAM,cAAc,KAAK,WAAW,WAAW;AAC/C,UAAI,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,GAAG;AACrE,aAAK,WAAW,mBAAmB,QAAQ,aAAa,EAAE,YAAY;AAAA,MACxE;AACA,UAAI,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,GAAG;AACnE,aAAK,WAAW,iBAAiB,QAAQ,YAAY,EAAE,YAAY;AAAA,MACrE;AACA,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7D,aAAK,WAAW,cAAc,QAAQ,SAAS,EAAE,YAAY;AAAA,MAC/D;AACA,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7D,aAAK,WAAW,cAAc,QAAQ,SAAS,EAAE,YAAY;AAAA,MAC/D;AACA,UAAI,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AAC/D,aAAK,WAAW,eAAe,QAAQ,UAAU,EAAE,YAAY;AAAA,MACjE;AACA,UAAI,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AAC/D,aAAK,WAAW,eAAe,QAAQ,UAAU,EAAE,YAAY;AAAA,MACjE;AACA,UAAI,QAAQ,mBAAmB,KAAK,CAAC,QAAQ,mBAAmB,EAAE,cAAc,GAAG;AACjF,aAAK,WAAW,wBAAwB,QAAQ,mBAAmB,EAAE,YAAY;AAAA,MACnF;AACA,UAAI,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,GAAG;AACnE,aAAK,WAAW,iBAAiB,QAAQ,YAAY,EAAE,YAAY;AAAA,MACrE;AACA,UAAI,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,GAAG;AACnE,aAAK,WAAW,iBAAiB,QAAQ,YAAY,EAAE,YAAY;AAAA,MACrE;AACA,UAAI,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,GAAG;AACnE,aAAK,WAAW,iBAAiB,QAAQ,YAAY,EAAE,YAAY;AAAA,MACrE;AACA,UAAI,QAAQ,iBAAiB,KAAK,CAAC,QAAQ,iBAAiB,EAAE,cAAc,GAAG;AAC7E,aAAK,WAAW,sBAAsB,QAAQ,iBAAiB,EAAE,YAAY;AAAA,MAC/E;AACA,UAAI,QAAQ,iBAAiB,KAAK,CAAC,QAAQ,iBAAiB,EAAE,cAAc,GAAG;AAC7E,aAAK,WAAW,sBAAsB,QAAQ,iBAAiB,EAAE,YAAY;AAAA,MAC/E;AACA,UAAI,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AAC/D,aAAK,WAAW,eAAe,QAAQ,UAAU,EAAE,YAAY;AAAA,MACjE;AACA,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7D,aAAK,WAAW,cAAc,QAAQ,SAAS,EAAE,YAAY;AAAA,MAC/D;AACA,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7D,aAAK,WAAW,cAAc,QAAQ,SAAS,EAAE,YAAY;AAAA,MAC/D;AACA,UAAI,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,GAAG;AACzD,aAAK,WAAW,YAAY,QAAQ,OAAO,EAAE,YAAY;AAAA,MAC3D;AACA,UAAI,QAAQ,WAAW,KAAK,CAAC,QAAQ,WAAW,EAAE,cAAc,GAAG;AACjE,aAAK,WAAW,gBAAgB,QAAQ,WAAW,EAAE,YAAY;AAAA,MACnE;AACA,UAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,EAAE,gBAAgB,CAAC,QAAQ,WAAW,EAAE,cAAc,GAAG;AACtG,aAAK,WAAW,UAAU,QAAQ,WAAW,EAAE,cAAc,KAAK,iBAAiB,CAAC;AAAA,MACtF;AACA,UAAI,QAAQ,sBAAsB,KAAK,QAAQ,sBAAsB,EAAE,cAAc;AACnF,aAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,UAAU,MAAM,QAAQ,sBAAsB,EAAE,cAAc,GAAG;AACzH,kBAAQ,KAAK,6GAA6G;AAAA,QAC5H;AACA,aAAK,WAAW,qBAAqB,QAAQ,sBAAsB,EAAE,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,CAAC,IAAI,GAAG,KAAK,cAAc,CAAC;AAAA,MACjJ;AACA,UAAI,KAAK,gBAAgB,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,CAAC,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,KAAK,CAAC,QAAQ,OAAO,GAAG;AACrJ,aAAK,WAAW,MAAM,KAAK,OAAO,GAAG,KAAK,aAAa,CAAC;AAAA,MAC1D,WAAW,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,CAAC,QAAQ,sBAAsB,KAAK,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,GAAG;AACnR,aAAK,WAAW,KAAK,KAAK,aAAa,GAAG,KAAK,cAAc,GAAG,QAAQ,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI,QAAW,QAAQ,QAAQ,IAAI,KAAK,OAAO,IAAI,QAAW,QAAQ,SAAS,KAAK,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,CAAC,IAAI,QAAW,QAAQ,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,CAAC,IAAI,MAAS;AAAA,MAClT;AAAA,IACF;AAAA;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,IACvB,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,YAAYA,MAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,MACrE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,MAClD,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,MAClD,cAAc,CAAC,GAAG,cAAc;AAAA,MAChC,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,qBAAqB,CAAC,GAAG,qBAAqB;AAAA,MAC9C,8BAA8B,CAAC,GAAG,8BAA8B;AAAA,MAChE,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,oBAAoB,CAAC,GAAG,oBAAoB;AAAA,MAC5C,cAAc,CAAC,GAAG,cAAc;AAAA,MAChC,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,0BAA0B,CAAC,GAAG,0BAA0B;AAAA,MACxD,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,MAClD,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,qBAAqB,CAAC,GAAG,qBAAqB;AAAA,MAC9C,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,cAAc,CAAC,GAAG,cAAc;AAAA,MAChC,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,sBAAsB,CAAC,GAAG,sBAAsB;AAAA,MAChD,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,cAAc,CAAC,GAAG,cAAc;AAAA,MAChC,aAAa,CAAC,GAAG,aAAa;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,UAAU,CAAC,GAAM,oBAAoB;AAAA,IACvE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;AAAA,IAC1B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,OAAO,MAAM,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,+EAA+E;AAAA,IACxF,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,UAAU;AAAA,MACtB,YAAY;AAAA,MACZ,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,mDAAmD;AAAA,IAC9D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AChhBH,IAAMC,OAAM,CAAC,SAAS;AACtB,IAAMC,OAAM,CAAC,GAAG;AACT,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAC3B,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,iBAAiB,MAAM;AAAA;AAAA,EAEvB,UAAU,MAAM;AAAA,EAChB,SAAS,MAAM;AAAA,EACf,YAAY,MAAM;AAAA,EAClB,aAAa,MAAM;AAAA,EACnB,YAAY,MAAM;AAAA,EAClB,SAAS,MAAM;AAAA,EACf,iBAAiB,MAAM;AAAA,EACvB,oBAAoB,MAAM;AAAA,EAC1B,kBAAkB,IAAI,aAAa;AAAA,EACnC,gBAAgB,IAAI,aAAa;AAAA,EACjC,aAAa,IAAI,aAAa;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,WAAW;AACT,QAAI,KAAK,QAAQ,KAAK,KAAK,OAAO,GAAG;AACnC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,GAAG;AAC3D,WAAK,eAAe,UAAU,KAAK,OAAO,CAAC;AAAA,IAC7C;AACA,QAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAC7D,WAAK,eAAe,UAAU,KAAK,QAAQ,EAAE,SAAS,WAAW;AAAA,IACnE;AACA,QAAI,QAAQ,WAAW,KAAK,CAAC,QAAQ,WAAW,EAAE,cAAc,GAAG;AACjE,WAAK,eAAe,aAAa,CAAC,CAAC,KAAK,UAAU,CAAC;AAAA,IACrD;AACA,QAAI,QAAQ,YAAY,KAAK,CAAC,QAAQ,YAAY,EAAE,cAAc,GAAG;AACnE,cAAQ,YAAY,EAAE,eAAe,KAAK,eAAe,SAAS,GAAG,MAAM,KAAK,WAAW,WAAW,IAAI,KAAK,eAAe,SAAS,GAAG,OAAO;AAAA,IACnJ;AACA,QAAI,QAAQ,gBAAgB,KAAK,CAAC,QAAQ,gBAAgB,EAAE,cAAc,GAAG;AAC3E,WAAK,eAAe,kBAAkB,QAAQ,gBAAgB,EAAE,YAAY;AAAA,IAC9E;AACA,QAAI,QAAQ,mBAAmB,KAAK,CAAC,QAAQ,mBAAmB,EAAE,cAAc,GAAG;AACjF,WAAK,eAAe,qBAAqB,QAAQ,mBAAmB,EAAE,YAAY;AAAA,IACpF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,WAAK,iBAAiB,KAAK,WAAW,UAAU;AAAA,QAC9C,gBAAgB;AAAA,UACd,QAAQ,KAAK,OAAO;AAAA,UACpB,QAAQ,KAAK,OAAO;AAAA,UACpB,gBAAgB,KAAK,eAAe;AAAA,UACpC,mBAAmB,KAAK,kBAAkB;AAAA,UAC1C,WAAW,KAAK,UAAU;AAAA,UAC1B,SAAS,KAAK,QAAQ;AAAA,UACtB,SAAS,KAAK,QAAQ;AAAA,UACtB,QAAQ,KAAK,OAAO;AAAA,UACpB,gBAAgB,KAAK,eAAe;AAAA,QACtC;AAAA,QACA,eAAe;AAAA,UACb,iBAAiB,KAAK;AAAA,UACtB,YAAY,KAAK;AAAA,UACjB,eAAe,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,aAAa,KAAK,cAAc;AAChD,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,YAAY;AAAA,EAClC;AAAA,EACA,kBAAkB,aAAa;AAC7B,SAAK,eAAe,UAAU,WAAW;AAAA,EAC3C;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,YAAYD,MAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,IAC5C;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoBC;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;AAAA,IACxB,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU,CAAC;AAC7B,QAAG,YAAY,WAAW,IAAI,OAAO,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AC9JH,IAAMC,OAAM,OAAO;AAAA,EACjB,iBAAiB;AACnB;AACA,IAAMC,OAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,WAAW,QAAQ,OAAO;AACjC,SAAO,KAAK,eAAe,KAAK;AAClC;AACA,SAAS,qFAAqF,IAAI,KAAK;AAAC;AACxG,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sFAAsF,GAAG,GAAG,eAAe,CAAC;AAAA,EAC/H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAGA,MAAK,UAAU,CAAC;AAAA,EAC7H;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,CAAC;AACpC,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,MAAM,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,UAAU;AACnC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,kBAAkB,IAAI,EAAE;AAAA,EAClD;AACF;AACA,SAAS,qFAAqF,IAAI,KAAK;AAAC;AACxG,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sFAAsF,GAAG,GAAG,eAAe,CAAC;AAAA,EAC/H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAGA,MAAK,UAAU,CAAC;AAAA,EACtH;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,CAAC;AACpC,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,MAAM,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,UAAU;AACnC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,WAAW,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,0DAA0D,GAAG,GAAG,cAAc,CAAC;AAAA,EACtL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,IAAG,cAAc,WAAW,WAAW,SAAS,IAAI,IAAI,CAAC;AAAA,EAC3D;AACF;AACO,IAAM,iBAAN,MAAM,gBAAe;AAAA,EAC1B,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,YAAY,EAAE,CAAC;AAAA,EAC7C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACI,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,mBAAmB,EAAE,CAAC;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI,SAAS;AACN,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EACvC,aAAa,OAAO,UAAU;AAAA,EAC9B,OAAO,OAAO,MAAM;AAAA;AAAA,EAEpB,SAAS,MAAM,SAAS;AAAA;AAAA,EAExB,mBAAmB,MAAM;AAAA,EACzB;AAAA,EACA;AAAA,EACA,gBAAgB,OAAO,CAAC,CAAC;AAAA,EACzB,UAAU,4BAA4B,QAAQ;AAAA,EAC9C,MAAM,IAAI,aAAa;AAAA,EACvB,qBAAqB;AACnB,UAAM,oBAAoB,MAAM,UAAU,KAAK,WAAW,aAAa,MAAM,EAAE,KAAK,OAAO,OAAK,EAAE,aAAa,KAAK,OAAO,KAAK,EAAE,mBAAmB,cAAc,KAAK,WAAW,YAAY,eAAe,KAAK,OAAO,CAAC,CAAC,CAAC;AAC7N,UAAM,MAAM,KAAK,WAAW,YAAY,KAAK,UAAU,iBAAiB,GAAG,UAAU,MAAM,MAAM,UAAU,KAAK,WAAW,aAAa,MAAM,GAAG,UAAU,KAAK,WAAW,aAAa,SAAS,CAAC,EAAE,KAAK,UAAU,MAAS,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AAC/O,WAAK,KAAK,IAAI,MAAM;AAClB,aAAK,cAAc;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,IAAI,IAAI,GAAG;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AAAA,EACvB;AAAA,EACA,eAAe,SAAS;AACtB,QAAI,QAAQ,IAAI;AACd,aAAO,QAAQ;AAAA,IACjB;AACA,UAAM,mBAAmB,KAAK,iBAAiB;AAC/C,QAAI,CAAC,kBAAkB;AACrB,cAAQ,KAAK,6EAA6E;AAC1F,aAAO;AAAA,IACT;AACA,UAAM,KAAK,QAAQ,aAAa,gBAAgB;AAChD,QAAI,CAAC,IAAI;AACP,cAAQ,KAAK,0CAA0C,gBAAgB,2BAA2B,OAAO;AACzG,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS;AAAA,MACb,QAAQ,CAAC,KAAK,OAAO;AAAA,IACvB;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,SAAS,CAAC,MAAM,WAAW,IAAI;AAAA,IACxC;AACA,UAAM,gBAAgB,KAAK,WAAW,YAAY,sBAAsB,MAAM;AAE9E,UAAM,OAAO,oBAAI,IAAI;AACrB,UAAM,SAAS,CAAC;AAChB,eAAW,WAAW,eAAe;AACnC,YAAM,KAAK,KAAK,eAAe,OAAO;AACtC,UAAI,CAAC,KAAK,IAAI,EAAE,GAAG;AACjB,aAAK,IAAI,EAAE;AACX,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF;AACA,SAAK,cAAc,IAAI,MAAM;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO,SAAS,oCAAoC,mBAAmB;AAC5E,WAAO,KAAK,qBAAqB,8BAA6B;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,IACxC,gBAAgB,SAAS,2CAA2C,IAAI,KAAK,UAAU;AACrF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,gBAAgB,GAAG,WAAW;AAC1D,QAAG,eAAe,UAAU,uBAAuB,GAAG,WAAW;AAAA,MACnE;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,MACxE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,IAC1C;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,GAAG,MAAM,UAAU,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,IAC3H,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,aAAa,CAAC;AAC9B,QAAG,iBAAiB,GAAG,4CAA4C,GAAG,GAAG,MAAM,MAAM,YAAY,IAAI;AAAA,MACvG;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,MAAM,IAAI,OAAO,EAAE,UAAU,IAAI,OAAO,CAAC,EAAE,SAAY,gBAAgB,GAAGD,IAAG,CAAC;AAC5F,QAAG,UAAU;AACb,QAAG,WAAW,IAAI,cAAc,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB,gBAAgB,gBAAgB;AAAA,IAChE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,SAAS,CAAC,iBAAiB,gBAAgB,gBAAgB;AAAA,MAC3D,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACrQH,IAAME,OAAM,CAAC,SAAS;AACtB,IAAMC,OAAM,CAAC,GAAG;AACT,IAAM,iBAAN,MAAM,gBAAe;AAAA,EAC1B,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,cAAc,MAAM;AAAA,EACpB,eAAe,MAAM;AAAA,EACrB,cAAc,MAAM;AAAA,EACpB,iBAAiB,MAAM;AAAA,EACvB,SAAS,MAAM;AAAA,EACf,YAAY,MAAM;AAAA,EAClB,WAAW,MAAM;AAAA;AAAA,EAEjB,UAAU,MAAM;AAAA,EAChB,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,aAAa,IAAI,aAAa;AAAA,EAC9B,YAAY,IAAI,aAAa;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,WAAW;AACT,QAAI,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,GAAG;AACxG,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AACxH,YAAM,YAAY,QAAQ,QAAQ,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,EAAE,SAAS;AAC9E,WAAK,WAAW,mBAAmB,KAAK,eAAe,IAAI;AAC3D,YAAM,mBAAmB,KAAK,YAAY;AAC1C,WAAK,WAAW,cAAc,kBAAkB,WAAW,KAAK,cAAc,OAAO,CAAC;AACtF,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,GAAG;AAC3D,YAAM,iBAAiB,QAAQ,QAAQ,EAAE;AACzC,UAAI,eAAe,gBAAgB;AACjC,aAAK,WAAW,sBAAsB,eAAe,cAAc;AAAA,MACrE;AACA,UAAI,KAAK,OAAO,KAAK,KAAK,OAAO,EAAE,kBAAkB,KAAK,eAAe;AACvE,aAAK,WAAW,iBAAiB,KAAK,OAAO,EAAE,gBAAgB,KAAK,aAAa;AAAA,MACnF;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,KAAK,eAAe;AACjF,WAAK,cAAc,UAAU,KAAK,OAAO,CAAC;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB,KAAK,YAAY;AACtC,SAAK,SAAS,KAAK,aAAa;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,UAAI,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG;AACnC,aAAK,WAAW,mBAAmB,KAAK,aAAa;AAAA,MACvD,WAAW,KAAK,OAAO,KAAK,KAAK,OAAO,EAAE,gBAAgB;AACxD,aAAK,WAAW,sBAAsB,KAAK,OAAO,EAAE,cAAc;AAAA,MACpE;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,WAAW,YAAY;AAAA,MACjC,cAAc;AAAA,QACZ,aAAa,KAAK,YAAY;AAAA,QAC9B,cAAc,KAAK,aAAa;AAAA,QAChC,aAAa,KAAK,YAAY;AAAA,QAC9B,gBAAgB,KAAK,eAAe;AAAA,QACpC,QAAQ,KAAK,OAAO;AAAA,QACpB,QAAQ,KAAK,OAAO;AAAA,QACpB,WAAW,KAAK,UAAU;AAAA,QAC1B,UAAU,KAAK,SAAS;AAAA,MAC1B;AAAA,MACA,aAAa;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAAG,KAAK,QAAQ,aAAa;AAAA,EAC/B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,WAAW,YAAY,UAAU,MAAM;AAC1C,UAAI,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG;AACnC,aAAK,WAAW,cAAc,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,EAAE,SAAS,WAAW;AAAA,MAC1G,WAAW,KAAK,OAAO,KAAK,KAAK,OAAO,EAAE,gBAAgB;AACxD,aAAK,WAAW,iBAAiB,KAAK,OAAO,EAAE,gBAAgB,KAAK;AAAA,MACtE,OAAO;AACL,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,YAAYD,MAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,cAAc,CAAC,GAAG,cAAc;AAAA,MAChC,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoBC;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;AAAA,IACxB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AC/JI,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,cAAc,MAAM,SAAS;AAAA,EAC7B,SAAS,MAAM,SAAS;AAAA,EACxB,UAAU,MAAM;AAAA,EAChB,cAAc;AAAA,EACd,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AACxH,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB,WAAW,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,GAAG;AAC5E,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,UAAI,WAAW,QAAW;AACxB;AAAA,MACF;AACA,aAAO,eAAe,KAAK,YAAY,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,aAAa,KAAK,YAAY;AAAA,MAC9B,QAAQ,KAAK,OAAO;AAAA,MACpB,SAAS,KAAK,QAAQ;AAAA,IACxB;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,IACxB;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACnFI,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EAChC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,MAAM,MAAM;AAAA,EACZ,cAAc,MAAM,SAAS;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,WAAW,WAAW,UAAU,MAAM,KAAK,KAAK,CAAC;AAAA,EACnE;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,aAAa,QAAW;AAC/B;AAAA,IACF;AACA,UAAM,SAAS,KAAK,WAAW,UAAU,KAAK,QAAQ;AACtD,QAAI,WAAW,QAAW;AACxB;AAAA,IACF;AACA,WAAO,YAAY;AAAA,MACjB,KAAK,KAAK,IAAI;AAAA,MACd,aAAa,QAAQ,aAAa,MAAM,SAAY,SAAY,KAAK,YAAY;AAAA,IACnF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ,QAAW;AAC1B,WAAK,IAAI,YAAY;AAAA,IACvB;AACA,QAAI,KAAK,aAAa,QAAW;AAC/B,WAAK,WAAW,aAAa,KAAK,QAAQ;AAC1C,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,cAAc;AAAA,MAClB,MAAM;AAAA,MACN,KAAK,KAAK,IAAI;AAAA,MACd,aAAa,KAAK,YAAY;AAAA,IAChC;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,WAAW;AAChD,SAAK,WAAW,KAAK,GAAG;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,aAAa,CAAC,GAAG,aAAa;AAAA,IAChC;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC3D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC/DI,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EACpC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,MAAM,MAAM;AAAA,EACZ,QAAQ,MAAM;AAAA,EACd,SAAS,MAAM;AAAA,EACf,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,WAAW,MAAM;AAAA,EACjB,cAAc,MAAM;AAAA,EACpB,WAAW,MAAM;AAAA,EACjB,WAAW,MAAM;AAAA,EACjB,cAAc;AAAA,EACd,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,EAAE,cAAc,KAAK,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,KAAK,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AACriB,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,KAAK,KAAK,IAAI;AAAA,MACd,OAAO,KAAK,MAAM;AAAA,MAClB,QAAQ,KAAK,OAAO;AAAA,MACpB,SAAS,KAAK,QAAQ;AAAA,MACtB,SAAS,KAAK,QAAQ;AAAA,MACtB,UAAU,KAAK,SAAS;AAAA,MACxB,aAAa,KAAK,YAAY;AAAA,MAC9B,UAAU,KAAK,SAAS;AAAA,MACxB,UAAU,KAAK,SAAS;AAAA,IAC1B;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC/D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACnGI,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,MAAM,MAAM;AAAA,EACZ,QAAQ,MAAM;AAAA,EACd,SAAS,MAAM;AAAA,EACf,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,WAAW,MAAM;AAAA,EACjB,SAAS,MAAM;AAAA,EACf,cAAc,MAAM;AAAA,EACpB,WAAW,MAAM;AAAA,EACjB,cAAc;AAAA,EACd,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,EAAE,cAAc,KAAK,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AACjiB,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,KAAK,KAAK,IAAI;AAAA,MACd,OAAO,KAAK,MAAM;AAAA,MAClB,QAAQ,KAAK,OAAO;AAAA,MACpB,SAAS,KAAK,QAAQ;AAAA,MACtB,SAAS,KAAK,QAAQ;AAAA,MACtB,UAAU,KAAK,SAAS;AAAA,MACxB,QAAQ,KAAK,OAAO;AAAA,MACpB,aAAa,KAAK,YAAY;AAAA,MAC9B,UAAU,KAAK,SAAS;AAAA,IAC1B;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7FI,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,MAAM,MAAM;AAAA,EACZ,QAAQ,MAAM;AAAA,EACd,SAAS,MAAM;AAAA,EACf,SAAS,MAAM;AAAA,EACf,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,cAAc,MAAM;AAAA,EACpB,YAAY,MAAM;AAAA,EAClB,WAAW,MAAM;AAAA,EACjB,cAAc;AAAA,EACd,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ,QAAQ,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,cAAc,KAAK,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,KAAK,QAAQ,WAAW,KAAK,CAAC,QAAQ,WAAW,EAAE,cAAc,KAAK,QAAQ,UAAU,KAAK,CAAC,QAAQ,UAAU,EAAE,cAAc,GAAG;AACrb,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB,WAAW,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,EAAE,cAAc,KAAK,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,EAAE,cAAc,GAAG;AACrH,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,UAAI,WAAW,QAAW;AACxB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,KAAK,KAAK,IAAI,GAAG;AAChC,eAAO,OAAO,KAAK,IAAI,CAAC;AAAA,MAC1B;AACA,UAAI,QAAQ,OAAO,KAAK,KAAK,MAAM,GAAG;AACpC,eAAO,SAAS,KAAK,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,WAAW,UAAU,KAAK,GAAG,CAAC,GAAG,OAAO;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,KAAK,KAAK,IAAI;AAAA,MACd,OAAO,KAAK,MAAM;AAAA,MAClB,QAAQ,KAAK,OAAO;AAAA,MACpB,QAAQ,KAAK,OAAO;AAAA,MACpB,SAAS,KAAK,QAAQ;AAAA,MACtB,SAAS,KAAK,QAAQ;AAAA,MACtB,aAAa,KAAK,YAAY;AAAA,MAC9B,WAAW,KAAK,UAAU;AAAA,MAC1B,UAAU,KAAK,SAAS;AAAA,IAC1B;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,OAAO,CAAC,GAAG,OAAO;AAAA,MAClB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,SAAS,CAAC,GAAG,SAAS;AAAA,MACtB,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3GI,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EAChC,aAAa,OAAO,UAAU;AAAA;AAAA,EAE9B,KAAK,MAAM,SAAS;AAAA;AAAA,EAEpB,OAAO,MAAM,SAAS;AAAA,EACtB,cAAc,MAAM,SAAS;AAAA,EAC7B,cAAc;AAAA,EACd,MAAM,IAAI,aAAa;AAAA,EACvB,WAAW;AACT,UAAM,OAAO,KAAK,WAAW,WAAW,UAAU,MAAM;AACtD,WAAK,KAAK;AACV,YAAM,MAAM,UAAU,KAAK,WAAW,aAAa,WAAW,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,WAAW,YAAY,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM;AACpJ,aAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,IAAI,IAAI,GAAG;AAAA,IAClB,CAAC;AACD,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,cAAc,GAAG;AACvD,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB,WAAW,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAa,EAAE,cAAc,GAAG;AAC5E,YAAM,SAAS,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC;AAClD,UAAI,WAAW,QAAW;AACxB;AAAA,MACF;AACA,aAAO,eAAe,KAAK,YAAY,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,IAAI,YAAY;AACrB,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW,aAAa,KAAK,GAAG,CAAC;AACtC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,WAAW,UAAU,KAAK,GAAG,CAAC,GAAG,MAAM;AAAA,EAC9C;AAAA,EACA,OAAO;AACL,SAAK,WAAW,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK;AAAA,EAC7C;AAAA,EACA,WAAW;AACT,WAAO,KAAK,WAAW,UAAU,KAAK,GAAG,CAAC,GAAG,SAAS;AAAA,EACxD;AAAA,EACA,OAAO;AACL,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,MAAM,KAAK,KAAK;AAAA,MAChB,aAAa,KAAK,YAAY;AAAA,IAChC;AACA,SAAK,WAAW,UAAU,KAAK,GAAG,GAAG,MAAM;AAC3C,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,QAAQ;AAAA,MACN,IAAI,CAAC,GAAG,IAAI;AAAA,MACZ,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,aAAa,CAAC,GAAG,aAAa;AAAA,IAChC;AAAA,IACA,UAAU,CAAI,oBAAoB;AAAA,IAClC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC3D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["filter", "import_mapbox_gl", "import_mapbox_gl", "import_mapbox_gl", "import_mapbox_gl", "import_mapbox_gl", "source", "_c0", "_c0", "_c1", "_c0", "_c1", "_c0", "_c1"]}