{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/idb-keyval@6.2.2/node_modules/idb-keyval/dist/index.js"], "sourcesContent": ["function promisifyRequest(request) {\n  return new Promise((resolve, reject) => {\n    // @ts-ignore - file size hacks\n    request.oncomplete = request.onsuccess = () => resolve(request.result);\n    // @ts-ignore - file size hacks\n    request.onabort = request.onerror = () => reject(request.error);\n  });\n}\nfunction createStore(dbName, storeName) {\n  let dbp;\n  const getDB = () => {\n    if (dbp) return dbp;\n    const request = indexedDB.open(dbName);\n    request.onupgradeneeded = () => request.result.createObjectStore(storeName);\n    dbp = promisifyRequest(request);\n    dbp.then(db => {\n      // It seems like <PERSON>fari sometimes likes to just close the connection.\n      // It's supposed to fire this event when that happens. Let's hope it does!\n      db.onclose = () => dbp = undefined;\n    }, () => {});\n    return dbp;\n  };\n  return (txMode, callback) => getDB().then(db => callback(db.transaction(storeName, txMode).objectStore(storeName)));\n}\nlet defaultGetStoreFunc;\nfunction defaultGetStore() {\n  if (!defaultGetStoreFunc) {\n    defaultGetStoreFunc = createStore('keyval-store', 'keyval');\n  }\n  return defaultGetStoreFunc;\n}\n/**\n * Get a value by its key.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction get(key, customStore = defaultGetStore()) {\n  return customStore('readonly', store => promisifyRequest(store.get(key)));\n}\n/**\n * Set a value with a key.\n *\n * @param key\n * @param value\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction set(key, value, customStore = defaultGetStore()) {\n  return customStore('readwrite', store => {\n    store.put(value, key);\n    return promisifyRequest(store.transaction);\n  });\n}\n/**\n * Set multiple values at once. This is faster than calling set() multiple times.\n * It's also atomic – if one of the pairs can't be added, none will be added.\n *\n * @param entries Array of entries, where each entry is an array of `[key, value]`.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction setMany(entries, customStore = defaultGetStore()) {\n  return customStore('readwrite', store => {\n    entries.forEach(entry => store.put(entry[1], entry[0]));\n    return promisifyRequest(store.transaction);\n  });\n}\n/**\n * Get multiple values by their keys\n *\n * @param keys\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction getMany(keys, customStore = defaultGetStore()) {\n  return customStore('readonly', store => Promise.all(keys.map(key => promisifyRequest(store.get(key)))));\n}\n/**\n * Update a value. This lets you see the old value and update it as an atomic operation.\n *\n * @param key\n * @param updater A callback that takes the old value and returns a new value.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction update(key, updater, customStore = defaultGetStore()) {\n  return customStore('readwrite', store =>\n  // Need to create the promise manually.\n  // If I try to chain promises, the transaction closes in browsers\n  // that use a promise polyfill (IE10/11).\n  new Promise((resolve, reject) => {\n    store.get(key).onsuccess = function () {\n      try {\n        store.put(updater(this.result), key);\n        resolve(promisifyRequest(store.transaction));\n      } catch (err) {\n        reject(err);\n      }\n    };\n  }));\n}\n/**\n * Delete a particular key from the store.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction del(key, customStore = defaultGetStore()) {\n  return customStore('readwrite', store => {\n    store.delete(key);\n    return promisifyRequest(store.transaction);\n  });\n}\n/**\n * Delete multiple keys at once.\n *\n * @param keys List of keys to delete.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction delMany(keys, customStore = defaultGetStore()) {\n  return customStore('readwrite', store => {\n    keys.forEach(key => store.delete(key));\n    return promisifyRequest(store.transaction);\n  });\n}\n/**\n * Clear all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction clear(customStore = defaultGetStore()) {\n  return customStore('readwrite', store => {\n    store.clear();\n    return promisifyRequest(store.transaction);\n  });\n}\nfunction eachCursor(store, callback) {\n  store.openCursor().onsuccess = function () {\n    if (!this.result) return;\n    callback(this.result);\n    this.result.continue();\n  };\n  return promisifyRequest(store.transaction);\n}\n/**\n * Get all keys in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction keys(customStore = defaultGetStore()) {\n  return customStore('readonly', store => {\n    // Fast path for modern browsers\n    if (store.getAllKeys) {\n      return promisifyRequest(store.getAllKeys());\n    }\n    const items = [];\n    return eachCursor(store, cursor => items.push(cursor.key)).then(() => items);\n  });\n}\n/**\n * Get all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction values(customStore = defaultGetStore()) {\n  return customStore('readonly', store => {\n    // Fast path for modern browsers\n    if (store.getAll) {\n      return promisifyRequest(store.getAll());\n    }\n    const items = [];\n    return eachCursor(store, cursor => items.push(cursor.value)).then(() => items);\n  });\n}\n/**\n * Get all entries in the store. Each entry is an array of `[key, value]`.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction entries(customStore = defaultGetStore()) {\n  return customStore('readonly', store => {\n    // Fast path for modern browsers\n    // (although, hopefully we'll get a simpler path some day)\n    if (store.getAll && store.getAllKeys) {\n      return Promise.all([promisifyRequest(store.getAllKeys()), promisifyRequest(store.getAll())]).then(([keys, values]) => keys.map((key, i) => [key, values[i]]));\n    }\n    const items = [];\n    return customStore('readonly', store => eachCursor(store, cursor => items.push([cursor.key, cursor.value])).then(() => items));\n  });\n}\nexport { clear, createStore, del, delMany, entries, get, getMany, keys, promisifyRequest, set, setMany, update, values };"], "mappings": ";;;AAAA,SAAS,iBAAiB,SAAS;AACjC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAQ,aAAa,QAAQ,YAAY,MAAM,QAAQ,QAAQ,MAAM;AAErE,YAAQ,UAAU,QAAQ,UAAU,MAAM,OAAO,QAAQ,KAAK;AAAA,EAChE,CAAC;AACH;AACA,SAAS,YAAY,QAAQ,WAAW;AACtC,MAAI;AACJ,QAAM,QAAQ,MAAM;AAClB,QAAI,IAAK,QAAO;AAChB,UAAM,UAAU,UAAU,KAAK,MAAM;AACrC,YAAQ,kBAAkB,MAAM,QAAQ,OAAO,kBAAkB,SAAS;AAC1E,UAAM,iBAAiB,OAAO;AAC9B,QAAI,KAAK,QAAM;AAGb,SAAG,UAAU,MAAM,MAAM;AAAA,IAC3B,GAAG,MAAM;AAAA,IAAC,CAAC;AACX,WAAO;AAAA,EACT;AACA,SAAO,CAAC,QAAQ,aAAa,MAAM,EAAE,KAAK,QAAM,SAAS,GAAG,YAAY,WAAW,MAAM,EAAE,YAAY,SAAS,CAAC,CAAC;AACpH;AACA,IAAI;AACJ,SAAS,kBAAkB;AACzB,MAAI,CAAC,qBAAqB;AACxB,0BAAsB,YAAY,gBAAgB,QAAQ;AAAA,EAC5D;AACA,SAAO;AACT;AAOA,SAAS,IAAI,KAAK,cAAc,gBAAgB,GAAG;AACjD,SAAO,YAAY,YAAY,WAAS,iBAAiB,MAAM,IAAI,GAAG,CAAC,CAAC;AAC1E;AAQA,SAAS,IAAI,KAAK,OAAO,cAAc,gBAAgB,GAAG;AACxD,SAAO,YAAY,aAAa,WAAS;AACvC,UAAM,IAAI,OAAO,GAAG;AACpB,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC3C,CAAC;AACH;AAQA,SAAS,QAAQA,UAAS,cAAc,gBAAgB,GAAG;AACzD,SAAO,YAAY,aAAa,WAAS;AACvC,IAAAA,SAAQ,QAAQ,WAAS,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACtD,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC3C,CAAC;AACH;AAOA,SAAS,QAAQC,OAAM,cAAc,gBAAgB,GAAG;AACtD,SAAO,YAAY,YAAY,WAAS,QAAQ,IAAIA,MAAK,IAAI,SAAO,iBAAiB,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACxG;AAQA,SAAS,OAAO,KAAK,SAAS,cAAc,gBAAgB,GAAG;AAC7D,SAAO,YAAY,aAAa;AAAA;AAAA;AAAA;AAAA,IAIhC,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/B,YAAM,IAAI,GAAG,EAAE,YAAY,WAAY;AACrC,YAAI;AACF,gBAAM,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;AACnC,kBAAQ,iBAAiB,MAAM,WAAW,CAAC;AAAA,QAC7C,SAAS,KAAK;AACZ,iBAAO,GAAG;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,GAAC;AACJ;AAOA,SAAS,IAAI,KAAK,cAAc,gBAAgB,GAAG;AACjD,SAAO,YAAY,aAAa,WAAS;AACvC,UAAM,OAAO,GAAG;AAChB,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC3C,CAAC;AACH;AAOA,SAAS,QAAQA,OAAM,cAAc,gBAAgB,GAAG;AACtD,SAAO,YAAY,aAAa,WAAS;AACvC,IAAAA,MAAK,QAAQ,SAAO,MAAM,OAAO,GAAG,CAAC;AACrC,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC3C,CAAC;AACH;AAMA,SAAS,MAAM,cAAc,gBAAgB,GAAG;AAC9C,SAAO,YAAY,aAAa,WAAS;AACvC,UAAM,MAAM;AACZ,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC3C,CAAC;AACH;AACA,SAAS,WAAW,OAAO,UAAU;AACnC,QAAM,WAAW,EAAE,YAAY,WAAY;AACzC,QAAI,CAAC,KAAK,OAAQ;AAClB,aAAS,KAAK,MAAM;AACpB,SAAK,OAAO,SAAS;AAAA,EACvB;AACA,SAAO,iBAAiB,MAAM,WAAW;AAC3C;AAMA,SAAS,KAAK,cAAc,gBAAgB,GAAG;AAC7C,SAAO,YAAY,YAAY,WAAS;AAEtC,QAAI,MAAM,YAAY;AACpB,aAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IAC5C;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,WAAW,OAAO,YAAU,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EAC7E,CAAC;AACH;AAMA,SAAS,OAAO,cAAc,gBAAgB,GAAG;AAC/C,SAAO,YAAY,YAAY,WAAS;AAEtC,QAAI,MAAM,QAAQ;AAChB,aAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,IACxC;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,WAAW,OAAO,YAAU,MAAM,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EAC/E,CAAC;AACH;AAMA,SAAS,QAAQ,cAAc,gBAAgB,GAAG;AAChD,SAAO,YAAY,YAAY,WAAS;AAGtC,QAAI,MAAM,UAAU,MAAM,YAAY;AACpC,aAAO,QAAQ,IAAI,CAAC,iBAAiB,MAAM,WAAW,CAAC,GAAG,iBAAiB,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,OAAMC,OAAM,MAAMD,MAAK,IAAI,CAAC,KAAK,MAAM,CAAC,KAAKC,QAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IAC9J;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,YAAY,YAAY,CAAAC,WAAS,WAAWA,QAAO,YAAU,MAAM,KAAK,CAAC,OAAO,KAAK,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EAC/H,CAAC;AACH;", "names": ["entries", "keys", "values", "store"]}