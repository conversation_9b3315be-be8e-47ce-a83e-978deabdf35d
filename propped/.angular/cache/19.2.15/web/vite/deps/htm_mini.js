import "./chunk-KBUIKKCC.js";

// node_modules/.pnpm/htm@3.1.1/node_modules/htm/mini/index.module.js
function index_module_default(n) {
  for (var l, e, s = arguments, t = 1, r = "", u = "", a = [0], c = function(n2) {
    1 === t && (n2 || (r = r.replace(/^\s*\n\s*|\s*\n\s*$/g, ""))) ? a.push(n2 ? s[n2] : r) : 3 === t && (n2 || r) ? (a[1] = n2 ? s[n2] : r, t = 2) : 2 === t && "..." === r && n2 ? a[2] = Object.assign(a[2] || {}, s[n2]) : 2 === t && r && !n2 ? (a[2] = a[2] || {})[r] = true : t >= 5 && (5 === t ? ((a[2] = a[2] || {})[e] = n2 ? r ? r + s[n2] : s[n2] : r, t = 6) : (n2 || r) && (a[2][e] += n2 ? r + s[n2] : r)), r = "";
  }, h = 0; h < n.length; h++) {
    h && (1 === t && c(), c(h));
    for (var i = 0; i < n[h].length; i++) l = n[h][i], 1 === t ? "<" === l ? (c(), a = [a, "", null], t = 3) : r += l : 4 === t ? "--" === r && ">" === l ? (t = 1, r = "") : r = l + r[0] : u ? l === u ? u = "" : r += l : '"' === l || "'" === l ? u = l : ">" === l ? (c(), t = 1) : t && ("=" === l ? (t = 5, e = r, r = "") : "/" === l && (t < 5 || ">" === n[h][i + 1]) ? (c(), 3 === t && (a = a[0]), t = a, (a = a[0]).push(this.apply(null, t.slice(1))), t = 0) : " " === l || "	" === l || "\n" === l || "\r" === l ? (c(), t = 2) : r += l), 3 === t && "!--" === r && (t = 4, a = a[0]);
  }
  return c(), a.length > 2 ? a.slice(1) : a[1];
}
export {
  index_module_default as default
};
//# sourceMappingURL=htm_mini.js.map
