{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs"], "sourcesContent": ["const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n"], "mappings": ";;;;;AAAA,IAAM,uBAAuB;AAC7B,IAAM,wBAAwB,YAAU;AACtC,QAAM,WAAW,eAAe,MAAM;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,eAAa;AACnC,UAAM,aAAa,UAAU,MAAM,oBAAoB;AAEvD,QAAI,WAAW,CAAC,MAAM,MAAM,WAAW,WAAW,GAAG;AACnD,iBAAW,MAAM;AAAA,IACnB;AACA,WAAO,kBAAkB,YAAY,QAAQ,KAAK,+BAA+B,SAAS;AAAA,EAC5F;AACA,QAAM,8BAA8B,CAAC,cAAc,uBAAuB;AACxE,UAAM,YAAY,uBAAuB,YAAY,KAAK,CAAC;AAC3D,QAAI,sBAAsB,+BAA+B,YAAY,GAAG;AACtE,aAAO,CAAC,GAAG,WAAW,GAAG,+BAA+B,YAAY,CAAC;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,oBAAoB,CAAC,YAAY,oBAAoB;AACzD,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,gBAAgB;AAAA,EACzB;AACA,QAAM,mBAAmB,WAAW,CAAC;AACrC,QAAM,sBAAsB,gBAAgB,SAAS,IAAI,gBAAgB;AACzE,QAAM,8BAA8B,sBAAsB,kBAAkB,WAAW,MAAM,CAAC,GAAG,mBAAmB,IAAI;AACxH,MAAI,6BAA6B;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,WAAW,WAAW,GAAG;AAC3C,WAAO;AAAA,EACT;AACA,QAAM,YAAY,WAAW,KAAK,oBAAoB;AACtD,SAAO,gBAAgB,WAAW,KAAK,CAAC;AAAA,IACtC;AAAA,EACF,MAAM,UAAU,SAAS,CAAC,GAAG;AAC/B;AACA,IAAM,yBAAyB;AAC/B,IAAM,iCAAiC,eAAa;AAClD,MAAI,uBAAuB,KAAK,SAAS,GAAG;AAC1C,UAAM,6BAA6B,uBAAuB,KAAK,SAAS,EAAE,CAAC;AAC3E,UAAM,WAAW,4BAA4B,UAAU,GAAG,2BAA2B,QAAQ,GAAG,CAAC;AACjG,QAAI,UAAU;AAEZ,aAAO,gBAAgB;AAAA,IACzB;AAAA,EACF;AACF;AAIA,IAAM,iBAAiB,YAAU;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW;AAAA,IACf,UAAU,oBAAI,IAAI;AAAA,IAClB,YAAY,CAAC;AAAA,EACf;AACA,aAAW,gBAAgB,aAAa;AACtC,8BAA0B,YAAY,YAAY,GAAG,UAAU,cAAc,KAAK;AAAA,EACpF;AACA,SAAO;AACT;AACA,IAAM,4BAA4B,CAAC,YAAY,iBAAiB,cAAc,UAAU;AACtF,aAAW,QAAQ,qBAAmB;AACpC,QAAI,OAAO,oBAAoB,UAAU;AACvC,YAAM,wBAAwB,oBAAoB,KAAK,kBAAkB,QAAQ,iBAAiB,eAAe;AACjH,4BAAsB,eAAe;AACrC;AAAA,IACF;AACA,QAAI,OAAO,oBAAoB,YAAY;AACzC,UAAI,cAAc,eAAe,GAAG;AAClC,kCAA0B,gBAAgB,KAAK,GAAG,iBAAiB,cAAc,KAAK;AACtF;AAAA,MACF;AACA,sBAAgB,WAAW,KAAK;AAAA,QAC9B,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,WAAO,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,KAAKA,WAAU,MAAM;AAC7D,gCAA0BA,aAAY,QAAQ,iBAAiB,GAAG,GAAG,cAAc,KAAK;AAAA,IAC1F,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,UAAU,CAAC,iBAAiB,SAAS;AACzC,MAAI,yBAAyB;AAC7B,OAAK,MAAM,oBAAoB,EAAE,QAAQ,cAAY;AACnD,QAAI,CAAC,uBAAuB,SAAS,IAAI,QAAQ,GAAG;AAClD,6BAAuB,SAAS,IAAI,UAAU;AAAA,QAC5C,UAAU,oBAAI,IAAI;AAAA,QAClB,YAAY,CAAC;AAAA,MACf,CAAC;AAAA,IACH;AACA,6BAAyB,uBAAuB,SAAS,IAAI,QAAQ;AAAA,EACvE,CAAC;AACD,SAAO;AACT;AACA,IAAM,gBAAgB,UAAQ,KAAK;AAGnC,IAAM,iBAAiB,kBAAgB;AACrC,MAAI,eAAe,GAAG;AACpB,WAAO;AAAA,MACL,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MAAC;AAAA,IACd;AAAA,EACF;AACA,MAAI,YAAY;AAChB,MAAI,QAAQ,oBAAI,IAAI;AACpB,MAAI,gBAAgB,oBAAI,IAAI;AAC5B,QAAM,SAAS,CAAC,KAAK,UAAU;AAC7B,UAAM,IAAI,KAAK,KAAK;AACpB;AACA,QAAI,YAAY,cAAc;AAC5B,kBAAY;AACZ,sBAAgB;AAChB,cAAQ,oBAAI,IAAI;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AAAA,IACL,IAAI,KAAK;AACP,UAAI,QAAQ,MAAM,IAAI,GAAG;AACzB,UAAI,UAAU,QAAW;AACvB,eAAO;AAAA,MACT;AACA,WAAK,QAAQ,cAAc,IAAI,GAAG,OAAO,QAAW;AAClD,eAAO,KAAK,KAAK;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,IAAI,KAAK,OAAO;AACd,UAAI,MAAM,IAAI,GAAG,GAAG;AAClB,cAAM,IAAI,KAAK,KAAK;AAAA,MACtB,OAAO;AACL,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAC3B,IAAM,4BAA4B,mBAAmB;AACrD,IAAM,uBAAuB,YAAU;AACrC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAOJ,MAAI,iBAAiB,eAAa;AAChC,UAAM,YAAY,CAAC;AACnB,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,gBAAgB;AACpB,QAAI;AACJ,aAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACrD,UAAI,mBAAmB,UAAU,KAAK;AACtC,UAAI,iBAAiB,KAAK,eAAe,GAAG;AAC1C,YAAI,qBAAqB,oBAAoB;AAC3C,oBAAU,KAAK,UAAU,MAAM,eAAe,KAAK,CAAC;AACpD,0BAAgB,QAAQ;AACxB;AAAA,QACF;AACA,YAAI,qBAAqB,KAAK;AAC5B,oCAA0B;AAC1B;AAAA,QACF;AAAA,MACF;AACA,UAAI,qBAAqB,KAAK;AAC5B;AAAA,MACF,WAAW,qBAAqB,KAAK;AACnC;AAAA,MACF,WAAW,qBAAqB,KAAK;AACnC;AAAA,MACF,WAAW,qBAAqB,KAAK;AACnC;AAAA,MACF;AAAA,IACF;AACA,UAAM,qCAAqC,UAAU,WAAW,IAAI,YAAY,UAAU,UAAU,aAAa;AACjH,UAAM,gBAAgB,uBAAuB,kCAAkC;AAC/E,UAAM,uBAAuB,kBAAkB;AAC/C,UAAM,+BAA+B,2BAA2B,0BAA0B,gBAAgB,0BAA0B,gBAAgB;AACpJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ;AACV,UAAM,aAAa,SAAS;AAC5B,UAAM,yBAAyB;AAC/B,qBAAiB,eAAa,UAAU,WAAW,UAAU,IAAI,uBAAuB,UAAU,UAAU,WAAW,MAAM,CAAC,IAAI;AAAA,MAChI,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,MACZ,sBAAsB;AAAA,MACtB,eAAe;AAAA,MACf,8BAA8B;AAAA,IAChC;AAAA,EACF;AACA,MAAI,4BAA4B;AAC9B,UAAM,yBAAyB;AAC/B,qBAAiB,eAAa,2BAA2B;AAAA,MACvD;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,yBAAyB,mBAAiB;AAC9C,MAAI,cAAc,SAAS,kBAAkB,GAAG;AAC9C,WAAO,cAAc,UAAU,GAAG,cAAc,SAAS,CAAC;AAAA,EAC5D;AAKA,MAAI,cAAc,WAAW,kBAAkB,GAAG;AAChD,WAAO,cAAc,UAAU,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAOA,IAAM,sBAAsB,YAAU;AACpC,QAAM,0BAA0B,OAAO,YAAY,OAAO,wBAAwB,IAAI,cAAY,CAAC,UAAU,IAAI,CAAC,CAAC;AACnH,QAAM,gBAAgB,eAAa;AACjC,QAAI,UAAU,UAAU,GAAG;AACzB,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,CAAC;AACzB,QAAI,oBAAoB,CAAC;AACzB,cAAU,QAAQ,cAAY;AAC5B,YAAM,sBAAsB,SAAS,CAAC,MAAM,OAAO,wBAAwB,QAAQ;AACnF,UAAI,qBAAqB;AACvB,wBAAgB,KAAK,GAAG,kBAAkB,KAAK,GAAG,QAAQ;AAC1D,4BAAoB,CAAC;AAAA,MACvB,OAAO;AACL,0BAAkB,KAAK,QAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AACD,oBAAgB,KAAK,GAAG,kBAAkB,KAAK,CAAC;AAChD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,oBAAoB,YAAW;AAAA,EACnC,OAAO,eAAe,OAAO,SAAS;AAAA,EACtC,gBAAgB,qBAAqB,MAAM;AAAA,EAC3C,eAAe,oBAAoB,MAAM;AAAA,GACtC,sBAAsB,MAAM;AAEjC,IAAM,sBAAsB;AAC5B,IAAM,iBAAiB,CAAC,WAAW,gBAAgB;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAQJ,QAAM,wBAAwB,CAAC;AAC/B,QAAM,aAAa,UAAU,KAAK,EAAE,MAAM,mBAAmB;AAC7D,MAAI,SAAS;AACb,WAAS,QAAQ,WAAW,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG;AAC9D,UAAM,oBAAoB,WAAW,KAAK;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,eAAe,iBAAiB;AACpC,QAAI,YAAY;AACd,eAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AACjE;AAAA,IACF;AACA,QAAI,qBAAqB,CAAC,CAAC;AAC3B,QAAI,eAAe,gBAAgB,qBAAqB,cAAc,UAAU,GAAG,4BAA4B,IAAI,aAAa;AAChI,QAAI,CAAC,cAAc;AACjB,UAAI,CAAC,oBAAoB;AAEvB,iBAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AACjE;AAAA,MACF;AACA,qBAAe,gBAAgB,aAAa;AAC5C,UAAI,CAAC,cAAc;AAEjB,iBAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AACjE;AAAA,MACF;AACA,2BAAqB;AAAA,IACvB;AACA,UAAM,kBAAkB,cAAc,SAAS,EAAE,KAAK,GAAG;AACzD,UAAM,aAAa,uBAAuB,kBAAkB,qBAAqB;AACjF,UAAM,UAAU,aAAa;AAC7B,QAAI,sBAAsB,SAAS,OAAO,GAAG;AAE3C;AAAA,IACF;AACA,0BAAsB,KAAK,OAAO;AAClC,UAAM,iBAAiB,4BAA4B,cAAc,kBAAkB;AACnF,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;AAC9C,YAAM,QAAQ,eAAe,CAAC;AAC9B,4BAAsB,KAAK,aAAa,KAAK;AAAA,IAC/C;AAEA,aAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AAAA,EACnE;AACA,SAAO;AACT;AAWA,SAAS,SAAS;AAChB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS;AACb,SAAO,QAAQ,UAAU,QAAQ;AAC/B,QAAI,WAAW,UAAU,OAAO,GAAG;AACjC,UAAI,gBAAgB,QAAQ,QAAQ,GAAG;AACrC,mBAAW,UAAU;AACrB,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,UAAU,SAAO;AACrB,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,CAAC,GAAG;AACV,UAAI,gBAAgB,QAAQ,IAAI,CAAC,CAAC,GAAG;AACnC,mBAAW,UAAU;AACrB,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,sBAAsB,kBAAkB;AACnE,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,WAAS,kBAAkB,WAAW;AACpC,UAAM,SAAS,iBAAiB,OAAO,CAAC,gBAAgB,wBAAwB,oBAAoB,cAAc,GAAG,kBAAkB,CAAC;AACxI,kBAAc,kBAAkB,MAAM;AACtC,eAAW,YAAY,MAAM;AAC7B,eAAW,YAAY,MAAM;AAC7B,qBAAiB;AACjB,WAAO,cAAc,SAAS;AAAA,EAChC;AACA,WAAS,cAAc,WAAW;AAChC,UAAM,eAAe,SAAS,SAAS;AACvC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,eAAe,WAAW,WAAW;AACpD,aAAS,WAAW,MAAM;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,oBAAoB;AAClC,WAAO,eAAe,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EACrD;AACF;AACA,IAAM,YAAY,SAAO;AACvB,QAAM,cAAc,WAAS,MAAM,GAAG,KAAK,CAAC;AAC5C,cAAY,gBAAgB;AAC5B,SAAO;AACT;AACA,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAE3B,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa,WAAS,cAAc,KAAK,KAAK;AACpD,IAAM,WAAW,WAAS,CAAC,CAAC,SAAS,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC;AAChE,IAAM,YAAY,WAAS,CAAC,CAAC,SAAS,OAAO,UAAU,OAAO,KAAK,CAAC;AACpE,IAAM,YAAY,WAAS,MAAM,SAAS,GAAG,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AAC7E,IAAM,eAAe,WAAS,gBAAgB,KAAK,KAAK;AACxD,IAAM,QAAQ,MAAM;AACpB,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA,EAIrB,gBAAgB,KAAK,KAAK,KAAK,CAAC,mBAAmB,KAAK,KAAK;AAAA;AAC7D,IAAM,UAAU,MAAM;AACtB,IAAM,WAAW,WAAS,YAAY,KAAK,KAAK;AAChD,IAAM,UAAU,WAAS,WAAW,KAAK,KAAK;AAC9C,IAAM,oBAAoB,WAAS,CAAC,iBAAiB,KAAK,KAAK,CAAC,oBAAoB,KAAK;AACzF,IAAM,kBAAkB,WAAS,oBAAoB,OAAO,aAAa,OAAO;AAChF,IAAM,mBAAmB,WAAS,oBAAoB,KAAK,KAAK;AAChE,IAAM,oBAAoB,WAAS,oBAAoB,OAAO,eAAe,YAAY;AACzF,IAAM,oBAAoB,WAAS,oBAAoB,OAAO,eAAe,QAAQ;AACrF,IAAM,sBAAsB,WAAS,oBAAoB,OAAO,iBAAiB,OAAO;AACxF,IAAM,mBAAmB,WAAS,oBAAoB,OAAO,cAAc,OAAO;AAClF,IAAM,oBAAoB,WAAS,oBAAoB,OAAO,eAAe,QAAQ;AACrF,IAAM,sBAAsB,WAAS,uBAAuB,KAAK,KAAK;AACtE,IAAM,4BAA4B,WAAS,uBAAuB,OAAO,aAAa;AACtF,IAAM,gCAAgC,WAAS,uBAAuB,OAAO,iBAAiB;AAC9F,IAAM,8BAA8B,WAAS,uBAAuB,OAAO,eAAe;AAC1F,IAAM,0BAA0B,WAAS,uBAAuB,OAAO,WAAW;AAClF,IAAM,2BAA2B,WAAS,uBAAuB,OAAO,YAAY;AACpF,IAAM,4BAA4B,WAAS,uBAAuB,OAAO,eAAe,IAAI;AAE5F,IAAM,sBAAsB,CAAC,OAAO,WAAW,cAAc;AAC3D,QAAM,SAAS,oBAAoB,KAAK,KAAK;AAC7C,MAAI,QAAQ;AACV,QAAI,OAAO,CAAC,GAAG;AACb,aAAO,UAAU,OAAO,CAAC,CAAC;AAAA,IAC5B;AACA,WAAO,UAAU,OAAO,CAAC,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AACA,IAAM,yBAAyB,CAAC,OAAO,WAAW,qBAAqB,UAAU;AAC/E,QAAM,SAAS,uBAAuB,KAAK,KAAK;AAChD,MAAI,QAAQ;AACV,QAAI,OAAO,CAAC,GAAG;AACb,aAAO,UAAU,OAAO,CAAC,CAAC;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB,WAAS,UAAU,cAAc,UAAU;AACnE,IAAM,eAAe,WAAS,UAAU,WAAW,UAAU;AAC7D,IAAM,cAAc,WAAS,UAAU,YAAY,UAAU,UAAU,UAAU;AACjF,IAAM,gBAAgB,WAAS,UAAU;AACzC,IAAM,gBAAgB,WAAS,UAAU;AACzC,IAAM,oBAAoB,WAAS,UAAU;AAC7C,IAAM,gBAAgB,WAAS,UAAU;AACzC,IAAM,aAA0B,OAAO,eAAe;AAAA,EACpD,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,MAAM;AAM7B,QAAM,aAAa,UAAU,OAAO;AACpC,QAAM,YAAY,UAAU,MAAM;AAClC,QAAM,YAAY,UAAU,MAAM;AAClC,QAAM,kBAAkB,UAAU,aAAa;AAC/C,QAAM,gBAAgB,UAAU,UAAU;AAC1C,QAAM,eAAe,UAAU,SAAS;AACxC,QAAM,kBAAkB,UAAU,YAAY;AAC9C,QAAM,iBAAiB,UAAU,WAAW;AAC5C,QAAM,eAAe,UAAU,SAAS;AACxC,QAAM,cAAc,UAAU,QAAQ;AACtC,QAAM,cAAc,UAAU,QAAQ;AACtC,QAAM,mBAAmB,UAAU,cAAc;AACjD,QAAM,kBAAkB,UAAU,aAAa;AAC/C,QAAM,kBAAkB,UAAU,aAAa;AAC/C,QAAM,YAAY,UAAU,MAAM;AAClC,QAAM,mBAAmB,UAAU,aAAa;AAChD,QAAM,cAAc,UAAU,QAAQ;AACtC,QAAM,YAAY,UAAU,MAAM;AAClC,QAAM,eAAe,UAAU,SAAS;AAQxC,QAAM,aAAa,MAAM,CAAC,QAAQ,SAAS,OAAO,cAAc,QAAQ,QAAQ,SAAS,QAAQ;AACjG,QAAM,gBAAgB,MAAM;AAAA,IAAC;AAAA,IAAU;AAAA,IAAO;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAS;AAAA;AAAA,IAEzE;AAAA,IAAY;AAAA;AAAA,IAEZ;AAAA,IAAa;AAAA;AAAA,IAEb;AAAA,IAAgB;AAAA;AAAA,IAEhB;AAAA,EAAa;AACb,QAAM,6BAA6B,MAAM,CAAC,GAAG,cAAc,GAAG,qBAAqB,gBAAgB;AACnG,QAAM,gBAAgB,MAAM,CAAC,QAAQ,UAAU,QAAQ,WAAW,QAAQ;AAC1E,QAAM,kBAAkB,MAAM,CAAC,QAAQ,WAAW,MAAM;AACxD,QAAM,0BAA0B,MAAM,CAAC,qBAAqB,kBAAkB,YAAY;AAC1F,QAAM,aAAa,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,wBAAwB,CAAC;AAClF,QAAM,4BAA4B,MAAM,CAAC,WAAW,QAAQ,WAAW,qBAAqB,gBAAgB;AAC5G,QAAM,6BAA6B,MAAM,CAAC,QAAQ;AAAA,IAChD,MAAM,CAAC,QAAQ,WAAW,qBAAqB,gBAAgB;AAAA,EACjE,GAAG,WAAW,qBAAqB,gBAAgB;AACnD,QAAM,4BAA4B,MAAM,CAAC,WAAW,QAAQ,qBAAqB,gBAAgB;AACjG,QAAM,wBAAwB,MAAM,CAAC,QAAQ,OAAO,OAAO,MAAM,qBAAqB,gBAAgB;AACtG,QAAM,wBAAwB,MAAM,CAAC,SAAS,OAAO,UAAU,WAAW,UAAU,UAAU,WAAW,YAAY,eAAe,UAAU;AAC9I,QAAM,0BAA0B,MAAM,CAAC,SAAS,OAAO,UAAU,WAAW,eAAe,UAAU;AACrG,QAAM,cAAc,MAAM,CAAC,QAAQ,GAAG,wBAAwB,CAAC;AAC/D,QAAM,cAAc,MAAM,CAAC,YAAY,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,GAAG,wBAAwB,CAAC;AAClJ,QAAM,aAAa,MAAM,CAAC,YAAY,qBAAqB,gBAAgB;AAC3E,QAAM,kBAAkB,MAAM,CAAC,GAAG,cAAc,GAAG,6BAA6B,qBAAqB;AAAA,IACnG,UAAU,CAAC,qBAAqB,gBAAgB;AAAA,EAClD,CAAC;AACD,QAAM,gBAAgB,MAAM,CAAC,aAAa;AAAA,IACxC,QAAQ,CAAC,IAAI,KAAK,KAAK,SAAS,OAAO;AAAA,EACzC,CAAC;AACD,QAAM,cAAc,MAAM,CAAC,QAAQ,SAAS,WAAW,yBAAyB,iBAAiB;AAAA,IAC/F,MAAM,CAAC,qBAAqB,gBAAgB;AAAA,EAC9C,CAAC;AACD,QAAM,4BAA4B,MAAM,CAAC,WAAW,2BAA2B,iBAAiB;AAChG,QAAM,cAAc,MAAM;AAAA;AAAA,IAE1B;AAAA,IAAI;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAqB;AAAA,EAAgB;AACtE,QAAM,mBAAmB,MAAM,CAAC,IAAI,UAAU,2BAA2B,iBAAiB;AAC1F,QAAM,iBAAiB,MAAM,CAAC,SAAS,UAAU,UAAU,QAAQ;AACnE,QAAM,iBAAiB,MAAM,CAAC,UAAU,YAAY,UAAU,WAAW,UAAU,WAAW,eAAe,cAAc,cAAc,cAAc,cAAc,aAAa,OAAO,cAAc,SAAS,YAAY;AAC5N,QAAM,yBAAyB,MAAM,CAAC,UAAU,WAAW,6BAA6B,mBAAmB;AAC3G,QAAM,YAAY,MAAM;AAAA;AAAA,IAExB;AAAA,IAAI;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAqB;AAAA,EAAgB;AAC5D,QAAM,cAAc,MAAM,CAAC,QAAQ,UAAU,qBAAqB,gBAAgB;AAClF,QAAM,aAAa,MAAM,CAAC,QAAQ,UAAU,qBAAqB,gBAAgB;AACjF,QAAM,YAAY,MAAM,CAAC,UAAU,qBAAqB,gBAAgB;AACxE,QAAM,iBAAiB,MAAM,CAAC,YAAY,QAAQ,GAAG,wBAAwB,CAAC;AAC9E,SAAO;AAAA,IACL,WAAW;AAAA,IACX,OAAO;AAAA,MACL,SAAS,CAAC,QAAQ,QAAQ,SAAS,QAAQ;AAAA,MAC3C,QAAQ,CAAC,OAAO;AAAA,MAChB,MAAM,CAAC,YAAY;AAAA,MACnB,YAAY,CAAC,YAAY;AAAA,MACzB,OAAO,CAAC,KAAK;AAAA,MACb,WAAW,CAAC,YAAY;AAAA,MACxB,eAAe,CAAC,YAAY;AAAA,MAC5B,MAAM,CAAC,MAAM,OAAO,QAAQ;AAAA,MAC5B,MAAM,CAAC,iBAAiB;AAAA,MACxB,eAAe,CAAC,QAAQ,cAAc,SAAS,UAAU,UAAU,YAAY,QAAQ,aAAa,OAAO;AAAA,MAC3G,gBAAgB,CAAC,YAAY;AAAA,MAC7B,SAAS,CAAC,QAAQ,SAAS,QAAQ,UAAU,WAAW,OAAO;AAAA,MAC/D,aAAa,CAAC,YAAY,QAAQ,UAAU,YAAY,WAAW,MAAM;AAAA,MACzE,QAAQ,CAAC,YAAY;AAAA,MACrB,QAAQ,CAAC,YAAY;AAAA,MACrB,SAAS,CAAC,MAAM,QAAQ;AAAA,MACxB,MAAM,CAAC,YAAY;AAAA,MACnB,eAAe,CAAC,YAAY;AAAA,MAC5B,UAAU,CAAC,WAAW,SAAS,UAAU,QAAQ,SAAS,QAAQ;AAAA,IACpE;AAAA,IACA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQX,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,QAAQ,UAAU,YAAY,kBAAkB,qBAAqB,WAAW;AAAA,MAC3F,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMD,WAAW,CAAC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvB,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,UAAU,kBAAkB,qBAAqB,cAAc;AAAA,MAC3E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,WAAW;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,CAAC,QAAQ,SAAS,cAAc,cAAc;AAAA,MAChE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,kBAAkB,CAAC,SAAS,OAAO;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,KAAK,CAAC;AAAA,QACJ,KAAK,CAAC,UAAU,SAAS;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC,SAAS,gBAAgB,UAAU,QAAQ,eAAe,SAAS,gBAAgB,iBAAiB,cAAc,gBAAgB,sBAAsB,sBAAsB,sBAAsB,mBAAmB,aAAa,aAAa,QAAQ,eAAe,YAAY,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnT,IAAI,CAAC,WAAW,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7B,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,SAAS,QAAQ,QAAQ,SAAS,KAAK;AAAA,MACjD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK;AAAA,MACzD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC,WAAW,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvC,cAAc,CAAC;AAAA,QACb,QAAQ,CAAC,WAAW,SAAS,QAAQ,QAAQ,YAAY;AAAA,MAC3D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,QAAQ,2BAA2B;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,cAAc;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,cAAc;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,cAAc;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,gBAAgB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,gBAAgB;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,gBAAgB;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC,UAAU,SAAS,YAAY,YAAY,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK9D,OAAO,CAAC;AAAA,QACN,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,WAAW;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,WAAW;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,KAAK,CAAC;AAAA,QACJ,KAAK,WAAW;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,KAAK,CAAC;AAAA,QACJ,KAAK,WAAW;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,WAAW;AAAA,MACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC,WAAW,aAAa,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAK/C,GAAG,CAAC;AAAA,QACF,GAAG,CAAC,WAAW,QAAQ,qBAAqB,gBAAgB;AAAA,MAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,YAAY,QAAQ,QAAQ,gBAAgB,GAAG,wBAAwB,CAAC;AAAA,MAClF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,MAAM,CAAC,OAAO,eAAe,OAAO,aAAa;AAAA,MACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,UAAU,QAAQ,cAAc;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,CAAC,UAAU,YAAY,QAAQ,WAAW,QAAQ,gBAAgB;AAAA,MAC1E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MAC5D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,WAAW,SAAS,QAAQ,QAAQ,qBAAqB,gBAAgB;AAAA,MACnF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,0BAA0B;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,KAAK,2BAA2B;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,0BAA0B;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,0BAA0B;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,0BAA0B;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,KAAK,2BAA2B;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,0BAA0B;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,0BAA0B;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,OAAO,OAAO,SAAS,aAAa,WAAW;AAAA,MAC/D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,sBAAsB;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,sBAAsB;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,KAAK,CAAC;AAAA,QACJ,KAAK,wBAAwB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,wBAAwB;AAAA,MACnC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,wBAAwB;AAAA,MACnC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,SAAS,CAAC,GAAG,sBAAsB,GAAG,QAAQ;AAAA,MAChD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,iBAAiB,CAAC,GAAG,wBAAwB,GAAG,QAAQ;AAAA,MAC1D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,CAAC,QAAQ,GAAG,wBAAwB,CAAC;AAAA,MACvD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,SAAS,CAAC,UAAU,GAAG,sBAAsB,CAAC;AAAA,MAChD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,OAAO,CAAC,GAAG,wBAAwB,GAAG;AAAA,UACpC,UAAU,CAAC,IAAI,MAAM;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,MAAM,CAAC,QAAQ,GAAG,wBAAwB,GAAG;AAAA,UAC3C,UAAU,CAAC,IAAI,MAAM;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,iBAAiB,sBAAsB;AAAA,MACzC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,CAAC,GAAG,wBAAwB,GAAG,UAAU;AAAA,MAC1D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,CAAC,QAAQ,GAAG,wBAAwB,CAAC;AAAA,MACrD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMD,GAAG,CAAC;AAAA,QACF,GAAG,wBAAwB;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,wBAAwB;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,GAAG,CAAC;AAAA,QACF,GAAG,YAAY;AAAA,MACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,IAAI,CAAC;AAAA,QACH,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,wBAAwB;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKrC,WAAW,CAAC;AAAA,QACV,WAAW,wBAAwB;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQrC,MAAM,CAAC;AAAA,QACL,MAAM,YAAY;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,GAAG,CAAC;AAAA,QACF,GAAG,CAAC,gBAAgB,UAAU,GAAG,YAAY,CAAC;AAAA,MAChD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS;AAAA,UAAC;AAAA,UAAgB;AAAA;AAAA,UAC1B;AAAA,UAAQ,GAAG,YAAY;AAAA,QAAC;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS;AAAA,UAAC;AAAA,UAAgB;AAAA,UAAU;AAAA;AAAA,UACpC;AAAA;AAAA,UACA;AAAA,YACE,QAAQ,CAAC,eAAe;AAAA,UAC1B;AAAA,UAAG,GAAG,YAAY;AAAA,QAAC;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,GAAG,CAAC;AAAA,QACF,GAAG,CAAC,UAAU,MAAM,GAAG,YAAY,CAAC;AAAA,MACtC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,UAAU,MAAM,QAAQ,GAAG,YAAY,CAAC;AAAA,MACpD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,UAAU,MAAM,GAAG,YAAY,CAAC;AAAA,MAC5C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,QAAQ,WAAW,2BAA2B,iBAAiB;AAAA,MACxE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC,eAAe,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKxD,cAAc,CAAC,UAAU,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAKrC,eAAe,CAAC;AAAA,QACd,MAAM,CAAC,iBAAiB,qBAAqB,iBAAiB;AAAA,MAChE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,CAAC,mBAAmB,mBAAmB,aAAa,kBAAkB,UAAU,iBAAiB,YAAY,kBAAkB,kBAAkB,WAAW,gBAAgB;AAAA,MAC9L,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,MAAM,CAAC,+BAA+B,kBAAkB,SAAS;AAAA,MACnE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,eAAe,CAAC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzB,oBAAoB,CAAC,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnC,cAAc,CAAC,eAAe,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7C,eAAe,CAAC,qBAAqB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnD,gBAAgB,CAAC,sBAAsB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,MAK1D,UAAU,CAAC;AAAA,QACT,UAAU,CAAC,eAAe,qBAAqB,gBAAgB;AAAA,MACjE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,CAAC,UAAU,QAAQ,qBAAqB,iBAAiB;AAAA,MACzE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS;AAAA;AAAA,UACT;AAAA,UAAc,GAAG,wBAAwB;AAAA,QAAC;AAAA,MAC5C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,CAAC,QAAQ,qBAAqB,gBAAgB;AAAA,MAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,uBAAuB,CAAC;AAAA,QACtB,MAAM,CAAC,UAAU,SAAS;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,MAAM,CAAC,QAAQ,WAAW,QAAQ,qBAAqB,gBAAgB;AAAA,MACzE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,MAAM,CAAC,QAAQ,UAAU,SAAS,WAAW,SAAS,KAAK;AAAA,MAC7D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMD,qBAAqB,CAAC;AAAA,QACpB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,MAAM,WAAW;AAAA,MACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC,aAAa,YAAY,gBAAgB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3E,yBAAyB,CAAC;AAAA,QACxB,YAAY,CAAC,GAAG,eAAe,GAAG,MAAM;AAAA,MAC1C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,6BAA6B,CAAC;AAAA,QAC5B,YAAY,CAAC,UAAU,aAAa,QAAQ,qBAAqB,iBAAiB;AAAA,MACpF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,yBAAyB,CAAC;AAAA,QACxB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,oBAAoB,CAAC,UAAU,QAAQ,qBAAqB,gBAAgB;AAAA,MAC9E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC,aAAa,aAAa,cAAc,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,MAKxE,iBAAiB,CAAC,YAAY,iBAAiB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,MAK1D,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,QAAQ,UAAU,WAAW,QAAQ;AAAA,MAC9C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,wBAAwB;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,OAAO,CAAC,YAAY,OAAO,UAAU,UAAU,YAAY,eAAe,OAAO,SAAS,qBAAqB,gBAAgB;AAAA,MACjI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,CAAC,UAAU,UAAU,OAAO,YAAY,YAAY,cAAc;AAAA,MAChF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,UAAU,SAAS,OAAO,MAAM;AAAA,MAC1C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,CAAC,cAAc,YAAY,QAAQ;AAAA,MAC3C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,QAAQ,UAAU,MAAM;AAAA,MACpC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,QAAQ,qBAAqB,gBAAgB;AAAA,MACzD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,iBAAiB,CAAC;AAAA,QAChB,IAAI,CAAC,SAAS,SAAS,QAAQ;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,CAAC,UAAU,WAAW,WAAW,MAAM;AAAA,MACpD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,UAAU,WAAW,SAAS;AAAA,MAC9C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,IAAI,gBAAgB;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,IAAI,cAAc;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,IAAI,YAAY;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,IAAI,CAAC,QAAQ;AAAA,UACX,QAAQ,CAAC;AAAA,YACP,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,UACjD,GAAG,WAAW,qBAAqB,gBAAgB;AAAA,UACnD,QAAQ,CAAC,IAAI,qBAAqB,gBAAgB;AAAA,UAClD,OAAO,CAAC,WAAW,qBAAqB,gBAAgB;AAAA,QAC1D,GAAG,0BAA0B,gBAAgB;AAAA,MAC/C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,IAAI,WAAW;AAAA,MACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,qBAAqB,CAAC;AAAA,QACpB,MAAM,0BAA0B;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,KAAK,0BAA0B;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,IAAI,0BAA0B;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,MAAM,WAAW;AAAA,MACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,KAAK,WAAW;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,IAAI,WAAW;AAAA,MACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,SAAS,CAAC;AAAA,QACR,SAAS,YAAY;AAAA,MACvB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,YAAY;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,YAAY;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,QAAQ,iBAAiB;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvC,YAAY,CAAC;AAAA,QACX,YAAY,iBAAiB;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvC,gBAAgB,CAAC;AAAA,QACf,QAAQ,CAAC,GAAG,eAAe,GAAG,UAAU,MAAM;AAAA,MAChD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,QAAQ,CAAC,GAAG,eAAe,GAAG,UAAU,MAAM;AAAA,MAChD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,YAAY,WAAW;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,SAAS,CAAC,GAAG,eAAe,GAAG,QAAQ,QAAQ;AAAA,MACjD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,kBAAkB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACpE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,SAAS,CAAC,IAAI,UAAU,2BAA2B,iBAAiB;AAAA,MACtE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,SAAS,WAAW;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,QAAQ,CAAC;AAAA,QACP,QAAQ;AAAA;AAAA,UAER;AAAA,UAAI;AAAA,UAAQ;AAAA,UAAa;AAAA,UAA2B;AAAA,QAAiB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,CAAC,QAAQ,kBAAkB,2BAA2B,iBAAiB;AAAA,MACzF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,sBAAsB,CAAC;AAAA,QACrB,gBAAgB,WAAW;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,MAAM,iBAAiB;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOD,gBAAgB,CAAC,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7B,cAAc,CAAC;AAAA,QACb,MAAM,WAAW;AAAA,MACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOD,iBAAiB,CAAC;AAAA,QAChB,eAAe,CAAC,UAAU,iBAAiB;AAAA,MAC7C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOD,qBAAqB,CAAC;AAAA,QACpB,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,cAAc,iBAAiB;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,cAAc,WAAW;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,CAAC,QAAQ,iBAAiB,2BAA2B,iBAAiB;AAAA,MACvF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,qBAAqB,CAAC;AAAA,QACpB,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MAC3D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,GAAG,eAAe,GAAG,eAAe,cAAc;AAAA,MAClE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,eAAe;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,UAAU,WAAW,WAAW,QAAQ,UAAU,MAAM;AAAA,MACxE,GAAG,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjB,kBAAkB,CAAC;AAAA,QACjB,MAAM,CAAC,OAAO,YAAY,aAAa,SAAS;AAAA,MAClD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,yBAAyB,CAAC;AAAA,QACxB,eAAe,CAAC,QAAQ;AAAA,MAC1B,CAAC;AAAA,MACD,8BAA8B,CAAC;AAAA,QAC7B,oBAAoB,uBAAuB;AAAA,MAC7C,CAAC;AAAA,MACD,4BAA4B,CAAC;AAAA,QAC3B,kBAAkB,uBAAuB;AAAA,MAC3C,CAAC;AAAA,MACD,gCAAgC,CAAC;AAAA,QAC/B,oBAAoB,WAAW;AAAA,MACjC,CAAC;AAAA,MACD,8BAA8B,CAAC;AAAA,QAC7B,kBAAkB,WAAW;AAAA,MAC/B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,eAAe,uBAAuB;AAAA,MACxC,CAAC;AAAA,MACD,uBAAuB,CAAC;AAAA,QACtB,aAAa,uBAAuB;AAAA,MACtC,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,aAAa,WAAW;AAAA,MAC1B,CAAC;AAAA,MACD,qBAAqB,CAAC;AAAA,QACpB,eAAe,CAAC,qBAAqB,gBAAgB;AAAA,MACvD,CAAC;AAAA,MACD,8BAA8B,CAAC;AAAA,QAC7B,oBAAoB,uBAAuB;AAAA,MAC7C,CAAC;AAAA,MACD,4BAA4B,CAAC;AAAA,QAC3B,kBAAkB,uBAAuB;AAAA,MAC3C,CAAC;AAAA,MACD,gCAAgC,CAAC;AAAA,QAC/B,oBAAoB,WAAW;AAAA,MACjC,CAAC;AAAA,MACD,8BAA8B,CAAC;AAAA,QAC7B,kBAAkB,WAAW;AAAA,MAC/B,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,eAAe,CAAC,UAAU,SAAS;AAAA,MACrC,CAAC;AAAA,MACD,0BAA0B,CAAC;AAAA,QACzB,eAAe,CAAC;AAAA,UACd,SAAS,CAAC,QAAQ,QAAQ;AAAA,UAC1B,UAAU,CAAC,QAAQ,QAAQ;AAAA,QAC7B,CAAC;AAAA,MACH,CAAC;AAAA,MACD,yBAAyB,CAAC;AAAA,QACxB,kBAAkB,cAAc;AAAA,MAClC,CAAC;AAAA,MACD,wBAAwB,CAAC;AAAA,QACvB,cAAc,CAAC,QAAQ;AAAA,MACzB,CAAC;AAAA,MACD,6BAA6B,CAAC;AAAA,QAC5B,mBAAmB,uBAAuB;AAAA,MAC5C,CAAC;AAAA,MACD,2BAA2B,CAAC;AAAA,QAC1B,iBAAiB,uBAAuB;AAAA,MAC1C,CAAC;AAAA,MACD,+BAA+B,CAAC;AAAA,QAC9B,mBAAmB,WAAW;AAAA,MAChC,CAAC;AAAA,MACD,6BAA6B,CAAC;AAAA,QAC5B,iBAAiB,WAAW;AAAA,MAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,SAAS,aAAa,OAAO;AAAA,MACtC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,CAAC,UAAU,WAAW,WAAW,QAAQ,UAAU,MAAM;AAAA,MAC1E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,MAAM,gBAAgB;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,MAAM,cAAc;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,MAAM,YAAY;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,SAAS,WAAW;AAAA,MACpC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,MAAM,CAAC,QAAQ,qBAAqB,gBAAgB;AAAA,MACtD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,QAAQ,CAAC;AAAA,QACP,QAAQ;AAAA;AAAA,UAER;AAAA,UAAI;AAAA,UAAQ;AAAA,UAAqB;AAAA,QAAgB;AAAA,MACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,UAAU;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MAC5D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe;AAAA;AAAA,UAEf;AAAA,UAAI;AAAA,UAAQ;AAAA,UAAiB;AAAA,UAA2B;AAAA,QAAiB;AAAA,MAC3E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,qBAAqB,CAAC;AAAA,QACpB,eAAe,WAAW;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MACjE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,cAAc,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MAChE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MAC5D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MAC7D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,mBAAmB;AAAA;AAAA,UAEnB;AAAA,UAAI;AAAA,UAAQ;AAAA,UAAqB;AAAA,QAAgB;AAAA,MACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,iBAAiB,CAAC;AAAA,QAChB,iBAAiB,UAAU;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,uBAAuB,CAAC;AAAA,QACtB,uBAAuB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACzE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,qBAAqB,CAAC;AAAA,QACpB,qBAAqB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,sBAAsB,CAAC;AAAA,QACrB,sBAAsB,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MAC5E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,uBAAuB,CAAC;AAAA,QACtB,uBAAuB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACzE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,mBAAmB,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MACzE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,oBAAoB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACtE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,qBAAqB,CAAC;AAAA,QACpB,qBAAqB,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,kBAAkB,CAAC,IAAI,UAAU,qBAAqB,gBAAgB;AAAA,MACxE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,mBAAmB,CAAC;AAAA,QAClB,QAAQ,CAAC,YAAY,UAAU;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,kBAAkB,wBAAwB;AAAA,MAC5C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,oBAAoB,wBAAwB;AAAA,MAC9C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,oBAAoB,wBAAwB;AAAA,MAC9C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,OAAO,CAAC,QAAQ,OAAO;AAAA,MACzB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,OAAO,QAAQ;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,YAAY,CAAC;AAAA,QACX,YAAY,CAAC,IAAI,OAAO,UAAU,WAAW,UAAU,aAAa,QAAQ,qBAAqB,gBAAgB;AAAA,MACnH,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,uBAAuB,CAAC;AAAA,QACtB,YAAY,CAAC,UAAU,UAAU;AAAA,MACnC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,CAAC,UAAU,WAAW,qBAAqB,gBAAgB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,MAAM,CAAC;AAAA,QACL,MAAM,CAAC,UAAU,WAAW,WAAW,qBAAqB,gBAAgB;AAAA,MAC9E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,UAAU,qBAAqB,gBAAgB;AAAA,MACzD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,SAAS,CAAC;AAAA,QACR,SAAS,CAAC,QAAQ,cAAc,qBAAqB,gBAAgB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,UAAU,CAAC;AAAA,QACT,UAAU,CAAC,UAAU,SAAS;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,CAAC,kBAAkB,qBAAqB,gBAAgB;AAAA,MACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,sBAAsB,CAAC;AAAA,QACrB,sBAAsB,2BAA2B;AAAA,MACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,YAAY;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,YAAY;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,YAAY;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,YAAY;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,WAAW;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,WAAW;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,WAAW;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvB,MAAM,CAAC;AAAA,QACL,MAAM,UAAU;AAAA,MAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,UAAU;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,UAAU,CAAC;AAAA,QACT,UAAU,UAAU;AAAA,MACtB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,CAAC,qBAAqB,kBAAkB,IAAI,QAAQ,OAAO,KAAK;AAAA,MAC7E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,oBAAoB,CAAC;AAAA,QACnB,QAAQ,2BAA2B;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,WAAW,CAAC,MAAM,MAAM;AAAA,MAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,WAAW,eAAe;AAAA,MAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,eAAe;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,eAAe;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,eAAe;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQnC,QAAQ,CAAC;AAAA,QACP,QAAQ,WAAW;AAAA,MACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,CAAC,QAAQ,MAAM;AAAA,MAC7B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,QAAQ,CAAC,UAAU,QAAQ,SAAS,cAAc,aAAa,YAAY;AAAA,MAC7E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,QAAQ,WAAW,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,gBAAgB,YAAY,QAAQ,aAAa,iBAAiB,SAAS,QAAQ,WAAW,QAAQ,YAAY,cAAc,cAAc,cAAc,YAAY,YAAY,YAAY,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,eAAe,eAAe,WAAW,YAAY,qBAAqB,gBAAgB;AAAA,MACpd,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,gBAAgB,CAAC;AAAA,QACf,gBAAgB,CAAC,SAAS,SAAS;AAAA,MACrC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,kBAAkB,CAAC;AAAA,QACjB,kBAAkB,CAAC,QAAQ,MAAM;AAAA,MACnC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,QAAQ,IAAI,KAAK,GAAG;AAAA,MAC/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,QAAQ,CAAC,QAAQ,QAAQ;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,wBAAwB;AAAA,MACtC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,YAAY,wBAAwB;AAAA,MACtC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,aAAa,wBAAwB;AAAA,MACvC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,cAAc,CAAC;AAAA,QACb,MAAM,CAAC,SAAS,OAAO,UAAU,YAAY;AAAA,MAC/C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,UAAU,QAAQ;AAAA,MAC3B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,aAAa,CAAC;AAAA,QACZ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,mBAAmB,CAAC;AAAA,QAClB,MAAM,CAAC,aAAa,WAAW;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,OAAO,CAAC;AAAA,QACN,OAAO,CAAC,QAAQ,QAAQ,cAAc;AAAA,MACxC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,aAAa,CAAC,KAAK,QAAQ,OAAO;AAAA,MACpC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,WAAW,CAAC;AAAA,QACV,aAAa,CAAC,KAAK,MAAM,MAAM;AAAA,MACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAK/B,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,QAAQ,QAAQ,OAAO,MAAM;AAAA,MACxC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,eAAe,CAAC;AAAA,QACd,eAAe,CAAC,QAAQ,UAAU,YAAY,aAAa,qBAAqB,gBAAgB;AAAA,MAClG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,MAAM,CAAC;AAAA,QACL,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC;AAAA,MAChC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,YAAY,CAAC;AAAA,QACX,QAAQ,CAAC,UAAU,2BAA2B,mBAAmB,iBAAiB;AAAA,MACpF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD,QAAQ,CAAC;AAAA,QACP,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC;AAAA,MAClC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQD,uBAAuB,CAAC;AAAA,QACtB,uBAAuB,CAAC,QAAQ,MAAM;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,IACA,wBAAwB;AAAA,MACtB,UAAU,CAAC,cAAc,YAAY;AAAA,MACrC,YAAY,CAAC,gBAAgB,cAAc;AAAA,MAC3C,OAAO,CAAC,WAAW,WAAW,SAAS,OAAO,OAAO,SAAS,UAAU,MAAM;AAAA,MAC9E,WAAW,CAAC,SAAS,MAAM;AAAA,MAC3B,WAAW,CAAC,OAAO,QAAQ;AAAA,MAC3B,MAAM,CAAC,SAAS,QAAQ,QAAQ;AAAA,MAChC,KAAK,CAAC,SAAS,OAAO;AAAA,MACtB,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAClD,IAAI,CAAC,MAAM,IAAI;AAAA,MACf,IAAI,CAAC,MAAM,IAAI;AAAA,MACf,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MAClD,IAAI,CAAC,MAAM,IAAI;AAAA,MACf,IAAI,CAAC,MAAM,IAAI;AAAA,MACf,MAAM,CAAC,KAAK,GAAG;AAAA,MACf,aAAa,CAAC,SAAS;AAAA,MACvB,cAAc,CAAC,eAAe,oBAAoB,cAAc,eAAe,cAAc;AAAA,MAC7F,eAAe,CAAC,YAAY;AAAA,MAC5B,oBAAoB,CAAC,YAAY;AAAA,MACjC,cAAc,CAAC,YAAY;AAAA,MAC3B,eAAe,CAAC,YAAY;AAAA,MAC5B,gBAAgB,CAAC,YAAY;AAAA,MAC7B,cAAc,CAAC,WAAW,UAAU;AAAA,MACpC,SAAS,CAAC,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,YAAY;AAAA,MACtM,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,aAAa,CAAC,cAAc,YAAY;AAAA,MACxC,kBAAkB,CAAC,oBAAoB,kBAAkB;AAAA,MACzD,YAAY,CAAC,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,YAAY;AAAA,MAC3H,cAAc,CAAC,cAAc,YAAY;AAAA,MACzC,cAAc,CAAC,cAAc,YAAY;AAAA,MACzC,gBAAgB,CAAC,kBAAkB,kBAAkB,kBAAkB,kBAAkB,kBAAkB,kBAAkB,kBAAkB,gBAAgB;AAAA,MAC/J,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,MACrD,kBAAkB,CAAC,kBAAkB,gBAAgB;AAAA,MACrD,WAAW,CAAC,eAAe,eAAe,gBAAgB;AAAA,MAC1D,kBAAkB,CAAC,aAAa,eAAe,eAAe,aAAa;AAAA,MAC3E,YAAY,CAAC,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAW;AAAA,MACnH,aAAa,CAAC,aAAa,WAAW;AAAA,MACtC,aAAa,CAAC,aAAa,WAAW;AAAA,MACtC,YAAY,CAAC,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAW;AAAA,MACnH,aAAa,CAAC,aAAa,WAAW;AAAA,MACtC,aAAa,CAAC,aAAa,WAAW;AAAA,MACtC,OAAO,CAAC,WAAW,WAAW,UAAU;AAAA,MACxC,WAAW,CAAC,OAAO;AAAA,MACnB,WAAW,CAAC,OAAO;AAAA,MACnB,YAAY,CAAC,OAAO;AAAA,IACtB;AAAA,IACA,gCAAgC;AAAA,MAC9B,aAAa,CAAC,SAAS;AAAA,IACzB;AAAA,IACA,yBAAyB,CAAC,KAAK,MAAM,SAAS,YAAY,UAAU,mBAAmB,QAAQ,gBAAgB,cAAc,UAAU,eAAe,WAAW;AAAA,EACnK;AACF;AAMA,IAAM,eAAe,CAAC,YAAY;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AACd,MAAM;AACJ,mBAAiB,YAAY,aAAa,SAAS;AACnD,mBAAiB,YAAY,UAAU,MAAM;AAC7C,mBAAiB,YAAY,8BAA8B,0BAA0B;AACrF,2BAAyB,WAAW,OAAO,SAAS,KAAK;AACzD,2BAAyB,WAAW,aAAa,SAAS,WAAW;AACrE,2BAAyB,WAAW,wBAAwB,SAAS,sBAAsB;AAC3F,2BAAyB,WAAW,gCAAgC,SAAS,8BAA8B;AAC3G,mBAAiB,YAAY,2BAA2B,SAAS,uBAAuB;AACxF,wBAAsB,WAAW,OAAO,OAAO,KAAK;AACpD,wBAAsB,WAAW,aAAa,OAAO,WAAW;AAChE,wBAAsB,WAAW,wBAAwB,OAAO,sBAAsB;AACtF,wBAAsB,WAAW,gCAAgC,OAAO,8BAA8B;AACtG,uBAAqB,YAAY,QAAQ,yBAAyB;AAClE,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,YAAY,aAAa,kBAAkB;AACnE,MAAI,kBAAkB,QAAW;AAC/B,eAAW,WAAW,IAAI;AAAA,EAC5B;AACF;AACA,IAAM,2BAA2B,CAAC,YAAY,mBAAmB;AAC/D,MAAI,gBAAgB;AAClB,eAAW,OAAO,gBAAgB;AAChC,uBAAiB,YAAY,KAAK,eAAe,GAAG,CAAC;AAAA,IACvD;AAAA,EACF;AACF;AACA,IAAM,wBAAwB,CAAC,YAAY,gBAAgB;AACzD,MAAI,aAAa;AACf,eAAW,OAAO,aAAa;AAC7B,2BAAqB,YAAY,aAAa,GAAG;AAAA,IACnD;AAAA,EACF;AACF;AACA,IAAM,uBAAuB,CAAC,YAAY,aAAa,QAAQ;AAC7D,QAAM,aAAa,YAAY,GAAG;AAClC,MAAI,eAAe,QAAW;AAC5B,eAAW,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,EAAE,OAAO,UAAU,IAAI;AAAA,EAC3E;AACF;AACA,IAAM,sBAAsB,CAAC,oBAAoB,iBAAiB,OAAO,oBAAoB,aAAa,oBAAoB,kBAAkB,iBAAiB,GAAG,YAAY,IAAI,oBAAoB,MAAM,aAAa,iBAAiB,GAAG,eAAe,GAAG,GAAG,YAAY;AAChR,IAAM,UAAuB,oBAAoB,gBAAgB;", "names": ["classGroup"]}