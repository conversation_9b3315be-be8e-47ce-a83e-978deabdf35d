{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/otpauth@9.4.0/node_modules/otpauth/dist/otpauth.esm.js"], "sourcesContent": ["//! otpauth 9.4.0 | (c) <PERSON><PERSON><PERSON> | MIT | https://github.com/hectorm/otpauth\n//! noble-hashes 1.7.1 | (c) <PERSON> | MIT | https://github.com/paulmillr/noble-hashes\n/// <reference types=\"./otpauth.d.ts\" />\n// @ts-nocheck\n/**\n * Converts an integer to an Uint8Array.\n * @param {number} num Integer.\n * @returns {Uint8Array} Uint8Array.\n */const uintDecode = num => {\n  const buf = new ArrayBuffer(8);\n  const arr = new Uint8Array(buf);\n  let acc = num;\n  for (let i = 7; i >= 0; i--) {\n    if (acc === 0) break;\n    arr[i] = acc & 255;\n    acc -= arr[i];\n    acc /= 256;\n  }\n  return arr;\n};\n\n/**\n * Internal assertion helpers.\n * @module\n */ /** Asserts something is positive integer. */\nfunction anumber(n) {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n/** Is number an Uint8Array? Copied from utils for perf. */\nfunction isBytes(a) {\n  return a instanceof Uint8Array || ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array';\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n  if (typeof h !== 'function' || typeof h.create !== 'function') throw new Error('Hash should be wrapped by utils.wrapConstructor');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */ /*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nfunction u32(arr) {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n// Cast array to view\nfunction createView(arr) {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n  return word << 32 - shift | word >>> shift;\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n  return word << shift | word >>> 32 - shift >>> 0;\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */(() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n// The byte swap operation for uint32\nfunction byteSwap(word) {\n  return word << 24 & 0xff000000 | word << 8 & 0xff0000 | word >>> 8 & 0xff00 | word >>> 24 & 0xff;\n}\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n}\n/**\n * Convert JS string to byte array.\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n  if (typeof str !== 'string') throw new Error('utf8ToBytes expected string, got ' + typeof str);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n  // Safe version that clones internal state\n  clone() {\n    return this._cloneInto();\n  }\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction wrapConstructor(hashCons) {\n  const hashC = msg => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\nclass HMAC extends Hash {\n  update(buf) {\n    aexists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out) {\n    aexists(this);\n    abytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest() {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to) {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to || (to = Object.create(Object.getPrototypeOf(this), {}));\n    const {\n      oHash,\n      iHash,\n      finished,\n      destroyed,\n      blockLen,\n      outputLen\n    } = this;\n    to = to;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n  constructor(hash, _key) {\n    super();\n    this.finished = false;\n    this.destroyed = false;\n    ahash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create();\n    if (typeof this.iHash.update !== 'function') throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create();\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    pad.fill(0);\n  }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number(value >> _32n & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n  return a & b ^ ~a & c;\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n  return a & b ^ a & c ^ b & c;\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends Hash {\n  update(data) {\n    aexists(this);\n    const {\n      view,\n      buffer,\n      blockLen\n    } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len;) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out) {\n    aexists(this);\n    aoutput(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const {\n      buffer,\n      view,\n      blockLen,\n      isLE\n    } = this;\n    let {\n      pos\n    } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest() {\n    const {\n      buffer,\n      outputLen\n    } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to) {\n    to || (to = new this.constructor());\n    to.set(...this.get());\n    const {\n      blockLen,\n      buffer,\n      length,\n      finished,\n      destroyed,\n      pos\n    } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n  constructor(blockLen, outputLen, padOffset, isLE) {\n    super();\n    this.blockLen = blockLen;\n    this.outputLen = outputLen;\n    this.padOffset = padOffset;\n    this.isLE = isLE;\n    this.finished = false;\n    this.length = 0;\n    this.pos = 0;\n    this.destroyed = false;\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n}\n\n// Initial state\nconst SHA1_IV = /* @__PURE__ */new Uint32Array([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA1_W = /* @__PURE__ */new Uint32Array(80);\nclass SHA1 extends HashMD {\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D,\n      E\n    } = this;\n    return [A, B, C, D, E];\n  }\n  set(A, B, C, D, E) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n  }\n  process(view, offset) {\n    for (let i = 0; i < 16; i++, offset += 4) SHA1_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 80; i++) SHA1_W[i] = rotl(SHA1_W[i - 3] ^ SHA1_W[i - 8] ^ SHA1_W[i - 14] ^ SHA1_W[i - 16], 1);\n    // Compression function main loop, 80 rounds\n    let {\n      A,\n      B,\n      C,\n      D,\n      E\n    } = this;\n    for (let i = 0; i < 80; i++) {\n      let F, K;\n      if (i < 20) {\n        F = Chi(B, C, D);\n        K = 0x5a827999;\n      } else if (i < 40) {\n        F = B ^ C ^ D;\n        K = 0x6ed9eba1;\n      } else if (i < 60) {\n        F = Maj(B, C, D);\n        K = 0x8f1bbcdc;\n      } else {\n        F = B ^ C ^ D;\n        K = 0xca62c1d6;\n      }\n      const T = rotl(A, 5) + F + E + K + SHA1_W[i] | 0;\n      E = D;\n      D = C;\n      C = rotl(B, 30);\n      B = A;\n      A = T;\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    E = E + this.E | 0;\n    this.set(A, B, C, D, E);\n  }\n  roundClean() {\n    SHA1_W.fill(0);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n  constructor() {\n    super(64, 20, 8, false);\n    this.A = SHA1_IV[0] | 0;\n    this.B = SHA1_IV[1] | 0;\n    this.C = SHA1_IV[2] | 0;\n    this.D = SHA1_IV[3] | 0;\n    this.E = SHA1_IV[4] | 0;\n  }\n}\n/** SHA1 (RFC 3174) legacy hash function. It was cryptographically broken. */\nconst sha1 = /* @__PURE__ */wrapConstructor(() => new SHA1());\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */ // prettier-ignore\nconst SHA256_K = /* @__PURE__ */new Uint32Array([0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */ // prettier-ignore\nconst SHA256_IV = /* @__PURE__ */new Uint32Array([0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */new Uint32Array(64);\nclass SHA256 extends HashMD {\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  set(A, B, C, D, E, F, G, H) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ W15 >>> 3;\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ W2 >>> 10;\n      SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = sigma0 + Maj(A, B, C) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = D + T1 | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = T1 + T2 | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    E = E + this.E | 0;\n    F = F + this.F | 0;\n    G = G + this.G | 0;\n    H = H + this.H | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  roundClean() {\n    SHA256_W.fill(0);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n  constructor() {\n    super(64, 32, 8, false);\n    // We cannot use array here since array allows indexing by variable\n    // which means optimizer/compiler cannot use registers.\n    this.A = SHA256_IV[0] | 0;\n    this.B = SHA256_IV[1] | 0;\n    this.C = SHA256_IV[2] | 0;\n    this.D = SHA256_IV[3] | 0;\n    this.E = SHA256_IV[4] | 0;\n    this.F = SHA256_IV[5] | 0;\n    this.G = SHA256_IV[6] | 0;\n    this.H = SHA256_IV[7] | 0;\n  }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n  constructor() {\n    super();\n    this.A = 0xc1059ed8 | 0;\n    this.B = 0x367cd507 | 0;\n    this.C = 0x3070dd17 | 0;\n    this.D = 0xf70e5939 | 0;\n    this.E = 0xffc00b31 | 0;\n    this.F = 0x68581511 | 0;\n    this.G = 0x64f98fa7 | 0;\n    this.H = 0xbefa4fa4 | 0;\n    this.outputLen = 28;\n  }\n}\n/** SHA2-256 hash function */\nconst sha256 = /* @__PURE__ */wrapConstructor(() => new SHA256());\n/** SHA2-224 hash function */\nconst sha224 = /* @__PURE__ */wrapConstructor(() => new SHA224());\n\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */BigInt(32);\nfunction fromBig(n, le = false) {\n  if (le) return {\n    h: Number(n & U32_MASK64),\n    l: Number(n >> _32n & U32_MASK64)\n  };\n  return {\n    h: Number(n >> _32n & U32_MASK64) | 0,\n    l: Number(n & U32_MASK64) | 0\n  };\n}\nfunction split(lst, le = false) {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const {\n      h,\n      l\n    } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\nconst toBig = (h, l) => BigInt(h >>> 0) << _32n | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => h >>> s | l << 32 - s;\nconst rotrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => h << 64 - s | l >>> s - 32;\nconst rotrBL = (h, l, s) => h >>> s - 32 | l << 64 - s;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => h << s | l >>> 32 - s;\nconst rotlSL = (h, l, s) => l << s | h >>> 32 - s;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => l << s - 32 | h >>> 64 - s;\nconst rotlBL = (h, l, s) => h << s - 32 | l >>> 64 - s;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return {\n    h: Ah + Bh + (l / 2 ** 32 | 0) | 0,\n    l: l | 0\n  };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;\n// prettier-ignore\nconst u64 = {\n  fromBig,\n  split,\n  toBig,\n  shrSH,\n  shrSL,\n  rotrSH,\n  rotrSL,\n  rotrBH,\n  rotrBL,\n  rotr32H,\n  rotr32L,\n  rotlSH,\n  rotlSL,\n  rotlBH,\n  rotlBL,\n  add,\n  add3L,\n  add3H,\n  add4L,\n  add4H,\n  add5H,\n  add5L\n};\n\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */(() => u64.split(['0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc', '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118', '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2', '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694', '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65', '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5', '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4', '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70', '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df', '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b', '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30', '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8', '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8', '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3', '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec', '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b', '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178', '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b', '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c', '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'].map(n => BigInt(n))))();\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */new Uint32Array(80);\nclass SHA512 extends HashMD {\n  // prettier-ignore\n  get() {\n    const {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32(offset += 4);\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = Eh & Fh ^ ~Eh & Gh;\n      const CHIl = El & Fl ^ ~El & Gl;\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = Ah & Bh ^ Ah & Ch ^ Bh & Ch;\n      const MAJl = Al & Bl ^ Al & Cl ^ Bl & Cl;\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({\n        h: Eh,\n        l: El\n      } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({\n      h: Ah,\n      l: Al\n    } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({\n      h: Bh,\n      l: Bl\n    } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({\n      h: Ch,\n      l: Cl\n    } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({\n      h: Dh,\n      l: Dl\n    } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({\n      h: Eh,\n      l: El\n    } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({\n      h: Fh,\n      l: Fl\n    } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({\n      h: Gh,\n      l: Gl\n    } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({\n      h: Hh,\n      l: Hl\n    } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  roundClean() {\n    SHA512_W_H.fill(0);\n    SHA512_W_L.fill(0);\n  }\n  destroy() {\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n  constructor() {\n    super(128, 64, 16, false);\n    // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n    // Also looks cleaner and easier to verify with spec.\n    // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0x6a09e667 | 0;\n    this.Al = 0xf3bcc908 | 0;\n    this.Bh = 0xbb67ae85 | 0;\n    this.Bl = 0x84caa73b | 0;\n    this.Ch = 0x3c6ef372 | 0;\n    this.Cl = 0xfe94f82b | 0;\n    this.Dh = 0xa54ff53a | 0;\n    this.Dl = 0x5f1d36f1 | 0;\n    this.Eh = 0x510e527f | 0;\n    this.El = 0xade682d1 | 0;\n    this.Fh = 0x9b05688c | 0;\n    this.Fl = 0x2b3e6c1f | 0;\n    this.Gh = 0x1f83d9ab | 0;\n    this.Gl = 0xfb41bd6b | 0;\n    this.Hh = 0x5be0cd19 | 0;\n    this.Hl = 0x137e2179 | 0;\n  }\n}\nclass SHA384 extends SHA512 {\n  constructor() {\n    super();\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0xcbbb9d5d | 0;\n    this.Al = 0xc1059ed8 | 0;\n    this.Bh = 0x629a292a | 0;\n    this.Bl = 0x367cd507 | 0;\n    this.Ch = 0x9159015a | 0;\n    this.Cl = 0x3070dd17 | 0;\n    this.Dh = 0x152fecd8 | 0;\n    this.Dl = 0xf70e5939 | 0;\n    this.Eh = 0x67332667 | 0;\n    this.El = 0xffc00b31 | 0;\n    this.Fh = 0x8eb44a87 | 0;\n    this.Fl = 0x68581511 | 0;\n    this.Gh = 0xdb0c2e0d | 0;\n    this.Gl = 0x64f98fa7 | 0;\n    this.Hh = 0x47b5481d | 0;\n    this.Hl = 0xbefa4fa4 | 0;\n    this.outputLen = 48;\n  }\n}\n/** SHA2-512 hash function. */\nconst sha512 = /* @__PURE__ */wrapConstructor(() => new SHA512());\n/** SHA2-384 hash function. */\nconst sha384 = /* @__PURE__ */wrapConstructor(() => new SHA384());\n\n// Various per round constants calculations\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nconst _0n = /* @__PURE__ */BigInt(0);\nconst _1n = /* @__PURE__ */BigInt(1);\nconst _2n = /* @__PURE__ */BigInt(2);\nconst _7n = /* @__PURE__ */BigInt(7);\nconst _256n = /* @__PURE__ */BigInt(256);\nconst _0x71n = /* @__PURE__ */BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((round + 1) * (round + 2) / 2 % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = (R << _1n ^ (R >> _7n) * _0x71n) % _256n;\n    if (R & _2n) t ^= _1n << (_1n << /* @__PURE__ */BigInt(j)) - _1n;\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */split(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s);\nconst rotlL = (h, l, s) => s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s);\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\n/** Keccak sponge function. */\nclass Keccak extends Hash {\n  keccak() {\n    if (!isLE) byteSwap32(this.state32);\n    keccakP(this.state32, this.rounds);\n    if (!isLE) byteSwap32(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data) {\n    aexists(this);\n    const {\n      blockLen,\n      state\n    } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len;) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  finish() {\n    if (this.finished) return;\n    this.finished = true;\n    const {\n      state,\n      suffix,\n      pos,\n      blockLen\n    } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  writeInto(out) {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const {\n      blockLen\n    } = this;\n    for (let pos = 0, len = out.length; pos < len;) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out) {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes) {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out) {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest() {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy() {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to) {\n    const {\n      blockLen,\n      suffix,\n      outputLen,\n      rounds,\n      enableXOF\n    } = this;\n    to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n    super();\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    this.pos = 0;\n    this.posOut = 0;\n    this.finished = false;\n    this.destroyed = false;\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (0 >= this.blockLen || this.blockLen >= 200) throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n}\nconst gen = (suffix, blockLen, outputLen) => wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nconst sha3_224 = /* @__PURE__ */gen(0x06, 144, 224 / 8);\n/** SHA3-256 hash function. Different from keccak-256. */\nconst sha3_256 = /* @__PURE__ */gen(0x06, 136, 256 / 8);\n/** SHA3-384 hash function. */\nconst sha3_384 = /* @__PURE__ */gen(0x06, 104, 384 / 8);\n/** SHA3-512 hash function. */\nconst sha3_512 = /* @__PURE__ */gen(0x06, 72, 512 / 8);\n\n/**\n * \"globalThis\" ponyfill.\n * @see [A horrifying globalThis polyfill in universal JavaScript](https://mathiasbynens.be/notes/globalthis)\n * @type {Object.<string, *>}\n */\nconst globalScope = (() => {\n  if (typeof globalThis === \"object\") return globalThis;else {\n    Object.defineProperty(Object.prototype, \"__GLOBALTHIS__\", {\n      get() {\n        return this;\n      },\n      configurable: true\n    });\n    try {\n      // @ts-expect-error\n      // eslint-disable-next-line no-undef\n      if (typeof __GLOBALTHIS__ !== \"undefined\") return __GLOBALTHIS__;\n    } finally {\n      // @ts-expect-error\n      delete Object.prototype.__GLOBALTHIS__;\n    }\n  }\n  // Still unable to determine \"globalThis\", fall back to a naive method.\n  if (typeof self !== \"undefined\") return self;else if (typeof window !== \"undefined\") return window;else if (typeof global !== \"undefined\") return global;\n  return undefined;\n})();\n\n/**\n * @noble/hashes hash functions.\n * @type {Object.<string, sha1|sha224|sha256|sha384|sha512|sha3_224|sha3_256|sha3_384|sha3_512>}\n */\nconst nobleHashes = {\n  SHA1: sha1,\n  SHA224: sha224,\n  SHA256: sha256,\n  SHA384: sha384,\n  SHA512: sha512,\n  \"SHA3-224\": sha3_224,\n  \"SHA3-256\": sha3_256,\n  \"SHA3-384\": sha3_384,\n  \"SHA3-512\": sha3_512\n};\n/**\n * Canonicalizes a hash algorithm name.\n * @param {string} algorithm Hash algorithm name.\n * @returns {\"SHA1\"|\"SHA224\"|\"SHA256\"|\"SHA384\"|\"SHA512\"|\"SHA3-224\"|\"SHA3-256\"|\"SHA3-384\"|\"SHA3-512\"} Canonicalized hash algorithm name.\n */\nconst canonicalizeAlgorithm = algorithm => {\n  switch (true) {\n    case /^(?:SHA-?1|SSL3-SHA1)$/i.test(algorithm):\n      return \"SHA1\";\n    case /^SHA(?:2?-)?224$/i.test(algorithm):\n      return \"SHA224\";\n    case /^SHA(?:2?-)?256$/i.test(algorithm):\n      return \"SHA256\";\n    case /^SHA(?:2?-)?384$/i.test(algorithm):\n      return \"SHA384\";\n    case /^SHA(?:2?-)?512$/i.test(algorithm):\n      return \"SHA512\";\n    case /^SHA3-224$/i.test(algorithm):\n      return \"SHA3-224\";\n    case /^SHA3-256$/i.test(algorithm):\n      return \"SHA3-256\";\n    case /^SHA3-384$/i.test(algorithm):\n      return \"SHA3-384\";\n    case /^SHA3-512$/i.test(algorithm):\n      return \"SHA3-512\";\n    default:\n      throw new TypeError(`Unknown hash algorithm: ${algorithm}`);\n  }\n};\n/**\n * Calculates an HMAC digest.\n * @param {string} algorithm Algorithm.\n * @param {Uint8Array} key Key.\n * @param {Uint8Array} message Message.\n * @returns {Uint8Array} Digest.\n */\nconst hmacDigest = (algorithm, key, message) => {\n  if (hmac) {\n    const hash = nobleHashes[algorithm] ?? nobleHashes[canonicalizeAlgorithm(algorithm)];\n    return hmac(hash, key, message);\n  } else {\n    throw new Error(\"Missing HMAC function\");\n  }\n};\n\n/**\n * RFC 4648 base32 alphabet without pad.\n * @type {string}\n */\nconst ALPHABET = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\";\n/**\n * Converts a base32 string to an Uint8Array (RFC 4648).\n * @see [LinusU/base32-decode](https://github.com/LinusU/base32-decode)\n * @param {string} str Base32 string.\n * @returns {Uint8Array} Uint8Array.\n */\nconst base32Decode = str => {\n  // Remove spaces (although they are not allowed by the spec, some issuers add them for readability).\n  str = str.replace(/ /g, \"\");\n  // Canonicalize to all upper case and remove padding if it exists.\n  let end = str.length;\n  while (str[end - 1] === \"=\") --end;\n  str = (end < str.length ? str.substring(0, end) : str).toUpperCase();\n  const buf = new ArrayBuffer(str.length * 5 / 8 | 0);\n  const arr = new Uint8Array(buf);\n  let bits = 0;\n  let value = 0;\n  let index = 0;\n  for (let i = 0; i < str.length; i++) {\n    const idx = ALPHABET.indexOf(str[i]);\n    if (idx === -1) throw new TypeError(`Invalid character found: ${str[i]}`);\n    value = value << 5 | idx;\n    bits += 5;\n    if (bits >= 8) {\n      bits -= 8;\n      arr[index++] = value >>> bits;\n    }\n  }\n  return arr;\n};\n/**\n * Converts an Uint8Array to a base32 string (RFC 4648).\n * @see [LinusU/base32-encode](https://github.com/LinusU/base32-encode)\n * @param {Uint8Array} arr Uint8Array.\n * @returns {string} Base32 string.\n */\nconst base32Encode = arr => {\n  let bits = 0;\n  let value = 0;\n  let str = \"\";\n  for (let i = 0; i < arr.length; i++) {\n    value = value << 8 | arr[i];\n    bits += 8;\n    while (bits >= 5) {\n      str += ALPHABET[value >>> bits - 5 & 31];\n      bits -= 5;\n    }\n  }\n  if (bits > 0) {\n    str += ALPHABET[value << 5 - bits & 31];\n  }\n  return str;\n};\n\n/**\n * Converts a hexadecimal string to an Uint8Array.\n * @param {string} str Hexadecimal string.\n * @returns {Uint8Array} Uint8Array.\n */\nconst hexDecode = str => {\n  // Remove spaces (although they are not allowed by the spec, some issuers add them for readability).\n  str = str.replace(/ /g, \"\");\n  const buf = new ArrayBuffer(str.length / 2);\n  const arr = new Uint8Array(buf);\n  for (let i = 0; i < str.length; i += 2) {\n    arr[i / 2] = parseInt(str.substring(i, i + 2), 16);\n  }\n  return arr;\n};\n/**\n * Converts an Uint8Array to a hexadecimal string.\n * @param {Uint8Array} arr Uint8Array.\n * @returns {string} Hexadecimal string.\n */\nconst hexEncode = arr => {\n  let str = \"\";\n  for (let i = 0; i < arr.length; i++) {\n    const hex = arr[i].toString(16);\n    if (hex.length === 1) str += \"0\";\n    str += hex;\n  }\n  return str.toUpperCase();\n};\n\n/**\n * Converts a Latin-1 string to an Uint8Array.\n * @param {string} str Latin-1 string.\n * @returns {Uint8Array} Uint8Array.\n */\nconst latin1Decode = str => {\n  const buf = new ArrayBuffer(str.length);\n  const arr = new Uint8Array(buf);\n  for (let i = 0; i < str.length; i++) {\n    arr[i] = str.charCodeAt(i) & 0xff;\n  }\n  return arr;\n};\n/**\n * Converts an Uint8Array to a Latin-1 string.\n * @param {Uint8Array} arr Uint8Array.\n * @returns {string} Latin-1 string.\n */\nconst latin1Encode = arr => {\n  let str = \"\";\n  for (let i = 0; i < arr.length; i++) {\n    str += String.fromCharCode(arr[i]);\n  }\n  return str;\n};\n\n/**\n * TextEncoder instance.\n * @type {TextEncoder|null}\n */\nconst ENCODER = globalScope.TextEncoder ? new globalScope.TextEncoder() : null;\n/**\n * TextDecoder instance.\n * @type {TextDecoder|null}\n */\nconst DECODER = globalScope.TextDecoder ? new globalScope.TextDecoder() : null;\n/**\n * Converts an UTF-8 string to an Uint8Array.\n * @param {string} str String.\n * @returns {Uint8Array} Uint8Array.\n */\nconst utf8Decode = str => {\n  if (!ENCODER) {\n    throw new Error(\"Encoding API not available\");\n  }\n  return ENCODER.encode(str);\n};\n/**\n * Converts an Uint8Array to an UTF-8 string.\n * @param {Uint8Array} arr Uint8Array.\n * @returns {string} String.\n */\nconst utf8Encode = arr => {\n  if (!DECODER) {\n    throw new Error(\"Encoding API not available\");\n  }\n  return DECODER.decode(arr);\n};\n\n/**\n * Returns random bytes.\n * @param {number} size Size.\n * @returns {Uint8Array} Random bytes.\n */\nconst randomBytes = size => {\n  if (globalScope.crypto?.getRandomValues) {\n    return globalScope.crypto.getRandomValues(new Uint8Array(size));\n  } else {\n    throw new Error(\"Cryptography API not available\");\n  }\n};\n\n/**\n * OTP secret key.\n */\nclass Secret {\n  /**\n  * Converts a Latin-1 string to a Secret object.\n  * @param {string} str Latin-1 string.\n  * @returns {Secret} Secret object.\n  */\n  static fromLatin1(str) {\n    return new Secret({\n      buffer: latin1Decode(str).buffer\n    });\n  }\n  /**\n  * Converts an UTF-8 string to a Secret object.\n  * @param {string} str UTF-8 string.\n  * @returns {Secret} Secret object.\n  */\n  static fromUTF8(str) {\n    return new Secret({\n      buffer: utf8Decode(str).buffer\n    });\n  }\n  /**\n  * Converts a base32 string to a Secret object.\n  * @param {string} str Base32 string.\n  * @returns {Secret} Secret object.\n  */\n  static fromBase32(str) {\n    return new Secret({\n      buffer: base32Decode(str).buffer\n    });\n  }\n  /**\n  * Converts a hexadecimal string to a Secret object.\n  * @param {string} str Hexadecimal string.\n  * @returns {Secret} Secret object.\n  */\n  static fromHex(str) {\n    return new Secret({\n      buffer: hexDecode(str).buffer\n    });\n  }\n  /**\n  * Secret key buffer.\n  * @deprecated For backward compatibility, the \"bytes\" property should be used instead.\n  * @type {ArrayBufferLike}\n  */\n  get buffer() {\n    return this.bytes.buffer;\n  }\n  /**\n  * Latin-1 string representation of secret key.\n  * @type {string}\n  */\n  get latin1() {\n    Object.defineProperty(this, \"latin1\", {\n      enumerable: true,\n      writable: false,\n      configurable: false,\n      value: latin1Encode(this.bytes)\n    });\n    return this.latin1;\n  }\n  /**\n  * UTF-8 string representation of secret key.\n  * @type {string}\n  */\n  get utf8() {\n    Object.defineProperty(this, \"utf8\", {\n      enumerable: true,\n      writable: false,\n      configurable: false,\n      value: utf8Encode(this.bytes)\n    });\n    return this.utf8;\n  }\n  /**\n  * Base32 string representation of secret key.\n  * @type {string}\n  */\n  get base32() {\n    Object.defineProperty(this, \"base32\", {\n      enumerable: true,\n      writable: false,\n      configurable: false,\n      value: base32Encode(this.bytes)\n    });\n    return this.base32;\n  }\n  /**\n  * Hexadecimal string representation of secret key.\n  * @type {string}\n  */\n  get hex() {\n    Object.defineProperty(this, \"hex\", {\n      enumerable: true,\n      writable: false,\n      configurable: false,\n      value: hexEncode(this.bytes)\n    });\n    return this.hex;\n  }\n  /**\n  * Creates a secret key object.\n  * @param {Object} [config] Configuration options.\n  * @param {ArrayBufferLike} [config.buffer] Secret key buffer.\n  * @param {number} [config.size=20] Number of random bytes to generate, ignored if 'buffer' is provided.\n  */\n  constructor({\n    buffer,\n    size = 20\n  } = {}) {\n    /**\n    * Secret key.\n    * @type {Uint8Array}\n    * @readonly\n    */\n    this.bytes = typeof buffer === \"undefined\" ? randomBytes(size) : new Uint8Array(buffer);\n    // Prevent the \"bytes\" property from being modified.\n    Object.defineProperty(this, \"bytes\", {\n      enumerable: true,\n      writable: false,\n      configurable: false,\n      value: this.bytes\n    });\n  }\n}\n\n/**\n * Returns true if a is equal to b, without leaking timing information that would allow an attacker to guess one of the values.\n * @param {string} a String a.\n * @param {string} b String b.\n * @returns {boolean} Equality result.\n */\nconst timingSafeEqual = (a, b) => {\n  {\n    if (a.length !== b.length) {\n      throw new TypeError(\"Input strings must have the same length\");\n    }\n    let i = -1;\n    let out = 0;\n    while (++i < a.length) {\n      out |= a.charCodeAt(i) ^ b.charCodeAt(i);\n    }\n    return out === 0;\n  }\n};\n\n/**\n * HOTP: An HMAC-based One-time Password Algorithm.\n * @see [RFC 4226](https://datatracker.ietf.org/doc/html/rfc4226)\n */\nclass HOTP {\n  /**\n  * Default configuration.\n  * @type {{\n  *   issuer: string,\n  *   label: string,\n  *   issuerInLabel: boolean,\n  *   algorithm: string,\n  *   digits: number,\n  *   counter: number\n  *   window: number\n  * }}\n  */\n  static get defaults() {\n    return {\n      issuer: \"\",\n      label: \"OTPAuth\",\n      issuerInLabel: true,\n      algorithm: \"SHA1\",\n      digits: 6,\n      counter: 0,\n      window: 1\n    };\n  }\n  /**\n  * Generates an HOTP token.\n  * @param {Object} config Configuration options.\n  * @param {Secret} config.secret Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.counter=0] Counter value.\n  * @returns {string} Token.\n  */\n  static generate({\n    secret,\n    algorithm = HOTP.defaults.algorithm,\n    digits = HOTP.defaults.digits,\n    counter = HOTP.defaults.counter\n  }) {\n    const digest = hmacDigest(algorithm, secret.bytes, uintDecode(counter));\n    const offset = digest[digest.byteLength - 1] & 15;\n    const otp = ((digest[offset] & 127) << 24 | (digest[offset + 1] & 255) << 16 | (digest[offset + 2] & 255) << 8 | digest[offset + 3] & 255) % 10 ** digits;\n    return otp.toString().padStart(digits, \"0\");\n  }\n  /**\n  * Generates an HOTP token.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.counter=this.counter++] Counter value.\n  * @returns {string} Token.\n  */\n  generate({\n    counter = this.counter++\n  } = {}) {\n    return HOTP.generate({\n      secret: this.secret,\n      algorithm: this.algorithm,\n      digits: this.digits,\n      counter\n    });\n  }\n  /**\n  * Validates an HOTP token.\n  * @param {Object} config Configuration options.\n  * @param {string} config.token Token value.\n  * @param {Secret} config.secret Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.counter=0] Counter value.\n  * @param {number} [config.window=1] Window of counter values to test.\n  * @returns {number|null} Token delta or null if it is not found in the search window, in which case it should be considered invalid.\n  */\n  static validate({\n    token,\n    secret,\n    algorithm,\n    digits = HOTP.defaults.digits,\n    counter = HOTP.defaults.counter,\n    window = HOTP.defaults.window\n  }) {\n    // Return early if the token length does not match the digit number.\n    if (token.length !== digits) return null;\n    let delta = null;\n    const check = (/** @type {number} */i) => {\n      const generatedToken = HOTP.generate({\n        secret,\n        algorithm,\n        digits,\n        counter: i\n      });\n      if (timingSafeEqual(token, generatedToken)) {\n        delta = i - counter;\n      }\n    };\n    check(counter);\n    for (let i = 1; i <= window && delta === null; ++i) {\n      check(counter - i);\n      if (delta !== null) break;\n      check(counter + i);\n      if (delta !== null) break;\n    }\n    return delta;\n  }\n  /**\n  * Validates an HOTP token.\n  * @param {Object} config Configuration options.\n  * @param {string} config.token Token value.\n  * @param {number} [config.counter=this.counter] Counter value.\n  * @param {number} [config.window=1] Window of counter values to test.\n  * @returns {number|null} Token delta or null if it is not found in the search window, in which case it should be considered invalid.\n  */\n  validate({\n    token,\n    counter = this.counter,\n    window\n  }) {\n    return HOTP.validate({\n      token,\n      secret: this.secret,\n      algorithm: this.algorithm,\n      digits: this.digits,\n      counter,\n      window\n    });\n  }\n  /**\n  * Returns a Google Authenticator key URI.\n  * @returns {string} URI.\n  */\n  toString() {\n    const e = encodeURIComponent;\n    return \"otpauth://hotp/\" + `${this.issuer.length > 0 ? this.issuerInLabel ? `${e(this.issuer)}:${e(this.label)}?issuer=${e(this.issuer)}&` : `${e(this.label)}?issuer=${e(this.issuer)}&` : `${e(this.label)}?`}` + `secret=${e(this.secret.base32)}&` + `algorithm=${e(this.algorithm)}&` + `digits=${e(this.digits)}&` + `counter=${e(this.counter)}`;\n  }\n  /**\n  * Creates an HOTP object.\n  * @param {Object} [config] Configuration options.\n  * @param {string} [config.issuer=''] Account provider.\n  * @param {string} [config.label='OTPAuth'] Account label.\n  * @param {boolean} [config.issuerInLabel=true] Include issuer prefix in label.\n  * @param {Secret|string} [config.secret=Secret] Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.counter=0] Initial counter value.\n  */\n  constructor({\n    issuer = HOTP.defaults.issuer,\n    label = HOTP.defaults.label,\n    issuerInLabel = HOTP.defaults.issuerInLabel,\n    secret = new Secret(),\n    algorithm = HOTP.defaults.algorithm,\n    digits = HOTP.defaults.digits,\n    counter = HOTP.defaults.counter\n  } = {}) {\n    /**\n    * Account provider.\n    * @type {string}\n    */\n    this.issuer = issuer;\n    /**\n    * Account label.\n    * @type {string}\n    */\n    this.label = label;\n    /**\n    * Include issuer prefix in label.\n    * @type {boolean}\n    */\n    this.issuerInLabel = issuerInLabel;\n    /**\n    * Secret key.\n    * @type {Secret}\n    */\n    this.secret = typeof secret === \"string\" ? Secret.fromBase32(secret) : secret;\n    /**\n    * HMAC hashing algorithm.\n    * @type {string}\n    */\n    this.algorithm = canonicalizeAlgorithm(algorithm);\n    /**\n    * Token length.\n    * @type {number}\n    */\n    this.digits = digits;\n    /**\n    * Initial counter value.\n    * @type {number}\n    */\n    this.counter = counter;\n  }\n}\n\n/**\n * TOTP: Time-Based One-Time Password Algorithm.\n * @see [RFC 6238](https://datatracker.ietf.org/doc/html/rfc6238)\n */\nclass TOTP {\n  /**\n  * Default configuration.\n  * @type {{\n  *   issuer: string,\n  *   label: string,\n  *   issuerInLabel: boolean,\n  *   algorithm: string,\n  *   digits: number,\n  *   period: number\n  *   window: number\n  * }}\n  */\n  static get defaults() {\n    return {\n      issuer: \"\",\n      label: \"OTPAuth\",\n      issuerInLabel: true,\n      algorithm: \"SHA1\",\n      digits: 6,\n      period: 30,\n      window: 1\n    };\n  }\n  /**\n  * Calculates the counter. i.e. the number of periods since timestamp 0.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.period=30] Token time-step duration.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {number} Counter.\n  */\n  static counter({\n    period = TOTP.defaults.period,\n    timestamp = Date.now()\n  } = {}) {\n    return Math.floor(timestamp / 1000 / period);\n  }\n  /**\n  * Calculates the counter. i.e. the number of periods since timestamp 0.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {number} Counter.\n  */\n  counter({\n    timestamp = Date.now()\n  } = {}) {\n    return TOTP.counter({\n      period: this.period,\n      timestamp\n    });\n  }\n  /**\n  * Calculates the remaining time in milliseconds until the next token is generated.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.period=30] Token time-step duration.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {number} counter.\n  */\n  static remaining({\n    period = TOTP.defaults.period,\n    timestamp = Date.now()\n  } = {}) {\n    return period * 1000 - timestamp % (period * 1000);\n  }\n  /**\n  * Calculates the remaining time in milliseconds until the next token is generated.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {number} counter.\n  */\n  remaining({\n    timestamp = Date.now()\n  } = {}) {\n    return TOTP.remaining({\n      period: this.period,\n      timestamp\n    });\n  }\n  /**\n  * Generates a TOTP token.\n  * @param {Object} config Configuration options.\n  * @param {Secret} config.secret Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.period=30] Token time-step duration.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {string} Token.\n  */\n  static generate({\n    secret,\n    algorithm,\n    digits,\n    period = TOTP.defaults.period,\n    timestamp = Date.now()\n  }) {\n    return HOTP.generate({\n      secret,\n      algorithm,\n      digits,\n      counter: TOTP.counter({\n        period,\n        timestamp\n      })\n    });\n  }\n  /**\n  * Generates a TOTP token.\n  * @param {Object} [config] Configuration options.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @returns {string} Token.\n  */\n  generate({\n    timestamp = Date.now()\n  } = {}) {\n    return TOTP.generate({\n      secret: this.secret,\n      algorithm: this.algorithm,\n      digits: this.digits,\n      period: this.period,\n      timestamp\n    });\n  }\n  /**\n  * Validates a TOTP token.\n  * @param {Object} config Configuration options.\n  * @param {string} config.token Token value.\n  * @param {Secret} config.secret Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.period=30] Token time-step duration.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @param {number} [config.window=1] Window of counter values to test.\n  * @returns {number|null} Token delta or null if it is not found in the search window, in which case it should be considered invalid.\n  */\n  static validate({\n    token,\n    secret,\n    algorithm,\n    digits,\n    period = TOTP.defaults.period,\n    timestamp = Date.now(),\n    window\n  }) {\n    return HOTP.validate({\n      token,\n      secret,\n      algorithm,\n      digits,\n      counter: TOTP.counter({\n        period,\n        timestamp\n      }),\n      window\n    });\n  }\n  /**\n  * Validates a TOTP token.\n  * @param {Object} config Configuration options.\n  * @param {string} config.token Token value.\n  * @param {number} [config.timestamp=Date.now] Timestamp value in milliseconds.\n  * @param {number} [config.window=1] Window of counter values to test.\n  * @returns {number|null} Token delta or null if it is not found in the search window, in which case it should be considered invalid.\n  */\n  validate({\n    token,\n    timestamp,\n    window\n  }) {\n    return TOTP.validate({\n      token,\n      secret: this.secret,\n      algorithm: this.algorithm,\n      digits: this.digits,\n      period: this.period,\n      timestamp,\n      window\n    });\n  }\n  /**\n  * Returns a Google Authenticator key URI.\n  * @returns {string} URI.\n  */\n  toString() {\n    const e = encodeURIComponent;\n    return \"otpauth://totp/\" + `${this.issuer.length > 0 ? this.issuerInLabel ? `${e(this.issuer)}:${e(this.label)}?issuer=${e(this.issuer)}&` : `${e(this.label)}?issuer=${e(this.issuer)}&` : `${e(this.label)}?`}` + `secret=${e(this.secret.base32)}&` + `algorithm=${e(this.algorithm)}&` + `digits=${e(this.digits)}&` + `period=${e(this.period)}`;\n  }\n  /**\n  * Creates a TOTP object.\n  * @param {Object} [config] Configuration options.\n  * @param {string} [config.issuer=''] Account provider.\n  * @param {string} [config.label='OTPAuth'] Account label.\n  * @param {boolean} [config.issuerInLabel=true] Include issuer prefix in label.\n  * @param {Secret|string} [config.secret=Secret] Secret key.\n  * @param {string} [config.algorithm='SHA1'] HMAC hashing algorithm.\n  * @param {number} [config.digits=6] Token length.\n  * @param {number} [config.period=30] Token time-step duration.\n  */\n  constructor({\n    issuer = TOTP.defaults.issuer,\n    label = TOTP.defaults.label,\n    issuerInLabel = TOTP.defaults.issuerInLabel,\n    secret = new Secret(),\n    algorithm = TOTP.defaults.algorithm,\n    digits = TOTP.defaults.digits,\n    period = TOTP.defaults.period\n  } = {}) {\n    /**\n    * Account provider.\n    * @type {string}\n    */\n    this.issuer = issuer;\n    /**\n    * Account label.\n    * @type {string}\n    */\n    this.label = label;\n    /**\n    * Include issuer prefix in label.\n    * @type {boolean}\n    */\n    this.issuerInLabel = issuerInLabel;\n    /**\n    * Secret key.\n    * @type {Secret}\n    */\n    this.secret = typeof secret === \"string\" ? Secret.fromBase32(secret) : secret;\n    /**\n    * HMAC hashing algorithm.\n    * @type {string}\n    */\n    this.algorithm = canonicalizeAlgorithm(algorithm);\n    /**\n    * Token length.\n    * @type {number}\n    */\n    this.digits = digits;\n    /**\n    * Token time-step duration.\n    * @type {number}\n    */\n    this.period = period;\n  }\n}\n\n/**\n * Key URI regex (otpauth://TYPE/[ISSUER:]LABEL?PARAMETERS).\n * @type {RegExp}\n */\nconst OTPURI_REGEX = /^otpauth:\\/\\/([ht]otp)\\/(.+)\\?([A-Z0-9.~_-]+=[^?&]*(?:&[A-Z0-9.~_-]+=[^?&]*)*)$/i;\n/**\n * RFC 4648 base32 alphabet with pad.\n * @type {RegExp}\n */\nconst SECRET_REGEX = /^[2-7A-Z]+=*$/i;\n/**\n * Regex for supported algorithms.\n * @type {RegExp}\n */\nconst ALGORITHM_REGEX = /^SHA(?:1|224|256|384|512|3-224|3-256|3-384|3-512)$/i;\n/**\n * Integer regex.\n * @type {RegExp}\n */\nconst INTEGER_REGEX = /^[+-]?\\d+$/;\n/**\n * Positive integer regex.\n * @type {RegExp}\n */\nconst POSITIVE_INTEGER_REGEX = /^\\+?[1-9]\\d*$/;\n/**\n * HOTP/TOTP object/string conversion.\n * @see [Key URI Format](https://github.com/google/google-authenticator/wiki/Key-Uri-Format)\n */\nclass URI {\n  /**\n  * Parses a Google Authenticator key URI and returns an HOTP/TOTP object.\n  * @param {string} uri Google Authenticator Key URI.\n  * @returns {HOTP|TOTP} HOTP/TOTP object.\n  */\n  static parse(uri) {\n    let uriGroups;\n    try {\n      uriGroups = uri.match(OTPURI_REGEX);\n      // eslint-disable-next-line no-unused-vars\n    } catch (_) {\n      /* Handled below */}\n    if (!Array.isArray(uriGroups)) {\n      throw new URIError(\"Invalid URI format\");\n    }\n    // Extract URI groups.\n    const uriType = uriGroups[1].toLowerCase();\n    const uriLabel = uriGroups[2].split(/(?::|%3A) *(.+)/i, 2).map(decodeURIComponent);\n    /** @type {Object.<string, string>} */\n    const uriParams = uriGroups[3].split(\"&\").reduce((acc, cur) => {\n      const pairArr = cur.split(/=(.*)/, 2).map(decodeURIComponent);\n      const pairKey = pairArr[0].toLowerCase();\n      const pairVal = pairArr[1];\n      /** @type {Object.<string, string>} */\n      const pairAcc = acc;\n      pairAcc[pairKey] = pairVal;\n      return pairAcc;\n    }, {});\n    // 'OTP' will be instantiated with 'config' argument.\n    let OTP;\n    const config = {};\n    if (uriType === \"hotp\") {\n      OTP = HOTP;\n      // Counter: required\n      if (typeof uriParams.counter !== \"undefined\" && INTEGER_REGEX.test(uriParams.counter)) {\n        config.counter = parseInt(uriParams.counter, 10);\n      } else {\n        throw new TypeError(\"Missing or invalid 'counter' parameter\");\n      }\n    } else if (uriType === \"totp\") {\n      OTP = TOTP;\n      // Period: optional\n      if (typeof uriParams.period !== \"undefined\") {\n        if (POSITIVE_INTEGER_REGEX.test(uriParams.period)) {\n          config.period = parseInt(uriParams.period, 10);\n        } else {\n          throw new TypeError(\"Invalid 'period' parameter\");\n        }\n      }\n    } else {\n      throw new TypeError(\"Unknown OTP type\");\n    }\n    // Label: required\n    // Issuer: optional\n    if (typeof uriParams.issuer !== \"undefined\") {\n      config.issuer = uriParams.issuer;\n    }\n    if (uriLabel.length === 2) {\n      config.label = uriLabel[1];\n      if (typeof config.issuer === \"undefined\" || config.issuer === \"\") {\n        config.issuer = uriLabel[0];\n      } else if (uriLabel[0] === \"\") {\n        config.issuerInLabel = false;\n      }\n    } else {\n      config.label = uriLabel[0];\n      if (typeof config.issuer !== \"undefined\" && config.issuer !== \"\") {\n        config.issuerInLabel = false;\n      }\n    }\n    // Secret: required\n    if (typeof uriParams.secret !== \"undefined\" && SECRET_REGEX.test(uriParams.secret)) {\n      config.secret = uriParams.secret;\n    } else {\n      throw new TypeError(\"Missing or invalid 'secret' parameter\");\n    }\n    // Algorithm: optional\n    if (typeof uriParams.algorithm !== \"undefined\") {\n      if (ALGORITHM_REGEX.test(uriParams.algorithm)) {\n        config.algorithm = uriParams.algorithm;\n      } else {\n        throw new TypeError(\"Invalid 'algorithm' parameter\");\n      }\n    }\n    // Digits: optional\n    if (typeof uriParams.digits !== \"undefined\") {\n      if (POSITIVE_INTEGER_REGEX.test(uriParams.digits)) {\n        config.digits = parseInt(uriParams.digits, 10);\n      } else {\n        throw new TypeError(\"Invalid 'digits' parameter\");\n      }\n    }\n    return new OTP(config);\n  }\n  /**\n  * Converts an HOTP/TOTP object to a Google Authenticator key URI.\n  * @param {HOTP|TOTP} otp HOTP/TOTP object.\n  * @returns {string} Google Authenticator Key URI.\n  */\n  static stringify(otp) {\n    if (otp instanceof HOTP || otp instanceof TOTP) {\n      return otp.toString();\n    }\n    throw new TypeError(\"Invalid 'HOTP/TOTP' object\");\n  }\n}\n\n/**\n * Library version.\n * @type {string}\n */\nconst version = \"9.4.0\";\nexport { HOTP, Secret, TOTP, URI, version };"], "mappings": ";;;AAQG,IAAM,aAAa,SAAO;AAC3B,QAAM,MAAM,IAAI,YAAY,CAAC;AAC7B,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,QAAI,QAAQ,EAAG;AACf,QAAI,CAAC,IAAI,MAAM;AACf,WAAO,IAAI,CAAC;AACZ,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMA,SAAS,QAAQ,GAAG;AAClB,MAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI,EAAG,OAAM,IAAI,MAAM,oCAAoC,CAAC;AAC9F;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,aAAa,cAAc,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACpF;AAEA,SAAS,OAAO,MAAM,SAAS;AAC7B,MAAI,CAAC,QAAQ,CAAC,EAAG,OAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM,EAAG,OAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;AAChJ;AAEA,SAAS,MAAM,GAAG;AAChB,MAAI,OAAO,MAAM,cAAc,OAAO,EAAE,WAAW,WAAY,OAAM,IAAI,MAAM,iDAAiD;AAChI,UAAQ,EAAE,SAAS;AACnB,UAAQ,EAAE,QAAQ;AACpB;AAEA,SAAS,QAAQ,UAAU,gBAAgB,MAAM;AAC/C,MAAI,SAAS,UAAW,OAAM,IAAI,MAAM,kCAAkC;AAC1E,MAAI,iBAAiB,SAAS,SAAU,OAAM,IAAI,MAAM,uCAAuC;AACjG;AAEA,SAAS,QAAQ,KAAK,UAAU;AAC9B,SAAO,GAAG;AACV,QAAM,MAAM,SAAS;AACrB,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,IAAI,MAAM,2DAA2D,GAAG;AAAA,EAChF;AACF;AAWA,SAAS,IAAI,KAAK;AAChB,SAAO,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK,MAAM,IAAI,aAAa,CAAC,CAAC;AACnF;AAEA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAChE;AAEA,SAAS,KAAK,MAAM,OAAO;AACzB,SAAO,QAAQ,KAAK,QAAQ,SAAS;AACvC;AAEA,SAAS,KAAK,MAAM,OAAO;AACzB,SAAO,QAAQ,QAAQ,SAAS,KAAK,UAAU;AACjD;AAEA,IAAM,QAAuB,MAAM,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAM;AAErG,SAAS,SAAS,MAAM;AACtB,SAAO,QAAQ,KAAK,aAAa,QAAQ,IAAI,WAAW,SAAS,IAAI,QAAS,SAAS,KAAK;AAC9F;AAEA,SAAS,WAAW,KAAK;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;AAAA,EAC1B;AACF;AAKA,SAAS,YAAY,KAAK;AACxB,MAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,sCAAsC,OAAO,GAAG;AAC7F,SAAO,IAAI,WAAW,IAAI,YAAY,EAAE,OAAO,GAAG,CAAC;AACrD;AAMA,SAAS,QAAQ,MAAM;AACrB,MAAI,OAAO,SAAS,SAAU,QAAO,YAAY,IAAI;AACrD,SAAO,IAAI;AACX,SAAO;AACT;AAEA,IAAM,OAAN,MAAW;AAAA;AAAA,EAET,QAAQ;AACN,WAAO,KAAK,WAAW;AAAA,EACzB;AACF;AAEA,SAAS,gBAAgB,UAAU;AACjC,QAAM,QAAQ,SAAO,SAAS,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAO;AAC5D,QAAM,MAAM,SAAS;AACrB,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,MAAM,SAAS;AAC9B,SAAO;AACT;AACA,IAAM,OAAN,cAAmB,KAAK;AAAA,EACtB,OAAO,KAAK;AACV,YAAQ,IAAI;AACZ,SAAK,MAAM,OAAO,GAAG;AACrB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,YAAQ,IAAI;AACZ,WAAO,KAAK,KAAK,SAAS;AAC1B,SAAK,WAAW;AAChB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,MAAM,OAAO,GAAG;AACrB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS;AACP,UAAM,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS;AAC/C,SAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,IAAI;AAEb,WAAO,KAAK,OAAO,OAAO,OAAO,eAAe,IAAI,GAAG,CAAC,CAAC;AACzD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK;AACL,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,MAAM,QAAQ;AACnB,SAAK,MAAM,QAAQ;AAAA,EACrB;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,UAAM,IAAI;AACV,UAAM,MAAM,QAAQ,IAAI;AACxB,SAAK,QAAQ,KAAK,OAAO;AACzB,QAAI,OAAO,KAAK,MAAM,WAAW,WAAY,OAAM,IAAI,MAAM,qDAAqD;AAClH,SAAK,WAAW,KAAK,MAAM;AAC3B,SAAK,YAAY,KAAK,MAAM;AAC5B,UAAM,WAAW,KAAK;AACtB,UAAM,MAAM,IAAI,WAAW,QAAQ;AAEnC,QAAI,IAAI,IAAI,SAAS,WAAW,KAAK,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,IAAI,GAAG;AACxE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,CAAC,KAAK;AAC/C,SAAK,MAAM,OAAO,GAAG;AAErB,SAAK,QAAQ,KAAK,OAAO;AAEzB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,CAAC,KAAK,KAAO;AACtD,SAAK,MAAM,OAAO,GAAG;AACrB,QAAI,KAAK,CAAC;AAAA,EACZ;AACF;AAWA,IAAM,OAAO,CAAC,MAAM,KAAK,YAAY,IAAI,KAAK,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,OAAO;AAChF,KAAK,SAAS,CAAC,MAAM,QAAQ,IAAI,KAAK,MAAM,GAAG;AAG/C,SAAS,aAAa,MAAM,YAAY,OAAOA,OAAM;AACnD,MAAI,OAAO,KAAK,iBAAiB,WAAY,QAAO,KAAK,aAAa,YAAY,OAAOA,KAAI;AAC7F,QAAMC,QAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAO,SAASA,QAAO,QAAQ;AAC1C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAM,IAAID,QAAO,IAAI;AACrB,QAAM,IAAIA,QAAO,IAAI;AACrB,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACzC;AAEA,SAAS,IAAI,GAAG,GAAG,GAAG;AACpB,SAAO,IAAI,IAAI,CAAC,IAAI;AACtB;AAEA,SAAS,IAAI,GAAG,GAAG,GAAG;AACpB,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B;AAKA,IAAM,SAAN,cAAqB,KAAK;AAAA,EACxB,OAAO,MAAM;AACX,YAAQ,IAAI;AACZ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAM;AAC5B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO,SAAU,MAAK,QAAQ,UAAU,GAAG;AACzE;AAAA,MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,SAAK,WAAW;AAIhB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAAA;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,WAAO,KAAK,IAAI;AAChB,SAAK,OAAO,SAAS,GAAG,EAAE,KAAK,CAAC;AAGhC,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;AAAA,IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU,IAAK,QAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAGA,KAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM,EAAG,OAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAM,QAAQ,KAAK,IAAI;AACvB,QAAI,SAAS,MAAM,OAAQ,OAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ,IAAK,OAAM,UAAU,IAAI,GAAG,MAAM,CAAC,GAAGA,KAAI;AAAA,EACxE;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,WAAW,IAAI;AACb,WAAO,KAAK,IAAI,KAAK,YAAY;AACjC,OAAG,IAAI,GAAG,KAAK,IAAI,CAAC;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,OAAG,SAAS;AACZ,OAAG,MAAM;AACT,OAAG,WAAW;AACd,OAAG,YAAY;AACf,QAAI,SAAS,SAAU,IAAG,OAAO,IAAI,MAAM;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,YAAY,UAAU,WAAW,WAAWA,OAAM;AAChD,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAOA;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;AAAA,EACpC;AACF;AAGA,IAAM,UAAyB,IAAI,YAAY,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU,CAAC;AAG3G,IAAM,SAAwB,IAAI,YAAY,EAAE;AAChD,IAAM,OAAN,cAAmB,OAAO;AAAA,EACxB,MAAM;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACvB;AAAA,EACA,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;AACjB,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AAAA,EACf;AAAA,EACA,QAAQ,MAAM,QAAQ;AACpB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU,EAAG,QAAO,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AAClF,aAAS,IAAI,IAAI,IAAI,IAAI,IAAK,QAAO,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE,GAAG,CAAC;AAEjH,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAI,GAAG;AACP,UAAI,IAAI,IAAI;AACV,YAAI,IAAI,GAAG,GAAG,CAAC;AACf,YAAI;AAAA,MACN,WAAW,IAAI,IAAI;AACjB,YAAI,IAAI,IAAI;AACZ,YAAI;AAAA,MACN,WAAW,IAAI,IAAI;AACjB,YAAI,IAAI,GAAG,GAAG,CAAC;AACf,YAAI;AAAA,MACN,OAAO;AACL,YAAI,IAAI,IAAI;AACZ,YAAI;AAAA,MACN;AACA,YAAM,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI;AAC/C,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE;AACd,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACxB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,CAAC;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,UAAM,IAAI,IAAI,GAAG,KAAK;AACtB,SAAK,IAAI,QAAQ,CAAC,IAAI;AACtB,SAAK,IAAI,QAAQ,CAAC,IAAI;AACtB,SAAK,IAAI,QAAQ,CAAC,IAAI;AACtB,SAAK,IAAI,QAAQ,CAAC,IAAI;AACtB,SAAK,IAAI,QAAQ,CAAC,IAAI;AAAA,EACxB;AACF;AAEA,IAAM,OAAsB,gBAAgB,MAAM,IAAI,KAAK,CAAC;AAG5D,IAAM,WAA0B,IAAI,YAAY,CAAC,YAAY,YAAY,YAAY,YAAY,WAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,UAAU,CAAC;AAEhzB,IAAM,YAA2B,IAAI,YAAY,CAAC,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,WAAY,UAAU,CAAC;AAKjJ,IAAM,WAA0B,IAAI,YAAY,EAAE;AAClD,IAAM,SAAN,cAAqB,OAAO;AAAA,EAC1B,MAAM;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC;AAAA;AAAA,EAEA,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC1B,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AAAA,EACf;AAAA,EACA,QAAQ,MAAM,QAAQ;AAEpB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU,EAAG,UAAS,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AACpF,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,SAAS,IAAI,EAAE;AAC3B,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,YAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAI,QAAQ;AAClD,YAAM,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,OAAO;AAChD,eAAS,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI;AAAA,IAC/D;AAEA,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAK,IAAI,SAAS,IAAI,GAAG,GAAG,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI;AACnE,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAK,SAAS,IAAI,GAAG,GAAG,CAAC,IAAI;AACnC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,IAAI,KAAK;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,KAAK;AAAA,IAChB;AAEA,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,IAAI;AACjB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACjC;AAAA,EACA,aAAa;AACX,aAAS,KAAK,CAAC;AAAA,EACjB;AAAA,EACA,UAAU;AACR,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,UAAM,IAAI,IAAI,GAAG,KAAK;AAGtB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AACxB,SAAK,IAAI,UAAU,CAAC,IAAI;AAAA,EAC1B;AACF;AAIA,IAAM,SAAN,cAAqB,OAAO;AAAA,EAC1B,cAAc;AACZ,UAAM;AACN,SAAK,IAAI,aAAa;AACtB,SAAK,IAAI,YAAa;AACtB,SAAK,IAAI,YAAa;AACtB,SAAK,IAAI,aAAa;AACtB,SAAK,IAAI,aAAa;AACtB,SAAK,IAAI,aAAa;AACtB,SAAK,IAAI,aAAa;AACtB,SAAK,IAAI,aAAa;AACtB,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,IAAM,SAAwB,gBAAgB,MAAM,IAAI,OAAO,CAAC;AAEhE,IAAM,SAAwB,gBAAgB,MAAM,IAAI,OAAO,CAAC;AAOhE,IAAM,aAA4B,OAAO,KAAK,KAAK,CAAC;AACpD,IAAM,OAAsB,OAAO,EAAE;AACrC,SAAS,QAAQ,GAAG,KAAK,OAAO;AAC9B,MAAI,GAAI,QAAO;AAAA,IACb,GAAG,OAAO,IAAI,UAAU;AAAA,IACxB,GAAG,OAAO,KAAK,OAAO,UAAU;AAAA,EAClC;AACA,SAAO;AAAA,IACL,GAAG,OAAO,KAAK,OAAO,UAAU,IAAI;AAAA,IACpC,GAAG,OAAO,IAAI,UAAU,IAAI;AAAA,EAC9B;AACF;AACA,SAAS,MAAM,KAAK,KAAK,OAAO;AAC9B,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE;AACtB,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAAA,EACxB;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AACA,IAAM,QAAQ,CAAC,GAAG,MAAM,OAAO,MAAM,CAAC,KAAK,OAAO,OAAO,MAAM,CAAC;AAEhE,IAAM,QAAQ,CAAC,GAAG,IAAI,MAAM,MAAM;AAClC,IAAM,QAAQ,CAAC,GAAG,GAAG,MAAM,KAAK,KAAK,IAAI,MAAM;AAE/C,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,MAAM,IAAI,KAAK,KAAK;AAChD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,KAAK,IAAI,MAAM;AAEhD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI;AACpD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,MAAM,IAAI,KAAK,KAAK,KAAK;AAErD,IAAM,UAAU,CAAC,IAAI,MAAM;AAC3B,IAAM,UAAU,CAAC,GAAG,OAAO;AAE3B,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK;AAChD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK;AAEhD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK;AACrD,IAAM,SAAS,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK;AAGrD,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3B,QAAM,KAAK,OAAO,MAAM,OAAO;AAC/B,SAAO;AAAA,IACL,GAAG,KAAK,MAAM,IAAI,KAAK,KAAK,KAAK;AAAA,IACjC,GAAG,IAAI;AAAA,EACT;AACF;AAEA,IAAM,QAAQ,CAAC,IAAI,IAAI,QAAQ,OAAO,MAAM,OAAO,MAAM,OAAO;AAChE,IAAM,QAAQ,CAAC,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK;AACxE,IAAM,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AACjF,IAAM,QAAQ,CAAC,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK;AACjF,IAAM,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAClG,IAAM,QAAQ,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK;AAE1F,IAAM,MAAM;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,IAAM,CAAC,WAAW,SAAS,KAAoB,MAAM,IAAI,MAAM,CAAC,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,sBAAsB,oBAAoB,EAAE,IAAI,OAAK,OAAO,CAAC,CAAC,CAAC,GAAG;AAEtzD,IAAM,aAA4B,IAAI,YAAY,EAAE;AACpD,IAAM,aAA4B,IAAI,YAAY,EAAE;AACpD,IAAM,SAAN,cAAqB,OAAO;AAAA;AAAA,EAE1B,MAAM;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,EACxE;AAAA;AAAA,EAEA,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClE,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EACA,QAAQ,MAAM,QAAQ;AAEpB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU,GAAG;AACxC,iBAAW,CAAC,IAAI,KAAK,UAAU,MAAM;AACrC,iBAAW,CAAC,IAAI,KAAK,UAAU,UAAU,CAAC;AAAA,IAC5C;AACA,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAE5B,YAAM,OAAO,WAAW,IAAI,EAAE,IAAI;AAClC,YAAM,OAAO,WAAW,IAAI,EAAE,IAAI;AAClC,YAAM,MAAM,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,MAAM,MAAM,CAAC;AAC3F,YAAM,MAAM,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,MAAM,MAAM,CAAC;AAE3F,YAAM,MAAM,WAAW,IAAI,CAAC,IAAI;AAChC,YAAM,MAAM,WAAW,IAAI,CAAC,IAAI;AAChC,YAAM,MAAM,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC;AACvF,YAAM,MAAM,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC;AAEvF,YAAM,OAAO,IAAI,MAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC;AACtE,YAAM,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC;AAC5E,iBAAW,CAAC,IAAI,OAAO;AACvB,iBAAW,CAAC,IAAI,OAAO;AAAA,IACzB;AACA,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE3B,YAAM,UAAU,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,UAAU,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AAEvF,YAAM,OAAO,KAAK,KAAK,CAAC,KAAK;AAC7B,YAAM,OAAO,KAAK,KAAK,CAAC,KAAK;AAG7B,YAAM,OAAO,IAAI,MAAM,IAAI,SAAS,MAAM,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC;AACrE,YAAM,MAAM,IAAI,MAAM,MAAM,IAAI,SAAS,MAAM,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC;AAC1E,YAAM,MAAM,OAAO;AAEnB,YAAM,UAAU,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,UAAU,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;AACtC,YAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;AACtC,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,OAAC;AAAA,QACC,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;AAC5C,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,YAAM,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI;AACxC,WAAK,IAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AACtC,WAAK,MAAM;AAAA,IACb;AAEA,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,KAAC;AAAA,MACC,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpD,SAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,EACzE;AAAA,EACA,aAAa;AACX,eAAW,KAAK,CAAC;AACjB,eAAW,KAAK,CAAC;AAAA,EACnB;AAAA,EACA,UAAU;AACR,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACzD;AAAA,EACA,cAAc;AACZ,UAAM,KAAK,IAAI,IAAI,KAAK;AAKxB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,YAAa;AACvB,SAAK,KAAK,YAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,YAAa;AAAA,EACzB;AACF;AACA,IAAM,SAAN,cAAqB,OAAO;AAAA,EAC1B,cAAc;AACZ,UAAM;AAEN,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,YAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,YAAa;AACvB,SAAK,KAAK,YAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AACvB,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,IAAM,SAAwB,gBAAgB,MAAM,IAAI,OAAO,CAAC;AAEhE,IAAM,SAAwB,gBAAgB,MAAM,IAAI,OAAO,CAAC;AAGhE,IAAM,UAAU,CAAC;AACjB,IAAM,YAAY,CAAC;AACnB,IAAM,aAAa,CAAC;AACpB,IAAM,MAAqB,OAAO,CAAC;AACnC,IAAM,MAAqB,OAAO,CAAC;AACnC,IAAM,MAAqB,OAAO,CAAC;AACnC,IAAM,MAAqB,OAAO,CAAC;AACnC,IAAM,QAAuB,OAAO,GAAG;AACvC,IAAM,SAAwB,OAAO,GAAI;AACzC,SAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,GAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAChC,UAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;AAE5B,YAAU,MAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,EAAE;AAEjD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAK,KAAK,OAAO,KAAK,OAAO,UAAU;AACvC,QAAI,IAAI,IAAK,MAAK,QAAQ,OAAsB,OAAO,CAAC,KAAK;AAAA,EAC/D;AACA,aAAW,KAAK,CAAC;AACnB;AACA,IAAM,CAAC,aAAa,WAAW,IAAmB,MAAM,YAAY,IAAI;AAExE,IAAM,QAAQ,CAAC,GAAG,GAAG,MAAM,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AACpE,IAAM,QAAQ,CAAC,GAAG,GAAG,MAAM,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAEpE,SAAS,QAAQ,GAAG,SAAS,IAAI;AAC/B,QAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,WAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,aAAS,IAAI,GAAG,IAAI,IAAI,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,KAAK,EAAE,IAAI;AACjB,YAAM,KAAK,EAAE,OAAO,CAAC;AACrB,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,UAAE,IAAI,CAAC,KAAK;AACZ,UAAE,IAAI,IAAI,CAAC,KAAK;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,OAAO,EAAE,CAAC;AACd,QAAI,OAAO,EAAE,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,QAAQ,CAAC;AACpB,aAAO,EAAE,EAAE;AACX,aAAO,EAAE,KAAK,CAAC;AACf,QAAE,EAAE,IAAI;AACR,QAAE,KAAK,CAAC,IAAI;AAAA,IACd;AAEA,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3C,eAAS,IAAI,GAAG,IAAI,IAAI,IAAK,GAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;AAAA,IAC5E;AAEA,MAAE,CAAC,KAAK,YAAY,KAAK;AACzB,MAAE,CAAC,KAAK,YAAY,KAAK;AAAA,EAC3B;AACA,IAAE,KAAK,CAAC;AACV;AAEA,IAAM,SAAN,MAAM,gBAAe,KAAK;AAAA,EACxB,SAAS;AACP,QAAI,CAAC,KAAM,YAAW,KAAK,OAAO;AAClC,YAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,QAAI,CAAC,KAAM,YAAW,KAAK,OAAO;AAClC,SAAK,SAAS;AACd,SAAK,MAAM;AAAA,EACb;AAAA,EACA,OAAO,MAAM;AACX,YAAQ,IAAI;AACZ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAM;AAC5B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM,IAAK,OAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,UAAI,KAAK,QAAQ,SAAU,MAAK,OAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,QAAI,KAAK,SAAU;AACnB,SAAK,WAAW;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,GAAG,KAAK;AACd,SAAK,SAAS,SAAU,KAAK,QAAQ,WAAW,EAAG,MAAK,OAAO;AAC/D,UAAM,WAAW,CAAC,KAAK;AACvB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,UAAU,KAAK;AACb,YAAQ,MAAM,KAAK;AACnB,WAAO,GAAG;AACV,SAAK,OAAO;AACZ,UAAM,YAAY,KAAK;AACvB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,aAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAM;AAC9C,UAAI,KAAK,UAAU,SAAU,MAAK,OAAO;AACzC,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,UAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK;AAEX,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,MAAM,uCAAuC;AAC5E,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA,EACA,IAAI,OAAO;AACT,YAAQ,KAAK;AACb,WAAO,KAAK,QAAQ,IAAI,WAAW,KAAK,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW,KAAK;AACd,YAAQ,KAAK,IAAI;AACjB,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,6BAA6B;AAChE,SAAK,UAAU,GAAG;AAClB,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;AAAA,EACvD;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,MAAM,KAAK,CAAC;AAAA,EACnB;AAAA,EACA,WAAW,IAAI;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,KAAK,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AACrE,OAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,OAAG,MAAM,KAAK;AACd,OAAG,SAAS,KAAK;AACjB,OAAG,WAAW,KAAK;AACnB,OAAG,SAAS;AAEZ,OAAG,SAAS;AACZ,OAAG,YAAY;AACf,OAAG,YAAY;AACf,OAAG,YAAY,KAAK;AACpB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,UAAU,QAAQ,WAAW,YAAY,OAAO,SAAS,IAAI;AACvE,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,YAAY;AAEjB,YAAQ,SAAS;AAGjB,QAAI,KAAK,KAAK,YAAY,KAAK,YAAY,IAAK,OAAM,IAAI,MAAM,0CAA0C;AAC1G,SAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,SAAK,UAAU,IAAI,KAAK,KAAK;AAAA,EAC/B;AACF;AACA,IAAM,MAAM,CAAC,QAAQ,UAAU,cAAc,gBAAgB,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAE1G,IAAM,WAA0B,IAAI,GAAM,KAAK,MAAM,CAAC;AAEtD,IAAM,WAA0B,IAAI,GAAM,KAAK,MAAM,CAAC;AAEtD,IAAM,WAA0B,IAAI,GAAM,KAAK,MAAM,CAAC;AAEtD,IAAM,WAA0B,IAAI,GAAM,IAAI,MAAM,CAAC;AAOrD,IAAM,eAAe,MAAM;AACzB,MAAI,OAAO,eAAe,SAAU,QAAO;AAAA,OAAgB;AACzD,WAAO,eAAe,OAAO,WAAW,kBAAkB;AAAA,MACxD,MAAM;AACJ,eAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AACD,QAAI;AAGF,UAAI,OAAO,mBAAmB,YAAa,QAAO;AAAA,IACpD,UAAE;AAEA,aAAO,OAAO,UAAU;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,YAAa,QAAO;AAAA,WAAc,OAAO,WAAW,YAAa,QAAO;AAAA,WAAgB,OAAO,WAAW,YAAa,QAAO;AAClJ,SAAO;AACT,GAAG;AAMH,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AACd;AAMA,IAAM,wBAAwB,eAAa;AACzC,UAAQ,MAAM;AAAA,IACZ,KAAK,0BAA0B,KAAK,SAAS;AAC3C,aAAO;AAAA,IACT,KAAK,oBAAoB,KAAK,SAAS;AACrC,aAAO;AAAA,IACT,KAAK,oBAAoB,KAAK,SAAS;AACrC,aAAO;AAAA,IACT,KAAK,oBAAoB,KAAK,SAAS;AACrC,aAAO;AAAA,IACT,KAAK,oBAAoB,KAAK,SAAS;AACrC,aAAO;AAAA,IACT,KAAK,cAAc,KAAK,SAAS;AAC/B,aAAO;AAAA,IACT,KAAK,cAAc,KAAK,SAAS;AAC/B,aAAO;AAAA,IACT,KAAK,cAAc,KAAK,SAAS;AAC/B,aAAO;AAAA,IACT,KAAK,cAAc,KAAK,SAAS;AAC/B,aAAO;AAAA,IACT;AACE,YAAM,IAAI,UAAU,2BAA2B,SAAS,EAAE;AAAA,EAC9D;AACF;AAQA,IAAM,aAAa,CAAC,WAAW,KAAK,YAAY;AAC9C,MAAI,MAAM;AACR,UAAM,OAAO,YAAY,SAAS,KAAK,YAAY,sBAAsB,SAAS,CAAC;AACnF,WAAO,KAAK,MAAM,KAAK,OAAO;AAAA,EAChC,OAAO;AACL,UAAM,IAAI,MAAM,uBAAuB;AAAA,EACzC;AACF;AAMA,IAAM,WAAW;AAOjB,IAAM,eAAe,SAAO;AAE1B,QAAM,IAAI,QAAQ,MAAM,EAAE;AAE1B,MAAI,MAAM,IAAI;AACd,SAAO,IAAI,MAAM,CAAC,MAAM,IAAK,GAAE;AAC/B,SAAO,MAAM,IAAI,SAAS,IAAI,UAAU,GAAG,GAAG,IAAI,KAAK,YAAY;AACnE,QAAM,MAAM,IAAI,YAAY,IAAI,SAAS,IAAI,IAAI,CAAC;AAClD,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC;AACnC,QAAI,QAAQ,GAAI,OAAM,IAAI,UAAU,4BAA4B,IAAI,CAAC,CAAC,EAAE;AACxE,YAAQ,SAAS,IAAI;AACrB,YAAQ;AACR,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,OAAO,IAAI,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAOA,IAAM,eAAe,SAAO;AAC1B,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAQ,SAAS,IAAI,IAAI,CAAC;AAC1B,YAAQ;AACR,WAAO,QAAQ,GAAG;AAChB,aAAO,SAAS,UAAU,OAAO,IAAI,EAAE;AACvC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,OAAO,GAAG;AACZ,WAAO,SAAS,SAAS,IAAI,OAAO,EAAE;AAAA,EACxC;AACA,SAAO;AACT;AAOA,IAAM,YAAY,SAAO;AAEvB,QAAM,IAAI,QAAQ,MAAM,EAAE;AAC1B,QAAM,MAAM,IAAI,YAAY,IAAI,SAAS,CAAC;AAC1C,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,QAAI,IAAI,CAAC,IAAI,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE;AAAA,EACnD;AACA,SAAO;AACT;AAMA,IAAM,YAAY,SAAO;AACvB,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,MAAM,IAAI,CAAC,EAAE,SAAS,EAAE;AAC9B,QAAI,IAAI,WAAW,EAAG,QAAO;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,IAAI,YAAY;AACzB;AAOA,IAAM,eAAe,SAAO;AAC1B,QAAM,MAAM,IAAI,YAAY,IAAI,MAAM;AACtC,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI;AAAA,EAC/B;AACA,SAAO;AACT;AAMA,IAAM,eAAe,SAAO;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,WAAO,OAAO,aAAa,IAAI,CAAC,CAAC;AAAA,EACnC;AACA,SAAO;AACT;AAMA,IAAM,UAAU,YAAY,cAAc,IAAI,YAAY,YAAY,IAAI;AAK1E,IAAM,UAAU,YAAY,cAAc,IAAI,YAAY,YAAY,IAAI;AAM1E,IAAM,aAAa,SAAO;AACxB,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,SAAO,QAAQ,OAAO,GAAG;AAC3B;AAMA,IAAM,aAAa,SAAO;AACxB,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,SAAO,QAAQ,OAAO,GAAG;AAC3B;AAOA,IAAM,cAAc,UAAQ;AAC1B,MAAI,YAAY,QAAQ,iBAAiB;AACvC,WAAO,YAAY,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC;AAAA,EAChE,OAAO;AACL,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACF;AAKA,IAAM,SAAN,MAAM,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,OAAO,WAAW,KAAK;AACrB,WAAO,IAAI,QAAO;AAAA,MAChB,QAAQ,aAAa,GAAG,EAAE;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,KAAK;AACnB,WAAO,IAAI,QAAO;AAAA,MAChB,QAAQ,WAAW,GAAG,EAAE;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WAAW,KAAK;AACrB,WAAO,IAAI,QAAO;AAAA,MAChB,QAAQ,aAAa,GAAG,EAAE;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ,KAAK;AAClB,WAAO,IAAI,QAAO;AAAA,MAChB,QAAQ,UAAU,GAAG,EAAE;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,eAAe,MAAM,UAAU;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,aAAa,KAAK,KAAK;AAAA,IAChC,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,eAAe,MAAM,QAAQ;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,WAAW,KAAK,KAAK;AAAA,IAC9B,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,eAAe,MAAM,UAAU;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,aAAa,KAAK,KAAK;AAAA,IAChC,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM;AACR,WAAO,eAAe,MAAM,OAAO;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,UAAU,KAAK,KAAK;AAAA,IAC7B,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AAAA,IACV;AAAA,IACA,OAAO;AAAA,EACT,IAAI,CAAC,GAAG;AAMN,SAAK,QAAQ,OAAO,WAAW,cAAc,YAAY,IAAI,IAAI,IAAI,WAAW,MAAM;AAEtF,WAAO,eAAe,MAAM,SAAS;AAAA,MACnC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAQA,IAAM,kBAAkB,CAAC,GAAG,MAAM;AAChC;AACE,QAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,YAAM,IAAI,UAAU,yCAAyC;AAAA,IAC/D;AACA,QAAI,IAAI;AACR,QAAI,MAAM;AACV,WAAO,EAAE,IAAI,EAAE,QAAQ;AACrB,aAAO,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC;AAAA,IACzC;AACA,WAAO,QAAQ;AAAA,EACjB;AACF;AAMA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaT,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,eAAe;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,SAAS;AAAA,IACd;AAAA,IACA,YAAY,MAAK,SAAS;AAAA,IAC1B,SAAS,MAAK,SAAS;AAAA,IACvB,UAAU,MAAK,SAAS;AAAA,EAC1B,GAAG;AACD,UAAM,SAAS,WAAW,WAAW,OAAO,OAAO,WAAW,OAAO,CAAC;AACtE,UAAM,SAAS,OAAO,OAAO,aAAa,CAAC,IAAI;AAC/C,UAAM,QAAQ,OAAO,MAAM,IAAI,QAAQ,MAAM,OAAO,SAAS,CAAC,IAAI,QAAQ,MAAM,OAAO,SAAS,CAAC,IAAI,QAAQ,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM;AACnJ,WAAO,IAAI,SAAS,EAAE,SAAS,QAAQ,GAAG;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AAAA,IACP,UAAU,KAAK;AAAA,EACjB,IAAI,CAAC,GAAG;AACN,WAAO,MAAK,SAAS;AAAA,MACnB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,MAAK,SAAS;AAAA,IACvB,UAAU,MAAK,SAAS;AAAA,IACxB,QAAAE,UAAS,MAAK,SAAS;AAAA,EACzB,GAAG;AAED,QAAI,MAAM,WAAW,OAAQ,QAAO;AACpC,QAAI,QAAQ;AACZ,UAAM,QAAQ,CAAsB,MAAM;AACxC,YAAM,iBAAiB,MAAK,SAAS;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AACD,UAAI,gBAAgB,OAAO,cAAc,GAAG;AAC1C,gBAAQ,IAAI;AAAA,MACd;AAAA,IACF;AACA,UAAM,OAAO;AACb,aAAS,IAAI,GAAG,KAAKA,WAAU,UAAU,MAAM,EAAE,GAAG;AAClD,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU,KAAM;AACpB,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU,KAAM;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS;AAAA,IACP;AAAA,IACA,UAAU,KAAK;AAAA,IACf,QAAAA;AAAA,EACF,GAAG;AACD,WAAO,MAAK,SAAS;AAAA,MACnB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,QAAAA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,IAAI;AACV,WAAO,kBAAuB,KAAK,OAAO,SAAS,IAAI,KAAK,gBAAgB,GAAG,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAe,EAAE,KAAK,OAAO,MAAM,CAAC,cAAmB,EAAE,KAAK,SAAS,CAAC,WAAgB,EAAE,KAAK,MAAM,CAAC,YAAiB,EAAE,KAAK,OAAO,CAAC;AAAA,EACvV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,YAAY;AAAA,IACV,SAAS,MAAK,SAAS;AAAA,IACvB,QAAQ,MAAK,SAAS;AAAA,IACtB,gBAAgB,MAAK,SAAS;AAAA,IAC9B,SAAS,IAAI,OAAO;AAAA,IACpB,YAAY,MAAK,SAAS;AAAA,IAC1B,SAAS,MAAK,SAAS;AAAA,IACvB,UAAU,MAAK,SAAS;AAAA,EAC1B,IAAI,CAAC,GAAG;AAKN,SAAK,SAAS;AAKd,SAAK,QAAQ;AAKb,SAAK,gBAAgB;AAKrB,SAAK,SAAS,OAAO,WAAW,WAAW,OAAO,WAAW,MAAM,IAAI;AAKvE,SAAK,YAAY,sBAAsB,SAAS;AAKhD,SAAK,SAAS;AAKd,SAAK,UAAU;AAAA,EACjB;AACF;AAMA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaT,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,eAAe;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ;AAAA,IACb,SAAS,MAAK,SAAS;AAAA,IACvB,YAAY,KAAK,IAAI;AAAA,EACvB,IAAI,CAAC,GAAG;AACN,WAAO,KAAK,MAAM,YAAY,MAAO,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AAAA,IACN,YAAY,KAAK,IAAI;AAAA,EACvB,IAAI,CAAC,GAAG;AACN,WAAO,MAAK,QAAQ;AAAA,MAClB,QAAQ,KAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,UAAU;AAAA,IACf,SAAS,MAAK,SAAS;AAAA,IACvB,YAAY,KAAK,IAAI;AAAA,EACvB,IAAI,CAAC,GAAG;AACN,WAAO,SAAS,MAAO,aAAa,SAAS;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AAAA,IACR,YAAY,KAAK,IAAI;AAAA,EACvB,IAAI,CAAC,GAAG;AACN,WAAO,MAAK,UAAU;AAAA,MACpB,QAAQ,KAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,MAAK,SAAS;AAAA,IACvB,YAAY,KAAK,IAAI;AAAA,EACvB,GAAG;AACD,WAAO,KAAK,SAAS;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,MAAK,QAAQ;AAAA,QACpB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AAAA,IACP,YAAY,KAAK,IAAI;AAAA,EACvB,IAAI,CAAC,GAAG;AACN,WAAO,MAAK,SAAS;AAAA,MACnB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,MAAK,SAAS;AAAA,IACvB,YAAY,KAAK,IAAI;AAAA,IACrB,QAAAA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,SAAS;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,MAAK,QAAQ;AAAA,QACpB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,QAAAA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS;AAAA,IACP;AAAA,IACA;AAAA,IACA,QAAAA;AAAA,EACF,GAAG;AACD,WAAO,MAAK,SAAS;AAAA,MACnB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,QAAAA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,IAAI;AACV,WAAO,kBAAuB,KAAK,OAAO,SAAS,IAAI,KAAK,gBAAgB,GAAG,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAe,EAAE,KAAK,OAAO,MAAM,CAAC,cAAmB,EAAE,KAAK,SAAS,CAAC,WAAgB,EAAE,KAAK,MAAM,CAAC,WAAgB,EAAE,KAAK,MAAM,CAAC;AAAA,EACrV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,YAAY;AAAA,IACV,SAAS,MAAK,SAAS;AAAA,IACvB,QAAQ,MAAK,SAAS;AAAA,IACtB,gBAAgB,MAAK,SAAS;AAAA,IAC9B,SAAS,IAAI,OAAO;AAAA,IACpB,YAAY,MAAK,SAAS;AAAA,IAC1B,SAAS,MAAK,SAAS;AAAA,IACvB,SAAS,MAAK,SAAS;AAAA,EACzB,IAAI,CAAC,GAAG;AAKN,SAAK,SAAS;AAKd,SAAK,QAAQ;AAKb,SAAK,gBAAgB;AAKrB,SAAK,SAAS,OAAO,WAAW,WAAW,OAAO,WAAW,MAAM,IAAI;AAKvE,SAAK,YAAY,sBAAsB,SAAS;AAKhD,SAAK,SAAS;AAKd,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,eAAe;AAKrB,IAAM,eAAe;AAKrB,IAAM,kBAAkB;AAKxB,IAAM,gBAAgB;AAKtB,IAAM,yBAAyB;AAK/B,IAAM,MAAN,MAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,OAAO,MAAM,KAAK;AAChB,QAAI;AACJ,QAAI;AACF,kBAAY,IAAI,MAAM,YAAY;AAAA,IAEpC,SAAS,GAAG;AAAA,IACS;AACrB,QAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,YAAM,IAAI,SAAS,oBAAoB;AAAA,IACzC;AAEA,UAAM,UAAU,UAAU,CAAC,EAAE,YAAY;AACzC,UAAM,WAAW,UAAU,CAAC,EAAE,MAAM,oBAAoB,CAAC,EAAE,IAAI,kBAAkB;AAEjF,UAAM,YAAY,UAAU,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,QAAQ;AAC7D,YAAM,UAAU,IAAI,MAAM,SAAS,CAAC,EAAE,IAAI,kBAAkB;AAC5D,YAAM,UAAU,QAAQ,CAAC,EAAE,YAAY;AACvC,YAAM,UAAU,QAAQ,CAAC;AAEzB,YAAM,UAAU;AAChB,cAAQ,OAAO,IAAI;AACnB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAEL,QAAI;AACJ,UAAM,SAAS,CAAC;AAChB,QAAI,YAAY,QAAQ;AACtB,YAAM;AAEN,UAAI,OAAO,UAAU,YAAY,eAAe,cAAc,KAAK,UAAU,OAAO,GAAG;AACrF,eAAO,UAAU,SAAS,UAAU,SAAS,EAAE;AAAA,MACjD,OAAO;AACL,cAAM,IAAI,UAAU,wCAAwC;AAAA,MAC9D;AAAA,IACF,WAAW,YAAY,QAAQ;AAC7B,YAAM;AAEN,UAAI,OAAO,UAAU,WAAW,aAAa;AAC3C,YAAI,uBAAuB,KAAK,UAAU,MAAM,GAAG;AACjD,iBAAO,SAAS,SAAS,UAAU,QAAQ,EAAE;AAAA,QAC/C,OAAO;AACL,gBAAM,IAAI,UAAU,4BAA4B;AAAA,QAClD;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI,UAAU,kBAAkB;AAAA,IACxC;AAGA,QAAI,OAAO,UAAU,WAAW,aAAa;AAC3C,aAAO,SAAS,UAAU;AAAA,IAC5B;AACA,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO,QAAQ,SAAS,CAAC;AACzB,UAAI,OAAO,OAAO,WAAW,eAAe,OAAO,WAAW,IAAI;AAChE,eAAO,SAAS,SAAS,CAAC;AAAA,MAC5B,WAAW,SAAS,CAAC,MAAM,IAAI;AAC7B,eAAO,gBAAgB;AAAA,MACzB;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,SAAS,CAAC;AACzB,UAAI,OAAO,OAAO,WAAW,eAAe,OAAO,WAAW,IAAI;AAChE,eAAO,gBAAgB;AAAA,MACzB;AAAA,IACF;AAEA,QAAI,OAAO,UAAU,WAAW,eAAe,aAAa,KAAK,UAAU,MAAM,GAAG;AAClF,aAAO,SAAS,UAAU;AAAA,IAC5B,OAAO;AACL,YAAM,IAAI,UAAU,uCAAuC;AAAA,IAC7D;AAEA,QAAI,OAAO,UAAU,cAAc,aAAa;AAC9C,UAAI,gBAAgB,KAAK,UAAU,SAAS,GAAG;AAC7C,eAAO,YAAY,UAAU;AAAA,MAC/B,OAAO;AACL,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,OAAO,UAAU,WAAW,aAAa;AAC3C,UAAI,uBAAuB,KAAK,UAAU,MAAM,GAAG;AACjD,eAAO,SAAS,SAAS,UAAU,QAAQ,EAAE;AAAA,MAC/C,OAAO;AACL,cAAM,IAAI,UAAU,4BAA4B;AAAA,MAClD;AAAA,IACF;AACA,WAAO,IAAI,IAAI,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,UAAU,KAAK;AACpB,QAAI,eAAe,QAAQ,eAAe,MAAM;AAC9C,aAAO,IAAI,SAAS;AAAA,IACtB;AACA,UAAM,IAAI,UAAU,4BAA4B;AAAA,EAClD;AACF;AAMA,IAAM,UAAU;", "names": ["isLE", "_32n", "window"]}