import "./chunk-KBUIKKCC.js";

// node_modules/.pnpm/fuzzy-search@3.2.1/node_modules/fuzzy-search/src/Helper.js
var Helper = class _Helper {
  static getDescendantProperty(object, path, list = []) {
    let firstSegment;
    let remaining;
    let dotIndex;
    let value;
    let index;
    let length;
    if (path) {
      dotIndex = path.indexOf(".");
      if (dotIndex === -1) {
        firstSegment = path;
      } else {
        firstSegment = path.slice(0, dotIndex);
        remaining = path.slice(dotIndex + 1);
      }
      value = object[firstSegment];
      if (value !== null && typeof value !== "undefined") {
        if (!remaining && (typeof value === "string" || typeof value === "number")) {
          list.push(value);
        } else if (Object.prototype.toString.call(value) === "[object Array]") {
          for (index = 0, length = value.length; index < length; index++) {
            _Helper.getDescendantProperty(value[index], remaining, list);
          }
        } else if (remaining) {
          _Helper.getDescendantProperty(value, remaining, list);
        }
      }
    } else {
      list.push(object);
    }
    return list;
  }
};

// node_modules/.pnpm/fuzzy-search@3.2.1/node_modules/fuzzy-search/src/FuzzySearch.js
var FuzzySearch = class _FuzzySearch {
  constructor(haystack = [], keys = [], options = {}) {
    if (!Array.isArray(keys)) {
      options = keys;
      keys = [];
    }
    this.haystack = haystack;
    this.keys = keys;
    this.options = Object.assign({
      caseSensitive: false,
      sort: false
    }, options);
  }
  search(query = "") {
    if (query === "") {
      return this.haystack;
    }
    const results = [];
    for (let i = 0; i < this.haystack.length; i++) {
      const item = this.haystack[i];
      if (this.keys.length === 0) {
        const score = _FuzzySearch.isMatch(item, query, this.options.caseSensitive);
        if (score) {
          results.push({
            item,
            score
          });
        }
      } else {
        for (let y = 0; y < this.keys.length; y++) {
          const propertyValues = Helper.getDescendantProperty(item, this.keys[y]);
          let found = false;
          for (let z = 0; z < propertyValues.length; z++) {
            const score = _FuzzySearch.isMatch(propertyValues[z], query, this.options.caseSensitive);
            if (score) {
              found = true;
              results.push({
                item,
                score
              });
              break;
            }
          }
          if (found) {
            break;
          }
        }
      }
    }
    if (this.options.sort) {
      results.sort((a, b) => a.score - b.score);
    }
    return results.map((result) => result.item);
  }
  static isMatch(item, query, caseSensitive) {
    item = String(item);
    query = String(query);
    if (!caseSensitive) {
      item = item.toLocaleLowerCase();
      query = query.toLocaleLowerCase();
    }
    const indexes = _FuzzySearch.nearestIndexesFor(item, query);
    if (!indexes) {
      return false;
    }
    if (item === query) {
      return 1;
    }
    if (indexes.length > 1) {
      return 2 + (indexes[indexes.length - 1] - indexes[0]);
    }
    return 2 + indexes[0];
  }
  static nearestIndexesFor(item, query) {
    const letters = query.split("");
    let indexes = [];
    const indexesOfFirstLetter = _FuzzySearch.indexesOfFirstLetter(item, query);
    indexesOfFirstLetter.forEach((startingIndex, loopingIndex) => {
      let index = startingIndex + 1;
      indexes[loopingIndex] = [startingIndex];
      for (let i = 1; i < letters.length; i++) {
        const letter = letters[i];
        index = item.indexOf(letter, index);
        if (index === -1) {
          indexes[loopingIndex] = false;
          break;
        }
        indexes[loopingIndex].push(index);
        index++;
      }
    });
    indexes = indexes.filter((letterIndexes) => letterIndexes !== false);
    if (!indexes.length) {
      return false;
    }
    return indexes.sort((a, b) => {
      if (a.length === 1) {
        return a[0] - b[0];
      }
      a = a[a.length - 1] - a[0];
      b = b[b.length - 1] - b[0];
      return a - b;
    })[0];
  }
  static indexesOfFirstLetter(item, query) {
    const match = query[0];
    return item.split("").map((letter, index) => {
      if (letter !== match) {
        return false;
      }
      return index;
    }).filter((index) => index !== false);
  }
};
export {
  FuzzySearch as default
};
//# sourceMappingURL=fuzzy-search.js.map
