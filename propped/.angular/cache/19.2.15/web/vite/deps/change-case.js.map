{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/change-case@5.4.4/node_modules/change-case/dist/index.js"], "sourcesContent": ["// Regexps involved with splitting words in various case formats.\nconst SPLIT_LOWER_UPPER_RE = /([\\p{Ll}\\d])(\\p{Lu})/gu;\nconst SPLIT_UPPER_UPPER_RE = /(\\p{Lu})([\\p{Lu}][\\p{Ll}])/gu;\n// Used to iterate over the initial split result and separate numbers.\nconst SPLIT_SEPARATE_NUMBER_RE = /(\\d)\\p{Ll}|(\\p{L})\\d/u;\n// Regexp involved with stripping non-word characters from the result.\nconst DEFAULT_STRIP_REGEXP = /[^\\p{L}\\d]+/giu;\n// The replacement value for splits.\nconst SPLIT_REPLACE_VALUE = \"$1\\0$2\";\n// The default characters to keep after transforming case.\nconst DEFAULT_PREFIX_SUFFIX_CHARACTERS = \"\";\n/**\n * Split any cased input strings into an array of words.\n */\nexport function split(value) {\n  let result = value.trim();\n  result = result.replace(SPLIT_LOWER_UPPER_RE, SPLIT_REPLACE_VALUE).replace(SPLIT_UPPER_UPPER_RE, SPLIT_REPLACE_VALUE);\n  result = result.replace(DEFAULT_STRIP_REGEXP, \"\\0\");\n  let start = 0;\n  let end = result.length;\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  if (start === end) return [];\n  while (result.charAt(end - 1) === \"\\0\") end--;\n  return result.slice(start, end).split(/\\0/g);\n}\n/**\n * Split the input string into an array of words, separating numbers.\n */\nexport function splitSeparateNumbers(value) {\n  const words = split(value);\n  for (let i = 0; i < words.length; i++) {\n    const word = words[i];\n    const match = SPLIT_SEPARATE_NUMBER_RE.exec(word);\n    if (match) {\n      const offset = match.index + (match[1] ?? match[2]).length;\n      words.splice(i, 1, word.slice(0, offset), word.slice(offset));\n    }\n  }\n  return words;\n}\n/**\n * Convert a string to space separated lower case (`foo bar`).\n */\nexport function noCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  return prefix + words.map(lowerFactory(options?.locale)).join(options?.delimiter ?? \" \") + suffix;\n}\n/**\n * Convert a string to camel case (`fooBar`).\n */\nexport function camelCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = options?.mergeAmbiguousCharacters ? capitalCaseTransformFactory(lower, upper) : pascalCaseTransformFactory(lower, upper);\n  return prefix + words.map((word, index) => {\n    if (index === 0) return lower(word);\n    return transform(word, index);\n  }).join(options?.delimiter ?? \"\") + suffix;\n}\n/**\n * Convert a string to pascal case (`FooBar`).\n */\nexport function pascalCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = options?.mergeAmbiguousCharacters ? capitalCaseTransformFactory(lower, upper) : pascalCaseTransformFactory(lower, upper);\n  return prefix + words.map(transform).join(options?.delimiter ?? \"\") + suffix;\n}\n/**\n * Convert a string to pascal snake case (`Foo_Bar`).\n */\nexport function pascalSnakeCase(input, options) {\n  return capitalCase(input, {\n    delimiter: \"_\",\n    ...options\n  });\n}\n/**\n * Convert a string to capital case (`Foo Bar`).\n */\nexport function capitalCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  return prefix + words.map(capitalCaseTransformFactory(lower, upper)).join(options?.delimiter ?? \" \") + suffix;\n}\n/**\n * Convert a string to constant case (`FOO_BAR`).\n */\nexport function constantCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  return prefix + words.map(upperFactory(options?.locale)).join(options?.delimiter ?? \"_\") + suffix;\n}\n/**\n * Convert a string to dot case (`foo.bar`).\n */\nexport function dotCase(input, options) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options\n  });\n}\n/**\n * Convert a string to kebab case (`foo-bar`).\n */\nexport function kebabCase(input, options) {\n  return noCase(input, {\n    delimiter: \"-\",\n    ...options\n  });\n}\n/**\n * Convert a string to path case (`foo/bar`).\n */\nexport function pathCase(input, options) {\n  return noCase(input, {\n    delimiter: \"/\",\n    ...options\n  });\n}\n/**\n * Convert a string to path case (`Foo bar`).\n */\nexport function sentenceCase(input, options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = capitalCaseTransformFactory(lower, upper);\n  return prefix + words.map((word, index) => {\n    if (index === 0) return transform(word);\n    return lower(word);\n  }).join(options?.delimiter ?? \" \") + suffix;\n}\n/**\n * Convert a string to snake case (`foo_bar`).\n */\nexport function snakeCase(input, options) {\n  return noCase(input, {\n    delimiter: \"_\",\n    ...options\n  });\n}\n/**\n * Convert a string to header case (`Foo-Bar`).\n */\nexport function trainCase(input, options) {\n  return capitalCase(input, {\n    delimiter: \"-\",\n    ...options\n  });\n}\nfunction lowerFactory(locale) {\n  return locale === false ? input => input.toLowerCase() : input => input.toLocaleLowerCase(locale);\n}\nfunction upperFactory(locale) {\n  return locale === false ? input => input.toUpperCase() : input => input.toLocaleUpperCase(locale);\n}\nfunction capitalCaseTransformFactory(lower, upper) {\n  return word => `${upper(word[0])}${lower(word.slice(1))}`;\n}\nfunction pascalCaseTransformFactory(lower, upper) {\n  return (word, index) => {\n    const char0 = word[0];\n    const initial = index > 0 && char0 >= \"0\" && char0 <= \"9\" ? \"_\" + char0 : upper(char0);\n    return initial + lower(word.slice(1));\n  };\n}\nfunction splitPrefixSuffix(input, options = {}) {\n  const splitFn = options.split ?? (options.separateNumbers ? splitSeparateNumbers : split);\n  const prefixCharacters = options.prefixCharacters ?? DEFAULT_PREFIX_SUFFIX_CHARACTERS;\n  const suffixCharacters = options.suffixCharacters ?? DEFAULT_PREFIX_SUFFIX_CHARACTERS;\n  let prefixIndex = 0;\n  let suffixIndex = input.length;\n  while (prefixIndex < input.length) {\n    const char = input.charAt(prefixIndex);\n    if (!prefixCharacters.includes(char)) break;\n    prefixIndex++;\n  }\n  while (suffixIndex > prefixIndex) {\n    const index = suffixIndex - 1;\n    const char = input.charAt(index);\n    if (!suffixCharacters.includes(char)) break;\n    suffixIndex = index;\n  }\n  return [input.slice(0, prefixIndex), splitFn(input.slice(prefixIndex, suffixIndex)), input.slice(suffixIndex)];\n}\n"], "mappings": ";;;;;AACA,IAAM,uBAAuB,WAAC,2BAAqB,IAAE;AACrD,IAAM,uBAAuB,WAAC,iCAA2B,IAAE;AAE3D,IAAM,2BAA2B,WAAC,4BAAqB,GAAC;AAExD,IAAM,uBAAuB;AAE7B,IAAM,sBAAsB;AAE5B,IAAM,mCAAmC;AAIlC,SAAS,MAAM,OAAO;AAC3B,MAAI,SAAS,MAAM,KAAK;AACxB,WAAS,OAAO,QAAQ,sBAAsB,mBAAmB,EAAE,QAAQ,sBAAsB,mBAAmB;AACpH,WAAS,OAAO,QAAQ,sBAAsB,IAAI;AAClD,MAAI,QAAQ;AACZ,MAAI,MAAM,OAAO;AAEjB,SAAO,OAAO,OAAO,KAAK,MAAM,KAAM;AACtC,MAAI,UAAU,IAAK,QAAO,CAAC;AAC3B,SAAO,OAAO,OAAO,MAAM,CAAC,MAAM,KAAM;AACxC,SAAO,OAAO,MAAM,OAAO,GAAG,EAAE,MAAM,KAAK;AAC7C;AAIO,SAAS,qBAAqB,OAAO;AAC1C,QAAM,QAAQ,MAAM,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,QAAQ,yBAAyB,KAAK,IAAI;AAChD,QAAI,OAAO;AACT,YAAM,SAAS,MAAM,SAAS,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACpD,YAAM,OAAO,GAAG,GAAG,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,MAAM,MAAM,CAAC;AAAA,IAC9D;AAAA,EACF;AACA,SAAO;AACT;AAIO,SAAS,OAAO,OAAO,SAAS;AACrC,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,SAAO,SAAS,MAAM,IAAI,aAAa,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS,aAAa,GAAG,IAAI;AAC7F;AAIO,SAAS,UAAU,OAAO,SAAS;AACxC,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,YAAY,SAAS,2BAA2B,4BAA4B,OAAO,KAAK,IAAI,2BAA2B,OAAO,KAAK;AACzI,SAAO,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU;AACzC,QAAI,UAAU,EAAG,QAAO,MAAM,IAAI;AAClC,WAAO,UAAU,MAAM,KAAK;AAAA,EAC9B,CAAC,EAAE,KAAK,SAAS,aAAa,EAAE,IAAI;AACtC;AAIO,SAAS,WAAW,OAAO,SAAS;AACzC,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,YAAY,SAAS,2BAA2B,4BAA4B,OAAO,KAAK,IAAI,2BAA2B,OAAO,KAAK;AACzI,SAAO,SAAS,MAAM,IAAI,SAAS,EAAE,KAAK,SAAS,aAAa,EAAE,IAAI;AACxE;AAIO,SAAS,gBAAgB,OAAO,SAAS;AAC9C,SAAO,YAAY,OAAO;AAAA,IACxB,WAAW;AAAA,KACR,QACJ;AACH;AAIO,SAAS,YAAY,OAAO,SAAS;AAC1C,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,SAAO,SAAS,MAAM,IAAI,4BAA4B,OAAO,KAAK,CAAC,EAAE,KAAK,SAAS,aAAa,GAAG,IAAI;AACzG;AAIO,SAAS,aAAa,OAAO,SAAS;AAC3C,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,SAAO,SAAS,MAAM,IAAI,aAAa,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS,aAAa,GAAG,IAAI;AAC7F;AAIO,SAAS,QAAQ,OAAO,SAAS;AACtC,SAAO,OAAO,OAAO;AAAA,IACnB,WAAW;AAAA,KACR,QACJ;AACH;AAIO,SAAS,UAAU,OAAO,SAAS;AACxC,SAAO,OAAO,OAAO;AAAA,IACnB,WAAW;AAAA,KACR,QACJ;AACH;AAIO,SAAS,SAAS,OAAO,SAAS;AACvC,SAAO,OAAO,OAAO;AAAA,IACnB,WAAW;AAAA,KACR,QACJ;AACH;AAIO,SAAS,aAAa,OAAO,SAAS;AAC3C,QAAM,CAAC,QAAQ,OAAO,MAAM,IAAI,kBAAkB,OAAO,OAAO;AAChE,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,QAAQ,aAAa,SAAS,MAAM;AAC1C,QAAM,YAAY,4BAA4B,OAAO,KAAK;AAC1D,SAAO,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU;AACzC,QAAI,UAAU,EAAG,QAAO,UAAU,IAAI;AACtC,WAAO,MAAM,IAAI;AAAA,EACnB,CAAC,EAAE,KAAK,SAAS,aAAa,GAAG,IAAI;AACvC;AAIO,SAAS,UAAU,OAAO,SAAS;AACxC,SAAO,OAAO,OAAO;AAAA,IACnB,WAAW;AAAA,KACR,QACJ;AACH;AAIO,SAAS,UAAU,OAAO,SAAS;AACxC,SAAO,YAAY,OAAO;AAAA,IACxB,WAAW;AAAA,KACR,QACJ;AACH;AACA,SAAS,aAAa,QAAQ;AAC5B,SAAO,WAAW,QAAQ,WAAS,MAAM,YAAY,IAAI,WAAS,MAAM,kBAAkB,MAAM;AAClG;AACA,SAAS,aAAa,QAAQ;AAC5B,SAAO,WAAW,QAAQ,WAAS,MAAM,YAAY,IAAI,WAAS,MAAM,kBAAkB,MAAM;AAClG;AACA,SAAS,4BAA4B,OAAO,OAAO;AACjD,SAAO,UAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AACzD;AACA,SAAS,2BAA2B,OAAO,OAAO;AAChD,SAAO,CAAC,MAAM,UAAU;AACtB,UAAM,QAAQ,KAAK,CAAC;AACpB,UAAM,UAAU,QAAQ,KAAK,SAAS,OAAO,SAAS,MAAM,MAAM,QAAQ,MAAM,KAAK;AACrF,WAAO,UAAU,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACtC;AACF;AACA,SAAS,kBAAkB,OAAO,UAAU,CAAC,GAAG;AAC9C,QAAM,UAAU,QAAQ,UAAU,QAAQ,kBAAkB,uBAAuB;AACnF,QAAM,mBAAmB,QAAQ,oBAAoB;AACrD,QAAM,mBAAmB,QAAQ,oBAAoB;AACrD,MAAI,cAAc;AAClB,MAAI,cAAc,MAAM;AACxB,SAAO,cAAc,MAAM,QAAQ;AACjC,UAAM,OAAO,MAAM,OAAO,WAAW;AACrC,QAAI,CAAC,iBAAiB,SAAS,IAAI,EAAG;AACtC;AAAA,EACF;AACA,SAAO,cAAc,aAAa;AAChC,UAAM,QAAQ,cAAc;AAC5B,UAAM,OAAO,MAAM,OAAO,KAAK;AAC/B,QAAI,CAAC,iBAAiB,SAAS,IAAI,EAAG;AACtC,kBAAc;AAAA,EAChB;AACA,SAAO,CAAC,MAAM,MAAM,GAAG,WAAW,GAAG,QAAQ,MAAM,MAAM,aAAa,WAAW,CAAC,GAAG,MAAM,MAAM,WAAW,CAAC;AAC/G;", "names": []}