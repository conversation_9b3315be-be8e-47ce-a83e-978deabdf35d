{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/preact@10.26.6/node_modules/preact/hooks/dist/hooks.module.js"], "sourcesContent": ["import { options as n } from \"preact\";\nvar t,\n  r,\n  u,\n  i,\n  o = 0,\n  f = [],\n  c = n,\n  e = c.__b,\n  a = c.__r,\n  v = c.diffed,\n  l = c.__c,\n  m = c.unmount,\n  s = c.__;\nfunction p(n, t) {\n  c.__h && c.__h(r, n, o || t), o = 0;\n  var u = r.__H || (r.__H = {\n    __: [],\n    __h: []\n  });\n  return n >= u.__.length && u.__.push({}), u.__[n];\n}\nfunction d(n) {\n  return o = 1, h(D, n);\n}\nfunction h(n, u, i) {\n  var o = p(t++, 2);\n  if (o.t = n, !o.__c && (o.__ = [i ? i(u) : D(void 0, u), function (n) {\n    var t = o.__N ? o.__N[0] : o.__[0],\n      r = o.t(t, n);\n    t !== r && (o.__N = [r, o.__[1]], o.__c.setState({}));\n  }], o.__c = r, !r.__f)) {\n    var f = function (n, t, r) {\n      if (!o.__c.__H) return !0;\n      var u = o.__c.__H.__.filter(function (n) {\n        return !!n.__c;\n      });\n      if (u.every(function (n) {\n        return !n.__N;\n      })) return !c || c.call(this, n, t, r);\n      var i = o.__c.props !== n;\n      return u.forEach(function (n) {\n        if (n.__N) {\n          var t = n.__[0];\n          n.__ = n.__N, n.__N = void 0, t !== n.__[0] && (i = !0);\n        }\n      }), c && c.call(this, n, t, r) || i;\n    };\n    r.__f = !0;\n    var c = r.shouldComponentUpdate,\n      e = r.componentWillUpdate;\n    r.componentWillUpdate = function (n, t, r) {\n      if (this.__e) {\n        var u = c;\n        c = void 0, f(n, t, r), c = u;\n      }\n      e && e.call(this, n, t, r);\n    }, r.shouldComponentUpdate = f;\n  }\n  return o.__N || o.__;\n}\nfunction y(n, u) {\n  var i = p(t++, 3);\n  !c.__s && C(i.__H, u) && (i.__ = n, i.u = u, r.__H.__h.push(i));\n}\nfunction _(n, u) {\n  var i = p(t++, 4);\n  !c.__s && C(i.__H, u) && (i.__ = n, i.u = u, r.__h.push(i));\n}\nfunction A(n) {\n  return o = 5, T(function () {\n    return {\n      current: n\n    };\n  }, []);\n}\nfunction F(n, t, r) {\n  o = 6, _(function () {\n    if (\"function\" == typeof n) {\n      var r = n(t());\n      return function () {\n        n(null), r && \"function\" == typeof r && r();\n      };\n    }\n    if (n) return n.current = t(), function () {\n      return n.current = null;\n    };\n  }, null == r ? r : r.concat(n));\n}\nfunction T(n, r) {\n  var u = p(t++, 7);\n  return C(u.__H, r) && (u.__ = n(), u.__H = r, u.__h = n), u.__;\n}\nfunction q(n, t) {\n  return o = 8, T(function () {\n    return n;\n  }, t);\n}\nfunction x(n) {\n  var u = r.context[n.__c],\n    i = p(t++, 9);\n  return i.c = n, u ? (null == i.__ && (i.__ = !0, u.sub(r)), u.props.value) : n.__;\n}\nfunction P(n, t) {\n  c.useDebugValue && c.useDebugValue(t ? t(n) : n);\n}\nfunction b(n) {\n  var u = p(t++, 10),\n    i = d();\n  return u.__ = n, r.componentDidCatch || (r.componentDidCatch = function (n, t) {\n    u.__ && u.__(n, t), i[1](n);\n  }), [i[0], function () {\n    i[1](void 0);\n  }];\n}\nfunction g() {\n  var n = p(t++, 11);\n  if (!n.__) {\n    for (var u = r.__v; null !== u && !u.__m && null !== u.__;) u = u.__;\n    var i = u.__m || (u.__m = [0, 0]);\n    n.__ = \"P\" + i[0] + \"-\" + i[1]++;\n  }\n  return n.__;\n}\nfunction j() {\n  for (var n; n = f.shift();) if (n.__P && n.__H) try {\n    n.__H.__h.forEach(z), n.__H.__h.forEach(B), n.__H.__h = [];\n  } catch (t) {\n    n.__H.__h = [], c.__e(t, n.__v);\n  }\n}\nc.__b = function (n) {\n  r = null, e && e(n);\n}, c.__ = function (n, t) {\n  n && t.__k && t.__k.__m && (n.__m = t.__k.__m), s && s(n, t);\n}, c.__r = function (n) {\n  a && a(n), t = 0;\n  var i = (r = n.__c).__H;\n  i && (u === r ? (i.__h = [], r.__h = [], i.__.forEach(function (n) {\n    n.__N && (n.__ = n.__N), n.u = n.__N = void 0;\n  })) : (i.__h.forEach(z), i.__h.forEach(B), i.__h = [], t = 0)), u = r;\n}, c.diffed = function (n) {\n  v && v(n);\n  var t = n.__c;\n  t && t.__H && (t.__H.__h.length && (1 !== f.push(t) && i === c.requestAnimationFrame || ((i = c.requestAnimationFrame) || w)(j)), t.__H.__.forEach(function (n) {\n    n.u && (n.__H = n.u), n.u = void 0;\n  })), u = r = null;\n}, c.__c = function (n, t) {\n  t.some(function (n) {\n    try {\n      n.__h.forEach(z), n.__h = n.__h.filter(function (n) {\n        return !n.__ || B(n);\n      });\n    } catch (r) {\n      t.some(function (n) {\n        n.__h && (n.__h = []);\n      }), t = [], c.__e(r, n.__v);\n    }\n  }), l && l(n, t);\n}, c.unmount = function (n) {\n  m && m(n);\n  var t,\n    r = n.__c;\n  r && r.__H && (r.__H.__.forEach(function (n) {\n    try {\n      z(n);\n    } catch (n) {\n      t = n;\n    }\n  }), r.__H = void 0, t && c.__e(t, r.__v));\n};\nvar k = \"function\" == typeof requestAnimationFrame;\nfunction w(n) {\n  var t,\n    r = function () {\n      clearTimeout(u), k && cancelAnimationFrame(t), setTimeout(n);\n    },\n    u = setTimeout(r, 100);\n  k && (t = requestAnimationFrame(r));\n}\nfunction z(n) {\n  var t = r,\n    u = n.__c;\n  \"function\" == typeof u && (n.__c = void 0, u()), r = t;\n}\nfunction B(n) {\n  var t = r;\n  n.__c = n.__(), r = t;\n}\nfunction C(n, t) {\n  return !n || n.length !== t.length || t.some(function (t, r) {\n    return t !== n[r];\n  });\n}\nfunction D(n, t) {\n  return \"function\" == typeof t ? t(n) : t;\n}\nexport { q as useCallback, x as useContext, P as useDebugValue, y as useEffect, b as useErrorBoundary, g as useId, F as useImperativeHandle, _ as useLayoutEffect, T as useMemo, h as useReducer, A as useRef, d as useState };\n"], "mappings": ";;;;;;AACA,IAAI;AAAJ,IACE;AADF,IAEE;AAFF,IAGE;AAHF,IAIE,IAAI;AAJN,IAKE,IAAI,CAAC;AALP,IAME,IAAI;AANN,IAOE,IAAI,EAAE;AAPR,IAQE,IAAI,EAAE;AARR,IASE,IAAI,EAAE;AATR,IAUEA,KAAI,EAAE;AAVR,IAWE,IAAI,EAAE;AAXR,IAYE,IAAI,EAAE;AACR,SAAS,EAAE,GAAGC,IAAG;AACf,IAAE,OAAO,EAAE,IAAI,GAAG,GAAG,KAAKA,EAAC,GAAG,IAAI;AAClC,MAAIC,KAAI,EAAE,QAAQ,EAAE,MAAM;AAAA,IACxB,IAAI,CAAC;AAAA,IACL,KAAK,CAAC;AAAA,EACR;AACA,SAAO,KAAKA,GAAE,GAAG,UAAUA,GAAE,GAAG,KAAK,CAAC,CAAC,GAAGA,GAAE,GAAG,CAAC;AAClD;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,IAAI,GAAG,EAAE,GAAG,CAAC;AACtB;AACA,SAAS,EAAE,GAAGA,IAAGC,IAAG;AAClB,MAAIC,KAAI,EAAE,KAAK,CAAC;AAChB,MAAIA,GAAE,IAAI,GAAG,CAACA,GAAE,QAAQA,GAAE,KAAK,CAACD,KAAIA,GAAED,EAAC,IAAI,EAAE,QAAQA,EAAC,GAAG,SAAUG,IAAG;AACpE,QAAIJ,KAAIG,GAAE,MAAMA,GAAE,IAAI,CAAC,IAAIA,GAAE,GAAG,CAAC,GAC/BE,KAAIF,GAAE,EAAEH,IAAGI,EAAC;AACd,IAAAJ,OAAMK,OAAMF,GAAE,MAAM,CAACE,IAAGF,GAAE,GAAG,CAAC,CAAC,GAAGA,GAAE,IAAI,SAAS,CAAC,CAAC;AAAA,EACrD,CAAC,GAAGA,GAAE,MAAM,GAAG,CAAC,EAAE,MAAM;AACtB,QAAIG,KAAI,SAAUF,IAAGJ,IAAGK,IAAG;AACzB,UAAI,CAACF,GAAE,IAAI,IAAK,QAAO;AACvB,UAAIF,KAAIE,GAAE,IAAI,IAAI,GAAG,OAAO,SAAUC,IAAG;AACvC,eAAO,CAAC,CAACA,GAAE;AAAA,MACb,CAAC;AACD,UAAIH,GAAE,MAAM,SAAUG,IAAG;AACvB,eAAO,CAACA,GAAE;AAAA,MACZ,CAAC,EAAG,QAAO,CAACG,MAAKA,GAAE,KAAK,MAAMH,IAAGJ,IAAGK,EAAC;AACrC,UAAIH,KAAIC,GAAE,IAAI,UAAUC;AACxB,aAAOH,GAAE,QAAQ,SAAUG,IAAG;AAC5B,YAAIA,GAAE,KAAK;AACT,cAAIJ,KAAII,GAAE,GAAG,CAAC;AACd,UAAAA,GAAE,KAAKA,GAAE,KAAKA,GAAE,MAAM,QAAQJ,OAAMI,GAAE,GAAG,CAAC,MAAMF,KAAI;AAAA,QACtD;AAAA,MACF,CAAC,GAAGK,MAAKA,GAAE,KAAK,MAAMH,IAAGJ,IAAGK,EAAC,KAAKH;AAAA,IACpC;AACA,MAAE,MAAM;AACR,QAAIK,KAAI,EAAE,uBACRC,KAAI,EAAE;AACR,MAAE,sBAAsB,SAAUJ,IAAGJ,IAAGK,IAAG;AACzC,UAAI,KAAK,KAAK;AACZ,YAAIJ,KAAIM;AACR,QAAAA,KAAI,QAAQD,GAAEF,IAAGJ,IAAGK,EAAC,GAAGE,KAAIN;AAAA,MAC9B;AACA,MAAAO,MAAKA,GAAE,KAAK,MAAMJ,IAAGJ,IAAGK,EAAC;AAAA,IAC3B,GAAG,EAAE,wBAAwBC;AAAA,EAC/B;AACA,SAAOH,GAAE,OAAOA,GAAE;AACpB;AACA,SAAS,EAAE,GAAGF,IAAG;AACf,MAAIC,KAAI,EAAE,KAAK,CAAC;AAChB,GAAC,EAAE,OAAO,EAAEA,GAAE,KAAKD,EAAC,MAAMC,GAAE,KAAK,GAAGA,GAAE,IAAID,IAAG,EAAE,IAAI,IAAI,KAAKC,EAAC;AAC/D;AACA,SAAS,EAAE,GAAGD,IAAG;AACf,MAAIC,KAAI,EAAE,KAAK,CAAC;AAChB,GAAC,EAAE,OAAO,EAAEA,GAAE,KAAKD,EAAC,MAAMC,GAAE,KAAK,GAAGA,GAAE,IAAID,IAAG,EAAE,IAAI,KAAKC,EAAC;AAC3D;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,IAAI,GAAG,EAAE,WAAY;AAC1B,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,EAAE,GAAGF,IAAGK,IAAG;AAClB,MAAI,GAAG,EAAE,WAAY;AACnB,QAAI,cAAc,OAAO,GAAG;AAC1B,UAAIA,KAAI,EAAEL,GAAE,CAAC;AACb,aAAO,WAAY;AACjB,UAAE,IAAI,GAAGK,MAAK,cAAc,OAAOA,MAAKA,GAAE;AAAA,MAC5C;AAAA,IACF;AACA,QAAI,EAAG,QAAO,EAAE,UAAUL,GAAE,GAAG,WAAY;AACzC,aAAO,EAAE,UAAU;AAAA,IACrB;AAAA,EACF,GAAG,QAAQK,KAAIA,KAAIA,GAAE,OAAO,CAAC,CAAC;AAChC;AACA,SAAS,EAAE,GAAGA,IAAG;AACf,MAAIJ,KAAI,EAAE,KAAK,CAAC;AAChB,SAAO,EAAEA,GAAE,KAAKI,EAAC,MAAMJ,GAAE,KAAK,EAAE,GAAGA,GAAE,MAAMI,IAAGJ,GAAE,MAAM,IAAIA,GAAE;AAC9D;AACA,SAAS,EAAE,GAAGD,IAAG;AACf,SAAO,IAAI,GAAG,EAAE,WAAY;AAC1B,WAAO;AAAA,EACT,GAAGA,EAAC;AACN;AACA,SAAS,EAAE,GAAG;AACZ,MAAIC,KAAI,EAAE,QAAQ,EAAE,GAAG,GACrBC,KAAI,EAAE,KAAK,CAAC;AACd,SAAOA,GAAE,IAAI,GAAGD,MAAK,QAAQC,GAAE,OAAOA,GAAE,KAAK,MAAID,GAAE,IAAI,CAAC,IAAIA,GAAE,MAAM,SAAS,EAAE;AACjF;AACA,SAAS,EAAE,GAAGD,IAAG;AACf,IAAE,iBAAiB,EAAE,cAAcA,KAAIA,GAAE,CAAC,IAAI,CAAC;AACjD;AACA,SAAS,EAAE,GAAG;AACZ,MAAIC,KAAI,EAAE,KAAK,EAAE,GACfC,KAAI,EAAE;AACR,SAAOD,GAAE,KAAK,GAAG,EAAE,sBAAsB,EAAE,oBAAoB,SAAUG,IAAGJ,IAAG;AAC7E,IAAAC,GAAE,MAAMA,GAAE,GAAGG,IAAGJ,EAAC,GAAGE,GAAE,CAAC,EAAEE,EAAC;AAAA,EAC5B,IAAI,CAACF,GAAE,CAAC,GAAG,WAAY;AACrB,IAAAA,GAAE,CAAC,EAAE,MAAM;AAAA,EACb,CAAC;AACH;AACA,SAAS,IAAI;AACX,MAAI,IAAI,EAAE,KAAK,EAAE;AACjB,MAAI,CAAC,EAAE,IAAI;AACT,aAASD,KAAI,EAAE,KAAK,SAASA,MAAK,CAACA,GAAE,OAAO,SAASA,GAAE,KAAK,CAAAA,KAAIA,GAAE;AAClE,QAAIC,KAAID,GAAE,QAAQA,GAAE,MAAM,CAAC,GAAG,CAAC;AAC/B,MAAE,KAAK,MAAMC,GAAE,CAAC,IAAI,MAAMA,GAAE,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE;AACX;AACA,SAAS,IAAI;AACX,WAAS,GAAG,IAAI,EAAE,MAAM,IAAI,KAAI,EAAE,OAAO,EAAE,IAAK,KAAI;AAClD,MAAE,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC;AAAA,EAC3D,SAASF,IAAG;AACV,MAAE,IAAI,MAAM,CAAC,GAAG,EAAE,IAAIA,IAAG,EAAE,GAAG;AAAA,EAChC;AACF;AACA,EAAE,MAAM,SAAU,GAAG;AACnB,MAAI,MAAM,KAAK,EAAE,CAAC;AACpB,GAAG,EAAE,KAAK,SAAU,GAAGA,IAAG;AACxB,OAAKA,GAAE,OAAOA,GAAE,IAAI,QAAQ,EAAE,MAAMA,GAAE,IAAI,MAAM,KAAK,EAAE,GAAGA,EAAC;AAC7D,GAAG,EAAE,MAAM,SAAU,GAAG;AACtB,OAAK,EAAE,CAAC,GAAG,IAAI;AACf,MAAIE,MAAK,IAAI,EAAE,KAAK;AACpB,EAAAA,OAAM,MAAM,KAAKA,GAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAGA,GAAE,GAAG,QAAQ,SAAUE,IAAG;AACjE,IAAAA,GAAE,QAAQA,GAAE,KAAKA,GAAE,MAAMA,GAAE,IAAIA,GAAE,MAAM;AAAA,EACzC,CAAC,MAAMF,GAAE,IAAI,QAAQ,CAAC,GAAGA,GAAE,IAAI,QAAQ,CAAC,GAAGA,GAAE,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI;AACtE,GAAG,EAAE,SAAS,SAAU,GAAG;AACzB,OAAK,EAAE,CAAC;AACR,MAAIF,KAAI,EAAE;AACV,EAAAA,MAAKA,GAAE,QAAQA,GAAE,IAAI,IAAI,WAAW,MAAM,EAAE,KAAKA,EAAC,KAAK,MAAM,EAAE,2BAA2B,IAAI,EAAE,0BAA0B,GAAG,CAAC,IAAIA,GAAE,IAAI,GAAG,QAAQ,SAAUI,IAAG;AAC9J,IAAAA,GAAE,MAAMA,GAAE,MAAMA,GAAE,IAAIA,GAAE,IAAI;AAAA,EAC9B,CAAC,IAAI,IAAI,IAAI;AACf,GAAG,EAAE,MAAM,SAAU,GAAGJ,IAAG;AACzB,EAAAA,GAAE,KAAK,SAAUI,IAAG;AAClB,QAAI;AACF,MAAAA,GAAE,IAAI,QAAQ,CAAC,GAAGA,GAAE,MAAMA,GAAE,IAAI,OAAO,SAAUA,IAAG;AAClD,eAAO,CAACA,GAAE,MAAM,EAAEA,EAAC;AAAA,MACrB,CAAC;AAAA,IACH,SAASC,IAAG;AACV,MAAAL,GAAE,KAAK,SAAUI,IAAG;AAClB,QAAAA,GAAE,QAAQA,GAAE,MAAM,CAAC;AAAA,MACrB,CAAC,GAAGJ,KAAI,CAAC,GAAG,EAAE,IAAIK,IAAGD,GAAE,GAAG;AAAA,IAC5B;AAAA,EACF,CAAC,GAAGL,MAAKA,GAAE,GAAGC,EAAC;AACjB,GAAG,EAAE,UAAU,SAAU,GAAG;AAC1B,OAAK,EAAE,CAAC;AACR,MAAIA,IACFK,KAAI,EAAE;AACR,EAAAA,MAAKA,GAAE,QAAQA,GAAE,IAAI,GAAG,QAAQ,SAAUD,IAAG;AAC3C,QAAI;AACF,QAAEA,EAAC;AAAA,IACL,SAASA,IAAG;AACV,MAAAJ,KAAII;AAAA,IACN;AAAA,EACF,CAAC,GAAGC,GAAE,MAAM,QAAQL,MAAK,EAAE,IAAIA,IAAGK,GAAE,GAAG;AACzC;AACA,IAAI,IAAI,cAAc,OAAO;AAC7B,SAAS,EAAE,GAAG;AACZ,MAAIL,IACFK,KAAI,WAAY;AACd,iBAAaJ,EAAC,GAAG,KAAK,qBAAqBD,EAAC,GAAG,WAAW,CAAC;AAAA,EAC7D,GACAC,KAAI,WAAWI,IAAG,GAAG;AACvB,QAAML,KAAI,sBAAsBK,EAAC;AACnC;AACA,SAAS,EAAE,GAAG;AACZ,MAAIL,KAAI,GACNC,KAAI,EAAE;AACR,gBAAc,OAAOA,OAAM,EAAE,MAAM,QAAQA,GAAE,IAAI,IAAID;AACvD;AACA,SAAS,EAAE,GAAG;AACZ,MAAIA,KAAI;AACR,IAAE,MAAM,EAAE,GAAG,GAAG,IAAIA;AACtB;AACA,SAAS,EAAE,GAAGA,IAAG;AACf,SAAO,CAAC,KAAK,EAAE,WAAWA,GAAE,UAAUA,GAAE,KAAK,SAAUA,IAAGK,IAAG;AAC3D,WAAOL,OAAM,EAAEK,EAAC;AAAA,EAClB,CAAC;AACH;AACA,SAAS,EAAE,GAAGL,IAAG;AACf,SAAO,cAAc,OAAOA,KAAIA,GAAE,CAAC,IAAIA;AACzC;", "names": ["l", "t", "u", "i", "o", "n", "r", "f", "c", "e"]}