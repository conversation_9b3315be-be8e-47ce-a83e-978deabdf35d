import {
  addDays,
  addMonths,
  addSeconds,
  addYears,
  format,
  formatISO,
  getDate,
  getDay,
  getDaysInMonth,
  getHours,
  getMinutes,
  getMonth,
  getSeconds,
  getYear,
  isDate,
  isValid,
  parse,
  parseISO,
  set
} from "./chunk-PXDVII2B.js";
import "./chunk-VFMQ6EA6.js";
import "./chunk-JEI6OX6H.js";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE
} from "./chunk-KRDPNAD6.js";
import "./chunk-DFX3ICCE.js";
import "./chunk-HO3US6ZP.js";
import "./chunk-LTOY44VK.js";
import "./chunk-QNDS3557.js";
import "./chunk-LCLHCW2F.js";
import "./chunk-DDFHEOER.js";
import "./chunk-V2YTM4XE.js";
import "./chunk-LIZ7XJXB.js";
import "./chunk-ENNE42C5.js";
import "./chunk-T2PAMC2C.js";
import "./chunk-5ZHNVRCV.js";
import "./chunk-SXVN3P42.js";
import "./chunk-HC7RFOC2.js";
import "./chunk-LRV4CD2M.js";
import "./chunk-UR6QSRX7.js";
import "./chunk-ZXCMRPRW.js";
import "./chunk-KCHFD3L6.js";
import "./chunk-SUBOW5CX.js";
import "./chunk-AHSZ23XV.js";
import "./chunk-FRDDUA6J.js";
import "./chunk-U5HOQPPH.js";
import {
  Injectable,
  NgModule,
  inject,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-H4Q5M6BP.js";
import "./chunk-YH7VELCU.js";
import "./chunk-KBUIKKCC.js";

// node_modules/.pnpm/@angular+material-date-fns-_e379aa840d14dba98d552c37ea18fde2/node_modules/@angular/material-date-fns-adapter/fesm2022/material-date-fns-adapter.mjs
function range(length, valueFunction) {
  const valuesArray = Array(length);
  for (let i = 0; i < length; i++) {
    valuesArray[i] = valueFunction(i);
  }
  return valuesArray;
}
var MONTH_FORMATS = {
  long: "LLLL",
  short: "LLL",
  narrow: "LLLLL"
};
var DAY_OF_WEEK_FORMATS = {
  long: "EEEE",
  short: "EEE",
  narrow: "EEEEE"
};
var DateFnsAdapter = class _DateFnsAdapter extends DateAdapter {
  constructor() {
    super();
    const matDateLocale = inject(MAT_DATE_LOCALE, {
      optional: true
    });
    this.setLocale(matDateLocale);
  }
  getYear(date) {
    return getYear(date);
  }
  getMonth(date) {
    return getMonth(date);
  }
  getDate(date) {
    return getDate(date);
  }
  getDayOfWeek(date) {
    return getDay(date);
  }
  getMonthNames(style) {
    const pattern = MONTH_FORMATS[style];
    return range(12, (i) => this.format(new Date(2017, i, 1), pattern));
  }
  getDateNames() {
    const dtf = typeof Intl !== "undefined" ? new Intl.DateTimeFormat(this.locale.code, {
      day: "numeric",
      timeZone: "utc"
    }) : null;
    return range(31, (i) => {
      if (dtf) {
        const date = /* @__PURE__ */ new Date();
        date.setUTCFullYear(2017, 0, i + 1);
        date.setUTCHours(0, 0, 0, 0);
        return dtf.format(date).replace(/[\u200e\u200f]/g, "");
      }
      return i + "";
    });
  }
  getDayOfWeekNames(style) {
    const pattern = DAY_OF_WEEK_FORMATS[style];
    return range(7, (i) => this.format(new Date(2017, 0, i + 1), pattern));
  }
  getYearName(date) {
    return this.format(date, "y");
  }
  getFirstDayOfWeek() {
    return this.locale.options?.weekStartsOn ?? 0;
  }
  getNumDaysInMonth(date) {
    return getDaysInMonth(date);
  }
  clone(date) {
    return new Date(date.getTime());
  }
  createDate(year, month, date) {
    if (typeof ngDevMode === "undefined" || ngDevMode) {
      if (month < 0 || month > 11) {
        throw Error(`Invalid month index "${month}". Month index has to be between 0 and 11.`);
      }
      if (date < 1) {
        throw Error(`Invalid date "${date}". Date has to be greater than 0.`);
      }
    }
    const result = /* @__PURE__ */ new Date();
    result.setFullYear(year, month, date);
    result.setHours(0, 0, 0, 0);
    if (result.getMonth() != month && (typeof ngDevMode === "undefined" || ngDevMode)) {
      throw Error(`Invalid date "${date}" for month with index "${month}".`);
    }
    return result;
  }
  today() {
    return /* @__PURE__ */ new Date();
  }
  parse(value, parseFormat) {
    if (typeof value == "string" && value.length > 0) {
      const iso8601Date = parseISO(value);
      if (this.isValid(iso8601Date)) {
        return iso8601Date;
      }
      const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];
      if (!parseFormat.length) {
        throw Error("Formats array must not be empty.");
      }
      for (const currentFormat of formats) {
        const fromFormat = parse(value, currentFormat, /* @__PURE__ */ new Date(), {
          locale: this.locale
        });
        if (this.isValid(fromFormat)) {
          return fromFormat;
        }
      }
      return this.invalid();
    } else if (typeof value === "number") {
      return new Date(value);
    } else if (value instanceof Date) {
      return this.clone(value);
    }
    return null;
  }
  format(date, displayFormat) {
    if (!this.isValid(date)) {
      throw Error("DateFnsAdapter: Cannot format invalid date.");
    }
    return format(date, displayFormat, {
      locale: this.locale
    });
  }
  addCalendarYears(date, years) {
    return addYears(date, years);
  }
  addCalendarMonths(date, months) {
    return addMonths(date, months);
  }
  addCalendarDays(date, days) {
    return addDays(date, days);
  }
  toIso8601(date) {
    return formatISO(date, {
      representation: "date"
    });
  }
  /**
   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings
   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an
   * invalid date for all other values.
   */
  deserialize(value) {
    if (typeof value === "string") {
      if (!value) {
        return null;
      }
      const date = parseISO(value);
      if (this.isValid(date)) {
        return date;
      }
    }
    return super.deserialize(value);
  }
  isDateInstance(obj) {
    return isDate(obj);
  }
  isValid(date) {
    return isValid(date);
  }
  invalid() {
    return /* @__PURE__ */ new Date(NaN);
  }
  setTime(target, hours, minutes, seconds) {
    if (typeof ngDevMode === "undefined" || ngDevMode) {
      if (hours < 0 || hours > 23) {
        throw Error(`Invalid hours "${hours}". Hours value must be between 0 and 23.`);
      }
      if (minutes < 0 || minutes > 59) {
        throw Error(`Invalid minutes "${minutes}". Minutes value must be between 0 and 59.`);
      }
      if (seconds < 0 || seconds > 59) {
        throw Error(`Invalid seconds "${seconds}". Seconds value must be between 0 and 59.`);
      }
    }
    return set(this.clone(target), {
      hours,
      minutes,
      seconds,
      milliseconds: 0
    });
  }
  getHours(date) {
    return getHours(date);
  }
  getMinutes(date) {
    return getMinutes(date);
  }
  getSeconds(date) {
    return getSeconds(date);
  }
  parseTime(value, parseFormat) {
    return this.parse(value, parseFormat);
  }
  addSeconds(date, amount) {
    return addSeconds(date, amount);
  }
  static ɵfac = function DateFnsAdapter_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DateFnsAdapter)();
  };
  static ɵprov = ɵɵdefineInjectable({
    token: _DateFnsAdapter,
    factory: _DateFnsAdapter.ɵfac
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DateFnsAdapter, [{
    type: Injectable
  }], () => [], null);
})();
var MAT_DATE_FNS_FORMATS = {
  parse: {
    dateInput: "P",
    timeInput: "p"
  },
  display: {
    dateInput: "P",
    timeInput: "p",
    monthYearLabel: "LLL uuuu",
    dateA11yLabel: "PP",
    monthYearA11yLabel: "LLLL uuuu",
    timeOptionLabel: "p"
  }
};
var DateFnsModule = class _DateFnsModule {
  static ɵfac = function DateFnsModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DateFnsModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _DateFnsModule
  });
  static ɵinj = ɵɵdefineInjector({
    providers: [{
      provide: DateAdapter,
      useClass: DateFnsAdapter,
      deps: [MAT_DATE_LOCALE]
    }]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DateFnsModule, [{
    type: NgModule,
    args: [{
      providers: [{
        provide: DateAdapter,
        useClass: DateFnsAdapter,
        deps: [MAT_DATE_LOCALE]
      }]
    }]
  }], null, null);
})();
var MatDateFnsModule = class _MatDateFnsModule {
  static ɵfac = function MatDateFnsModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatDateFnsModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _MatDateFnsModule
  });
  static ɵinj = ɵɵdefineInjector({
    providers: [provideDateFnsAdapter()]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatDateFnsModule, [{
    type: NgModule,
    args: [{
      providers: [provideDateFnsAdapter()]
    }]
  }], null, null);
})();
function provideDateFnsAdapter(formats = MAT_DATE_FNS_FORMATS) {
  return [{
    provide: DateAdapter,
    useClass: DateFnsAdapter,
    deps: [MAT_DATE_LOCALE]
  }, {
    provide: MAT_DATE_FORMATS,
    useValue: formats
  }];
}
export {
  DateFnsAdapter,
  DateFnsModule,
  MAT_DATE_FNS_FORMATS,
  MatDateFnsModule,
  provideDateFnsAdapter
};
//# sourceMappingURL=@angular_material-date-fns-adapter.js.map
