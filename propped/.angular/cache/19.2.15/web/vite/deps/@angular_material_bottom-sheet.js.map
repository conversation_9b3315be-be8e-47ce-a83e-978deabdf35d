{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@angular+material@19.2.18_285bc3c224c839ebf1057ffc6879888d/node_modules/@angular/material/fesm2022/bottom-sheet.mjs"], "sourcesContent": ["import { CdkDialogContainer, Dialog, DialogModule } from '@angular/cdk/dialog';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, Injectable, NgModule } from '@angular/core';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Overlay } from '@angular/cdk/overlay';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, merge } from 'rxjs';\nimport { filter, take } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nfunction MatBottomSheetContainer_ng_template_0_Template(rf, ctx) {}\nconst ENTER_ANIMATION = '_mat-bottom-sheet-enter';\nconst EXIT_ANIMATION = '_mat-bottom-sheet-exit';\n/**\n * Internal component that wraps user-provided bottom sheet content.\n * @docs-private\n */\nclass MatBottomSheetContainer extends CdkDialogContainer {\n  _breakpointSubscription;\n  _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations';\n  /** The state of the bottom sheet animations. */\n  _animationState = 'void';\n  /** Emits whenever the state of the animation changes. */\n  _animationStateChanged = new EventEmitter();\n  /** Whether the component has been destroyed. */\n  _destroyed;\n  constructor() {\n    super();\n    const breakpointObserver = inject(BreakpointObserver);\n    this._breakpointSubscription = breakpointObserver.observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge]).subscribe(() => {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.toggle('mat-bottom-sheet-container-medium', breakpointObserver.isMatched(Breakpoints.Medium));\n      classList.toggle('mat-bottom-sheet-container-large', breakpointObserver.isMatched(Breakpoints.Large));\n      classList.toggle('mat-bottom-sheet-container-xlarge', breakpointObserver.isMatched(Breakpoints.XLarge));\n    });\n  }\n  /** Begin animation of bottom sheet entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      if (this._animationsDisabled) {\n        this._simulateAnimation(ENTER_ANIMATION);\n      }\n    }\n  }\n  /** Begin animation of the bottom sheet exiting from view. */\n  exit() {\n    if (!this._destroyed) {\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      if (this._animationsDisabled) {\n        this._simulateAnimation(EXIT_ANIMATION);\n      }\n    }\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._breakpointSubscription.unsubscribe();\n    this._destroyed = true;\n  }\n  _simulateAnimation(name) {\n    this._ngZone.run(() => {\n      this._handleAnimationEvent(true, name);\n      setTimeout(() => this._handleAnimationEvent(false, name));\n    });\n  }\n  _trapFocus() {\n    // The bottom sheet starts off-screen and animates in, and at the same time we trap focus\n    // within it. With some styles this appears to cause the page to jump around. See:\n    // https://github.com/angular/components/issues/30774. Preventing the browser from\n    // scrolling resolves the issue and isn't really necessary since the bottom sheet\n    // normally isn't scrollable.\n    super._trapFocus({\n      preventScroll: true\n    });\n  }\n  _handleAnimationEvent(isStart, animationName) {\n    const isEnter = animationName === ENTER_ANIMATION;\n    const isExit = animationName === EXIT_ANIMATION;\n    if (isEnter || isExit) {\n      this._animationStateChanged.emit({\n        toState: isEnter ? 'visible' : 'hidden',\n        phase: isStart ? 'start' : 'done'\n      });\n    }\n  }\n  static ɵfac = function MatBottomSheetContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatBottomSheetContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatBottomSheetContainer,\n    selectors: [[\"mat-bottom-sheet-container\"]],\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-bottom-sheet-container\"],\n    hostVars: 9,\n    hostBindings: function MatBottomSheetContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"animationstart\", function MatBottomSheetContainer_animationstart_HostBindingHandler($event) {\n          return ctx._handleAnimationEvent(true, $event.animationName);\n        })(\"animationend\", function MatBottomSheetContainer_animationend_HostBindingHandler($event) {\n          return ctx._handleAnimationEvent(false, $event.animationName);\n        })(\"animationcancel\", function MatBottomSheetContainer_animationcancel_HostBindingHandler($event) {\n          return ctx._handleAnimationEvent(false, $event.animationName);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-label\", ctx._config.ariaLabel);\n        i0.ɵɵclassProp(\"mat-bottom-sheet-container-animations-enabled\", !ctx._animationsDisabled)(\"mat-bottom-sheet-container-enter\", ctx._animationState === \"visible\")(\"mat-bottom-sheet-container-exit\", ctx._animationState === \"hidden\");\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkPortalOutlet\", \"\"]],\n    template: function MatBottomSheetContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatBottomSheetContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\"@keyframes _mat-bottom-sheet-enter{from{transform:translateY(100%)}to{transform:none}}@keyframes _mat-bottom-sheet-exit{from{transform:none}to{transform:translateY(100%)}}.mat-bottom-sheet-container{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);padding:8px 16px;min-width:100vw;box-sizing:border-box;display:block;outline:0;max-height:80vh;overflow:auto;position:relative;background:var(--mat-bottom-sheet-container-background-color, var(--mat-sys-surface-container-low));color:var(--mat-bottom-sheet-container-text-color, var(--mat-sys-on-surface));font-family:var(--mat-bottom-sheet-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-bottom-sheet-container-text-size, var(--mat-sys-body-large-size));line-height:var(--mat-bottom-sheet-container-text-line-height, var(--mat-sys-body-large-line-height));font-weight:var(--mat-bottom-sheet-container-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-bottom-sheet-container-text-tracking, var(--mat-sys-body-large-tracking))}@media(forced-colors: active){.mat-bottom-sheet-container{outline:1px solid}}.mat-bottom-sheet-container-animations-enabled{transform:translateY(100%)}.mat-bottom-sheet-container-animations-enabled.mat-bottom-sheet-container-enter{animation:_mat-bottom-sheet-enter 195ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-bottom-sheet-container-animations-enabled.mat-bottom-sheet-container-exit{animation:_mat-bottom-sheet-exit 375ms cubic-bezier(0.4, 0, 1, 1) backwards}.mat-bottom-sheet-container-xlarge,.mat-bottom-sheet-container-large,.mat-bottom-sheet-container-medium{border-top-left-radius:var(--mat-bottom-sheet-container-shape, 28px);border-top-right-radius:var(--mat-bottom-sheet-container-shape, 28px)}.mat-bottom-sheet-container-medium{min-width:384px;max-width:calc(100vw - 128px)}.mat-bottom-sheet-container-large{min-width:512px;max-width:calc(100vw - 256px)}.mat-bottom-sheet-container-xlarge{min-width:576px;max-width:calc(100vw - 384px)}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBottomSheetContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-bottom-sheet-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mat-bottom-sheet-container',\n        '[class.mat-bottom-sheet-container-animations-enabled]': '!_animationsDisabled',\n        '[class.mat-bottom-sheet-container-enter]': '_animationState === \"visible\"',\n        '[class.mat-bottom-sheet-container-exit]': '_animationState === \"hidden\"',\n        'tabindex': '-1',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '(animationstart)': '_handleAnimationEvent(true, $event.animationName)',\n        '(animationend)': '_handleAnimationEvent(false, $event.animationName)',\n        '(animationcancel)': '_handleAnimationEvent(false, $event.animationName)'\n      },\n      imports: [CdkPortalOutlet],\n      template: \"<ng-template cdkPortalOutlet></ng-template>\\r\\n\",\n      styles: [\"@keyframes _mat-bottom-sheet-enter{from{transform:translateY(100%)}to{transform:none}}@keyframes _mat-bottom-sheet-exit{from{transform:none}to{transform:translateY(100%)}}.mat-bottom-sheet-container{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);padding:8px 16px;min-width:100vw;box-sizing:border-box;display:block;outline:0;max-height:80vh;overflow:auto;position:relative;background:var(--mat-bottom-sheet-container-background-color, var(--mat-sys-surface-container-low));color:var(--mat-bottom-sheet-container-text-color, var(--mat-sys-on-surface));font-family:var(--mat-bottom-sheet-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-bottom-sheet-container-text-size, var(--mat-sys-body-large-size));line-height:var(--mat-bottom-sheet-container-text-line-height, var(--mat-sys-body-large-line-height));font-weight:var(--mat-bottom-sheet-container-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-bottom-sheet-container-text-tracking, var(--mat-sys-body-large-tracking))}@media(forced-colors: active){.mat-bottom-sheet-container{outline:1px solid}}.mat-bottom-sheet-container-animations-enabled{transform:translateY(100%)}.mat-bottom-sheet-container-animations-enabled.mat-bottom-sheet-container-enter{animation:_mat-bottom-sheet-enter 195ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-bottom-sheet-container-animations-enabled.mat-bottom-sheet-container-exit{animation:_mat-bottom-sheet-exit 375ms cubic-bezier(0.4, 0, 1, 1) backwards}.mat-bottom-sheet-container-xlarge,.mat-bottom-sheet-container-large,.mat-bottom-sheet-container-medium{border-top-left-radius:var(--mat-bottom-sheet-container-shape, 28px);border-top-right-radius:var(--mat-bottom-sheet-container-shape, 28px)}.mat-bottom-sheet-container-medium{min-width:384px;max-width:calc(100vw - 128px)}.mat-bottom-sheet-container-large{min-width:512px;max-width:calc(100vw - 256px)}.mat-bottom-sheet-container-xlarge{min-width:576px;max-width:calc(100vw - 384px)}\\n\"]\n    }]\n  }], () => [], null);\n})();\n\n/** Injection token that can be used to access the data that was passed in to a bottom sheet. */\nconst MAT_BOTTOM_SHEET_DATA = new InjectionToken('MatBottomSheetData');\n/**\n * Configuration used when opening a bottom sheet.\n */\nclass MatBottomSheetConfig {\n  /** The view container to place the overlay for the bottom sheet into. */\n  viewContainerRef;\n  /** Extra CSS classes to be added to the bottom sheet container. */\n  panelClass;\n  /** Text layout direction for the bottom sheet. */\n  direction;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Whether the bottom sheet has a backdrop. */\n  hasBackdrop = true;\n  /** Custom class for the backdrop. */\n  backdropClass;\n  /** Whether the user can use escape or clicking outside to close the bottom sheet. */\n  disableClose = false;\n  /** Aria label to assign to the bottom sheet element. */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Whether the bottom sheet should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation = true;\n  /**\n   * Where the bottom sheet should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the bottom sheet should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus = true;\n  /** Scroll strategy to be used for the bottom sheet. */\n  scrollStrategy;\n  /** Height for the bottom sheet. */\n  height = '';\n  /** Minimum height for the bottom sheet. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Maximum height for the bottom sheet. If a number is provided, assumes pixel units. */\n  maxHeight;\n}\n\n/**\n * Reference to a bottom sheet dispatched from the bottom sheet service.\n */\nclass MatBottomSheetRef {\n  _ref;\n  /** Instance of the component making up the content of the bottom sheet. */\n  get instance() {\n    return this._ref.componentInstance;\n  }\n  /**\n   * `ComponentRef` of the component opened into the bottom sheet. Will be\n   * null when the bottom sheet is opened using a `TemplateRef`.\n   */\n  get componentRef() {\n    return this._ref.componentRef;\n  }\n  /**\n   * Instance of the component into which the bottom sheet content is projected.\n   * @docs-private\n   */\n  containerInstance;\n  /** Whether the user is allowed to close the bottom sheet. */\n  disableClose;\n  /** Subject for notifying the user that the bottom sheet has opened and appeared. */\n  _afterOpened = new Subject();\n  /** Result to be passed down to the `afterDismissed` stream. */\n  _result;\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  _closeFallbackTimeout;\n  constructor(_ref, config, containerInstance) {\n    this._ref = _ref;\n    this.containerInstance = containerInstance;\n    this.disableClose = config.disableClose;\n    // Emit when opening animation completes\n    containerInstance._animationStateChanged.pipe(filter(event => event.phase === 'done' && event.toState === 'visible'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    containerInstance._animationStateChanged.pipe(filter(event => event.phase === 'done' && event.toState === 'hidden'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._ref.close(this._result);\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._ref.close(this._result);\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE))).subscribe(event => {\n      if (!this.disableClose && (event.type !== 'keydown' || !hasModifierKey(event))) {\n        event.preventDefault();\n        this.dismiss();\n      }\n    });\n  }\n  /**\n   * Dismisses the bottom sheet.\n   * @param result Data to be passed back to the bottom sheet opener.\n   */\n  dismiss(result) {\n    if (!this.containerInstance) {\n      return;\n    }\n    // Transition the backdrop in parallel to the bottom sheet.\n    this.containerInstance._animationStateChanged.pipe(filter(event => event.phase === 'start'), take(1)).subscribe(() => {\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has fired.\n      this._closeFallbackTimeout = setTimeout(() => this._ref.close(this._result), 500);\n      this._ref.overlayRef.detachBackdrop();\n    });\n    this._result = result;\n    this.containerInstance.exit();\n    this.containerInstance = null;\n  }\n  /** Gets an observable that is notified when the bottom sheet is finished closing. */\n  afterDismissed() {\n    return this._ref.closed;\n  }\n  /** Gets an observable that is notified when the bottom sheet has opened and appeared. */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n}\n\n/** Injection token that can be used to specify default bottom sheet options. */\nconst MAT_BOTTOM_SHEET_DEFAULT_OPTIONS = new InjectionToken('mat-bottom-sheet-default-options');\n/**\n * Service to trigger Material Design bottom sheets.\n */\nclass MatBottomSheet {\n  _overlay = inject(Overlay);\n  _parentBottomSheet = inject(MatBottomSheet, {\n    optional: true,\n    skipSelf: true\n  });\n  _defaultOptions = inject(MAT_BOTTOM_SHEET_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _bottomSheetRefAtThisLevel = null;\n  _dialog = inject(Dialog);\n  /** Reference to the currently opened bottom sheet. */\n  get _openedBottomSheetRef() {\n    const parent = this._parentBottomSheet;\n    return parent ? parent._openedBottomSheetRef : this._bottomSheetRefAtThisLevel;\n  }\n  set _openedBottomSheetRef(value) {\n    if (this._parentBottomSheet) {\n      this._parentBottomSheet._openedBottomSheetRef = value;\n    } else {\n      this._bottomSheetRefAtThisLevel = value;\n    }\n  }\n  constructor() {}\n  open(componentOrTemplateRef, config) {\n    const _config = {\n      ...(this._defaultOptions || new MatBottomSheetConfig()),\n      ...config\n    };\n    let ref;\n    this._dialog.open(componentOrTemplateRef, {\n      ..._config,\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on detachments so that we can sync up the animation.\n      closeOnOverlayDetachments: false,\n      maxWidth: '100%',\n      container: MatBottomSheetContainer,\n      scrollStrategy: _config.scrollStrategy || this._overlay.scrollStrategies.block(),\n      positionStrategy: this._overlay.position().global().centerHorizontally().bottom('0'),\n      templateContext: () => ({\n        bottomSheetRef: ref\n      }),\n      providers: (cdkRef, _cdkConfig, container) => {\n        ref = new MatBottomSheetRef(cdkRef, _config, container);\n        return [{\n          provide: MatBottomSheetRef,\n          useValue: ref\n        }, {\n          provide: MAT_BOTTOM_SHEET_DATA,\n          useValue: _config.data\n        }];\n      }\n    });\n    // When the bottom sheet is dismissed, clear the reference to it.\n    ref.afterDismissed().subscribe(() => {\n      // Clear the bottom sheet ref if it hasn't already been replaced by a newer one.\n      if (this._openedBottomSheetRef === ref) {\n        this._openedBottomSheetRef = null;\n      }\n    });\n    if (this._openedBottomSheetRef) {\n      // If a bottom sheet is already in view, dismiss it and enter the\n      // new bottom sheet after exit animation is complete.\n      this._openedBottomSheetRef.afterDismissed().subscribe(() => ref.containerInstance?.enter());\n      this._openedBottomSheetRef.dismiss();\n    } else {\n      // If no bottom sheet is in view, enter the new bottom sheet.\n      ref.containerInstance.enter();\n    }\n    this._openedBottomSheetRef = ref;\n    return ref;\n  }\n  /**\n   * Dismisses the currently-visible bottom sheet.\n   * @param result Data to pass to the bottom sheet instance.\n   */\n  dismiss(result) {\n    if (this._openedBottomSheetRef) {\n      this._openedBottomSheetRef.dismiss(result);\n    }\n  }\n  ngOnDestroy() {\n    if (this._bottomSheetRefAtThisLevel) {\n      this._bottomSheetRefAtThisLevel.dismiss();\n    }\n  }\n  static ɵfac = function MatBottomSheet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatBottomSheet)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatBottomSheet,\n    factory: MatBottomSheet.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBottomSheet, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass MatBottomSheetModule {\n  static ɵfac = function MatBottomSheetModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatBottomSheetModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatBottomSheetModule,\n    imports: [DialogModule, MatCommonModule, PortalModule, MatBottomSheetContainer],\n    exports: [MatBottomSheetContainer, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatBottomSheet],\n    imports: [DialogModule, MatCommonModule, PortalModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBottomSheetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, MatCommonModule, PortalModule, MatBottomSheetContainer],\n      exports: [MatBottomSheetContainer, MatCommonModule],\n      providers: [MatBottomSheet]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material bottom sheet.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst matBottomSheetAnimations = {\n  // Represents the output of:\n  // trigger('state', [\n  //   state('void, hidden', style({transform: 'translateY(100%)'})),\n  //   state('visible', style({transform: 'translateY(0%)'})),\n  //   transition(\n  //     'visible => void, visible => hidden',\n  //     group([\n  //       animate('375ms cubic-bezier(0.4, 0, 1, 1)'),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //   ),\n  //   transition(\n  //     'void => visible',\n  //     group([\n  //       animate('195ms cubic-bezier(0, 0, 0.2, 1)'),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //   ),\n  // ])\n  /** Animation that shows and hides a bottom sheet. */\n  bottomSheetState: {\n    type: 7,\n    name: 'state',\n    definitions: [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(100%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'visible => void, visible => hidden',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: null,\n          timings: '375ms cubic-bezier(0.4, 0, 1, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => visible',\n      animation: {\n        type: 3,\n        steps: [{\n          type: 4,\n          styles: null,\n          timings: '195ms cubic-bezier(0, 0, 0.2, 1)'\n        }, {\n          type: 11,\n          selector: '@*',\n          animation: {\n            type: 9,\n            options: null\n          },\n          options: {\n            optional: true\n          }\n        }],\n        options: null\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_BOTTOM_SHEET_DATA, MAT_BOTTOM_SHEET_DEFAULT_OPTIONS, MatBottomSheet, MatBottomSheetConfig, MatBottomSheetContainer, MatBottomSheetModule, MatBottomSheetRef, matBottomSheetAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AAKvB,IAAM,0BAAN,MAAM,iCAAgC,mBAAmB;AAAA,EACvD;AAAA,EACA,sBAAsB,OAAO,uBAAuB;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC,MAAM;AAAA;AAAA,EAEP,kBAAkB;AAAA;AAAA,EAElB,yBAAyB,IAAI,aAAa;AAAA;AAAA,EAE1C;AAAA,EACA,cAAc;AACZ,UAAM;AACN,UAAM,qBAAqB,OAAO,kBAAkB;AACpD,SAAK,0BAA0B,mBAAmB,QAAQ,CAAC,YAAY,QAAQ,YAAY,OAAO,YAAY,MAAM,CAAC,EAAE,UAAU,MAAM;AACrI,YAAM,YAAY,KAAK,YAAY,cAAc;AACjD,gBAAU,OAAO,qCAAqC,mBAAmB,UAAU,YAAY,MAAM,CAAC;AACtG,gBAAU,OAAO,oCAAoC,mBAAmB,UAAU,YAAY,KAAK,CAAC;AACpG,gBAAU,OAAO,qCAAqC,mBAAmB,UAAU,YAAY,MAAM,CAAC;AAAA,IACxG,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AACrC,WAAK,mBAAmB,cAAc;AACtC,UAAI,KAAK,qBAAqB;AAC5B,aAAK,mBAAmB,eAAe;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,YAAY,cAAc,aAAa,YAAY,EAAE;AAC1D,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AACrC,UAAI,KAAK,qBAAqB;AAC5B,aAAK,mBAAmB,cAAc;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,wBAAwB,YAAY;AACzC,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB,MAAM;AACvB,SAAK,QAAQ,IAAI,MAAM;AACrB,WAAK,sBAAsB,MAAM,IAAI;AACrC,iBAAW,MAAM,KAAK,sBAAsB,OAAO,IAAI,CAAC;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AAMX,UAAM,WAAW;AAAA,MACf,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,SAAS,eAAe;AAC5C,UAAM,UAAU,kBAAkB;AAClC,UAAM,SAAS,kBAAkB;AACjC,QAAI,WAAW,QAAQ;AACrB,WAAK,uBAAuB,KAAK;AAAA,QAC/B,SAAS,UAAU,YAAY;AAAA,QAC/B,OAAO,UAAU,UAAU;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,IAC1C,WAAW,CAAC,YAAY,MAAM,GAAG,4BAA4B;AAAA,IAC7D,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,kBAAkB,SAAS,0DAA0D,QAAQ;AACzG,iBAAO,IAAI,sBAAsB,MAAM,OAAO,aAAa;AAAA,QAC7D,CAAC,EAAE,gBAAgB,SAAS,wDAAwD,QAAQ;AAC1F,iBAAO,IAAI,sBAAsB,OAAO,OAAO,aAAa;AAAA,QAC9D,CAAC,EAAE,mBAAmB,SAAS,2DAA2D,QAAQ;AAChG,iBAAO,IAAI,sBAAsB,OAAO,OAAO,aAAa;AAAA,QAC9D,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,QAAQ,IAAI,EAAE,cAAc,IAAI,QAAQ,SAAS,EAAE,cAAc,IAAI,QAAQ,SAAS;AACjH,QAAG,YAAY,iDAAiD,CAAC,IAAI,mBAAmB,EAAE,oCAAoC,IAAI,oBAAoB,SAAS,EAAE,mCAAmC,IAAI,oBAAoB,QAAQ;AAAA,MACtO;AAAA,IACF;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,CAAC;AAAA,IAChC,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,eAAe,CAAC;AAAA,MACzF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,+/DAA+/D;AAAA,IACxgE,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,yDAAyD;AAAA,QACzD,4CAA4C;AAAA,QAC5C,2CAA2C;AAAA,QAC3C,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA,MACV,QAAQ,CAAC,+/DAA+/D;AAAA,IAC1gE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,wBAAwB,IAAI,eAAe,oBAAoB;AAIrE,IAAM,uBAAN,MAA2B;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,cAAc;AAAA;AAAA,EAEd;AAAA;AAAA,EAEA,eAAe;AAAA;AAAA,EAEf,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,eAAe;AAAA;AAAA,EAEf;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET;AAAA;AAAA,EAEA;AACF;AAKA,IAAM,oBAAN,MAAwB;AAAA,EACtB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,MAAM,QAAQ,mBAAmB;AAC3C,SAAK,OAAO;AACZ,SAAK,oBAAoB;AACzB,SAAK,eAAe,OAAO;AAE3B,sBAAkB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,UAAU,MAAM,YAAY,SAAS,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC7I,WAAK,aAAa,KAAK;AACvB,WAAK,aAAa,SAAS;AAAA,IAC7B,CAAC;AAED,sBAAkB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,UAAU,MAAM,YAAY,QAAQ,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC5I,mBAAa,KAAK,qBAAqB;AACvC,WAAK,KAAK,MAAM,KAAK,OAAO;AAAA,IAC9B,CAAC;AACD,SAAK,WAAW,YAAY,EAAE,UAAU,MAAM;AAC5C,WAAK,KAAK,MAAM,KAAK,OAAO;AAAA,IAC9B,CAAC;AACD,UAAM,KAAK,cAAc,GAAG,KAAK,cAAc,EAAE,KAAK,OAAO,WAAS,MAAM,YAAY,MAAM,CAAC,CAAC,EAAE,UAAU,WAAS;AACnH,UAAI,CAAC,KAAK,iBAAiB,MAAM,SAAS,aAAa,CAAC,eAAe,KAAK,IAAI;AAC9E,cAAM,eAAe;AACrB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,QAAQ;AACd,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AAEA,SAAK,kBAAkB,uBAAuB,KAAK,OAAO,WAAS,MAAM,UAAU,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAMpH,WAAK,wBAAwB,WAAW,MAAM,KAAK,KAAK,MAAM,KAAK,OAAO,GAAG,GAAG;AAChF,WAAK,KAAK,WAAW,eAAe;AAAA,IACtC,CAAC;AACD,SAAK,UAAU;AACf,SAAK,kBAAkB,KAAK;AAC5B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;AAGA,IAAM,mCAAmC,IAAI,eAAe,kCAAkC;AAI9F,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,WAAW,OAAO,OAAO;AAAA,EACzB,qBAAqB,OAAO,iBAAgB;AAAA,IAC1C,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,kBAAkB,OAAO,kCAAkC;AAAA,IACzD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,6BAA6B;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,IAAI,wBAAwB;AAC1B,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,wBAAwB,KAAK;AAAA,EACtD;AAAA,EACA,IAAI,sBAAsB,OAAO;AAC/B,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,wBAAwB;AAAA,IAClD,OAAO;AACL,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,KAAK,wBAAwB,QAAQ;AACnC,UAAM,UAAU,kCACV,KAAK,mBAAmB,IAAI,qBAAqB,IAClD;AAEL,QAAI;AACJ,SAAK,QAAQ,KAAK,wBAAwB,iCACrC,UADqC;AAAA;AAAA,MAGxC,cAAc;AAAA;AAAA,MAEd,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,QAAQ,kBAAkB,KAAK,SAAS,iBAAiB,MAAM;AAAA,MAC/E,kBAAkB,KAAK,SAAS,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,OAAO,GAAG;AAAA,MACnF,iBAAiB,OAAO;AAAA,QACtB,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW,CAAC,QAAQ,YAAY,cAAc;AAC5C,cAAM,IAAI,kBAAkB,QAAQ,SAAS,SAAS;AACtD,eAAO,CAAC;AAAA,UACN,SAAS;AAAA,UACT,UAAU;AAAA,QACZ,GAAG;AAAA,UACD,SAAS;AAAA,UACT,UAAU,QAAQ;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF,EAAC;AAED,QAAI,eAAe,EAAE,UAAU,MAAM;AAEnC,UAAI,KAAK,0BAA0B,KAAK;AACtC,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,QAAI,KAAK,uBAAuB;AAG9B,WAAK,sBAAsB,eAAe,EAAE,UAAU,MAAM,IAAI,mBAAmB,MAAM,CAAC;AAC1F,WAAK,sBAAsB,QAAQ;AAAA,IACrC,OAAO;AAEL,UAAI,kBAAkB,MAAM;AAAA,IAC9B;AACA,SAAK,wBAAwB;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,QAAQ;AACd,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB,QAAQ,MAAM;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,QAAQ;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,IACxB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,iBAAiB,cAAc,uBAAuB;AAAA,IAC9E,SAAS,CAAC,yBAAyB,eAAe;AAAA,EACpD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,cAAc;AAAA,IAC1B,SAAS,CAAC,cAAc,iBAAiB,cAAc,eAAe;AAAA,EACxE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,iBAAiB,cAAc,uBAAuB;AAAA,MAC9E,SAAS,CAAC,yBAAyB,eAAe;AAAA,MAClD,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqB/B,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,GAAG;AAAA,UACD,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,QACD,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,GAAG;AAAA,UACD,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,SAAS;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,QACD,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}