# version: '3.9'
services:
  # MySQL Service
  mysql:
    image: mysql:8
    container_name: xyz-mysql
    tty: true
    ports:
      - '33061:3306'
    environment:
      MYSQL_DATABASE: xyz
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      SERVICE_TAGS: prod
      SERVICE_NAME: mysql
    command: ['mysqld', '--mysql-native-password=ON']
    volumes:
      - xyz-mysql-volume:/var/lib/mysql/
    networks:
      - xyz-network

  # Mongo Service
  mongo:
    image: mongo
    container_name: xyz-mongo
    tty: true
    ports:
      - '27019:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD}
    volumes:
      - xyz-mongo-volume:/data/db
    networks:
      - xyz-network

  # Minio Service
  minio:
    image: 'minio/minio:RELEASE.2025-02-07T23-21-09Z'
    container_name: xyz-minio
    tty: true
    ports:
      - '8600:9000'
      - '8601:9001'
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server /data --console-address ":9001"
    volumes:
      - xyz-minio-volume:/data
    networks:
      - xyz-network

  # Mailpit Service
  mailpit:
    image: axllent/mailpit
    container_name: xyz-mailpit
    tty: true
    ports:
      - '10251:1025'
      - '18024:8025'
    networks:
      - xyz-network

# Docker Networks
networks:
  xyz-network:
    driver: bridge

# Volumes
volumes:
  xyz-mysql-volume:
    driver: local
  xyz-mongo-volume:
    driver: local
  xyz-minio-volume:
    driver: local
