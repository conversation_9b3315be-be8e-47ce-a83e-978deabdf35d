import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';

const colorVariant = [
  'primary',
  'secondary',
  'accent',
  'info',
  'success',
  'warning',
  'error',
  'neutral',
  'ghost',
];

const sizeVariant = [
  'xs',
  'sm',
  'md',
  'lg',
  'xl',
  '2xl',
  '3xl',
  '4xl',
  '5xl',
  '6xl',
  '7xl',
  '8xl',
  '9xl',
  '10xl',
];

const makeColorVariant = (prefix: string) => colorVariant.map((color) => `${prefix}-${color}`);
const makeSizeVariant = (prefix: string) => sizeVariant.map((size) => `${prefix}-${size}`);

const classGroups = {
  daisyUI_btn_color: makeColorVariant('btn'),
  daisyUI_btn_size: makeSizeVariant('btn'),
  daisyUI_btn_type: [
    'btn-link',
    'btn-clear-outline',
    'btn-outline',
    'btn-block',
    'btn-circle',
    'btn-square',
    'btn-wide',
    'btn-glass',
    'btn-loading',
    'btn-no-animation',
  ],
  daisyUI_card_type: ['card-bordered', 'card-compact', 'card-side'],
  daisyUI_alert_color: makeColorVariant('alert'),
  daisyUI_badge_color: makeColorVariant('badge'),
  daisyUI_avatar_shape: ['avatar-circle', 'avatar-square'],
  daisyUI_navbar_position: ['navbar-center', 'navbar-start', 'navbar-end'],
  daisyUI_tooltip_position: ['tooltip-top', 'tooltip-bottom', 'tooltip-left', 'tooltip-right'],
  daisyUI_tabs_type: ['tab-bordered', 'tab-lifted'],
  daisyUI_table: ['table', 'table-zebra', 'table-compact'],

  daisyUI_input_color: makeColorVariant('input'),
  daisyUI_input_size: makeSizeVariant('input'),

  daisyUI_textarea_color: makeColorVariant('textarea'),
  daisyUI_textarea_size: makeSizeVariant('textarea'),

  daisyUI_select_color: makeColorVariant('select'),
  daisyUI_select_size: makeSizeVariant('select'),

  daisyUI_checkbox_color: makeColorVariant('checkbox'),
  daisyUI_checkbox_size: makeSizeVariant('checkbox'),

  daisyUI_radio_color: makeColorVariant('radio'),
  daisyUI_radio_size: makeSizeVariant('radio'),

  daisyUI_toggle_color: makeColorVariant('toggle'),
  daisyUI_toggle_size: makeSizeVariant('toggle'),

  daisyUI_progress_color: makeColorVariant('progress'),
  daisyUI_progress_size: makeSizeVariant('progress'),

  daisyUI_spinner_color: makeColorVariant('spinner'),
  daisyUI_spinner_size: makeSizeVariant('spinner'),

  daisyUI_divider_type: ['divider-horizontal', 'divider-vertical'],

  daisyUI_collapse_state: ['collapse-open', 'collapse-close'],
};

export const daisyMerge = (...inputs: ClassValue[]) =>
  extendTailwindMerge({ extend: { classGroups } as any })(clsx(inputs));
