const SCRIPT_REGISTER: Record<string, Promise<void>> = {};

export const loadScript = (
  src: string,
  opts: { async?: boolean; type?: string; [key: string]: any } = {},
): Promise<void> => {
  SCRIPT_REGISTER[src] ??= new Promise((resolve, reject) => {
    opts.type = opts.type || 'text/javascript';
    opts.async = opts.async ?? true;
    const script = document.createElement('script');
    for (const [k, v] of Object.entries(opts)) if (v) script.setAttribute(k, `${v}`);
    const rejectFn = (e: Event) => {
      script.removeEventListener('load', resolveFn);
      reject(e);
    };
    const resolveFn = () => {
      script.removeEventListener('error', rejectFn);
      resolve();
    };
    script.addEventListener('load', resolveFn, { once: true });
    script.addEventListener('error', rejectFn, { once: true });
    script.src = src;
    document.head.appendChild(script);
  });
  return SCRIPT_REGISTER[src];
};

export const loadStyle = (href: string, opts: Record<string, any> = {}): Promise<void> => {
  SCRIPT_REGISTER[href] ??= new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.media = 'print';
    link.setAttribute('defer', 'defer');
    for (const [k, v] of Object.entries(opts)) if (v) link.setAttribute(k, `${v}`);
    const rejectFn = (e: Event) => {
      link.removeEventListener('load', resolveFn);
      reject(e);
    };
    const resolveFn = () => {
      link.removeEventListener('error', rejectFn);
      link.media = 'all';
      resolve();
    };
    link.addEventListener('load', resolveFn, { once: true });
    link.addEventListener('error', rejectFn, { once: true });
    link.href = href;
    document.head.appendChild(link);
  });
  return SCRIPT_REGISTER[href];
};
