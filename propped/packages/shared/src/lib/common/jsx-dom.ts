import htm from 'htm/mini';
import * as jsxDom from 'jsx-dom';
const h = (jsxDom as any).default;

export const html: (strings: TemplateStringsArray, ...values: any[]) => any = htm.bind(
  h.createElement,
);
// export const html = htm.bind(function () {
//   this[0] = 3;
//   // eslint-disable-next-line prefer-spread, prefer-rest-params
//   // biome-ignore lint/style/noArguments: <explanation>
//   return h.createElement.apply(h, arguments);
// });
