import { NgOptimizedImage } from '@angular/common';
import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Component, Injectable, NgZone, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import type { Subscription } from 'rxjs';
import { Subject, firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';

export enum PreviewFileType {
  IMG = 0,
  VID = 1,
  TEXT = 2,
}

export interface PreviewRef {
  close: () => void;
  dialogRef: MatDialogRef<PreviewModalComponent>;
  onClose: Promise<any>;
}

@Component({
  selector: 'app-preview-modal',
  imports: [MatButtonModule, MatDialogModule, NgOptimizedImage],
  template: `<div class="h-full w-full flex flex-col item-center">
    <button mat-stroked-button mat-dialog-close color="warn" style="position:absolute;right:0">
      Close
    </button>
    @if (url) {
      <img class="h-full w-full object-contain" [ngSrc]="url" placeholder fill />
    } @else if (urlVideo) {
      <video controls class="m-auto" autoplay (loadedmetadata)="videoLoaded($event)">
        <source [src]="urlVideo" type="video/mp4" />
      </video>
    }

    @if (text) {
      <div class="w-full flex justify-center ">
        <div
          class="text-sm md:text-3xl text-center p-1 m-8 h-auto w-auto bg-black absolute bottom-48"
        >
          {{ text }}
        </div>
      </div>
    }
  </div> `,
  styles: [
    `
      :host {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    `,
  ],
})
export class PreviewModalComponent implements OnInit, OnDestroy {
  readonly #dialogRef: MatDialogRef<PreviewModalComponent> = inject(MatDialogRef);
  readonly #ngZone: NgZone = inject(NgZone);
  readonly #data: {
    data: Subject<{ value: any; actionType: any }>;
    onOpen: PromiseWithResolvers<PreviewRef>;
  } = inject(MAT_DIALOG_DATA);
  public fileData: any;
  public url?: string;
  public text?: string;
  public urlVideo?: string;
  public start = 0;
  readonly #subscriptions: Record<string, Subscription> = {};

  public ngOnInit() {
    this.#subscriptions['#data'] = this.#data.data.subscribe((fileData) => {
      this.#ngZone.run(() => {
        this.fileData = fileData;
        // this.url = undefined;
        // this.urlVideo = undefined;
        this.setData();
      });
    });
    this.#data.onOpen.resolve({
      close: () => this.#dialogRef.close(),
      dialogRef: this.#dialogRef,
      onClose: firstValueFrom(this.#dialogRef.afterClosed()),
    });
  }

  public videoLoaded({ target: vid }: any) {
    if (this.start) vid.currentTime = this.start / 1000;
  }

  public close() {
    this.#dialogRef.close();
  }

  public setData() {
    this.start = this.fileData.start;
    if (this.fileData.actionType === PreviewFileType.IMG) {
      if (this.fileData.value instanceof File) {
        this.revokeUrl(this.url);
        this.url = URL.createObjectURL(this.fileData.value);
      } else if (this.fileData.value.url) this.url = this.fileData.value.url;
      else this.url = this.fileData.value;
    } else if (this.fileData.actionType === PreviewFileType.VID) {
      if (this.fileData.value instanceof File) {
        this.revokeUrl(this.urlVideo);
        this.urlVideo = URL.createObjectURL(this.fileData.value);
      } else if (this.fileData.value.url) this.urlVideo = this.fileData.value.url;
      else this.urlVideo = this.fileData.value;
    } else if (this.fileData.actionType === PreviewFileType.TEXT) {
      if (this.fileData.value.text) this.text = this.fileData.value.text;
    }
  }

  revokeUrl(url?: string) {
    if (url?.startsWith('blob:')) URL.revokeObjectURL(url);
  }

  ngOnDestroy(): void {
    this.revokeUrl(this.url);
    this.revokeUrl(this.urlVideo);
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    (this as any).#subscriptions = undefined;
  }
}

@Injectable({ providedIn: 'root' })
export class PreviewModalService {
  readonly #dialog = inject(MatDialog);
  readonly #ngZone = inject(NgZone);
  public dialogRef: MatDialogRef<any> | undefined;
  public data = new Subject<{ value: null; actionType: null; start?: number }>();

  constructor() {
    EnsureSingleInstance(this);
  }

  public async show(value: any, actionType: PreviewFileType) {
    await this.#ngZone.run(async () => {
      if (!this.dialogRef) await this.open();
      this.setValue(value, actionType);
    });
  }

  public async open() {
    await this.#ngZone.run(async () => {
      await this.close();
      const onOpen = Promise.withResolvers<PreviewRef>();
      this.dialogRef = this.#dialog.open(PreviewModalComponent, {
        width: '100vw',
        height: '100vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        data: { data: this.data, onOpen },
      });
      this.dialogRef.afterClosed().subscribe(() => (this.dialogRef = undefined));
      return onOpen.promise;
    });
  }

  public async close() {
    if (!this.dialogRef) return;
    await this.#ngZone.run(async () => {
      if (!this.dialogRef) return;
      const state = this.dialogRef.getState();
      if (state === 0) {
        const val = firstValueFrom(this.dialogRef.afterClosed());
        this.dialogRef.close();
        await val;
      }
      this.dialogRef = undefined;
    });
  }

  public setValue(value: any, actionType: any, start = 0) {
    this.data.next({ value, actionType, start });
  }
}
