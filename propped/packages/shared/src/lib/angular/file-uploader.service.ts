import { HttpClient, HttpEventType, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Component, Injectable, inject } from '@angular/core';
import type { MatDialogRef } from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { firstValueFrom, lastValueFrom, tap } from 'rxjs';
import { lazyFastQueue } from '../common/fun';
import { EnsureSingleInstance } from '../common/test-root-service';

export interface FileData {
  url: string;
  headers?: Record<string, string>;
}

export interface FileListI {
  url: string;
  file: File;
  upload: number;
  headers?: Record<string, string>;
  fn?: URLResolver;
}

export type URLResolver = () => Promise<[FileData, File]>;

@Injectable({ providedIn: 'root' })
export class FileUploaderService {
  readonly #dialog = inject(MatDialog);
  readonly #httpClient = inject(HttpClient);
  _fileList: FileListI[] = [];
  #fileQueue!: (v: FileListI) => Promise<void>;
  #dialogRef!: MatDialogRef<FileUploaderComponent, any>;

  constructor() {
    EnsureSingleInstance(this);
    this.#init();
  }

  #init() {
    this.#fileQueue = lazyFastQueue<FileListI>(
      4,
      async (fileInfo) => {
        this.#openModal();
        const fn = fileInfo.fn;
        const [data, file] = await fn!();
        fileInfo.url = data.url;
        fileInfo.headers = data.headers;
        fileInfo.file = file;
        await this.#fileUpload(fileInfo);
      },
      () => {
        this._fileList = [];
        this.#dialogRef?.close();
      },
    );
  }

  #fileUpload(item: FileListI) {
    const data = this.#upload(item).pipe(
      tap((event) => {
        if (event.type === HttpEventType.UploadProgress)
          item.upload = Math.round((100 * event.loaded) / event.total!);
      }),
    );
    return lastValueFrom(data);
  }

  async #openModal() {
    if (this.#dialogRef) return;
    this.#dialogRef = this.#dialog.open(FileUploaderComponent, {
      width: '540px',
      height: '540px',
      disableClose: true,
    });
    await firstValueFrom(this.#dialogRef.afterClosed());
    this.#dialogRef = undefined as any;
  }

  public uploadFiles(list: URLResolver[]) {
    return Promise.allSettled(
      list.map((v) => {
        const data = { url: '', file: {} as File, upload: 0, fn: v };
        this._fileList.push(data);
        return this.#fileQueue(data);
      }),
    );
  }

  #upload(item: FileListI) {
    const head = item.headers || {};
    head['Cache-Control'] ||= 'public, max-age=31536000, immutable';
    head['Content-Type'] ||= item.file.type;
    const headers = new HttpHeaders(head);
    const req = new HttpRequest('PUT', item.url, item.file, {
      reportProgress: true,
      responseType: 'text',
      headers,
    });
    return this.#httpClient.request(req);
  }
}

@Component({
  imports: [MatProgressBarModule],
  template: `<div class="flex flex-col p-6">
    <h2>Uploading Files</h2>
    <div class="flex flex-col gap-2">
      @for (item of fileUploaderService._fileList; track item) {
        <div>
          <div class="p-1">{{ item.file.name || 'loading...' }}</div>
          <mat-progress-bar color="accent" mode="determinate" [value]="item.upload" />
        </div>
      }
    </div>
  </div>`,
})
export class FileUploaderComponent {
  readonly fileUploaderService = inject(FileUploaderService);
}
