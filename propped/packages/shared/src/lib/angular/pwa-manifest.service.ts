import { Injectable } from '@angular/core';
import { EnsureSingleInstance } from '../common/test-root-service';

const urlResolver = (path: string | string[]) => {
  const pathName = Array.isArray(path) ? path.join('/') : path;
  const url = new URL(pathName, location.origin);
  url.pathname = url.pathname.replace(/\/+/g, '/');
  return url.href;
};

const manifest = {
  name: 'App',
  short_name: 'App',
  theme_color: '#1D0D3B',
  background_color: '#1D0D3B',
  display: 'standalone',
  scope: urlResolver('/'),
  start_url: urlResolver('./'),
  icons: [] as { src: string; sizes: string; type: string; purpose?: string }[],
};

@Injectable({ providedIn: 'root' })
export class PWAManifestService {
  constructor() {
    EnsureSingleInstance(this);
  }

  public makeManifest(setting: {
    name: string;
    icons: { src: string; sizes: string; type: string }[];
    startUrl: string | string[];
    scope: string | string[];
  }) {
    manifest.name = setting.name ?? manifest.name;
    manifest.short_name = setting.name ?? manifest.short_name;
    manifest.icons =
      setting.icons?.map((icon) => ({ ...icon, src: urlResolver(icon.src) })) ?? manifest.icons;
    manifest.start_url = setting.startUrl ? urlResolver(setting.startUrl) : manifest.start_url;
    manifest.scope = setting.scope ? urlResolver(setting.scope) : manifest.scope;
    const stringManifest = JSON.stringify(manifest);
    const manifestURL = `data:application/manifest+json;base64,${btoa(stringManifest)}`;
    const oldLink = document.querySelector('link[rel=manifest]') as HTMLLinkElement;
    if (oldLink) {
      oldLink.href = manifestURL;
      return;
    }
    const link = document.createElement('link');
    link.rel = 'manifest';
    link.href = manifestURL;
    document.head.appendChild(link);
  }
}
