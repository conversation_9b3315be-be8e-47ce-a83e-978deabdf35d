import type { OnInit } from '@angular/core';
import { Component, Injectable, inject } from '@angular/core';
import type { MatBottomSheetConfig } from '@angular/material/bottom-sheet';
import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheet,
  MatBottomSheetModule,
  MatBottomSheetRef,
} from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { firstValueFrom, race } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';
import { SVGIconComponent } from './svg-icon.component';

interface ActionI {
  name: string;
  icon?: string;
  url?: string;
  cssClass?: string;
  iconClass?: string;
  listClass?: string;
}

export type ActionsT = Record<string, string | ActionI>;

interface ActionDataI {
  actions: ActionsT;
  listClass?: string;
}

interface FormattedActionI {
  name: string;
  icon?: string;
  cssClass?: string;
  iconClass?: string;
  listClass?: string;
  key: string;
  lines?: string[];
}

interface ActionResultI {
  action: boolean;
  key?: string;
}

@Injectable({ providedIn: 'root' })
export class ActionModalService {
  readonly #bottomSheet = inject(MatBottomSheet);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(
    data: ActionDataI,
    options: MatBottomSheetConfig<any> = {},
  ): Promise<ActionResultI | undefined> {
    const onSelected = Promise.withResolvers<ActionResultI | undefined>();
    const dialogRef = this.#bottomSheet.open(ActionModalComponent, {
      ...options,
      data: { data, onSelected },
    });
    return firstValueFrom(race(onSelected.promise, dialogRef.afterDismissed()));
  }
}

@Component({
  imports: [MatButtonModule, MatBottomSheetModule, MatListModule, MatIconModule, SVGIconComponent],
  template: `<mat-nav-list>
    @for (item of actions; track item.key) {
      <button mat-list-item (click)="select(item.key)" [class]="item.cssClass || ''">
        <span
          matListItemTitle
          class="flex justify-center items-center gap-2"
          [class]="item.listClass || ''"
        >
          @if (item.icon) {
            <mat-icon [class]="item.iconClass || ''">{{ item.icon }}</mat-icon>
          } @else if (item.url) {
            <app-svg-icon [class]="item.iconClass || ''" [icon]="item.url" />
          }
          <div>{{ item.name }}</div>
        </span>
        @for (line of item.lines; track line) {
          <span matLine>{{ line }}</span>
        }
      </button>
    }
  </mat-nav-list>`,
})
export class ActionModalComponent implements OnInit {
  readonly #bottomSheetRef: MatBottomSheetRef<ActionModalComponent> = inject(MatBottomSheetRef);
  readonly #data: { data: ActionDataI; onSelected: PromiseWithResolvers<ActionResultI> } =
    inject(MAT_BOTTOM_SHEET_DATA);

  public actions: FormattedActionI[] = [];

  ngOnInit(): void {
    const actionList: FormattedActionI[] = this.actions;
    const actions = this.#data.data.actions;
    const data = this.#data.data;
    for (const key of Object.keys(actions)) {
      const action = actions[key];
      let act: FormattedActionI;
      if (typeof action === 'string') act = { name: action, key, listClass: data.listClass };
      else act = { ...action, key, listClass: action.listClass ?? data.listClass };
      [act.name, ...act.lines] = act.name.split('\n');
      actionList.push(act);
    }
  }

  select(key: string) {
    this.#data.onSelected.resolve({ action: true, key });
    this.#bottomSheetRef.dismiss();
  }
}
