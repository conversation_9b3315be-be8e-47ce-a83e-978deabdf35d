import {
  Directive,
  ElementRef,
  HostBinding,
  HostListener,
  Input,
  Renderer2,
  forwardRef,
  inject,
  input,
} from '@angular/core';
import type { ControlValueAccessor } from '@angular/forms';
import { NG_VALUE_ACCESSOR } from '@angular/forms';

@Directive({
  selector: '[appContentEditable]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ContentEditableFormDirective),
      multi: true,
    },
  ],
})
export class ContentEditableFormDirective implements ControlValueAccessor {
  @HostBinding('attr.contenteditable')
  get attrValue() {
    return this.enabled && (this.richText() || 'plaintext-only');
  }

  @Input('appContentEditable') enabled = false;
  readonly dblClickEnable = input(false);
  readonly richText = input(false);

  readonly #elementRef = inject(ElementRef);
  readonly #renderer = inject(Renderer2);

  private onChange!: (value: string) => void;
  private onTouched!: () => void;
  private removeDisabledState!: () => void;

  @HostListener('input') onInput(): void {
    this.onChange?.(this.#elementRef.nativeElement.innerText);
  }

  @HostListener('blur') onBlur(): void {
    this.onTouched?.();
    if (this.dblClickEnable()) this.enabled = false;
  }

  @HostListener('dblclick') dblclick(): void {
    if (this.dblClickEnable()) this.enabled = true;
  }

  writeValue(value: string): void {
    this.#renderer.setProperty(this.#elementRef.nativeElement, 'innerText', value || '');
  }

  // onlyPlainText($event: ClipboardEvent) {
  //   $event.preventDefault();
  //   const text = $event.clipboardData?.getData('text/plain');
  //   document.execCommand('insertText', false, text);
  // }

  registerOnChange(onChange: (value: string) => void): void {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: () => void): void {
    this.onTouched = onTouched;
  }

  setDisabledState(disabled: boolean): void {
    this.enabled = !disabled;
  }
}
