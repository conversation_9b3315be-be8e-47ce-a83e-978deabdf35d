import type { ProviderToken } from '@angular/core';
import { Injector, inject, runInInjectionContext } from '@angular/core';

export const inject2 = <T>(service: ProviderToken<T>, injector: Injector) => {
  let s!: T;
  runInInjectionContext(injector, () => (s = inject(service)));
  return s;
};

export const getLazyInjector = () => {
  const injector = inject(Injector);
  return <T>(service: ProviderToken<T>) => inject2(service, injector);
};
