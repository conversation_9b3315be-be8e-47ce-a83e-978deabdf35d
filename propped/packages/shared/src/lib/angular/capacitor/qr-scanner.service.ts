import { Injectable } from '@angular/core';
import { BarcodeScanner } from '@capacitor-community/barcode-scanner';
import { EnsureSingleInstance } from '../../common/test-root-service';

@Injectable({ providedIn: 'root' })
export class QrScannerService {
  constructor() {
    EnsureSingleInstance(this);
  }

  checkPermission() {
    return BarcodeScanner.checkPermission({ force: true });
  }

  async scan() {
    const perm = await this.checkPermission();
    if (!perm.granted) throw Error('No Permission');
    await this.prepare();
    const textP = this.qRScanner();
    this.showQRScanner();
    const text = await textP;
    this.destroy();
    return text.content;
  }

  public prepare() {
    return BarcodeScanner.prepare();
  }

  public destroy() {
    this.hideQRScanner();
    return BarcodeScanner.stopScan();
  }

  public qRScanner() {
    return BarcodeScanner.startScan();
  }

  public showQRScanner() {
    document.body.classList.add('scanner-active');
  }

  public hideQRScanner() {
    BarcodeScanner.hideBackground();
    document.body.classList.remove('scanner-active');
  }
}
