import type { PipeTransform } from '@angular/core';
import { Pipe } from '@angular/core';
import { format } from 'date-fns';

@Pipe({ name: 'dfnsFormat', pure: true })
export class DfnsFormatPipe implements PipeTransform {
  transform(date: string | Date, toFormat = 'dd-MMM-yy hh:mm:ss a') {
    if (!date) return '';
    if (typeof date === 'string') date = new Date(date);
    return format(date, toFormat);
  }
}
