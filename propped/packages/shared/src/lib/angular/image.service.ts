import { Injectable, NgZone, inject } from '@angular/core';
import type { PicaStatic } from 'pica';
import * as PICA_CLIENT from 'pica';
import { EnsureSingleInstance } from '../common/test-root-service';
import { SnackBarService } from './snack-bar.service';

const PicaClient: PicaStatic = (PICA_CLIENT as any).default;

const fitInside = ([x, y]: [number, number], [w, h]: [number, number]) => {
  if (x <= w && y <= h) return;
  if (x > w) {
    y /= x / w;
    x = w;
  }
  if (y > h) {
    x /= y / h;
    y = h;
  }
  return [Math.floor(x), Math.floor(y)];
};

export interface UploadThumbOption {
  maxSize?: number;
  type?: RegExp;
  exportTo?: string;
  resize?: [number, number];
  forceSize?: boolean;
}

const defaultThumbOption = {
  maxSize: 4 * 1024 * 1024,
  type: /^image\/(png|jpeg)$/,
  resize: [480, 480] as [number, number],
};

@Injectable({ providedIn: 'root' })
export class ImageService {
  readonly #ngZone = inject(NgZone);
  readonly #snackBarService = inject(SnackBarService);
  readonly #picaClient = PicaClient();
  constructor() {
    EnsureSingleInstance(this);
  }
  public async resize(image: HTMLImageElement, to: [number, number] = [600, 600]) {
    const res = fitInside([image.naturalWidth, image.naturalHeight], to);
    if (!res) return;
    const picaClient = this.#picaClient;
    const canvas = document.createElement('canvas');
    canvas.width = res[0];
    canvas.height = res[1];
    const resized = await picaClient.resize(image, canvas, {
      unsharpAmount: 80,
      unsharpRadius: 0.6,
      unsharpThreshold: 2,
    });
    canvas.remove();
    return await picaClient.toBlob(resized, 'image/jpeg', 0.9);
  }

  #prepareImage(file: File): Promise<HTMLImageElement> {
    const image = document.createElement('img');
    image.width = 200;
    image.height = 200;
    const { promise, resolve, reject } = Promise.withResolvers<HTMLImageElement>();
    image.onload = () => resolve(image);
    image.onerror = reject;
    const url = URL.createObjectURL(file);
    image.src = url;
    return promise;
  }

  #removeImage(image: HTMLImageElement) {
    URL.revokeObjectURL(image.src);
    image.remove();
  }

  public resizeFromFile(
    file: File,
    opt: { exportTo?: string; resize?: [number, number] } = {},
  ): Promise<Blob> {
    return this.#ngZone.runOutsideAngular(() => this.#resizeFromFile(file, opt));
  }

  async #resizeFromFile(
    file: File,
    opt: { exportTo?: string; resize?: [number, number]; forceSize?: boolean } = {},
  ): Promise<Blob> {
    opt.resize ||= defaultThumbOption.resize;
    const image = await this.#prepareImage(file);
    const res = opt.forceSize
      ? opt.resize
      : fitInside([image.naturalWidth, image.naturalHeight], opt.resize);
    if (!res) {
      this.#removeImage(image);
      return file;
    }
    const picaClient = this.#picaClient;
    const canvas = document.createElement('canvas');
    canvas.width = res[0];
    canvas.height = res[1];
    const resized = await picaClient.resize(image, canvas, {
      unsharpAmount: 80,
      unsharpRadius: 0.6,
      unsharpThreshold: 2,
    });
    const blob = await picaClient.toBlob(resized, opt.exportTo || 'image/jpeg', 0.9);
    canvas.remove();
    this.#removeImage(image);
    return blob;
  }

  public resizeForUpload(file: File, options: UploadThumbOption = {}) {
    if (file.size > (options.maxSize || defaultThumbOption.maxSize)) {
      this.#snackBarService.warn('Image should be less then 4MB');
      return;
    }
    if (!(options.type || defaultThumbOption.type).test(file.type)) {
      this.#snackBarService.warn('Image formats invalids should be png/jpeg');
      return;
    }
    options.resize ||= defaultThumbOption.resize;
    return this.#ngZone.runOutsideAngular(() => this.#resizeFromFile(file, options));
  }

  public async uploadThumbImage(file: File, options: UploadThumbOption = {}) {
    if (file.size > (options.maxSize || defaultThumbOption.maxSize)) {
      this.#snackBarService.warn('Image should be less then 2MB');
      return;
    }
    if (!(options.type || defaultThumbOption.type).test(file.type)) {
      this.#snackBarService.warn('Image formats invalids should be png/jpeg');
      return;
    }
    options.resize ||= defaultThumbOption.resize;
    return await this.resizeFromFile(file, options);
  }
}
