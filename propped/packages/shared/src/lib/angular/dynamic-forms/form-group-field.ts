import { FocusMonitor } from '@angular/cdk/a11y';
import type { BooleanInput } from '@angular/cdk/coercion';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import type { On<PERSON><PERSON>roy } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import type { ControlValueAccessor, FormGroup } from '@angular/forms';
import { FormArray, FormsModule, NgControl, ReactiveFormsModule } from '@angular/forms';
import type { MatFormField } from '@angular/material/form-field';
import { MAT_FORM_FIELD, MatFormFieldControl } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { Subject } from 'rxjs';
import { isNil } from '../../common/fun';
import { FormModalService } from './form-modal.service';
import type { SilverField } from './silver-field.component';
import { SilverFieldService } from './silver-field.component';

@Component({
  selector: 'silver-form-group-field',
  imports: [FormsModule, ReactiveFormsModule, MatIconModule],
  providers: [{ provide: MatFormFieldControl, useExisting: FormGroupFieldComponent }],
  template: `
    <div
      class="flex flex-col w-full mb-6"
      [attr.aria-labelledby]="_formField?.getLabelId?.()"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
      cdk-overlay-origin
    >
      <div class="overflow-x-auto" [style.height]="settings?.rowHeight ?? 'auto'">
        <table class="table table-zebra whitespace-nowrap table-pin-rows table-pin-cols">
          <thead>
            <tr>
              @for (col of columns; track col) {
                <th>{{ col.label }}</th>
              }
              <th class="w-8"></th>
            </tr>
          </thead>
          <tbody>
            @for (row of formRows; track row; let i = $index) {
              <tr>
                @for (item of row; track item; let j = $index) {
                  <td (click)="edit(row, formGroups[i])" class="cursor-pointer">
                    {{ formGroups[i].controls[item.key].value }}
                  </td>
                }
                <th class="flex">
                  <mat-icon
                    matTooltip="Cancel"
                    class="cursor-pointer text-error !overflow-visible"
                    (click)="remove(i)"
                    >close</mat-icon
                  >
                </th>
              </tr>
            }
          </tbody>
        </table>
      </div>
      <button class="btn btn-primary btn-outline btn-sm w-full" (click)="addNew()">
        {{ settings?.addRowText ?? 'Add' }}
      </button>
    </div>
  `,
  host: {
    '[class.mat-mdc-select]': 'true',
    '[class.silver-select-floating]': 'shouldLabelFloat',
    '[id]': 'id',
  },
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormGroupFieldComponent
  implements ControlValueAccessor, MatFormFieldControl<any[]>, OnDestroy
{
  static nextId = 0;
  readonly #formModalService = inject(FormModalService);
  readonly #silverFieldService = inject(SilverFieldService);
  readonly #focusMonitor = inject(FocusMonitor);
  readonly #elementRef = inject(ElementRef);
  readonly ngControl = inject(NgControl, { self: true, optional: true });
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly _formField: MatFormField | null = inject(MAT_FORM_FIELD, { optional: true });
  stateChanges = new Subject<void>();
  focused = false;
  touched = false;
  controlType = 'silver-form-group';
  id = `silver-form-group-${FormGroupFieldComponent.nextId++}`;

  public columns: SilverField[] = [];
  public inline = false;
  public formRows: SilverField[][] = [];
  public formGroups: FormGroup<any>[] = [];
  public formArray = new FormArray<any>([]);

  onChange = (_: any) => {
    //
  };
  onTouched = () => {
    //
  };

  private _options: any;
  public settings!: {
    rowCount: number;
    rowHeight: string;
    minRowCount: number;
    maxRowCount: number;
    addRowText: string;
  };
  _value: any[] | null = null;

  get empty() {
    return isNil(this._value);
  }

  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('aria-describedby') userAriaDescribedBy!: string;

  @Input()
  get placeholder(): string {
    return this._placeholder;
  }
  set placeholder(value: string) {
    this._placeholder = value;
    this.stateChanges.next();
  }

  @Input()
  get options(): any {
    return this._options;
  }
  set options(value: any) {
    this._options = value;
    this.columns = value.columns;
    this.settings = value.settings;
    {
      let i = this.settings?.rowCount ?? 0;
      while (i-- > 0) this.add();
    }
    this.stateChanges.next();
  }

  private _placeholder!: string;

  @Input()
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges.next();
  }
  private _required = false;

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges.next();
  }

  private _disabled = false;

  @Input()
  get value(): any[] | null {
    const val = this._value;
    return isNil(val) ? null : val;
  }

  set value(val: any | null) {
    this.removeAll();
    for (const item of val) this.add(item);
    this._value = val;
    this.#cdRef.detectChanges();
  }

  get errorState(): boolean {
    return this.touched;
  }

  constructor() {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }

  focus() {
    this.focused = true;
    this.stateChanges.next();
  }

  focusOut() {
    this.focused = false;
    this.stateChanges.next();
  }

  onFocusIn(_event: FocusEvent) {
    if (!this.focused) {
      this.focused = true;
      this.stateChanges.next();
    }
  }

  onFocusOut(event: FocusEvent) {
    if (!this.#elementRef.nativeElement.contains(event.relatedTarget as Element)) {
      this.touched = true;
      this.focused = false;
      this.onTouched();
      this.stateChanges.next();
    }
  }

  setDescribedByIds(_ids: string[]) {
    //
  }

  add(item?: any) {
    if (this.settings?.maxRowCount && this.settings.maxRowCount <= this.formArray.length) return;
    const columns = this.columns;
    const formFields = [];
    for (const field of columns) formFields.push({ ...field });
    this.formRows.push(formFields);
    const formGroup = this.#silverFieldService.fieldsToForm(formFields);
    this.formGroups.push(formGroup);
    this.formArray.push(formGroup);
    if (item) formGroup.patchValue(item);
    this.onChange(this.formArray.value);
  }

  async addNew() {
    if (this.settings?.maxRowCount && this.settings.maxRowCount <= this.formArray.length) return;
    const columns = this.columns;
    const form = [];
    for (const field of columns) form.push({ ...field });
    const result = await this.#formModalService.open({ heading: 'Edit', form });
    if (!result?.action) return;
    this.formRows.push(form);
    const formGroup = this.#silverFieldService.fieldsToForm(form);
    this.formGroups.push(formGroup);
    this.formArray.push(formGroup);
    formGroup.patchValue(result.value);
    this.#cdRef.detectChanges();
    this.onChange(this.formArray.value);
  }

  async edit(form: SilverField[], formGroup: FormGroup<any>) {
    for (const field of form) field.value = formGroup.controls[field.key].value;
    const result = await this.#formModalService.open({ heading: 'Edit', form });
    if (!result?.action) return;
    formGroup.patchValue(result.value);
    this.#cdRef.detectChanges();
    this.onChange(this.formArray.value);
  }

  remove(ix: number) {
    if (this.settings?.minRowCount && this.settings.minRowCount >= this.formArray.length) {
      return;
    }
    this.formArray.removeAt(ix);
    this.formGroups.splice(ix, 1);
    this.formRows.splice(ix, 1);
    this.#cdRef.detectChanges();
    this.onChange(this.formArray.value);
  }

  removeAll() {
    this.formArray.clear();
    this.formGroups.length = 0;
    this.formRows.length = 0;
  }

  onContainerClick() {
    this.focus();
  }

  writeValue(tel: any[] | null): void {
    this.value = tel;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnDestroy() {
    this.stateChanges.complete();
    this.#focusMonitor.stopMonitoring(this.#elementRef);
  }
}
