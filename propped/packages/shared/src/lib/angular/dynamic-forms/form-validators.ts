import type { AbstractControl, ValidatorFn } from '@angular/forms';

// https://stackoverflow.com/a/59317682/8784402

export const pwdError = {
  password: '9-30 char with num, small, capital, special(!@#$%..) char required',
};

export const userNameError = { username: '6-12 small alphanumeric and _ allowed' };
export const pwd =
  /^(?=(.*[a-z]){1,})(?=(.*[A-Z]){1,})(?=(.*[0-9]){1,})(?=(.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]){1,}).{9,30}$/;

export const usernameRegex = /^[a-z][a-z0-9_]{5,11}$/;
export const tokenRegex = (n: number) => new RegExp(`^\\d{${n}}$`);

export const password = (control: AbstractControl): Record<string, string> | null => {
  const { value } = control;
  if (!value || value.match(pwd) !== null) return null;
  return pwdError;
};

export const username = (control: AbstractControl): Record<string, string> | null => {
  const { value } = control;
  if (!value || value.match(usernameRegex) !== null) return null;
  return userNameError;
};

export const numberToken = (size: number) => {
  const regex = tokenRegex(size);
  return (control: AbstractControl): Record<string, string> | null => {
    const { value } = control;
    if (!value || value.toString().match(regex) !== null) return null;
    return { numberToken: `exact ${size} digit number allowed` };
  };
};

export const noLeadingTrailingSpace = (control: AbstractControl): Record<string, string> | null => {
  const { value } = control;
  if (!value || (!value.startsWith(' ') && !value.endsWith(' '))) return null;
  return { noLeadingTrailingSpace: 'Leading and trailing space not allowed' };
};

export const equalTo = (equalControl: AbstractControl): ValidatorFn => {
  let subscribe = false;
  return (control: AbstractControl) => {
    if (!subscribe) {
      subscribe = true;
      equalControl.valueChanges.subscribe(() => control.updateValueAndValidity());
    }
    const v = control.value;
    return equalControl.value === v
      ? null
      : { equalTo: { equalTo: equalControl.value, actual: v } };
  };
};
