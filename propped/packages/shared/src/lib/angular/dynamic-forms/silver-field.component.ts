import { ScrollingModule } from '@angular/cdk/scrolling';
import { Component, Injectable, Input } from '@angular/core';
import type { AbstractControl } from '@angular/forms';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FuzzySearch } from '../../common/fuzzy-search';
import { LifeDirective } from '../directives/life.directive';
import * as CustomValidators from './form-validators';
import { SelectFilter } from './select-filters';
import { SilverCodeComponent } from './silver-code-field';
import { SilverSelectComponent } from './silver-select-field';
import type { LIST_TYPE } from './silver-select.service';
import { SilverTextArrayComponent } from './text-array-field';
import { SilverTimePickerComponent } from './time-picker/silver-time-picker';

export const SilverFieldTypes = {
  SELECT: 'select',
  SMART_SELECT: 'smart-select',
  SMART_SELECT_FAST: 'smart-select-fast',
  MATRIX: 'matrix',
  RANGE: 'range',
  CODE: 'code',
  TEXT: 'text',
  BOOLEAN: 'boolean',
  DATE: 'date',
  DATE_RANGE: 'date-range',
  DATE_TIME: 'date-time',
  TEXT_ARRAY: 'text-array',
  LONG_TEXT: 'long-text',
  PARA: 'para',
  COLOR: 'color',
};

export class BaseField {
  controlType!: string;
  label?: string;
  key!: string;
  value: any;
  valid?: any;
  cssClass?: string;
  silverClass?: string;
  innerClass?: string;
}

export class SelectField extends BaseField {
  override controlType = SilverFieldTypes.SELECT;
  multiple? = false;
  dropdown? = true;
  nullable? = false;
  options!: [any, any][];
}

export class SmartSelectField extends BaseField {
  override controlType = SilverFieldTypes.SMART_SELECT;
  multiple? = false;
  searchParam?: string[];
  options!: any[];
  filter?: SelectFilter<any>;
  show?: string;
  vKey?: string;
  scroll? = false;
  nullable? = false;
  fuzzyOpt?: Record<string, any>;
  filterOpt?: Record<string, any>;
}

export class TextField extends BaseField {
  override controlType = SilverFieldTypes.TEXT;
  type!: string;
  placeholder!: string;
}

export class Paragraph extends BaseField {
  override controlType = SilverFieldTypes.PARA;
  dom?: (box: HTMLElement, event: 'OnInit' | 'OnDestroy' | 'AfterViewInit') => void;
  html = false;
}

export class TextArea extends BaseField {
  override controlType = SilverFieldTypes.LONG_TEXT;
  rows!: number;
}

export class RangeField extends BaseField {
  override controlType = SilverFieldTypes.RANGE;
  min!: number;
  max!: number;
  step!: number;
  declare value: number;
}

export class CodeField extends BaseField {
  override controlType = SilverFieldTypes.CODE;
  settings!: any;
  declare value: string;
}

export class BooleanField extends BaseField {
  override controlType = SilverFieldTypes.BOOLEAN;
  declare value: boolean;
}

export class ColorField extends BaseField {
  override controlType = SilverFieldTypes.COLOR;
  declare value: string;
}

export class DateField extends BaseField {
  override controlType = SilverFieldTypes.DATE;
  declare value: Date;
}

export class DateRangeField extends BaseField {
  override controlType = SilverFieldTypes.DATE_RANGE;
  declare value: { from: Date; to: Date };
}

export class DateTimeField extends BaseField {
  override controlType = SilverFieldTypes.DATE_TIME;
  declare value: Date;
}

export class TextArrayField extends BaseField {
  override controlType = SilverFieldTypes.TEXT_ARRAY;
  type!: string;
  declare value: string[];
}

export class SmartSelectFast extends BaseField {
  override controlType = SilverFieldTypes.SMART_SELECT_FAST;
  options!: {
    heading: string;
    data?: any[];
    asyncFun: boolean;
    asyncOpt: any;
    api: any;
    options: {
      multiple?: boolean;
      nullable?: boolean;
      searchParam?: string[];
      title?: string;
      desc?: string;
      pic?: string;
      vKey?: string;
      returnKey?: boolean;
      mode?: LIST_TYPE;
    };
  };
}

export class MatrixField extends BaseField {
  override controlType = SilverFieldTypes.MATRIX;
  columns!: SilverField[];
  settings!: {
    rowCount: number;
    rowHeight: string;
    minRowCount: number;
    maxRowCount: number;
    addRowText: string;
  };
  declare value: any;
}

export type SilverField =
  | BooleanField
  | CodeField
  | ColorField
  | DateField
  | DateRangeField
  | DateTimeField
  | MatrixField
  | Paragraph
  | RangeField
  | SelectField
  | SmartSelectField
  | TextArea
  | TextArrayField
  | TextField;

@Injectable({ providedIn: 'root' })
export class SilverFieldService {
  public fieldsToForm(fields: SilverField[]) {
    const formGroup = new FormGroup({});
    for (const field of fields) {
      if (field.controlType === SilverFieldTypes.PARA) continue;
      const validations = [];
      if (field.valid) {
        const v = field.valid;
        if (v.required) validations.push(Validators.required);
        if (v.min) validations.push(Validators.min(v.min));
        if (v.max) validations.push(Validators.max(v.max));
        if (v.email) validations.push(Validators.email);
        if (v.minLength) validations.push(Validators.minLength(v.minLength));
        if (v.maxLength) validations.push(Validators.maxLength(v.maxLength));
        if (v.username) validations.push(CustomValidators.username);
        if (v.password) validations.push(CustomValidators.password);
        if (
          (field.controlType === SilverFieldTypes.TEXT && (field as TextField).type === 'text') ||
          field.controlType === SilverFieldTypes.LONG_TEXT
        ) {
          if (!('noLeadingTrailingSpace' in v) || v.noLeadingTrailingSpace) {
            validations.push(CustomValidators.noLeadingTrailingSpace);
          }
        }
        if (v.numberToken) validations.push(CustomValidators.numberToken(v.numberToken));
        if (v.equalTo) {
          const ctrl = formGroup.get(v.equalTo);
          if (ctrl) validations.push(CustomValidators.equalTo(ctrl));
        }
      }
      if (field.controlType === SilverFieldTypes.DATE_RANGE) {
        const range = new FormGroup({
          from: new FormControl<Date | null>(field.value.from, validations),
          to: new FormControl<Date | null>(field.value.to, validations),
        });
        formGroup.addControl(field.key, range);
      } else if (field.controlType === SilverFieldTypes.SMART_SELECT) {
        const f = field as SmartSelectField;
        f.filterOpt ??= f.scroll ? {} : { max: 30 };
        f.fuzzyOpt ??= { sort: true };
        f.filter = new SelectFilter<any>([], undefined, f.filterOpt);
        f.filter.update(f.options, new FuzzySearch(f.options, f.searchParam, f.fuzzyOpt));
        formGroup.addControl(field.key, new FormControl(field.value, validations));
      } else if (field.controlType === SilverFieldTypes.MATRIX) {
        formGroup.addControl(field.key, new FormControl<any>(field.value ?? []));
      } else {
        formGroup.addControl(field.key, new FormControl(field.value, validations));
      }
    }
    return formGroup;
  }

  public destroyFields(fields: SilverField[]) {
    for (const field of fields) {
      if (field.controlType === SilverFieldTypes.SMART_SELECT) {
        const f = field as SmartSelectField;
        f.filter?.destroy();
      }
    }
  }

  public refineValue(fields: SilverField[], formValue: any) {
    for (const field of fields) {
      if (field.controlType === SilverFieldTypes.SMART_SELECT) {
        const f = field as SmartSelectField;
        if (f.vKey) {
          if (f.multiple)
            formValue[field.key] = formValue[field.key]?.map((x: any) => x[f.vKey as string]);
          else formValue[field.key] = formValue[field.key]?.[f.vKey];
        }
      } else if (field.controlType === SilverFieldTypes.TEXT) {
        const f = field as TextField;
        if (f.type === 'number') {
          formValue[field.key] =
            formValue[field.key] === null || formValue[field.key] === undefined
              ? formValue[field.key]
              : formValue[field.key] === ''
                ? null
                : Number(formValue[field.key]);
        }
      }
    }
    return formValue;
  }
}

@Component({
  imports: [
    FormsModule,
    LifeDirective,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatNativeDateModule,
    MatSelectModule,
    MatSliderModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    ScrollingModule,
    SilverCodeComponent,
    SilverSelectComponent,
    SilverTextArrayComponent,
    SilverTimePickerComponent,
  ],
  selector: 'app-silver-field',
  template: `
    @if (field.label) {
      <div class="mb-2 text-sm font-medium text-gray-900 dark:text-white">
        {{ field.label }} {{ field.valid?.required ? '*' : '' }}
      </div>
    }
    @if (field.controlType === SilverFieldTypes.PARA) {
      @if ($any(field).dom) {
        <div
          [class]="field.innerClass || ''"
          #box
          appLife
          (lifeStatus)="$any(field).dom(box, $event)"
        ></div>
      } @else if ($any(field).html) {
        <div [innerHtml]="field.value" [class]="field.innerClass || ''"></div>
      } @else {
        <div [class]="field.innerClass || ''">{{ field.value }}</div>
      }
    } @else {
      <ng-container [formGroup]="form">
        @switch (field.controlType) {
          @case (SilverFieldTypes.TEXT) {
            <mat-form-field [class]="field.innerClass || ''">
              <input
                [formControl]="form.controls[ctrlName]"
                matInput
                #fieldInput
                [type]="field.type"
                [placeholder]="field.placeholder"
              />
              @if (form.controls[ctrlName].invalid) {
                <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
              }
              @if (field.type === 'password') {
                <mat-icon
                  matSuffix
                  (click)="
                    $event.stopPropagation();
                    $event.preventDefault();
                    fieldInput.type = fieldInput.type === 'password' ? 'text' : 'password'
                  "
                  >{{ fieldInput.type === 'password' ? 'visibility_off' : 'visibility' }}</mat-icon
                >
              }
            </mat-form-field>
          }
          @case (SilverFieldTypes.LONG_TEXT) {
            <mat-form-field [class]="field.innerClass || ''">
              <textarea
                matInput
                [formControl]="form.controls[ctrlName]"
                [rows]="field.rows || 3"
              ></textarea>
              @if (form.controls[ctrlName].invalid) {
                <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
              }
            </mat-form-field>
          }
          @case (SilverFieldTypes.SELECT) {
            @if (field.dropdown === false && !field.multiple) {
              <div class="flex flex-row flex-wrap gap-2 mb-2">
                @for (opt of field.options; track opt) {
                  <label class="flex flex-row gap-2 cursor-pointer p-2">
                    <input
                      [formControl]="form.controls[ctrlName]"
                      type="radio"
                      [name]="field.key + id"
                      class="radio radio-accent"
                      [value]="opt[0]"
                    />
                    <span class="label-text leading-6">{{ opt[1] }}</span>
                  </label>
                }
              </div>
            } @else {
              <mat-form-field [class]="field.innerClass || ''">
                <mat-select [multiple]="field.multiple" [formControl]="form.controls[ctrlName]">
                  @if (field.nullable) {
                    <mat-option
                      [value]="null"
                      (click)="form.controls[ctrlName].setValue(field.multiple ? [] : null)"
                      >--</mat-option
                    >
                  }
                  @for (opt of field.options; track opt) {
                    <mat-option [value]="opt[0]">{{ opt[1] }}</mat-option>
                  }
                </mat-select>
                @if (form.controls[ctrlName].invalid) {
                  <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
                }
              </mat-form-field>
            }
          }
          @case (SilverFieldTypes.SMART_SELECT) {
            @if (field.scroll) {
              <mat-form-field [class]="field.innerClass || ''">
                <mat-select
                  [formControl]="form.controls[ctrlName]"
                  [multiple]="field.multiple"
                  (openedChange)="
                    scrollView.scrollToIndex(
                      field.filter.filteredList.indexOf(
                        field.multiple
                          ? form.controls[ctrlName].value?.[0]
                          : form.controls[ctrlName].value
                      )
                    );
                    scrollView.checkViewportSize()
                  "
                >
                  <mat-option>
                    <ngx-mat-select-search
                      [formControl]="field.filter.filterFormControl"
                      (input)="scrollView.scrollToIndex(0); scrollView.checkViewportSize()"
                      placeholderLabel="Search"
                      noEntriesFoundLabel="no match found"
                    />
                  </mat-option>
                  <cdk-virtual-scroll-viewport
                    #scrollView
                    [itemSize]="48"
                    minBufferPx="240"
                    maxBufferPx="240"
                    [style.height.px]="4 * 48"
                  >
                    @if (field.nullable) {
                      <mat-option
                        [value]="null"
                        (click)="form.controls[ctrlName].setValue(field.multiple ? [] : null)"
                      >
                        --
                      </mat-option>
                    }
                    <mat-option
                      *cdkVirtualFor="let item of field.filter.filteredList"
                      [value]="item"
                    >
                      {{ item[field.show] }}
                    </mat-option>
                  </cdk-virtual-scroll-viewport>
                  @if (!field.multiple && form.controls[ctrlName].value) {
                    <mat-option [value]="form.controls[ctrlName].value" class="!hidden">
                      {{ form.controls[ctrlName].value[field.show] }}
                    </mat-option>
                  }
                  @if (field.multiple && form.controls[ctrlName].value?.length) {
                    @for (item of form.controls[ctrlName].value; track item) {
                      <mat-option [value]="item" class="!hidden">
                        {{ item[field.show] }}
                      </mat-option>
                    }
                  }
                </mat-select>
                @if (form.controls[ctrlName].invalid) {
                  <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
                }
              </mat-form-field>
            } @else {
              <mat-form-field [class]="field.innerClass || ''">
                <mat-select [formControl]="form.controls[ctrlName]" [multiple]="field.multiple">
                  <mat-option>
                    <ngx-mat-select-search
                      [formControl]="field.filter.filterFormControl"
                      placeholderLabel="Search"
                      noEntriesFoundLabel="no match found"
                    />
                  </mat-option>
                  @if (!field.multiple && form.controls[ctrlName].value) {
                    <mat-option
                      [value]="form.controls[ctrlName].value"
                      (click)="form.controls[ctrlName].setValue(null)"
                    >
                      {{ form.controls[ctrlName].value[field.show] }}
                    </mat-option>
                  }
                  @if (field.nullable) {
                    <mat-option
                      [value]="null"
                      (click)="form.controls[ctrlName].setValue(field.multiple ? [] : null)"
                      >--</mat-option
                    >
                  }
                  @for (item of field.filter.filteredList; track item) {
                    <mat-option [value]="item">{{ item[field.show] }} </mat-option>
                  }
                  @if (field.multiple && form.controls[ctrlName].value?.length) {
                    @for (item of form.controls[ctrlName].value; track item) {
                      <mat-option [value]="item" class="!hidden">
                        {{ item[field.show] }}
                      </mat-option>
                    }
                  }
                </mat-select>
                @if (form.controls[ctrlName].invalid) {
                  <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
                }
              </mat-form-field>
            }
          }
          @case (SilverFieldTypes.DATE_RANGE) {
            <mat-form-field [class]="field.innerClass || ''">
              <mat-date-range-input [formGroup]="form.controls[ctrlName]" [rangePicker]="picker">
                <input matStartDate formControlName="from" placeholder="Start date" />
                <input matEndDate formControlName="to" placeholder="End date" />
              </mat-date-range-input>
              <!-- <mat-hint>MM/DD/YYYY - MM/DD/YYYY</mat-hint> -->
              <mat-datepicker-toggle matIconSuffix [for]="picker" />
              <mat-date-range-picker #picker />
              @if ($any(form.controls[ctrlName]).controls.from.hasError('matStartDateInvalid')) {
                <mat-error>Invalid start date</mat-error>
              }
              @if ($any(form.controls[ctrlName]).controls.to.hasError('matEndDateInvalid')) {
                <mat-error>Invalid end date</mat-error>
              }
              @if (form.controls[ctrlName].invalid) {
                <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
              }
            </mat-form-field>
          }
          @case (SilverFieldTypes.DATE) {
            <mat-form-field appearance="fill" [class]="field.innerClass || ''">
              <input matInput [matDatepicker]="picker2" [formControl]="form.controls[ctrlName]" />
              <!-- <mat-hint>MM/DD/YYYY</mat-hint> -->
              <mat-datepicker-toggle matSuffix [for]="picker2" />
              <mat-datepicker #picker2 />
              @if (form.controls[ctrlName].invalid) {
                <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
              }
            </mat-form-field>
          }
          @case (SilverFieldTypes.DATE_TIME) {
            <mat-form-field appearance="fill" [class]="field.innerClass || ''">
              <silver-time-picker [formControl]="form.controls[ctrlName]" />
            </mat-form-field>
          }
          @case (SilverFieldTypes.RANGE) {
            <mat-slider
              thumbLabel
              class="mx-3"
              [min]="field.min"
              [max]="field.max"
              [step]="field.step"
              [class]="field.innerClass || ''"
              discrete
              showTickMarks
            >
              <input matSliderThumb [formControl]="form.controls[ctrlName]" />
            </mat-slider>
          }
          @case (SilverFieldTypes.BOOLEAN) {
            <label class="cursor-pointer label" [class]="field.innerClass || ''">
              <span class="label-text text-opacity-60">{{ field.placeholder }}</span>
              <input
                type="checkbox"
                class="toggle toggle-accent"
                [formControl]="form.controls[ctrlName]"
                [checked]="field.value"
              />
            </label>
          }
          @case (SilverFieldTypes.COLOR) {
            <label class="cursor-pointer label" [class]="field.innerClass || ''">
              <input type="color" [formControl]="form.controls[ctrlName]" />
            </label>
          }
          @case (SilverFieldTypes.CODE) {
            <mat-form-field>
              <silver-code
                [formControl]="form.controls[ctrlName]"
                class="w-full"
                [settings]="field.settings"
              />
            </mat-form-field>
          }
          @case (SilverFieldTypes.SMART_SELECT_FAST) {
            <mat-form-field>
              <silver-select [formControl]="form.controls[ctrlName]" [options]="field.options" />
            </mat-form-field>
          }
          @case (SilverFieldTypes.TEXT_ARRAY) {
            <mat-form-field>
              <silver-text-array [formControl]="form.controls[ctrlName]" />
              @if (form.controls[ctrlName].invalid) {
                <mat-error>{{ getErrorMessage(form.controls[ctrlName]) }}</mat-error>
              }
            </mat-form-field>
          }
        }
      </ng-container>
    }
  `,
})
export class SilverFieldComponent {
  @Input() field!: any;
  @Input() form!: FormGroup<any>;
  @Input() ctrlName!: string;
  static id = 0;
  id = 0;
  SilverFieldTypes = SilverFieldTypes;

  constructor() {
    this.id = SilverFieldComponent.id++;
  }

  public getErrorMessage(x: AbstractControl) {
    if (x.hasError('required')) return 'You must enter a value';
    if (x.hasError('email')) return 'You must enter a valid email';
    if (x.hasError('maxlength'))
      return `at most ${x.errors!['maxlength'].requiredLength} char allowed`;
    if (x.hasError('password')) return x.errors!['password'];
    if (x.hasError('username')) return x.errors!['username'];
    if (x.hasError('noLeadingTrailingSpace')) return x.errors!['noLeadingTrailingSpace'];
    if (x.hasError('numberToken')) return x.errors!['numberToken'];
    if (x.hasError('equalTo')) return 'You must enter same here';
    if (x.hasError('minlength'))
      return `at least ${x.errors!['minlength'].requiredLength} char required`;
    return '';
  }
}
