import { FormControl } from '@angular/forms';
import type { Subscription } from 'rxjs';
import { Subject, filter, startWith } from 'rxjs';
import type { FuzzySearch } from '../../common/fuzzy-search';

export class SelectFilter<T> {
  public filterFormControl = new FormControl('');
  public fuzzyList: typeof FuzzySearch;
  public filteredList = [];
  public subscriptions: Subscription[] = [];
  public afterSearch$ = new Subject<T[]>();
  public opt: Record<string, any>;

  constructor(
    public list: T[],
    fuzzyList: typeof FuzzySearch,
    opt: Record<string, any> = {},
  ) {
    this.fuzzyList = fuzzyList;
    this.opt = opt ?? {};
    this.subscribe();
  }

  public subscribe() {
    const sub = this.filterFormControl.valueChanges
      .pipe(
        startWith(''),
        filter((v) => !(v === null || v === undefined)),
      )
      .subscribe((v) => this.search(v as string));
    this.subscriptions.push(sub);
  }

  public update(list: T[], fuzzyList: typeof FuzzySearch) {
    this.list = list;
    this.fuzzyList = fuzzyList;
    this.search('');
  }

  public search(key: string) {
    if (!this.fuzzyList) return;
    this.filteredList = this.fuzzyList.search(key);
    if (this.opt['max'] && this.filteredList.length > this.opt['max']) {
      this.filteredList = this.filteredList.slice(0, this.opt['max']);
    }
    this.afterSearch$.next(this.filteredList);
  }

  public destroy() {
    for (const sub of this.subscriptions) sub?.unsubscribe();
    this.subscriptions = [];
  }
}
