import { FocusMonitor } from '@angular/cdk/a11y';
import type { BooleanInput } from '@angular/cdk/coercion';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import type { OnD<PERSON>roy } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import type { ControlValueAccessor } from '@angular/forms';
import { FormsModule, NgControl, ReactiveFormsModule } from '@angular/forms';
import type { MatFormField } from '@angular/material/form-field';
import { MAT_FORM_FIELD, MatFormFieldControl } from '@angular/material/form-field';
import { Subject } from 'rxjs';
import { isNil } from '../../common/fun';
import { SilverSelectModalService } from './silver-select.service';

@Component({
  selector: 'silver-select',
  imports: [FormsModule, ReactiveFormsModule],
  providers: [{ provide: MatFormFieldControl, useExisting: SilverSelectComponent }],
  template: `
    <div
      class="mat-mdc-select-trigger"
      [attr.aria-labelledby]="_formField?.getLabelId?.()"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
      cdk-overlay-origin
    >
      <div class="mat-mdc-select-value">
        @if (!empty) {
          <span class="mat-mdc-select-min-line">{{ getToShow() }}</span>
        }
      </div>
      <div
        class="mat-mdc-select-arrow-wrapper"
        style="height:14px"
        [style.transform]="empty ? 'translateY(-12px)' : 'translateY(-8px)'"
      >
        <div class="mat-mdc-select-arrow">
          <svg viewBox="0 0 24 24" width="24px" height="24px" focusable="false" aria-hidden="true">
            <path d="M7 10l5 5 5-5z" />
          </svg>
        </div>
      </div>
    </div>
  `,
  host: {
    '[class.mat-mdc-select]': 'true',
    '[class.silver-select-floating]': 'shouldLabelFloat',
    '[id]': 'id',
  },
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SilverSelectComponent
  implements ControlValueAccessor, MatFormFieldControl<any[]>, OnDestroy
{
  static nextId = 0;
  readonly #silverSelectModalService = inject(SilverSelectModalService);
  readonly #focusMonitor = inject(FocusMonitor);
  readonly #elementRef = inject(ElementRef);
  readonly ngControl = inject(NgControl, { self: true, optional: true });
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly _formField: MatFormField | null = inject(MAT_FORM_FIELD, { optional: true });
  public stateChanges = new Subject<void>();
  public focused = false;
  public touched = false;
  public controlType = 'silver-select';
  public id = `silver-select-${SilverSelectComponent.nextId++}`;

  onChange = (_: any) => {
    //
  };
  onTouched = () => {
    //
  };

  private _options: any;
  _lastResult = { selected: [] as any[], selectedObj: [] as any[] };
  _value: any[] | null = null;

  get empty() {
    return this.isMultiSelect() ? !this._value?.length : isNil(this._value?.[0]);
  }

  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('aria-describedby') userAriaDescribedBy!: string;

  @Input()
  get placeholder(): string {
    return this._placeholder;
  }
  set placeholder(value: string) {
    this._placeholder = value;
    this.stateChanges.next();
  }

  @Input()
  get options(): any {
    return this._options;
  }
  set options(value: any) {
    this._options = value;
    this.stateChanges.next();
  }

  private _placeholder!: string;

  @Input()
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges.next();
  }
  private _required = false;

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges.next();
  }

  private _disabled = false;

  @Input()
  get value(): any[] | null {
    const val = this._value;
    if (val?.length) {
      return this.isMultiSelect() ? val : isNil(val[0]) ? null : val[0];
    }
    return null;
  }
  set value(val: any | null) {
    this._value = isNil(val) ? null : this.isMultiSelect() ? val : [val];
  }

  get errorState(): boolean {
    return this.touched;
  }

  constructor() {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }
  }

  isMultiSelect() {
    return this.options.options.multiple;
  }

  focus() {
    this.focused = true;
    this.stateChanges.next();
  }

  getToShow() {
    if (this.isMultiSelect()) return `${this._value?.length} Selected`;
    return !isNil(this._value?.[0])
      ? this._lastResult.selectedObj[0][this.options.options.title]
      : '';
  }

  focusOut() {
    this.focused = false;
    this.stateChanges.next();
  }

  onFocusIn(_event: FocusEvent) {
    if (!this.focused) {
      this.focused = true;
      this.stateChanges.next();
    }
  }

  onFocusOut(event: FocusEvent) {
    if (!this.#elementRef.nativeElement.contains(event.relatedTarget as Element)) {
      this.touched = true;
      this.focused = false;
      this.onTouched();
      this.stateChanges.next();
    }
  }

  setDescribedByIds(_ids: string[]) {
    //
  }

  async open() {
    const opt = {
      ...this.options,
      options: { ...this.options.options, value: this._value },
    };
    const result = await this.#silverSelectModalService.open(opt);
    if (result?.action) {
      this._value = result.value;
      this._lastResult.selectedObj = result.selectedObj;
      this._lastResult.selected = result.selected;
      this.onChange(this.value);
      this.stateChanges.next();
    }
    if (this.empty) this.focusOut();
    this.#cdRef.detectChanges();
  }

  onContainerClick() {
    this.focus();
    this.open();
  }

  writeValue(tel: any[] | null): void {
    this.value = tel;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnDestroy() {
    this.stateChanges.complete();
    this.#focusMonitor.stopMonitoring(this.#elementRef);
  }
}
