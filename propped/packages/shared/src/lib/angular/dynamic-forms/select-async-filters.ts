import { FormControl } from '@angular/forms';
import type { Observable, Subscription } from 'rxjs';
import {
  BehaviorSubject,
  Subject,
  debounceTime,
  filter,
  noop,
  startWith,
  switchMap,
  tap,
} from 'rxjs';

export class SelectAsyncFilter<T> {
  public filterFormControl = new FormControl('');
  public filteredList: T[] = [];
  public subscriptions: Subscription[] = [];
  public afterSearch$ = new Subject<T[]>();
  public opt: Record<string, any>;
  public loading = new BehaviorSubject<boolean>(true);

  constructor(
    public api: (search: string) => Observable<T[]>,
    opt: Record<string, any> = {},
  ) {
    this.opt = opt ?? {};
    this.subscribe();
  }

  public subscribe() {
    const sub = (this.filterFormControl.valueChanges as Observable<string>)
      .pipe(
        startWith(''),
        filter((v) => !(v === null || v === undefined)),
        this.opt['debounceTime'] === 0 ? tap(noop) : debounceTime(this.opt['debounceTime'] ?? 500),
        tap(() => this.loading.next(true)),
        switchMap((v) => this.api(v)),
      )
      .subscribe((v) => {
        this.filteredList = v;
        this.afterSearch$.next(this.filteredList);
        this.loading.next(false);
      });
    this.subscriptions.push(sub);
  }

  public destroy() {
    for (const sub of this.subscriptions) sub?.unsubscribe();
    this.subscriptions = [];
  }
}
