import { NgOptimizedImage } from '@angular/common';
import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  inject,
} from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import type { MatDialogConfig } from '@angular/material/dialog';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import type { Subscription } from 'rxjs';
import { Subject, firstValueFrom, lastValueFrom, startWith } from 'rxjs';
import { EnsureSingleInstance } from '../../common/test-root-service';
import { blobToFile, dataURLToFile } from '../../web/file.fun';
import { CommonFunctions } from '../common.fun';
import { FilePickerService } from '../file-picker.service';
import type { UploadThumbOption } from '../image.service';
import { ImageService } from '../image.service';
import { LetterAvatar, getInitial } from '../letter-avatar';
import { MatrixFormFieldComponent } from './matrix-form-field';
import type { SilverField } from './silver-field.component';
import {
  SilverFieldComponent,
  SilverFieldService,
  SilverFieldTypes,
} from './silver-field.component';

export interface ProfileModalDataI {
  heading: string;
  form: SilverField[];
  btn1Name?: string;
  btn2Name?: string;
  closeOnSubmit?: boolean;
  autoImageField?: string;
  imageUrl?: string;
  autoRefineValue?: boolean;
  imgOptions?: UploadThumbOption;
}

interface InputRef_I {
  onFormCreate: PromiseWithResolvers<FormGroup>;
  onFormSubmit: Subject<ProfileFormModalResponseI>;
}

export interface FormModalRef_I {
  formReady: () => Promise<any>;
  afterClosed: () => Promise<any>;
  refineValue: () => any;
  onSubmit$: Subject<ProfileFormModalResponseI>;
  close: () => void;
  closed: boolean;
  dialogRef: MatDialogRef<ProfileFormModalComponent, any>;
}

interface ProfileFormModalResponseI {
  action?: boolean;
  value: any;
  image: File;
  refineValue: (() => any) | undefined;
}

@Injectable({ providedIn: 'root' })
export class ProfileFormModalService {
  readonly #dialog = inject(MatDialog);
  readonly #silverFieldService = inject(SilverFieldService);

  constructor() {
    EnsureSingleInstance(this);
  }

  public async open(data: ProfileModalDataI, options: MatDialogConfig = {}) {
    data.closeOnSubmit = data.closeOnSubmit !== false;
    data.autoRefineValue = data.autoRefineValue !== false;
    const dialogRef = this.#dialog.open(ProfileFormModalComponent, {
      width: '400px',
      data: { data },
      panelClass: ['animate__animated', 'animate__slideInUp-10', 'g-dialog-mob-full'],
      ...options,
    });
    return await lastValueFrom(dialogRef.afterClosed());
  }

  public openForm(data: ProfileModalDataI, options: MatDialogConfig = {}) {
    data.closeOnSubmit = data.closeOnSubmit !== false;
    data.autoRefineValue = data.autoRefineValue !== false;
    const onFormCreate = Promise.withResolvers<FormGroup>();
    const onFormSubmit = new Subject<ProfileFormModalResponseI>();
    const input: InputRef_I = { onFormCreate, onFormSubmit };
    const dialogRef = this.#dialog.open(ProfileFormModalComponent, {
      width: '400px',
      data: { data, ref: input },
      panelClass: ['animate__animated', 'animate__slideInUp-10', 'g-dialog-mob-full'],
      ...options,
    });
    const afterClosed = firstValueFrom(dialogRef.afterClosed());
    const ref: FormModalRef_I = {
      onSubmit$: onFormSubmit,
      formReady: () => onFormCreate.promise,
      close: () => dialogRef.close(),
      afterClosed: () => afterClosed,
      refineValue: async () =>
        this.#silverFieldService.refineValue(data.form, (await onFormCreate.promise).value),
      closed: false,
      dialogRef,
    };
    afterClosed.then(() => {
      ref.closed = true;
      onFormSubmit.complete();
    });
    return ref;
  }
}

@Component({
  imports: [
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatrixFormFieldComponent,
    NgOptimizedImage,
    ReactiveFormsModule,
    SilverFieldComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'app-profile-form-modal',
  template: `<div mat-dialog-title style="font-size: 1.2rem">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography" [formGroup]="formGroup">
      <div matTooltip="Update Pic" class="m-4">
        @if (url) {
          <img
            [src]="url"
            placeholder
            class="h-28 w-28 rounded-full m-auto object-cover cursor-pointer"
            (click)="pickImage()"
          />
        } @else if (imageUrl) {
          <img
            [ngSrc]="imageUrl"
            placeholder
            width="112"
            height="112"
            class="h-28 w-28 rounded-full m-auto object-cover cursor-pointer"
            (click)="pickImage()"
          />
        }
      </div>
      @for (item of form; track i; let i = $index) {
        <div class="flex flex-col mb-3" [class]="item.cssClass || ''">
          @if (item.controlType === SilverFieldTypes.MATRIX) {
            @if (item.label) {
              <div class="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                {{ item.label }} {{ item.valid?.required ? '*' : '' }}
              </div>
            }
            <silver-matrix-field
              color="primary"
              [formControl]="formGroup.controls[item.key]"
              [options]="item"
            />
          } @else {
            <app-silver-field
              class="flex flex-col"
              [class]="item.silverClass || ''"
              [field]="item"
              [ctrlName]="item.key"
              [form]="formGroup"
            />
          }
        </div>
      }
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button [mat-dialog-close]="{ action: false }" class="btn btn-outline btn-sm btn-error mr-3">
        {{ btn1Name }}
      </button>
      <button class="btn btn-outline btn-sm btn-primary" (click)="submit()">
        {{ btn2Name }}
        <mat-icon [class]="formGroup.invalid ? 'text-error' : 'text-success'">{{
          formGroup.invalid ? 'error_outline' : 'done_all'
        }}</mat-icon>
      </button>
    </mat-dialog-actions>`,
})
export class ProfileFormModalComponent implements OnInit, OnDestroy {
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly #dialogRef: MatDialogRef<ProfileFormModalComponent> = inject(MatDialogRef);
  readonly #filePickerService = inject(FilePickerService);
  readonly #imageService = inject(ImageService);
  readonly #silverFieldService = inject(SilverFieldService);
  readonly #data: { data: ProfileModalDataI; ref: any } = inject(MAT_DIALOG_DATA);

  public readonly SilverFieldTypes = SilverFieldTypes;
  public formGroup!: FormGroup<any>;
  public modalData!: ProfileModalDataI;
  public btn1Name!: string;
  public btn2Name!: string;
  public form: SilverField[] = [];
  public ref!: InputRef_I;
  public url!: string;
  public imageUrl?: string;

  #image!: File;
  #subscriptions: Record<string, Subscription> = {};
  #lastInitial!: string;

  refineValue = () => this.#silverFieldService.refineValue(this.form, this.formGroup.value);

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    this.ref = this.#data.ref;
    this.btn1Name = this.modalData.btn1Name ?? 'Cancel';
    this.btn2Name = this.modalData.btn2Name ?? 'Submit';
    this.imageUrl = this.modalData.imageUrl;
    this.form = this.modalData.form ?? [];
    this.formGroup = this.#silverFieldService.fieldsToForm(this.form);
    if (this.ref) this.ref.onFormCreate.resolve(this.formGroup);
    this.autoImageGen();
  }

  public autoImageGen() {
    if (this.imageUrl) return;
    const field = this.modalData.autoImageField;
    if (!field) return;
    const fieldCtrl = this.formGroup.get(field);
    if (!fieldCtrl) return;
    this.#subscriptions['nameChange']?.unsubscribe();
    const nameSubscription = fieldCtrl.valueChanges
      .pipe(startWith(fieldCtrl.value || '?'))
      .subscribe((v) => {
        if (v) this.setProfile(v);
      });
    this.#subscriptions['nameChange'] = nameSubscription;
  }

  public async setProfile(name: string) {
    name = (name || '?').trim();
    const initial = getInitial(name, true);
    if (this.#lastInitial === initial) return;
    this.#lastInitial = initial;
    const color = CommonFunctions.getRandomColor();
    const m = LetterAvatar({
      name,
      background: color[0],
      color: color[1],
      rounded: false,
      uppercase: true,
    });
    this.#image = await dataURLToFile(m, 'profile.png');
    this.url = m;
    this.#cdRef.detectChanges();
  }

  public async pickImage() {
    const [file] = await this.#filePickerService.pick({ accept: 'image/jpeg,image/png' });
    if (!file) return;
    const imageBlob = await this.#imageService.resizeForUpload(
      file,
      this.modalData.imgOptions ?? { resize: [256, 256] },
    );
    if (!imageBlob) return;
    if (this.url?.startsWith('blob:')) URL.revokeObjectURL(this.url);
    this.url = URL.createObjectURL(imageBlob);
    this.#cdRef.detectChanges();
    this.#image = imageBlob instanceof File ? imageBlob : blobToFile(imageBlob, file.name);
    this.#subscriptions['nameChange']?.unsubscribe();
  }

  public submit() {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    const toReturn = {
      value: this.modalData.autoRefineValue ? this.refineValue() : this.formGroup.value,
      image: this.#image,
      refineValue: this.modalData.autoRefineValue ? undefined : this.refineValue,
    };
    if (this.modalData.closeOnSubmit) {
      (toReturn as any).action = true;
      this.#dialogRef.close(toReturn);
    } else {
      this.ref.onFormSubmit.next(toReturn);
    }
  }

  public ngOnDestroy(): void {
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    this.#subscriptions = undefined as any;
    if (this.url?.startsWith('blob:')) URL.revokeObjectURL(this.url);
    this.#silverFieldService.destroyFields(this.form);
  }
}
