import { ScrollingModule } from '@angular/cdk/scrolling';
import { AsyncPipe } from '@angular/common';
import type { OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  inject,
} from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { daisyMerge } from '@lib/common/tailwind-merge';
import type { Observable } from 'rxjs';
import { lastValueFrom } from 'rxjs';
import { FuzzySearch } from '../../common/fuzzy-search';
import { EnsureSingleInstance } from '../../common/test-root-service';
import { SelectAsyncFilter } from './select-async-filters';
import { SelectFilter } from './select-filters';

export enum LIST_TYPE {
  TEXT = 'text',
  TEXT_DESC = 'text_desc',
  AVATAR_TEXT = 'avatar_text',
  AVATAR_TEXT_DESC = 'avatar_text_desc',
}
export interface SilverSelectOptionsI {
  multiple?: boolean;
  searchParam?: string[];
  filter?: SelectFilter<any> | SelectAsyncFilter<any>;
  title?: string;
  value?: any[];
  selected?: any[];
  selectedObj?: any[];
  desc?: string;
  mode: LIST_TYPE;
  pic?: string;
  vKey?: string;
  nullable?: boolean;
  returnKey?: boolean;
  fuzzyOpt?: Record<string, any>;
  filterOpt?: Record<string, any>;
}

export interface ModalDataI {
  heading: string;
  btn1Name?: string;
  btn2Name?: string;
  btn1Class?: string;
  btn2Class?: string;
  options?: SilverSelectOptionsI;
  data?: any[];
  asyncFun?: boolean;
  asyncOpt?: any;
  api?: (search: string) => Observable<any[]>;
}

export interface SilverSelectResult_I {
  action: boolean;
  value: any[];
  selected: any[];
  selectedObj: any[];
}

@Injectable({ providedIn: 'root' })
export class SilverSelectModalService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public async open(data: ModalDataI, options = {}) {
    {
      const f = data.options as SilverSelectOptionsI;
      f.returnKey ??= true;
      if (data.asyncFun) {
        f.filter = new SelectAsyncFilter<any>(data.api!, data.asyncOpt);
      } else {
        f.filterOpt = {};
        f.fuzzyOpt ??= { sort: true };
        f.filter = new SelectFilter<any>([], undefined, f.filterOpt);
        f.filter.update(data.data!, new FuzzySearch(data.data, f.searchParam, f.fuzzyOpt));
      }
    }
    const dialogRef = this.#dialog.open(SilverSelectModalComponent, {
      width: '400px',
      data: { data },
      ...options,
    });
    return (await lastValueFrom(dialogRef.afterClosed())) as SilverSelectResult_I;
  }
}

@Component({
  imports: [
    AsyncPipe,
    FormsModule,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    ReactiveFormsModule,
    ScrollingModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `<div mat-dialog-title style="font-size: 1.2rem" class="!px-4 !pb-3">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography !p-0 !text-inherit">
      <div class="flex flex-row">
        <form
          class="mx-5 flex flex-row grow items-end justify-end md:items-center md:justify-center border border-primary rounded-lg"
        >
          <mat-icon class="text-primary !text-3xl !w-11 !h-11 items-center !flex">search</mat-icon>
          <input
            [formControl]="field.filter!.filterFormControl"
            type="text"
            class="h-11 w-full border-none outline-none bg-transparent"
            placeholder="Search"
          />
        </form>
        <!-- <button>
          <mat-icon
            class="text-primary"
            matTooltip="Clear"
            (click)="field.filter!.filterFormControl.setValue('')"
            >close</mat-icon
          >
        </button> -->
      </div>

      <!-- <div class="tabs w-full h-12">
        <a class="tab tab-bordered w-1/2">List</a>
        <a class="tab tab-bordered w-1/2 tab-active">Selected</a>
      </div> -->
      <ul class="menu rounded-box" [class.hidden]="$any(field.filter).loading | async">
        <cdk-virtual-scroll-viewport
          [itemSize]="heights[field.mode]"
          [minBufferPx]="rows[field.mode] * heights[field.mode]"
          [maxBufferPx]="rows[field.mode] * heights[field.mode]"
          [style.height.px]="(rows[field.mode] - 1) * heights[field.mode]"
        >
          <li
            *cdkVirtualFor="
              let item of field.filter!.filteredList;
              let i = index;
              trackBy: identity
            "
            [class]="item.cssClass || ''"
          >
            <label class="label cursor-pointer justify-start">
              <input
                type="checkbox"
                (change)="select(item)"
                [checked]="selected.includes(field.vKey ? item[field.vKey] : item)"
                class="checkbox w-5 h-5 mr-2"
              />
              @if (field.mode === LIST_TYPE.TEXT || field.mode === LIST_TYPE.TEXT_DESC) {
                <a>
                  @if (field.mode === LIST_TYPE.TEXT_DESC) {
                    <div>
                      <div>{{ item[field.title] }}</div>
                      <div class="text-sm opacity-50">{{ item[field.desc] }}</div>
                    </div>
                  } @else {
                    <span class="label-text">{{ item[field.title] }}</span>
                  }
                </a>
              } @else {
                <a class="flex items-center gap-2">
                  <span class="avatar">
                    <div class="w-8 rounded-full"><img [src]="item[field.pic]" /></div>
                  </span>
                  @if (field.mode === LIST_TYPE.AVATAR_TEXT_DESC) {
                    <div>
                      <div>{{ item[field.title] }}</div>
                      <div class="text-sm opacity-50">{{ item[field.desc] }}</div>
                    </div>
                  } @else if (field.mode === LIST_TYPE.AVATAR_TEXT) {
                    <span class="label-text">{{ item[field.title] }}</span>
                  }
                </a>
              }
            </label>
          </li>
        </cdk-virtual-scroll-viewport>
      </ul>
      @if ($any(field.filter).loading | async) {
        <div class="flex" [style.height.px]="(rows[field.mode] - 1) * heights[field.mode] + 16">
          <span class="loading loading-dots loading-lg m-auto"></span>
        </div>
      }
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      @if (selected.length > 0) {
        <button
          [class]="daisyMerge('btn btn-outline btn-sm btn-error mr-3', btn1Class)"
          (click)="clearSelection()"
        >
          Clear ({{ selected.length }})
        </button>
      }
      <button
        [class]="daisyMerge('btn btn-outline btn-sm btn-error mr-3', btn1Class)"
        [mat-dialog-close]="false"
      >
        {{ btn1Name }}
      </button>
      <button
        [class]="daisyMerge('btn btn-outline btn-sm btn-primary', btn2Class)"
        (click)="submit()"
      >
        {{ btn2Name }}
      </button>
    </mat-dialog-actions>`,
})
export class SilverSelectModalComponent implements OnInit {
  readonly #dialogRef: MatDialogRef<SilverSelectModalComponent> = inject(MatDialogRef);
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly #data: { data: ModalDataI; ref: any } = inject(MAT_DIALOG_DATA);
  public formGroup!: FormGroup<any>;
  public modalData!: ModalDataI;
  public btn1Name!: string;
  public btn2Name!: string;
  public btn1Class!: string;
  public btn2Class!: string;
  public field!: SilverSelectOptionsI;
  public LIST_TYPE = LIST_TYPE;
  public value!: any[];
  public selected!: any[];
  public selectedObj!: any[];
  public daisyMerge = daisyMerge;
  public rows = {
    [LIST_TYPE.TEXT]: 12,
    [LIST_TYPE.TEXT_DESC]: 9,
    [LIST_TYPE.AVATAR_TEXT]: 10,
    [LIST_TYPE.AVATAR_TEXT_DESC]: 9,
  };
  public heights = {
    [LIST_TYPE.TEXT]: 36,
    [LIST_TYPE.TEXT_DESC]: 56,
    [LIST_TYPE.AVATAR_TEXT]: 48,
    [LIST_TYPE.AVATAR_TEXT_DESC]: 56,
  };

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    this.btn1Name = this.modalData.btn1Name ?? 'Cancel';
    this.btn2Name = this.modalData.btn2Name ?? 'Confirm';
    this.btn1Class = this.modalData.btn1Class ?? '';
    this.btn2Class = this.modalData.btn2Class ?? '';
    this.field = this.modalData.options as SilverSelectOptionsI;
    this.field!.filter!.afterSearch$.subscribe(() => {
      this.#cdRef.detectChanges();
    });
    this.value = this.field.value ? [...this.field.value] : [];
    const hasReturnKey = !!this.field.returnKey;
    if (hasReturnKey) {
      this.selected = this.value!;
      this.selectedObj = this.field.vKey
        ? this.field.selectedObj?.length === this.selected.length
          ? [...this.field.selectedObj]
          : [...Array(this.selected.length)]
        : [...this.selected];
    } else {
      this.selectedObj = this.value!;
      this.selected = this.field.vKey
        ? this.selectedObj.map((x) => x[this.field.vKey!])
        : [...this.selectedObj];
    }
    this.#cdRef.detectChanges();
  }

  clearSelection() {
    this.selected = [];
    this.selectedObj = [];
  }

  select(item: any) {
    const key = this.field.vKey ? item[this.field.vKey] : item;
    if (this.field.multiple) {
      if (this.selected.includes(key)) {
        const ix = this.selected.indexOf(key);
        this.selected.splice(ix, 1);
        this.selectedObj.splice(ix, 1);
      } else {
        this.selected.push(key);
        this.selectedObj.push(item);
      }
    } else {
      if (this.selected[0] === key) {
        this.selected[0] = undefined;
        this.selectedObj[0] = undefined;
      } else {
        this.selected[0] = key;
        this.selectedObj[0] = item;
      }
    }
  }

  public submit() {
    const res = {
      action: true,
      value: this.field.returnKey ? this.selected : this.selectedObj,
      selected: this.selected,
      selectedObj: this.selectedObj,
    };
    this.#dialogRef.close(res);
  }

  identity(_: number, item: any) {
    return item;
  }
}
