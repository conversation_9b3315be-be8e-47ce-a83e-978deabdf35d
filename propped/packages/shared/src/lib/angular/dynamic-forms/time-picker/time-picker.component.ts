import type { ElementRef, OnInit } from '@angular/core';
import { ChangeDetectionStrategy, Component, viewChild } from '@angular/core';
import { wait } from '../../../common/fun';

@Component({
  selector: 'app-time-picker',
  template: `<div class="ios-clock m-auto">
    <div class="picker"></div>
    <div class="row" #hourRow>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      @for (item of hours; track $index) {
        <div class="cell" (click)="inView(hourRow, $index)">{{ item }}</div>
      }
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
    </div>
    <div class="row" #minuteRow>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      @for (item of minutes; track $index) {
        <div class="cell" (click)="inView(minuteRow, $index)">{{ item }}</div>
      }
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
    </div>
    <div class="row" #amPmRow>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="cell" (click)="inView(amPmRow, 0)">AM</div>
      <div class="cell" (click)="inView(amPmRow, 1)">PM</div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
      <div class="blank-cell"></div>
    </div>
  </div> `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  styles: `
    .ios-clock {
      @apply flex flex-row w-[174px] relative;
      font-size: 1rem;
      user-select: none;
      .row {
        @apply px-4 h-[170px] overflow-x-hidden overflow-y-auto;
        scrollbar-width: none;
        scroll-snap-type: y mandatory;
        z-index: 1;
        .blank-cell {
          @apply w-[26px] h-[34px];
          scroll-snap-align: none;
        }
        .cell {
          cursor: pointer;
          @apply w-[26px] h-[34px];
          line-height: 34px;
          text-align: center;
          scroll-snap-align: center;
        }
      }
      .picker {
        @apply absolute h-[34px] border border-accent w-full;
        top: calc(50% - 17px);
      }
    }
  `,
})
export class TimePickerComponent implements OnInit {
  readonly hourRow = viewChild.required<ElementRef<HTMLDivElement>>('hourRow');
  readonly minuteRow = viewChild.required<ElementRef<HTMLDivElement>>('minuteRow');
  readonly amPmRow = viewChild.required<ElementRef<HTMLDivElement>>('amPmRow');
  readonly minutes = [...Array(60)].map((_, i) => i.toString().padStart(2, '0'));
  readonly hours = [...Array(12)].map((_, i) => (i + 1).toString().padStart(2, '0'));

  value: [number, number, string] = [1, 0, 'AM'];

  ngOnInit(): void {
    this.scroll();
  }

  getNthElement(v: HTMLDivElement, i: number) {
    const ix = Math.round(
      Math.round(v.scrollTop) / Math.round((v.children[0] as HTMLDivElement).offsetHeight),
    );
    return v.children[ix + i + 2] as HTMLDivElement;
  }

  getTime() {
    const hour = this.getNthElement(this.hourRow().nativeElement, 0).textContent;
    const minute = this.getNthElement(this.minuteRow().nativeElement, 0).textContent;
    const amPm = this.getNthElement(this.amPmRow().nativeElement, 0).textContent;
    return [Number(hour), Number(minute), amPm] as [number, number, string];
  }

  inView(el: HTMLDivElement, ix: number) {
    const pos = (ix + 1) * (el.children[0] as HTMLDivElement).offsetHeight;
    el.scrollTo({ top: pos, behavior: 'smooth' });
  }

  async inView2(el: HTMLDivElement, ix: number) {
    const pos = (ix + 1) * (el.children[0] as HTMLDivElement).offsetHeight;
    await wait();
    el.scrollTo({ top: pos, behavior: 'instant' });
  }

  scroll() {
    const [hour, minute, amPm] = this.value;
    this.inView2(this.hourRow().nativeElement, hour - 1);
    this.inView2(this.minuteRow().nativeElement, minute);
    this.inView2(this.amPmRow().nativeElement, amPm === 'AM' ? 0 : 1);
  }

  setTime(value: Date) {
    let hour = value.getHours();
    const minute = value.getMinutes();
    const amPm = hour < 12 ? 'AM' : 'PM';
    if (hour === 0) hour = 12;
    else if (hour > 12) hour -= 12;
    this.value = [hour, minute, amPm];
    this.scroll();
  }
}
