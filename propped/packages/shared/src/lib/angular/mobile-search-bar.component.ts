import { ChangeDetectionStrategy, Component, EventEmitter, Output, input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SVGIconComponent } from './svg-icon.component';

@Component({
  selector: 'silver-mobile-search-bar',
  imports: [SVGIconComponent, FormsModule],
  template: `<form
    (ngSubmit)="submitText.emit(searchBar.value)"
    class="flex flex-row items-center border border-neutral rounded-full ps-2  sm:max-w-80 !w-full"
    [tabIndex]="0"
  >
    <app-svg-icon class="text-neutral !text-3xl !w-11 !h-11 items-center !flex" [icon]="icon()" />
    <input
      #searchBar
      type="text"
      class="sm:h-11 h-auto w-full border-none outline-none bg-transparent"
      (input)="query.emit(searchBar.value)"
      [placeholder]="placeholder()"
    />
    <div
      class="text-neutral !w-8 !h-8 items-center !flex cursor-pointer"
      (click)="searchBar.value = ''; query.emit(''); submitText.emit('')"
    >
      @if (searchBar.value) {
        <app-svg-icon icon="iconify:mdi:close" class="text-xl" />
      }
    </div>
  </form>`,
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SilverMobileSearchBarComponent {
  readonly icon = input('iconify:mdi:magnify');
  readonly placeholder = input('Search');
  @Output() readonly query = new EventEmitter<string>();
  @Output() readonly submitText = new EventEmitter<string>();
}
