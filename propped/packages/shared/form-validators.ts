// import type { AbstractControl, ValidatorFn } from '@angular/forms';

// // https://stackoverflow.com/a/59317682/8784402
// export class CustomValidators {
//   public static pwdError = {
//     password:
//       '9-30 char with num, small, capital, special(!@#$%..) char required',
//   };

//   public static userNameError = { username: '6-12 alphanumeric and _ allowed' };
//   public static pwd =
//     /^(?=(.*[a-z]){1,})(?=(.*[A-Z]){1,})(?=(.*[0-9]){1,})(?=(.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]){1,}).{9,30}$/;

//   public static usernameRegex = /^[A-z][A-z0-9_]{5,11}$/;
//   public static tokenRegex = (n: number) => new RegExp(`^\\d{${n}}$`);

//   public static password(
//     control: AbstractControl,
//   ): Record<string, string> | null {
//     const { value } = control;
//     if (!value || value.match(CustomValidators.pwd) !== null) return null;
//     return CustomValidators.pwdError;
//   }

//   public static username(
//     control: AbstractControl,
//   ): Record<string, string> | null {
//     const { value } = control;
//     if (!value || value.match(CustomValidators.usernameRegex) !== null)
//       return null;
//     return CustomValidators.userNameError;
//   }

//   public static noLeadingTrailingSpace(
//     control: AbstractControl,
//   ): Record<string, string> | null {
//     const { value } = control;
//     if (!value || (!value.startsWith(' ') && !value.endsWith(' '))) return null;
//     return { noLeadingTrailingSpace: 'Leading and trailing space not allowed' };
//   }

//   public static numberToken(size: number) {
//     const regex = CustomValidators.tokenRegex(size);
//     return (control: AbstractControl): Record<string, string> | null => {
//       const { value } = control;
//       if (!value || value.toString().match(regex) !== null) return null;
//       return CustomValidators.userNameError;
//     };
//   }

//   public static equalTo(equalControl: AbstractControl): ValidatorFn {
//     let subscribe = false;
//     return (control: AbstractControl) => {
//       if (!subscribe) {
//         subscribe = true;
//         equalControl.valueChanges.subscribe(() => {
//           control.updateValueAndValidity();
//         });
//       }
//       const v = control.value;
//       return equalControl.value === v
//         ? null
//         : { equalTo: { equalTo: equalControl.value, actual: v } };
//     };
//   }
// }
