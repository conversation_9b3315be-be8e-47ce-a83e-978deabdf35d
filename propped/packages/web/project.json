{"name": "web", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "packages/web/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/web", "index": "packages/web/src/index.html", "browser": "packages/web/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "packages/web/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "packages/web/public"}], "styles": ["packages/web/src/styles.scss"], "scripts": []}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "5000000kb", "maximumError": "10000mb"}, {"type": "anyComponentStyle", "maximumWarning": "20000kb", "maximumError": "40000kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "packages/web/src/environments/environment.ts", "with": "packages/web/src/environments/environment.prod.ts"}]}, "test": {"budgets": [{"type": "initial", "maximumWarning": "5000000kb", "maximumError": "10000mb"}, {"type": "anyComponentStyle", "maximumWarning": "20000kb", "maximumError": "40000kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "packages/web/src/environments/environment.ts", "with": "packages/web/src/environments/environment.test.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "assets": [{"glob": "**/*", "input": "packages/web/public"}, {"glob": "**/*", "input": "icons", "output": "/icons/"}]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "web:build:production"}, "development": {"buildTarget": "web:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "packages/web/proxy.conf.json"}}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "web:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "web:build", "staticFilePath": "dist/web/browser", "spa": true}}}}