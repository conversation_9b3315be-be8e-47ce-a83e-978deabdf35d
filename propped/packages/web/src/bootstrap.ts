import { bootstrapApplication } from '@angular/platform-browser';
import { useControllerConfig } from '@lib/angular/controller.hook';
import { PREFETCHED_ICONS } from '@lib/angular/svg-icon.component';
import { RootServiceTester } from '@lib/common/test-root-service';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';
import { environment } from './environments/environment';
import './polyfills';

export const bootstrap = () => {
  RootServiceTester.check = !environment.production;
  useControllerConfig.baseUrl = environment.domain + environment.apiDomain;
  const loadSvg = async () => {
    const filename = document.querySelector('meta[name=svg-file]')?.getAttribute('href');
    if (!filename || filename.includes('no.file.json')) return;
    const json = await fetch(filename).then((v) => v.json());
    PREFETCHED_ICONS.icons = json;
  };
  loadSvg();
  bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err));
};
