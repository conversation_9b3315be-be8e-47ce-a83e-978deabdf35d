(() => {
  /*
  description: fix safari audio context
  */
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  if (!isSafari) return;
  const AudioContext: typeof window.AudioContext =
    window.AudioContext || (window as any).webkitAudioContext;
  let audioContext: AudioContext;
  if (window.AudioContext) {
    audioContext = (window as any).audioContext = new AudioContext();
  }
  const fixAudioContext = () => {
    if (audioContext) {
      // Create empty buffer
      const buffer = audioContext.createBuffer(1, 1, 22050);
      const source = audioContext.createBufferSource();
      source.buffer = buffer;
      // Connect to output (speakers)
      source.connect(audioContext.destination);
      // Play sound
      if (source.start) {
        source.start(0);
      } else if ((source as any).play) {
        (source as any).play(0);
      } else if ((source as any).noteOn) {
        (source as any).noteOn(0);
      }
    }
    // Remove events
    document.removeEventListener('touchstart', fixAudioContext);
    document.removeEventListener('touchend', fixAudioContext);
  };
  // iOS 6-8
  document.addEventListener('touchstart', fixAudioContext);
  // iOS 9
  document.addEventListener('touchend', fixAudioContext);
})();

(Object as any).groupBy = <T>(x: T[], f: (v: T, i: number, x: T[]) => string) =>
  x.reduce((a, b, i) => ((a[f(b, i, x)] ||= []).push(b), a), {} as Record<string, T[]>);

const globalVar = window as any;
globalVar.process = { env: {} };
globalVar.global = globalVar;
