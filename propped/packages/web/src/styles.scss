@use 'animate.css' as *;
@use './styles/material' as *;
@use './styles/scroll-bars.scss' as *;
@use './styles/animation.scss' as *;
@use './styles/dialog.scss' as *;
@import 'mapbox-gl/dist/mapbox-gl.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: Inter, 'Helvetica Neue', sans-serif;
}

app-svg-icon {
  font-size: 1.5rem;
}

.zo-general-card {
  @apply h-44 w-44 rounded-xl flex flex-col bg-base-200 gap-x-4 justify-center items-center cursor-pointer transition ease-in-out hover:scale-110 p-2 text-center;
}

.parent-drawer-content > *:last-child {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
}
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(6px);
  }
  100% {
    transform: translateY(0px);
  }
}

.floating-arrow {
  animation: float 1.5s ease-in-out infinite;
}

.user-location-marker {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 123, 255, 0.5);
  border: 3px solid rgba(0, 123, 255, 1);
  box-shadow: 0 0 0 rgba(0, 123, 255, 0.4);
  animation: pulse 2s infinite;
}
