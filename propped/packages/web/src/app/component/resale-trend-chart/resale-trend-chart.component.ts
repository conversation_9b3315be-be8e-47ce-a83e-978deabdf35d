import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ViewChild,
  type AfterViewInit,
  type ElementRef,
  type OnChanges,
  type OnDestroy,
  type SimpleChanges,
} from '@angular/core';
import { Chart, registerables } from 'chart.js';

Chart.register(...registerables);

export interface ResaleTrendChartData {
  // Chart title
  title: string;

  // Selected filters to display
  filters: {
    town?: string;
    street?: string;
    flatType?: string[];
    dateRange?: { from: string; to: string };
    sortBy?: string;
    chartType?: string;
  };

  // Data for the chart
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string;
    pointRadius?: number;
    pointBackgroundColor?: string;
    borderWidth?: number;
    showLine?: boolean;
    fill?: boolean;
    borderDash?: number[];
    tension?: number;
    spanGaps?: boolean;
  }[];

  // Chart type: 'line' or 'bar'
  chartType?: 'line' | 'bar';
}

@Component({
  selector: 'app-resale-trend-chart',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="w-full h-full flex flex-col">
      <!-- Chart Header -->
      <div class="p-2 text-center text-black font-semibold text-xl dark:text-white">
        {{ chartData.title || 'Resale Trend' }}
      </div>

      <!-- Filter Summary -->
      <div class="flex items-center justify-center gap-2">
        @for (part of filterSummary; track $index) {
          <button class="px-2 py-1 text-sm text-gray-600 border border-gray-700 rounded">
            {{ part }}
          </button>
        }
      </div>

      <!-- Chart Container -->
      <div class="grow overflow-x-auto">
        <canvas #chartCanvas class="w-full"></canvas>
      </div>
    </div>
  `,
})
export class ResaleTrendChartComponent implements AfterViewInit, OnChanges, OnDestroy {
  @ViewChild('chartCanvas') chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() chartData!: ResaleTrendChartData;

  public chart!: Chart;
  public filterSummary: string[] = [];
  private isViewInitialized = false;

  ngAfterViewInit() {
    this.isViewInitialized = true;
    if (this.chartData) {
      this.initializeChart();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['chartData'] && !changes['chartData'].firstChange && this.isViewInitialized) {
      this.initializeChart();
    }
  }

  private initializeChart() {
    this.generateFilterSummary();
    this.drawChart();
  }

  private generateFilterSummary() {
    const filters = this.chartData.filters;
    this.filterSummary = [];
    if (filters.town && filters.town !== 'All Towns') {
      this.filterSummary.push(`Town: ${filters.town}`);
    }

    if (filters.street && filters.street !== 'All Streets') {
      this.filterSummary.push(`Street: ${filters.street}`);
    }

    if (
      filters.flatType &&
      filters.flatType.length > 0 &&
      !(filters.flatType.length === 1 && filters.flatType[0] === 'All Types')
    ) {
      this.filterSummary.push(`Flat Type: ${filters.flatType.join(', ')}`);
    }

    if (filters.dateRange) {
      const from = this.formatDate(filters.dateRange.from);
      const to = this.formatDate(filters.dateRange.to);
      this.filterSummary.push(`Period: ${from} - ${to}`);
    }

    if (filters.sortBy) {
      this.filterSummary.push(`Sort By: ${filters.sortBy}`);
    }

    if (filters.chartType) {
      this.filterSummary.push(`Chart Type: ${filters.chartType}`);
    }
  }

  private formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  }

  private drawChart() {
    if (!this.chartCanvas?.nativeElement || !this.chartData) {
      console.error('Chart canvas element or chart data is not available.');
      return;
    }

    // Format labels based on the sort option
    const formattedLabels = this.formatLabels(this.chartData.labels, this.chartData.filters.sortBy);

    // Destroy previous chart if it exists
    this.chart?.destroy();

    // Create new chart
    this.chart = new Chart(this.chartCanvas.nativeElement, {
      type: this.chartData.chartType || 'line',
      data: {
        labels: formattedLabels,
        datasets: this.chartData.datasets,
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: true,
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          },
        },
        scales: {
          x: {
            ticks: {
              autoSkip: true,
              maxTicksLimit: 12,
              maxRotation: 45,
              minRotation: 0,
            },
          },
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  /**
   * Format labels based on the sort option
   * @param labels Original labels
   * @param sortBy Sort option (Month, Quarter, Year)
   * @returns Formatted labels
   */
  private formatLabels(labels: string[], sortBy?: string): string[] {
    if (!labels || !sortBy) return labels;

    return labels.map((label) => {
      // Parse the label - assuming it contains date information
      try {
        const date = new Date(label);

        switch (sortBy.toLowerCase()) {
          case 'quarter':
            // Format as Q1-2024, Q2-2024, etc.
            const quarter = Math.floor(date.getMonth() / 3) + 1;
            return `Q${quarter}-${date.getFullYear()}`;

          case 'month':
            // Format as JAN-2024, FEB-2024, etc.
            return date
              .toLocaleDateString('en-US', {
                month: 'short',
                year: 'numeric',
              })
              .toUpperCase()
              .replace(' ', '-');

          case 'year':
            // Format as 2022, 2023, 2024, etc.
            return date.getFullYear().toString();

          default:
            return label;
        }
      } catch (e) {
        // If parsing fails, return the original label
        return label;
      }
    });
  }

  ngOnDestroy() {
    this.chart?.destroy();
  }
}
