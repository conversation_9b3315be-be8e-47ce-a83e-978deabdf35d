import {
  Component,
  type ElementRef,
  Injectable,
  type OnDestroy,
  type OnInit,
  inject,
  viewChild,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, type MatDialogConfig } from '@angular/material/dialog';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { Chart, registerables } from 'chart.js';
import { firstValueFrom } from 'rxjs';

Chart.register(...registerables);

interface DualChartDataI {
  heading: string;
  header: string[]; // e.g. ['Date', 'Median Price', 'Average PSF']
  data: any[][]; // [['Jan 2024', 500000, 700], ...]
}

@Component({
  selector: 'app-dual-chart',
  standalone: true,
  template: `
    <div class="relative">
      <div class="absolute top-0 left-[20%] z-[9999999] w-[60%] h-full flex flex-col gap-6 p-4">
        <div class="text-center text-xl font-semibold">{{ heading }}</div>

        <div class="grow border rounded p-3 shadow-md">
          <div class="text-sm font-bold text-center mb-2">Monthly Median Price</div>
          <canvas #medianChartCanvas class="w-full"></canvas>
        </div>

        <div class="grow border rounded p-3 shadow-md">
          <div class="text-sm font-bold text-center mb-2">Monthly Average PSF</div>
          <canvas #psfChartCanvas class="w-full"></canvas>
        </div>
      </div>
    </div>
  `,
})
export class DualChartDataComponent implements OnInit, OnDestroy {
  readonly medianChartCanvas = viewChild<ElementRef<HTMLCanvasElement>>('medianChartCanvas');
  readonly psfChartCanvas = viewChild<ElementRef<HTMLCanvasElement>>('psfChartCanvas');
  readonly data: { data: DualChartDataI } = inject(MAT_DIALOG_DATA);
  public heading = this.data.data.heading;

  private medianChart!: Chart;
  private psfChart!: Chart;

  ngOnInit() {
    this.drawCharts(this.data.data);
  }

  drawCharts(config: DualChartDataI) {
    const labels = config.data.map((d) => d[0]);

    const medianPrices = config.data.map((d) => d[1]);
    const averagePSFs = config.data.map((d) => d[2]);

    this.medianChart = new Chart(this.medianChartCanvas().nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Median Price',
            data: medianPrices,
            fill: false,
            tension: 0.2,
          },
        ],
      },
      options: { responsive: true, scales: { y: { beginAtZero: false } } },
    });

    this.psfChart = new Chart(this.psfChartCanvas().nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Average PSF',
            data: averagePSFs,
            fill: false,
            tension: 0.2,
          },
        ],
      },
      options: { responsive: true, scales: { y: { beginAtZero: false } } },
    });
  }

  ngOnDestroy() {
    this.medianChart?.destroy();
    this.psfChart?.destroy();
  }
}

@Injectable({ providedIn: 'root' })
export class DualChartViewerService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: DualChartDataI, options: MatDialogConfig<DualChartDataComponent> = {}) {
    const dialogRef = this.#dialog.open(DualChartDataComponent, {
      width: '60vw',
      height: '70vh',
      maxWidth: '800px',
      maxHeight: '600px',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__fadeIn'],
      ...options,
    });
    return firstValueFrom(dialogRef.afterClosed());
  }
}
