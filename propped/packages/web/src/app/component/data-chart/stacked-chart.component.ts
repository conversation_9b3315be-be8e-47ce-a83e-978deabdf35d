import {
  type AfterViewInit,
  Component,
  type ElementRef,
  Input,
  type OnChanges,
  type On<PERSON><PERSON>roy,
  type SimpleChang<PERSON>,
  ViewChild,
} from '@angular/core';
import { Chart, registerables } from 'chart.js';

Chart.register(...registerables);

interface ChartDataI {
  heading: string;
  header: any[];
  data: any[][];
  formattedLabels: any[];
  extraPoint?: {
    label: string;
    values: number[];
    labels?: string[];
  };
}

@Component({
  selector: 'app-stacked-chart',
  template: `
    <div class="w-full h-full flex flex-col">
      <div class="p-2 text-center font-semibold text-lg dark:text-white">{{ heading }}</div>
      <div class="grow overflow-x-auto">
        <canvas #chartCanvas class="w-full"></canvas>
      </div>
    </div>
  `,
})
export class StackedChartComponent implements AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('chartCanvas') chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() chartData!: ChartDataI;

  public chart!: Chart;
  public heading = '';
  private isViewInitialized = false;

  ngAfterViewInit() {
    this.isViewInitialized = true;
    if (this.chartData) {
      this.initializeChart();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['chartData'] && !changes['chartData'].firstChange && this.isViewInitialized) {
      this.initializeChart();
    }
  }

  private initializeChart() {
    this.heading = this.chartData.heading;
    this.drawChart(this.chartData);
  }

  private drawChart(config: ChartDataI) {
    // Initialize labels and datasets
    let labels: string[] = [];
    let datasets: any[] = [];

    // Case 1 & 3: If data exists, process it
    if (config.data && config.data.length > 0) {
      labels = config.data.map((d) => d[0]); // Get labels from data
      datasets = config.header.slice(1).map((h, i) => ({
        label: h,
        data: config.data.map((d) => d[i + 1]),
        backgroundColor: this.getColor(i, 0.5),
        borderColor: this.getColor(i, 1),
        borderWidth: 2,
        fill: true,
      }));
    }

    // Case 1 & 3: If extraPoint exists, add it
    if (config.extraPoint) {
      // If we don't have any labels yet (Case 1: only extraPoint exists)
      if (labels.length === 0) {
        // Use provided labels if available, otherwise use default label
        if (config.extraPoint.labels && config.extraPoint.labels.length > 0) {
          labels = config.extraPoint.labels;
        } else {
          // Fallback to using a single label
          labels = [config.extraPoint.label];
        }

        // Create a single dataset that connects all bookmark points
        datasets.push({
          label: 'Bookmarked Transactions',
          data: config.extraPoint.values,
          backgroundColor: 'rgba(128, 0, 128, 0.3)',
          borderColor: 'purple',
          borderWidth: 2,
          fill: false,
          tension: 0.1, // Adds slight curve to the line
          pointRadius: 5,
          pointBackgroundColor: 'purple',
        });
      }
      // Case 3: Both data and extraPoint exist
      else {
        // Create a dataset for extraPoint that properly maps to the existing labels
        // This is the key fix - we need to map the bookmarked points to the same labels as the main data
        const extraPointData: (number | null)[] = [];

        // For each label in our chart
        labels.forEach((label) => {
          // Check if we have a matching bookmarked point for this label
          if (config.extraPoint?.labels) {
            const matchIndex = config.extraPoint.labels.findIndex(
              (bookmarkLabel) => bookmarkLabel === label,
            );
            if (matchIndex !== -1) {
              // We found a matching label, add the corresponding value
              extraPointData.push(config.extraPoint.values[matchIndex]);
            } else {
              // No matching label, add null to maintain the line's continuity
              extraPointData.push(null);
            }
          } else {
            // If no labels are provided for extraPoint, we can't match them
            extraPointData.push(null);
          }
        });

        // Add the dataset with properly mapped data points
        datasets.push({
          label: 'Bookmarked Transactions',
          data: extraPointData,
          backgroundColor: 'rgba(128, 0, 128, 0.3)',
          borderColor: 'purple',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
          pointRadius: 5,
          pointBackgroundColor: 'purple',
          spanGaps: true, // This ensures the line connects across null values
        });
      }
    }

    // Only draw if we have data to display
    if (labels.length > 0 && datasets.length > 0) {
      this.draw(labels, datasets);
    } else {
      console.warn('No data available to draw chart');
    }
  }

  private draw(
    labels: string[],
    datasets: {
      label: string;
      data: number[];
      borderColor?: string;
      backgroundColor?: string;
      pointRadius?: number;
      pointBackgroundColor?: string;
      borderWidth?: number;
      showLine?: boolean;
      fill?: boolean;
      borderDash?: number[];
      tension?: number;
      spanGaps?: boolean;
    }[],
  ) {
    if (!this.chartCanvas?.nativeElement) {
      console.error('Chart canvas element is not available.');
      return;
    }

    this.chart?.destroy();

    this.chart = new Chart(this.chartCanvas.nativeElement, {
      type: 'line',
      data: {
        labels,
        datasets,
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: true,
            position: 'top',
          },
        },
        scales: {
          x: {
            stacked: false, // Changed to false to better display the month-year labels
            ticks: {
              autoSkip: true,
              maxTicksLimit: 10,
              maxRotation: 45, // Allow rotation for better readability
              minRotation: 0,
            },
          },
          y: {
            stacked: false, // Changed to false for bookmark-only view
            beginAtZero: true,
          },
        },
      },
    });
  }

  private getColor(index: number, opacity = 1): string {
    const colors = [
      `rgba(75, 192, 192, ${opacity})`, // teal
      `rgba(255, 99, 132, ${opacity})`, // red
      `rgba(54, 162, 235, ${opacity})`, // blue
      `rgba(255, 206, 86, ${opacity})`, // yellow
      `rgba(153, 102, 255, ${opacity})`, // purple
      `rgba(255, 159, 64, ${opacity})`, // orange
    ];
    return colors[index % colors.length];
  }

  ngOnDestroy() {
    this.chart?.destroy();
  }
}
