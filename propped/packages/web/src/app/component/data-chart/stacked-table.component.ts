import { Component, Injectable, Input, type <PERSON><PERSON><PERSON><PERSON>, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  type MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { RouterModule } from '@angular/router';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { firstValueFrom } from 'rxjs';

interface TableDataI {
  heading: string;
  header: string[];
  data: any[][];
}

@Component({
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    RouterModule,
  ],
  selector: 'app-data-table',
  template: `<div class="w-full h-full flex flex-col rounded-lg shadow-lg p-4">
    <div class="py-4 text-center font-bold text-lg">{{ heading }}</div>
    <button
      (click)="onShowChartClick()"
      class="absolute top-4 right-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
    >
      Show Chart
    </button>
    <div>
      <table class="w-full table">
        <thead class="">
          <tr class="">
            @for (col of tableData.header; track col) {
              <th class="">{{ col }}</th>
            }
          </tr>
        </thead>
        <tbody>
          @for (row of tableData.data; track row) {
            <tr class="  hover:bg-base-200 transition-all">
              @for (cell of row; track cell) {
                <td>{{ cell }}</td>
              }
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>`,
})
export class DataTableComponent implements OnInit, OnDestroy {
  @Input() tableData!: TableDataI;
  readonly data: { data: TableDataI } = inject(MAT_DIALOG_DATA);
  readonly dialogRef = inject(MatDialogRef<DataTableComponent>);
  public heading = '';

  ngOnInit() {
    console.log('TABLE HEADING', this.data);
    this.initializeTable();
  }

  private initializeTable() {
    if (this.data?.data) {
      this.heading = this.data.data.heading;
      this.tableData = this.data.data;
    }
  }
  onShowChartClick() {
    this.dialogRef.close({ showChart: true });
  }

  ngOnDestroy() {
    console.log('Table component destroyed.');
  }
}

@Injectable({ providedIn: 'root' })
export class TableViewerService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: TableDataI, options: MatDialogConfig<DataTableComponent> = {}) {
    const dialogRef = this.#dialog.open(DataTableComponent, {
      width: '50vw',
      height: '60vh',
      maxHeight: '60vh',
      maxWidth: '50vw',
      data: { data },
      enterAnimationDuration: 0,
      panelClass: ['animate__animated', 'animate__bounceIn'],
      ...options,
    });
    return firstValueFrom(dialogRef.afterClosed());
  }
}
