import { inject, Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import type { Observable } from 'rxjs';
import { AnnotationModalComponent } from './annotation-modal.component';

@Injectable({
  providedIn: 'root',
})
export class AnnotationModalService {
  private dialog = inject(MatDialog);

  openAnnotationModal(): Observable<any> {
    const dialogRef = this.dialog.open(AnnotationModalComponent, {
      width: '500px',
      maxWidth: '95vw',
      maxHeight: '95vh',
      panelClass: 'annotation-modal',
      disableClose: false,
    });

    return dialogRef.afterClosed();
  }
}
