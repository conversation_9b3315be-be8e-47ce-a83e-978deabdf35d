import { CommonModule } from '@angular/common';
import type { AfterViewInit, ElementRef, OnDestroy } from '@angular/core';
import { Component, inject, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-annotation-modal',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, MatIconModule],
  template: `
    <div class="annotation-modal p-6">
      <div class="absolute top-0 right-2">
        <mat-icon class="cursor-pointer dark:text-white" (click)="dialogRef.close()"
          >close</mat-icon
        >
      </div>
      <h2 class="text-xl font-bold text-center mb-2">Annotate on Floorplan</h2>
      <p class="text-center mb-4">
        Use this feature to mark any location on the floorplan to make it easier to ask the seller a
        question
      </p>

      <div class="relative mb-4 border border-gray-300 rounded-lg overflow-hidden">
        <!-- Floorplan Image -->
        <img
          #floorplanImage
          src="assets/floor-plan-1.jpg"
          alt="Floorplan"
          class="w-full object-contain"
        />
        <!-- Canvas Overlay -->
        <canvas
          #annotationCanvas
          class="absolute top-0 left-0 w-full h-full cursor-crosshair"
        ></canvas>
      </div>

      <!-- Tool Controls -->
      <div class="flex justify-center space-x-4 mb-4">
        <button
          class="flex items-center px-6 py-2 rounded-full"
          [ngClass]="{
            'bg-blue-600 text-white': drawMode === 'draw',
            'bg-transparent border border-blue-600 text-black dark:text-white': drawMode !== 'draw',
          }"
          (click)="setDrawMode('draw')"
        >
          <mat-icon class="mr-2 dark:text-white">edit</mat-icon>
          Draw
        </button>
        <button
          class="flex items-center px-6 py-2 rounded-full"
          [ngClass]="{
            'bg-blue-600 text-white': drawMode === 'erase',
            'bg-transparent border border-blue-600 text-black dark:text-white':
              drawMode !== 'erase',
          }"
          (click)="setDrawMode('erase')"
        >
          <mat-icon class="mr-2 dark:text-white">cleaning_services</mat-icon>
          Erase
        </button>
      </div>

      <!-- Save Button -->
      <button
        color="primary"
        class="w-full py-2 bg-blue-600 text-white rounded-md flex items-center justify-center"
        [disabled]="!hasAnnotations"
        (click)="saveAnnotations()"
      >
        Save Annotations to Chat
      </button>
    </div>
  `,
  styles: [
    `
      .annotation-modal {
        max-width: 500px;
        margin: 0 auto;
      }
    `,
  ],
})
export class AnnotationModalComponent implements AfterViewInit, OnDestroy {
  @ViewChild('annotationCanvas') canvasRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('floorplanImage') imageRef!: ElementRef<HTMLImageElement>;

  private ctx!: CanvasRenderingContext2D;
  private isDrawing = false;
  private lastX = 0;
  private lastY = 0;

  drawMode: 'draw' | 'erase' = 'draw';
  hasAnnotations = false;

  // Use inject for MatDialogRef
  public dialogRef = inject(MatDialogRef<AnnotationModalComponent>);

  ngAfterViewInit(): void {
    // Wait for the image to load to set up the canvas correctly
    this.imageRef.nativeElement.onload = () => {
      this.setupCanvas();
    };

    // If the image is already loaded (from cache)
    if (this.imageRef.nativeElement.complete) {
      this.setupCanvas();
    }
  }

  ngOnDestroy(): void {
    // Remove event listeners to prevent memory leaks
    const canvas = this.canvasRef?.nativeElement;
    if (canvas) {
      canvas.removeEventListener('mousedown', this.startDrawing.bind(this));
      canvas.removeEventListener('mousemove', this.draw.bind(this));
      canvas.removeEventListener('mouseup', this.stopDrawing.bind(this));
      canvas.removeEventListener('mouseout', this.stopDrawing.bind(this));
      canvas.removeEventListener('touchstart', this.handleTouchStart.bind(this));
      canvas.removeEventListener('touchmove', this.handleTouchMove.bind(this));
      canvas.removeEventListener('touchend', this.stopDrawing.bind(this));
    }
  }

  private setupCanvas(): void {
    const canvas = this.canvasRef.nativeElement;
    const img = this.imageRef.nativeElement;

    // Set canvas dimensions to match the image
    canvas.width = img.width;
    canvas.height = img.height;

    // Get the canvas context
    this.ctx = canvas.getContext('2d')!;
    this.ctx.lineJoin = 'round';
    this.ctx.lineCap = 'round';
    this.ctx.lineWidth = 5;
    this.ctx.strokeStyle = '#FF0000'; // Red color for annotations

    // Add event listeners for drawing
    canvas.addEventListener('mousedown', this.startDrawing.bind(this));
    canvas.addEventListener('mousemove', this.draw.bind(this));
    canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
    canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

    // Touch events for mobile
    canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    canvas.addEventListener('touchend', this.stopDrawing.bind(this));
  }

  private startDrawing(e: MouseEvent): void {
    this.isDrawing = true;
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    this.lastX = e.clientX - rect.left;
    this.lastY = e.clientY - rect.top;
  }

  private draw(e: MouseEvent): void {
    if (!this.isDrawing) return;

    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    this.ctx.beginPath();

    if (this.drawMode === 'erase') {
      this.ctx.globalCompositeOperation = 'destination-out';
      this.ctx.lineWidth = 20; // Wider for eraser
    } else {
      this.ctx.globalCompositeOperation = 'source-over';
      this.ctx.lineWidth = 5;
      this.ctx.strokeStyle = '#FF0000';
    }

    this.ctx.moveTo(this.lastX, this.lastY);
    this.ctx.lineTo(currentX, currentY);
    this.ctx.stroke();

    this.lastX = currentX;
    this.lastY = currentY;
    this.hasAnnotations = true;
  }

  private stopDrawing(): void {
    this.isDrawing = false;
  }

  private handleTouchStart(e: TouchEvent): void {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY,
    });
    this.startDrawing(mouseEvent);
  }

  private handleTouchMove(e: TouchEvent): void {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY,
    });
    this.draw(mouseEvent);
  }

  setDrawMode(mode: 'draw' | 'erase'): void {
    this.drawMode = mode;
  }

  saveAnnotations(): void {
    // Create a composite image with the floorplan and annotations
    const canvas = document.createElement('canvas');
    const img = this.imageRef.nativeElement;

    canvas.width = img.width;
    canvas.height = img.height;

    const ctx = canvas.getContext('2d')!;

    // Draw the floorplan image first
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    // Draw the annotations on top
    ctx.drawImage(this.canvasRef.nativeElement, 0, 0);

    // Convert to data URL
    const annotatedImageUrl = canvas.toDataURL('image/png');

    // Close the dialog and return the annotated image
    this.dialogRef.close({ annotatedImage: annotatedImageUrl });
  }
}
