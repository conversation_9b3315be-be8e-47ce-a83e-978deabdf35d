import {
  animate,
  animateChild,
  group,
  query,
  style,
  transition,
  trigger,
} from '@angular/animations';

export const slideInAnimation = trigger('routeAnimations', [
  transition(':increment', [
    style({ position: 'relative' }),
    query(
      ':enter, :leave',
      [style({ position: 'absolute', top: '40px', left: 0, bottom: 0, width: '100%' })],
      { optional: true },
    ),
    query(':enter', [style({ transform: 'translateX(100%)' })], { optional: true }),
    query(':leave', animateChild(), { optional: true }),
    group([
      query(
        ':leave',
        [animate('200ms ease-out', style({ transform: 'translateX(-100%)' /* , opacity: 0  */ }))],
        { optional: true },
      ),
      query(':enter', [animate('200ms ease-out', style({ transform: 'translateX(0)' }))], {
        optional: true,
      }),
      query('@*', animateChild(), { optional: true }),
    ]),
  ]),
  transition(':decrement', [
    style({ position: 'relative' }),
    query(
      ':enter, :leave',
      [style({ position: 'absolute', top: '40px', left: 0, bottom: 0, width: '100%' })],
      { optional: true },
    ),
    query(':enter', [style({ transform: 'translateX(-100%)' })], { optional: true }),
    query(':leave', animateChild(), { optional: true }),
    group([
      query(
        ':leave',
        [animate('200ms ease-out', style({ transform: 'translateX(100%)' /* , opacity: 0  */ }))],
        { optional: true },
      ),
      query(':enter', [animate('200ms ease-out', style({ transform: 'translateX(0)' }))], {
        optional: true,
      }),
      query('@*', animateChild(), { optional: true }),
    ]),
  ]),
]);
