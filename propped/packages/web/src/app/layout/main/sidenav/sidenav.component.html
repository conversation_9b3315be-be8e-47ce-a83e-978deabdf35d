<div class="flex flex-col">
  <app-svg-icon
    class="!w-4/5 !h-auto mt-3 m-auto"
    style="color: #7974ff"
    icon="iconify:cbi:start-tv"
    width="auto"
    height="auto"
  />
</div>
<div>
  <silver-search-bar class="mx-3 mt-3" (query)="search($event)" />
  @for (item of searchMenus; track item) {
    <div>
      <div class="p-4 uppercase text-lg">{{ item.title }}</div>
      @for (item of item.children; track item) {
        <div>
          @if (item.type === 'item' && searchResult.includes(item)) {
            <a
              class="p-3 flex flex-row items-start justify-center hover:bg-base-200 mx-4 rounded-lg opacity-60 text-base select-none no-underline outline-none"
              [routerLink]="item.url"
              [routerLinkActiveOptions]="{ exact: true }"
              routerLinkActive="!bg-base-200 !text-base-content !opacity-100 animate__animated animate__pulse"
            >
              @if (item.icon) {
                <mat-icon class="mr-4">{{ item.icon }}</mat-icon>
              } @else if (item.iconSrc) {
                <app-svg-icon class="mr-4" [icon]="item.iconSrc" />
              }
              <div class="grow">{{ item.title }}</div>
            </a>
          }
        </div>
      }
    </div>
  }
</div>
