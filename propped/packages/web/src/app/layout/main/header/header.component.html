<div class="h-[100px] pl-3 flex flex-row items-center justify-center bg-base-200">
  <button (click)="toggleSideBar()" class="btn btn-sm btn-square btn-ghost !h-auto !min-h-0">
    <span class="material-symbols-sharp">menu</span>
  </button>

  <div class="ml-auto flex flex-row items-center h-full">
    @if (fileDownloaderService._fileList.length > 0) {
      <mat-icon
        matTooltip="Download Dashboard"
        class="m-auto cursor-pointer"
        aria-hidden="false"
        (click)="fileDownloaderService.showPanel()"
        [matBadge]="fileDownloaderService._fileList.length"
        >downloading</mat-icon
      >
    }
    <mat-icon
      matTooltip="Change theme"
      class="m-auto cursor-pointer mx-4"
      (click)="setTheme(theme === 'light' ? 'dark' : 'light')"
      >{{ theme === 'dark' ? 'light_mode' : 'dark_mode' }}</mat-icon
    >

    <div class="dropdown dropdown-bottom dropdown-end">
      <div
        tabindex="0"
        class="!h-auto !min-h-0 btn btn-ghost rounded-btn px-1.5 hover:bg-base-content/20"
      >
        <div class="flex items-center gap-2">
          <div class="avatar">
            <div class="mask mask-squircle" style="width: 30px; height: 30px">
              @if (loginUser?.pic) {
                <img
                  class="h-8 w-8 object-cover rounded-full"
                  [ngSrc]="loginUser?.pic"
                  placeholder
                  width="32"
                  height="32"
                />
              }
            </div>
          </div>
          <div class="flex flex-col items-start">
            {{ loginUser?.name }}
          </div>
        </div>
      </div>
      <ul
        tabindex="0"
        class="dropdown-content menu p-2 shadow bg-base-100 rounded-box mt-4 w-52"
        role="menu"
      >
        <li (click)="updateUser(loginUser)">
          <div><mat-icon>person</mat-icon>Account</div>
        </li>
        <li (click)="logout()">
          <div class="text-error"><mat-icon>logout</mat-icon>Logout</div>
        </li>
      </ul>
    </div>

    <button
      (click)="toggleWidgetsPanel()"
      matTooltip="Widgets"
      class="p8 s40 mr4 !h-auto !min-h-0 btn btn-sm btn-square btn-ghost"
    >
      <mat-icon class="widgets">widgets</mat-icon>
    </button>
  </div>
</div>
