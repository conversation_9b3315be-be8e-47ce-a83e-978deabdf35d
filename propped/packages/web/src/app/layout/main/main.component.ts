import type { <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import type { MatDrawerMode } from '@angular/material/sidenav';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ChildrenOutletContexts, RouterModule } from '@angular/router';
import { type Subscription, fromEvent } from 'rxjs';
import { SideNavService } from '../../services/design/sidenav.service';
import { slideInAnimation } from './animations';
import { HeaderComponent } from './header/header.component';
import { SidenavComponent } from './sidenav/sidenav.component';
import { WidgetsComponent } from './widgets/widgets.component';

@Component({
  selector: 'app-main-layout',
  imports: [
    HeaderComponent,
    RouterModule,
    SidenavComponent,
    WidgetsComponent,
    MatSidenavModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
  ],
  animations: [slideInAnimation],
  // changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <mat-drawer-container
      class="h-full w-full"
      [hasBackdrop]="sideNavBackDrop || widgetsPanel.backDrop"
    >
      <mat-drawer
        class="sidenav !w-auto"
        [opened]="sideNavOpen"
        [mode]="sideNavMode"
        (openedChange)="openedChange($event)"
      >
        <app-sidenav />
      </mat-drawer>
      <mat-drawer
        position="end"
        class="sidenav"
        [opened]="widgetsPanel.open"
        mode="over"
        (openedChange)="openedWidgetsPanelChange($event)"
      >
        <app-widgets />
      </mat-drawer>
      <mat-drawer-content
        class="!flex flex-col parent-drawer-content !overflow-hidden"
        [@routeAnimations]="getRouteAnimationData()"
      >
        <app-header [style.display]="showMainHeader ? 'inherit' : 'none'" />
        <router-outlet />
      </mat-drawer-content>
    </mat-drawer-container>
  `,
  styles: [
    `
      :host {
        overflow: auto;
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    `,
  ],
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  readonly #sideNavService = inject(SideNavService);
  readonly #contexts = inject(ChildrenOutletContexts);
  public sideNavOpen = true;
  public showMainHeader = true;
  public widgetsPanel: any;
  public sideNavMode: MatDrawerMode = 'side';
  public sideNavBackDrop = false;

  readonly #subscriptions: Record<string, Subscription> = {};

  private lastRoute = '';
  private pos = 0;
  private action = '';
  // private lastNavId = 0;

  constructor() {
    this.#subscriptions['sideNavService#sideNav'] = this.#sideNavService.sideNav.subscribe((v) => {
      this.sideNavOpen = v.open;
      if (!(v.header === null || v.header === undefined)) this.showMainHeader = v.header;
      this.sideNavMode = v.mode ?? this.sideNavMode;
      this.sideNavBackDrop = v.backDrop ?? this.sideNavBackDrop;
    });
    this.#sideNavService.widgetPanel.subscribe((v) => (this.widgetsPanel = v));
  }

  ngOnInit(): void {
    this.#subscriptions['window#popstate'] = fromEvent(window, 'popstate').subscribe(() => {
      this.action = 'POP';
    });
  }

  getRouteAnimationData() {
    const snapshot = this.#contexts.getContext('primary')?.route?.routeConfig;
    if (!snapshot) return this.pos;
    snapshot.data ||= {};
    const page = (snapshot.data['animation'] ??= Math.random().toString().slice(2));
    if (this.lastRoute === page) return this.pos;
    this.lastRoute = page;
    if (this.action === 'POP') {
      this.action = '';
      return (this.pos -= 1);
    }
    return (this.pos += 1);
  }

  public openedChange(x): void {
    this.#sideNavService.sideNav.next({ open: x });
  }

  public openedWidgetsPanelChange(x): void {
    this.#sideNavService.widgetPanel.next({ open: x });
  }

  public ngOnDestroy(): void {
    for (const sub of Object.values(this.#subscriptions)) sub.unsubscribe();
    (this as any).#subscriptions = null;
  }
}
