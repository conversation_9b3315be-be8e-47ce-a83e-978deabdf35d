import type { HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';

export const onError =
  (authService: AuthService, snackBarService: SnackBarService) => (err: Error) => {
    if (err instanceof HttpErrorResponse) {
      if (err.status === 401) {
        if (
          err.error.message === 'TokenExpiredError' ||
          // err.error.message === 'LoginRequiredError' ||
          err.error.message === 'JsonWebTokenError'
        ) {
          snackBarService.warn('Token Expire');
          authService.logout();
        }
      } else if (err.status === 0) {
        console.log(err.status);
      } else {
        console.log(`Request Error${JSON.stringify(err)}`);
      }
    }
    return throwError(() => err);
  };

export const AuthInterceptor: HttpInterceptorFn = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
) => {
  const authService = inject(AuthService);
  const snackBarService = inject(SnackBarService);
  if (req.url.includes('/api/')) {
    const accessToken = authService.getToken()?.accessToken;
    let authReq = req;
    let headers = req.headers;
    if (accessToken) {
      headers = headers.set('Authorization', `Bearer ${accessToken}`);
      if (!headers.has('Accept')) headers = headers.set('Accept', 'application/json');
      if (!headers.has('Content-Type')) headers = headers.set('Content-Type', 'application/json');
      authReq = req.clone({ headers });
    }
    return next(authReq).pipe(catchError(onError(authService, snackBarService)));
  }
  return next(req);
};
