import { Injectable } from '@angular/core';
import { importProxy } from '@lib/common/fun';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { fromEvent } from 'rxjs';

export const compressibleMimeTypes = [
  // Text-based file types
  'text/plain', // .txt
  'text/csv', // .csv
  'application/json', // .json
  'application/xml', // .xml
  'text/html', // .html, .htm
  'text/markdown', // .md
  'text/log', // .log (may vary)

  // Code and script files
  'application/javascript', // .js
  'text/css', // .css
  'application/x-httpd-php', // .php
  'text/x-python', // .py
  'application/x-ruby', // .rb
  'text/x-java-source', // .java
  'text/x-c', // .c, .h
  'text/x-c++src', // .cpp

  // Documents
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.ms-powerpoint', // .ppt
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.oasis.opendocument.text', // .odt
  'application/pdf', // .pdf (variable compression)

  // Config and data files
  'application/x-yaml', // .yml, .yaml
  'text/x-ini', // .ini
  'application/x-config', // .config (generic)
  'text/x-env', // .env (may vary)
  'application/sql', // .sql
];

// todo: use stream and also show progress

@Injectable({ providedIn: 'root' })
export class FileCompressService {
  #brotliModule!: Promise<any>;
  #ref: Promise<any>;
  #worker: Worker;
  #register = {} as any;
  #inc = 0;
  #lastProcess: any;

  constructor() {
    EnsureSingleInstance(this);
  }

  getBrotli(): Promise<any> {
    return this.#getBrotli();
  }

  #getBrotli(): Promise<any> {
    if (this.#ref) return this.#ref;
    this.#brotliModule = importProxy('https://unpkg.com/brotli-wasm@3.0.1/index.web.js?module');
    const brotli = this.#brotliModule.then((m) => m.default);
    this.#ref = brotli;
    return this.#ref;
  }

  #getBrotliWorker(): Worker {
    if (this.#worker) return this.#worker;
    const worker = new Worker(new URL('./compress.worker', import.meta.url));
    this.#worker = worker;
    return this.#worker;
  }

  #setupWorker() {
    if (this.#worker) return this.#worker;
    const worker = this.#getBrotliWorker();
    fromEvent(worker, 'message').subscribe((e: any) => {
      const id = e.data.id;
      const res = this.#register[id];
      res?.(e.data.buffer);
      delete this.#register[id];
    });
    this.#worker = worker;
    return worker;
  }

  public compressByWorker(data: Uint8Array, opt: { quality: number } = {} as any) {
    opt.quality = opt.quality ?? 5;
    const worker = this.#setupWorker();
    return new Promise((res) => {
      const id = this.#inc++;
      this.#register[id] = res;
      worker.postMessage({ id, command: 'compress', opt, buffer: data }, [data.buffer]);
    });
  }

  public decompressByWorker(buffer: Uint8Array) {
    const worker = this.#setupWorker();
    return new Promise((res) => {
      const id = this.#inc++;
      this.#register[id] = res;
      worker.postMessage({ id, command: 'decompress', buffer }, [buffer.buffer]);
    });
  }

  public async compressFileByWorker(file: File, opt: { quality: number } = {} as any) {
    await this.#lastProcess;
    const data = new Uint8Array(await file.arrayBuffer());
    const source = data.length;
    const result: Uint8Array = (await this.compressByWorker(data, opt)) as any;
    return {
      file: new File([result], file.name, { type: file.type }),
      meta: { ratio: result.length / source, source, output: result.length },
    };
  }

  public compressFileAsync(file: File, opt: { quality: number } = {} as any) {
    const result = this.compressFileByWorker(file, opt);
    this.#lastProcess = result;
    return result;
  }

  public async compress(data: Uint8Array, opt: { quality: number } = {} as any) {
    opt.quality = opt.quality ?? 5;
    const brotli = await this.#getBrotli();
    return brotli.compress(data, opt);
  }

  public async decompress(data: Uint8Array) {
    const brotli = await this.#getBrotli();
    return brotli.decompress(data);
  }

  public async compressFile(file: File, opt: { quality: number } = {} as any) {
    const data = new Uint8Array(await file.arrayBuffer());
    const result = await this.compress(data, opt);
    return {
      file: new File([result], `${file.name}.br`, { type: file.type }),
      meta: { ratio: result.length / data.length, source: data.length, output: result.length },
    };
  }
}
