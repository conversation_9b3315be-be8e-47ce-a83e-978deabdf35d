import { Injectable, inject } from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { apiRPC, injectController } from '@api/rpc';
import { ActiveUser } from '@lib/angular/auth/models/active-user.model';
import { TokenData } from '@lib/angular/auth/models/login-module';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import { SilverFieldTypes } from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { MFA_STATUS } from '@lib/common/enums/tfa-status';
import { getRandomCode } from '@lib/common/fun';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { TEMP_CACHE_KEYS, TempCacheService } from './temp-cache.service';

export const DataBaseKeys = {
  Token: '8dd879ed_bcf2_497b_bdef_ee73a1809c02',
  User: 'feb65e8a_da4c_4902_8134_1cb0dbf11ce2',
};

@Injectable({ providedIn: 'root' })
export class AuthService {
  readonly #snackBarService = inject(SnackBarService);
  readonly #tempCacheService = inject(TempCacheService);
  readonly #formModalService = inject(FormModalService);

  readonly #authController = injectController(apiRPC.AuthController);
  readonly #settingsController = injectController(apiRPC.SettingsController);

  readonly sessionId = getRandomCode(10);

  private _activeUserP!: Promise<ActiveUser>;
  public permissionMap: Record<string, number[]> = {};

  constructor() {
    EnsureSingleInstance(this);
  }

  public getLoginUserSync(): ActiveUser {
    return this.#tempCacheService.get(TEMP_CACHE_KEYS.PROFILE);
  }

  public getToken(): TokenData {
    let token = this.#tempCacheService.get(DataBaseKeys.Token);
    if (token) return token;
    token = localStorage.getItem(DataBaseKeys.Token);
    if (token) {
      token = JSON.parse(atob(token));
      this.#tempCacheService.set(DataBaseKeys.Token, token);
    }
    return token || null;
  }

  private setToken(tokenData: TokenData) {
    localStorage.setItem(DataBaseKeys.Token, btoa(JSON.stringify(tokenData)));
    this.#tempCacheService.set(DataBaseKeys.Token, tokenData);
  }

  public async login(username: string, password: string) {
    const res = await this.#authController.login({ username, password });
    let data: { accessToken: string };
    if ('MFAStatus' in res) {
      if (res.MFAStatus === MFA_STATUS.OTP_SENT) data = await this.openVerifyEmailOtp(username);
      if (res.MFAStatus === MFA_STATUS.TOTP_REQUIRED) data = await this.openTOTP(username);
      if (!data) throw new Error('Token not provided');
    } else data = res;
    const tokenData = new TokenData(data);
    this.setToken(tokenData);
    return tokenData;
  }

  public async openTOTP(username: string) {
    const form = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Select',
        key: 'type',
        value: 'totp',
        valid: { required: true },
        options: Object.entries({ totp: 'TOTP', eotp: 'Email OTP' }),
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'TOTP',
        placeholder: 'Enter TOTP',
        type: 'number',
        key: 'totp',
        value: '',
        cssClass: '',
        valid: { numberToken: 6 },
      },
    ];
    const res = this.#formModalService.openForm({ heading: 'TOTP', form });
    const formGroup: FormGroup = await res.formReady();
    const sub = formGroup.controls.type.valueChanges.subscribe((v) => {
      if (v === 'eotp') form[1].cssClass = 'hidden';
      else form[1].cssClass = '';
    });
    const result = await res.afterClosed();
    sub.unsubscribe();
    if (!result?.action) return;
    const formRes = formGroup.value;
    if (formRes.type === 'totp') {
      return await this.#authController.verifyTOTP({ username, token: `${formRes.totp}` });
    }
    if (formRes.type === 'eotp') {
      await this.#authController.sendTFAEmail({ username });
      this.#snackBarService.success('OTP has been sent on your email');
      return this.openVerifyEmailOtp(username);
    }
  }

  public async openVerifyEmailOtp(username: string) {
    const res = await this.#formModalService.open({
      heading: 'Email OTP',
      form: [
        {
          controlType: SilverFieldTypes.TEXT,
          label: 'OTP',
          placeholder: 'Enter OTP',
          type: 'text',
          key: 'otp',
          value: '',
          valid: { minLength: 5, maxLength: 5 },
        },
      ],
    });
    if (!res?.action) return;
    const token = res.value.otp;
    return await this.#authController.verifyTFAEmail({ username, token });
  }

  public async getActiveUser(force = false): Promise<ActiveUser> {
    const profile = this.#tempCacheService.get(TEMP_CACHE_KEYS.PROFILE);
    if (!force && profile) return profile;
    try {
      if (!force && this._activeUserP) return await this._activeUserP;
    } catch {
      //
    }
    this._activeUserP = (async () => {
      const data = await this.#authController.getProfile();
      const user = new ActiveUser(data.data);
      const result = await this.#settingsController.getOne({ key: 'permission' });
      const permissionMap = JSON.parse(result?.data?.value || '{}');
      this.permissionMap = permissionMap;
      this.#tempCacheService.set(TEMP_CACHE_KEYS.PROFILE, user);
      return user;
    })();
    return this._activeUserP;
  }

  public registration(userData: any) {
    return this.#authController.createUser(userData);
  }

  public async isUserLogin() {
    const token = this.getToken();
    if (!token) return false;
    const user = await this.getActiveUser();
    return !!user;
  }

  public setPassword(data: any) {
    return this.#authController.resetPwd(data);
  }

  public forgotPassword(data: { email: string }) {
    return this.#authController.forgotPassword(data);
  }

  public logout() {
    localStorage.removeItem(DataBaseKeys.User);
    localStorage.removeItem(DataBaseKeys.Token);
    // localStorage.clear();
    // this.#router.navigate(['/auth']);
    window.location.reload();
    return { done: true };
  }

  public verifyEmailId(email: string) {
    return this.#authController.verifyEmail({ email });
  }
}
