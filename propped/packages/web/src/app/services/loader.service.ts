import { BehaviorSubject } from 'rxjs';

class Loader {
  private count = 0;
  public isShowing = false;
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  public isLoading$ = this.isLoadingSubject.asObservable();

  public show() {
    if (this.isShowing) ++this.count;
    else {
      this.isShowing = true;
      this.isLoadingSubject.next(true);
    }
  }

  public hide() {
    if (this.count > 0) --this.count;
    else {
      this.isShowing = false;
      this.isLoadingSubject.next(false);
    }
  }
}

export const LoaderService = new Loader();

globalThis.LoaderService = LoaderService;
