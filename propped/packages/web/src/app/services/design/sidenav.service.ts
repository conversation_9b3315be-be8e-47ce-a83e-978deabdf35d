import { Injectable, inject } from '@angular/core';
import type { MatDrawerMode } from '@angular/material/sidenav';
import { EnsureSingleInstance } from '@lib/common/test-root-service';
import { BehaviorSubject } from 'rxjs';
import { MatchMediaService } from './match-media.service';

@Injectable({ providedIn: 'root' })
export class SideNavService {
  readonly #matchMediaService = inject(MatchMediaService);
  public sideNav = new BehaviorSubject<{
    open?: boolean;
    header?: boolean;
    mode?: MatDrawerMode;
    backDrop?: boolean;
  }>({ open: true, mode: 'side', backDrop: true });

  public widgetPanel = new BehaviorSubject<{
    open?: boolean;
    mode?: MatDrawerMode;
    backDrop?: boolean;
  }>({ open: false, mode: 'over', backDrop: false });

  constructor() {
    EnsureSingleInstance(this);
  }

  init() {
    this.#matchMediaService.onMediaChange.subscribe((v) => {
      if (v === 'xs') this.sideNav.next({ open: false, mode: 'over', backDrop: true });
      else this.sideNav.next({ open: true, mode: 'side', backDrop: false });
    });
  }

  hide() {
    this.sideNav.next({ open: false });
  }

  show() {
    this.sideNav.next({ open: true });
  }

  hideHeader() {
    this.sideNav.next({ header: false });
  }

  showHeader() {
    this.sideNav.next({ header: true });
  }
}
