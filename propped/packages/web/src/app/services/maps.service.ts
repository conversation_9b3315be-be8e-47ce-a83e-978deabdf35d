import { Injectable, inject } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { loadScript } from '@lib/common/script-loader';

@Injectable({ providedIn: 'root' })
export class MapsService {
  readonly #domSanitizer = inject(DomSanitizer);
  private readonly apiKey = 'AIzaSyCMPcqQHg4RSZJJMye1w7MSZtS2gNeZuzo';
  #gMapApiReady: Promise<void>;

  loadGoogleMapsApi() {
    if (this.#gMapApiReady) return this.#gMapApiReady;
    const loaded = Promise.withResolvers<void>();
    globalThis.initMap = loaded.resolve;
    loadScript(`https://maps.googleapis.com/maps/api/js?key=${this.apiKey}&callback=initMap`);
    this.#gMapApiReady = loaded.promise;
    return this.#gMapApiReady;
  }

  getGMapUrl() {
    return this.#domSanitizer.bypassSecurityTrustResourceUrl(
      `https://www.google.com/maps/embed/v1/place?key=${this.apiKey}&q=Singapore`,
    );
  }
}
