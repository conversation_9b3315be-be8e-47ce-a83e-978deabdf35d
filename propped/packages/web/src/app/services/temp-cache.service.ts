import { Injectable } from '@angular/core';

export const TEMP_CACHE_KEYS = {
  ROOT_FOLDER_ID: 'ROOT_FOLDER_ID',
  FOLDER_LIST: 'FOLDER_LIST',
  PROFILE: 'PROFILE',
};

@Injectable({ providedIn: 'root' })
export class TempCacheService {
  private box = {} as any;
  public get(key: string) {
    return this.box[key];
  }

  public getOrCreate(key: string) {
    return (this.box[key] ||= {});
  }

  public has(key: string) {
    return this.box[key] !== undefined;
  }

  public set(key: string, value: any) {
    this.box[key] = value;
  }

  public keys() {
    return Object.keys(this.box);
  }

  public remove(key: string) {
    delete this.box[key];
  }
}
