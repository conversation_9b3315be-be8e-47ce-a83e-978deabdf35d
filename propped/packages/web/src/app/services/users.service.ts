import { Injectable, inject } from '@angular/core';
import { apiRPC, injectController } from '@api/rpc';
import {
  LIST_TYPE,
  SilverSelectModalService,
} from '@lib/angular/dynamic-forms/silver-select.service';
import { EnsureSingleInstance } from '@lib/common/test-root-service';

@Injectable({ providedIn: 'root' })
export class UsersService {
  readonly #silverSelectModalService = inject(SilverSelectModalService);
  readonly #usersController = injectController(apiRPC.AuthController);

  constructor() {
    EnsureSingleInstance(this);
  }

  async pickUser(value = [], multiple = true) {
    const users = await this.#usersController.getAllUser();
    const selectedObj = value.length ? users.filter((u) => value.includes(u.id)) : [];
    const result = await this.#silverSelectModalService.open({
      heading: 'Select Users',
      data: users,
      options: {
        multiple,
        nullable: true,
        searchParam: ['name'],
        title: 'name',
        value: selectedObj.map((m) => m.id),
        selectedObj,
        vKey: 'id',
        mode: LIST_TYPE.TEXT,
      },
    });
    return result;
  }
}
