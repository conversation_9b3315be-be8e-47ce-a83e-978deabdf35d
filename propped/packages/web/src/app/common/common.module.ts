import { IMAGE_CONFIG, IMAGE_LOADER, NgOptimizedImage } from '@angular/common';
import { NgModule } from '@angular/core';
import { awsImageLoader } from '@lib/angular/aws-image-loader';
import { CUSTOM_DATE_FORMAT } from '../shared/date-format.config';

const modules = [NgOptimizedImage];

const providers = [
  ...CUSTOM_DATE_FORMAT,
  { provide: IMAGE_LOADER, useValue: awsImageLoader },
  {
    provide: IMAGE_CONFIG,
    useValue: { disableImageSizeWarning: true, disableImageLazyLoadWarning: true },
  },
];
@NgModule({ imports: modules, exports: modules, providers })
export class AppCommonModule {}
