import { inject } from '@angular/core';
import type { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Router } from '@angular/router';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { ROLES } from '@lib/common/const/roles';
import { base64Url, base64UrlDecode } from '@lib/common/fun';
import { AuthService } from '../services/auth.service';

export const UserLoginGuard = async (next: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const isLogin = await authService.isUserLogin();
  if (!isLogin) {
    router.navigate(['/auth'], { queryParams: { return: base64Url(btoa(state.url ?? '/')) } });
    return false;
  }
  return true;
};

export const UserPermissionGuard = (next: ActivatedRouteSnapshot) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const user = authService.getLoginUserSync();
  if (next.data.perm === undefined) return true;
  if (user.hasRole(ROLES.MASTER.id)) return true;
  const yes = user.hasAnyRole(authService.permissionMap[next.data.perm] ?? []);
  if (yes) return true;
  router.navigate(['/']);
  return false;
};

export const RolesGuard = async (next: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const authService = inject(AuthService);
  const snackBarService = inject(SnackBarService);
  const router = inject(Router);
  const isLogin = await authService.isUserLogin();
  if (!isLogin) {
    router.navigate(['/auth'], { queryParams: { return: base64Url(btoa(state.url ?? '/')) } });
    return false;
  }
  const user = authService.getLoginUserSync();
  if (!user.hasAnyRole(next.data.roles ?? [])) {
    snackBarService.error('You are not authorized to access this page');
    router.navigate(['/']);
    return false;
  }
  return true;
};

export const UserNotLoginGuard = async (next: ActivatedRouteSnapshot) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const isLogin = await authService.isUserLogin();
  if (isLogin) {
    router.navigate([atob(base64UrlDecode(next.queryParamMap.get('return') ?? 'Lw'))]);
    return false;
  }
  return true;
};

export const CanLeaveGuard = (component: { canLeave: () => boolean | Promise<boolean> }) => {
  return component.canLeave();
};
