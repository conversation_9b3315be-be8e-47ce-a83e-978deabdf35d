import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoadHelperStyleSheet } from '@lib/angular/material.helper';
import { SilverMenuComponent } from '@lib/angular/silver-menu.service';
import { SideNavService } from './services/design/sidenav.service';
import { LoaderService } from './services/loader.service';

@Component({
  imports: [RouterModule, AsyncPipe, SilverMenuComponent],
  selector: 'app-root',
  template: `<router-outlet /><app-silver-menu />
    @if (loaderService.isLoading$ | async) {
      <div class="spinner-overlay flex">
        <div class="loader m-auto"></div>
      </div>
    }`,
  styles: [
    `
      .spinner-overlay {
        position: fixed;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background-color: rgb(60 60 80 / 50%);
        z-index: 9999;
      }

      .loader {
        width: 160px;
        aspect-ratio: 1;
        border-radius: 50%;
        background:
          radial-gradient(farthest-side, #ffa516 94%, #0000) top/8px 8px no-repeat,
          conic-gradient(#0000 30%, #ffa516);
        -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
        animation: l13 1s infinite linear;
      }
      @keyframes l13 {
        100% {
          transform: rotate(1turn);
        }
      }
    `,
  ],
})
export class AppComponent {
  readonly #sideNavService = inject(SideNavService);
  readonly loaderService = LoaderService;

  constructor() {
    LoadHelperStyleSheet();
    this.#sideNavService.init();
  }
}
