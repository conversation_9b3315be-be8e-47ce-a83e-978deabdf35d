import { Injectable, inject } from '@angular/core';
import { SQLWebService } from '@lib/angular/sql.service';
import { isNil } from '@lib/common/fun';
import { type JDataConfig } from '@lib/common/j-data.interface';

// # Schema
/*
-- Lookup table for projects
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for districts
CREATE TABLE IF NOT EXISTS districts (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for streets
CREATE TABLE IF NOT EXISTS streets (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for market segments
CREATE TABLE IF NOT EXISTS market_segments (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for type of areas
CREATE TABLE IF NOT EXISTS type_of_areas (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Lookup table for property types
CREATE TABLE IF NOT EXISTS property_types (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Main sales table
CREATE TABLE IF NOT EXISTS sales (
    id INTEGER PRIMARY KEY,
    projectId INTEGER,
    districtId INTEGER,
    streetId INTEGER,
    marketSegmentId INTEGER,
    contractDate INTEGER,
    startFloor INTEGER,
    endFloor INTEGER,
    typeOfSale INTEGER,
    areaSqm INTEGER,
    typeOfAreaId INTEGER,
    price INTEGER,
    propertyTypeId INTEGER,
    districtNum INTEGER,
    totalYear INTEGER, -- Total years of the contract
    leaseYear INTEGER -- year of the lease commencement
);
*/

const escapeSQLiteValue = (value: any) => {
  if (value === null || value === undefined) return 'NULL';
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return (value ? 1 : 0).toString();
  if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
  if (value instanceof Date) return `'${value.toISOString()}'`;
  if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
  throw new Error('Unsupported data type');
};
@Injectable({ providedIn: 'root' })
export class CondoSalesService {
  salesDb: any;
  readonly #sqlWebService = inject(SQLWebService);

  loadDB() {
    this.salesDb ??= this.#sqlWebService.loadDB('/assets/condo-sales.sqlite');
    return this.salesDb;
  }

  getAllDistricts(db: any) {
    return db.exec('select * from districts')[0].values;
  }

  getAllMarketSegments(db: any) {
    return db.exec('select * from market_segments')[0].values;
  }

  getAllPropertyTypes(db: any) {
    return db.exec('select * from property_types')[0].values;
  }

  getAllTypeOfAreas(db: any) {
    return db.exec('select * from type_of_areas')[0].values;
  }

  getAllStreets(db: any) {
    return db.exec('select * from streets')[0].values;
  }

  getAllProjects(db: any) {
    return db.exec('select * from projects')[0].values;
  }

  public getConfigs(): JDataConfig {
    return { page: 1, limit: 50, sort: 'resaleAt', order: 'DESC' };
  }

  generateSqlQuery(db: any, filters, configs) {
    let query = /* SQL */ `SELECT
      sales.id as id,
      datetime(sales.contractDate, 'unixepoch') AS contract_date,
      projects.name AS project_name,
      streets.name AS street_name,
      sales.districtNum as district,
      sales.startFloor || '-' || sales.endFloor as floor_range,
      sales.areaSqm as area_sqm,
      sales.price as price,
      CAST(sales.price AS REAL) / (sales.areaSqm * 10.73692) as psf,
      sales.totalYear as tenure
    FROM sales
    JOIN projects ON sales.projectId = projects.id
    JOIN districts ON sales.districtId = districts.id
    JOIN streets ON sales.streetId = streets.id
    JOIN market_segments ON sales.marketSegmentId = market_segments.id
    JOIN type_of_areas ON sales.typeOfAreaId = type_of_areas.id
    JOIN property_types ON sales.propertyTypeId = property_types.id
    WHERE 1=1
    `;

    let condition = '';

    if (filters.project?.length)
      condition += ` AND projectId IN (${filters.project.map(escapeSQLiteValue).join(', ')})`;

    if (filters.districtId?.length)
      condition += ` AND districtId IN (${filters.districtId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.streetId?.length)
      condition += ` AND streetId IN (${filters.streetId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.marketSegmentId?.length)
      condition += ` AND marketSegmentId IN (${filters.marketSegmentId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.propertyTypeId?.length)
      condition += ` AND propertyTypeId IN (${filters.propertyTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.typeOfAreaId?.length)
      condition += ` AND typeOfAreaId IN (${filters.typeOfAreaId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        condition += ` AND contractDate >= ${escapeSQLiteValue(
          new Date(filters.timePeriod.from).getTime(),
        )}`;

      if (filters.timePeriod.to)
        condition += ` AND contractDate <= ${escapeSQLiteValue(
          new Date(filters.timePeriod.to).getTime(),
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumLeaseYear))
      condition += ` AND leaseYear >= ${escapeSQLiteValue(filters.minimumLeaseYear)}`;

    if (!isNil(filters.maximumLeaseYear))
      condition += ` AND leaseYear <= ${escapeSQLiteValue(filters.maximumLeaseYear)}`;

    if (!isNil(filters.minimumStorey))
      condition += ` AND startFloor >= ${escapeSQLiteValue(filters.minimumStorey)}`;

    if (!isNil(filters.maximumStorey))
      condition += ` AND endFloor <= ${escapeSQLiteValue(filters.maximumStorey)}`;

    if (!isNil(filters.areaSqm))
      condition += ` AND areaSqm = ${escapeSQLiteValue(filters.areaSqm)}`;

    if (!isNil(filters.minimumSqft)) {
      // Convert SQFT to sqm (1 sqm = 10.73692 sqft)
      const minSqm = Math.floor(filters.minimumSqft / 10.73692);
      condition += ` AND areaSqm >= ${escapeSQLiteValue(minSqm)}`;
    }

    if (!isNil(filters.maximumSqft)) {
      // Convert SQFT to sqm (1 sqm = 10.73692 sqft)
      const maxSqm = Math.ceil(filters.maximumSqft / 10.73692);
      condition += ` AND areaSqm <= ${escapeSQLiteValue(maxSqm)}`;
    }

    const total = db.exec(`SELECT COUNT(*) FROM sales WHERE 1=1 ${condition}`)[0].values[0][0];

    query += ` ${condition}`;
    query += ` ORDER BY ${configs.sort} ${configs.order}`;
    query += ` LIMIT ${configs.limit} OFFSET ${(configs.page - 1) * configs.limit}`;

    console.time('query time');
    const res = db.exec(query);
    console.timeEnd('query time');
    console.log('<-----SQL QUERY--->', query);
    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      meta: {
        total,
        start: (configs.page - 1) * configs.limit,
        end: Math.min(total, configs.page * configs.limit),
      },
    };
  }

  getNumberOfUnitSoldByMonth(db: any) {
    console.time('query time');
    const rs = db.exec(/* SQL */ `
      SELECT
        strftime('%Y-%m', contractDate, 'unixepoch') AS month,
        COUNT(*) AS total
      FROM sales
      GROUP BY month
      ORDER BY month
    `)[0].values;
    console.timeEnd('query time');
    console.log(rs);
  }

  getNumberOfUnitSoldByProject(db: any) {
    console.time('query time');
    const rs = db.exec(/* SQL */ `
      SELECT
        projects.name AS project_name,
        COUNT(*) AS total
      FROM sales
      JOIN projects ON sales.projectId = projects.id
      GROUP BY projects.name
      ORDER BY total DESC
    `)[0].values;
    console.timeEnd('query time');
    console.log(rs);
  }
  /**
   * Get all data for statistics calculation based on the current filters
   * This returns all matching records without pagination for accurate statistics
   */
  getAllFilteredDataForStats(db: any, filters) {
    let query = /* SQL */ `SELECT
    sales.price AS price,
    ROUND(sales.price/(sales.areaSqm*10.73692), 2) AS psf,
    date(sales.contractDate, 'unixepoch') AS contract_date
  FROM sales
  JOIN projects ON sales.projectId = projects.id
  JOIN property_types ON sales.propertyTypeId = property_types.id
  JOIN districts ON sales.districtId = districts.id
  JOIN market_segments ON sales.marketSegmentId = market_segments.id
  JOIN streets ON sales.streetId = streets.id
  JOIN type_of_areas ON sales.typeOfAreaId = type_of_areas.id
  WHERE 1=1
  `;

    // Apply all filters
    if (filters.districtId?.length)
      query += ` AND districtId IN (${filters.districtId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.marketSegmentId?.length)
      query += ` AND marketSegmentId IN (${filters.marketSegmentId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.propertyTypeId?.length)
      query += ` AND propertyTypeId IN (${filters.propertyTypeId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.typeOfAreaId?.length)
      query += ` AND typeOfAreaId IN (${filters.typeOfAreaId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.streetId?.length)
      query += ` AND streetId IN (${filters.streetId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.projectId?.length)
      query += ` AND projectId IN (${filters.projectId.map(escapeSQLiteValue).join(', ')})`;

    if (filters.timePeriod) {
      if (filters.timePeriod.from)
        query += ` AND saleDate >= ${escapeSQLiteValue(
          new Date(filters.timePeriod.from).getTime() / 1000,
        )}`;

      if (filters.timePeriod.to)
        query += ` AND saleDate <= ${escapeSQLiteValue(
          new Date(filters.timePeriod.to).getTime() / 1000,
        )}`;
    }

    if (!isNil(filters.minimumPrice))
      query += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;

    if (!isNil(filters.maximumPrice))
      query += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;

    if (!isNil(filters.minimumPsf))
      query += ` AND ROUND(price/area, 2) >= ${escapeSQLiteValue(filters.minimumPsf)}`;

    if (!isNil(filters.maximumPsf))
      query += ` AND ROUND(price/area, 2) <= ${escapeSQLiteValue(filters.maximumPsf)}`;

    if (!isNil(filters.minimumArea))
      query += ` AND area >= ${escapeSQLiteValue(filters.minimumArea)}`;

    if (!isNil(filters.maximumArea))
      query += ` AND area <= ${escapeSQLiteValue(filters.maximumArea)}`;

    if (!isNil(filters.tenure)) query += ` AND tenure = ${escapeSQLiteValue(filters.tenure)}`;

    console.time('condo stats query time');
    console.log('Condo stats query:', query);
    const res = db.exec(query);
    console.timeEnd('condo stats query time');

    return {
      headers: res[0]?.columns ?? [],
      data: res[0]?.values ?? [],
      count: res[0]?.values.length ?? 0,
    };
  }
  getStatsQuery(db: any, filters: any) {
    let condition = 'WHERE 1 = 1';

    // Apply dynamic filters
    if (filters.project?.length) {
      condition += ` AND projectId IN (${filters.project.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.districtId?.length) {
      condition += ` AND districtId IN (${filters.districtId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.streetId?.length) {
      condition += ` AND streetId IN (${filters.streetId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.marketSegmentId?.length) {
      condition += ` AND marketSegmentId IN (${filters.marketSegmentId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.propertyTypeId?.length) {
      condition += ` AND propertyTypeId IN (${filters.propertyTypeId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.typeOfAreaId?.length) {
      condition += ` AND typeOfAreaId IN (${filters.typeOfAreaId.map(escapeSQLiteValue).join(', ')})`;
    }

    if (filters.timePeriod) {
      if (filters.timePeriod.from) {
        condition += ` AND contractDate >= ${escapeSQLiteValue(new Date(filters.timePeriod.from).getTime())}`;
      }

      if (filters.timePeriod.to) {
        condition += ` AND contractDate <= ${escapeSQLiteValue(new Date(filters.timePeriod.to).getTime())}`;
      }
    }

    if (!isNil(filters.minimumPrice)) {
      condition += ` AND price >= ${escapeSQLiteValue(filters.minimumPrice)}`;
    }

    if (!isNil(filters.maximumPrice)) {
      condition += ` AND price <= ${escapeSQLiteValue(filters.maximumPrice)}`;
    }

    if (!isNil(filters.minimumLeaseYear)) {
      condition += ` AND leaseYear >= ${escapeSQLiteValue(filters.minimumLeaseYear)}`;
    }

    if (!isNil(filters.maximumLeaseYear)) {
      condition += ` AND leaseYear <= ${escapeSQLiteValue(filters.maximumLeaseYear)}`;
    }

    if (!isNil(filters.minimumStorey)) {
      condition += ` AND startFloor >= ${escapeSQLiteValue(filters.minimumStorey)}`;
    }

    if (!isNil(filters.maximumStorey)) {
      condition += ` AND endFloor <= ${escapeSQLiteValue(filters.maximumStorey)}`;
    }

    if (!isNil(filters.areaSqm)) {
      condition += ` AND areaSqm = ${escapeSQLiteValue(filters.areaSqm)}`;
    }

    if (!isNil(filters.minimumSqft)) {
      const minSqm = Math.floor(filters.minimumSqft / 10.73692);
      condition += ` AND areaSqm >= ${escapeSQLiteValue(minSqm)}`;
    }

    if (!isNil(filters.maximumSqft)) {
      const maxSqm = Math.ceil(filters.maximumSqft / 10.73692);
      condition += ` AND areaSqm <= ${escapeSQLiteValue(maxSqm)}`;
    }

    // SQL query to calculate highest, lowest, and median for price and psf
    const query = /* SQL */ `
      WITH PriceData AS (
        SELECT
          price,
          CAST(price AS REAL) / (areaSqm * 10.73692) AS psf
        FROM sales
        ${condition}
      ),
      OrderedPrice AS (
        SELECT price FROM PriceData ORDER BY price
      ),
      OrderedPsf AS (
        SELECT psf FROM PriceData ORDER BY psf
      ),
      PriceCount AS (
        SELECT COUNT(*) as count FROM PriceData
      )

      SELECT
        MAX(price) AS max_price,
        MIN(price) AS min_price,
        MAX(psf) AS max_psf,
        MIN(psf) AS min_psf,
        (SELECT price FROM OrderedPrice LIMIT 1 OFFSET (SELECT (count - 1) / 2 FROM PriceCount)) AS median_price,
        (SELECT psf FROM OrderedPsf LIMIT 1 OFFSET (SELECT (count - 1) / 2 FROM PriceCount)) AS median_psf
      FROM PriceData;
    `;

    console.time('query time');
    const res = db.exec(query);
    console.timeEnd('query time');
    console.log('<-----SQL QUERY--->', query);

    return {
      data: res[0]?.values[0] ?? [],
      headers: res[0]?.columns ?? [],
    };
  }
}
