<div class="flex flex-col h-full">
  <!-- Map Header -->
  <div class="p-4 shadow-md z-10">
    <h1 class="text-xl font-bold text-gray-800 dark:text-white">MapBox Overview</h1>
    <div class="flex items-center gap-4 mt-2">
      <div class="flex items-center gap-2">
        <label for="mapStyle" class="text-sm font-medium text-gray-700 dark:text-gray-300"
          >Map Style:</label
        >
        <select
          id="mapStyle"
          [(ngModel)]="style"
          (change)="changeMapStyle()"
          class="p-2 border border-gray-300 rounded-md text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        >
          <option value="mapbox://styles/mapbox/streets-v12">Streets</option>
          <option value="mapbox://styles/mapbox/outdoors-v12">Outdoors</option>
          <option value="mapbox://styles/mapbox/light-v11">Light</option>
          <option value="mapbox://styles/mapbox/dark-v11">Dark</option>
          <option value="mapbox://styles/mapbox/satellite-v9">Satellite</option>
          <option value="mapbox://styles/mapbox/satellite-streets-v12">Satellite Streets</option>
          <option value="mapbox://styles/mapbox/navigation-day-v1">Navigation Day</option>
          <option value="mapbox://styles/mapbox/navigation-night-v1">Navigation Night</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Map Container -->
  <div
    class="h-full md:h-[600px]"
    [ngClass]="
      showHDBListings || showCondoListings || showPropertyDetails || showSalesPropertyDetails
        ? 'flex flex-col relative md:grid md:grid-cols-2 gap-4'
        : 'relative flex-grow'
    "
  >
    <!-- ngx-mapbox-gl map component -->
    <div class="relative w-full h-full">
      @if (shouldShowCancelButton()) {
        <div class="absolute top-16 right-6 md:top-1 md:right-1 z-[9999999]">
          <button
            class="bg-white dark:bg-gray-800 text-red-600 dark:text-red-400 p-2 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
            (click)="clearAllMarkers()"
            title="Clear all markers"
          >
            <mat-icon>cancel</mat-icon>
          </button>
        </div>
      }
      <!-- Search bar and location button -->
      <div class="absolute top-0 left-0 right-0 sm:left-4 sm:right-auto z-[9999999] px-4 sm:px-0">
        <div class="relative flex">
          <div class="relative m-2 w-full max-w-full sm:max-w-md md:max-w-lg">
            <div>
              <div class="flex gap-2 md:flex-row md:items-center w-full sm:w-auto">
                <div
                  class="flex items-center shadow-lg rounded-full overflow-hidden border border-blue-100 dark:border-gray-700 bg-white dark:bg-gray-800 w-full sm:w-auto"
                >
                  <div class="flex items-center pl-4">
                    <mat-icon class="text-blue-500 dark:text-blue-400">location_on</mat-icon>
                  </div>
                  <input
                    type="text"
                    class="px-3 py-3 w-full sm:w-64 md:w-80 focus:outline-none bg-transparent dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Enter address, car park, or landmark"
                    [(ngModel)]="searchQuery"
                    (input)="onSearchInput()"
                  />
                  <button
                    class="px-4 py-3 bg-blue-600 text-white hover:bg-blue-700 transition-colors flex items-center justify-center"
                    (click)="onSearchInput()"
                    aria-label="Search"
                  >
                    <mat-icon>search</mat-icon>
                  </button>
                  @if (
                    (searchResults.length > 0 || (recentSearches.length > 0 && searchQuery)) &&
                    searchResultsDropdown
                  ) {
                    <div
                      class="absolute left-0 w-full mt-1 bg-white rounded-md shadow-lg z-[999] max-h-60 overflow-y-auto"
                      style="top: 100%; min-width: 250px"
                    >
                      <!-- Search Results Section -->
                      @if (searchResults.length > 0) {
                        <div class="p-2 bg-gray-50 border-b border-gray-200">
                          <span class="text-sm font-medium text-gray-700">Search Results</span>
                        </div>
                        @for (result of searchResults; track result) {
                          <div
                            class="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                            (click)="selectSearchResult(result)"
                          >
                            <div class="font-medium">
                              {{ result.BUILDING || result.ROAD_NAME }}
                            </div>
                            <div class="text-sm text-gray-600 truncate">{{ result.ADDRESS }}</div>
                          </div>
                        }
                      }

                      <!-- Recent Searches Section -->
                      @if (recentSearches.length > 0 && searchQuery) {
                        <div class="p-2 bg-gray-50 border-b border-gray-200">
                          <span class="text-sm font-medium text-gray-700">Recent Searches</span>
                        </div>
                        @for (searchResult of recentSearches; track searchResult) {
                          <div
                            class="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 flex justify-between items-center"
                            (click)="selectSearchResult(searchResult)"
                          >
                            <div class="flex items-center">
                              <i class="material-icons text-gray-500 mr-2 text-sm">schedule</i>
                              <div>
                                <div class="font-medium">
                                  {{ searchResult.BUILDING || searchResult.ROAD_NAME }}
                                </div>
                                <div class="text-sm text-gray-600 truncate">
                                  {{ searchResult.ADDRESS }}
                                </div>
                              </div>
                            </div>
                            <button
                              class="text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-200"
                              (click)="removeRecentSearch(searchResult, $event)"
                            >
                              <i class="material-icons text-sm">close</i>
                            </button>
                          </div>
                        }
                      }
                    </div>
                  }
                </div>

                <!-- Current location button next to search bar -->
                <button
                  class="p-2 text-blue-500 bg-white rounded-full flex items-center justify-center w-10 h-10 dark:bg-primary-content shadow-md"
                  (click)="getUserLocation()"
                  title="My Location"
                >
                  <mat-icon>my_location</mat-icon>
                </button>
                <button
                  [ngClass]="
                    is3DEnabled
                      ? 'p-2 text-white bg-blue-500 rounded-full flex items-center justify-center w-10 h-10 shadow-md'
                      : 'p-2 text-blue-500 bg-white rounded-full flex items-center justify-center w-10 h-10 dark:bg-primary-content shadow-md'
                  "
                  (click)="toggle3DBuildings()"
                  title="Toggle 3D Buildings"
                >
                  3D
                </button>
              </div>
            </div>

            <div class="mt-2 w-[180px]">
              <div
                class="bg-white dark:bg-gray-800 rounded-full shadow-lg px-2 py-1 flex justify-between items-center"
              >
                <!-- Day -->
                <button
                  class="p-2 rounded-full transition-colors flex items-center justify-center"
                  [ngClass]="
                    currentLightPreset === 'day'
                      ? 'bg-blue-100 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  "
                  (click)="setMapLightPreset('day')"
                  title="Day Mode"
                >
                  <mat-icon class="text-xl">wb_sunny</mat-icon>
                </button>

                <!-- Dawn -->
                <button
                  class="p-2 rounded-full transition-colors flex items-center justify-center"
                  [ngClass]="
                    currentLightPreset === 'dawn'
                      ? 'bg-blue-100 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  "
                  (click)="setMapLightPreset('dawn')"
                  title="Dawn Mode"
                >
                  <mat-icon class="text-xl">wb_twilight</mat-icon>
                </button>

                <!-- Dusk -->
                <button
                  class="p-2 rounded-full transition-colors flex items-center justify-center"
                  [ngClass]="
                    currentLightPreset === 'dusk'
                      ? 'bg-blue-100 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  "
                  (click)="setMapLightPreset('dusk')"
                  title="Dusk Mode"
                >
                  <mat-icon class="text-xl">nights_stay</mat-icon>
                </button>

                <!-- Night -->
                <button
                  class="p-2 rounded-full transition-colors flex items-center justify-center"
                  [ngClass]="
                    currentLightPreset === 'night'
                      ? 'bg-blue-100 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  "
                  (click)="setMapLightPreset('night')"
                  title="Night Mode"
                >
                  <mat-icon class="text-xl">dark_mode</mat-icon>
                </button>
              </div>
            </div>
            <!-- Search Results and Recent Searches Dropdown -->
          </div>
        </div>
      </div>
      <mgl-map
        [style]="style"
        [zoom]="[zoom]"
        [center]="[lng, lat]"
        (mapLoad)="onMapLoad($event.target)"
        [maxBounds]="[
          [103.6, 1.16],
          [104.1, 1.47],
        ]"
        [bearing]="-17.6"
        [pitch]="45"
        (styleData)="updateMapStyle($event.target)"
        [ngClass]="
          showHDBListings || showCondoListings || showPropertyDetails
            ? 'h-[calc(100vh-12rem)] relative md:h-full w-full order-1'
            : 'absolute inset-0 w-screen h-full md:w-full md:h-full'
        "
      >
        @if (userLocation) {
          <mgl-marker [feature]="userLocation">
            <div
              class="marker-container flex flex-col items-center cursor-pointer"
              tabindex="0"
              (click)="showUserLocationPopup = true"
            >
              <div class="marker-icon bg-blue-500 text-white rounded-full p-1 shadow-lg">
                <mat-icon>location_on</mat-icon>
              </div>
              <div
                class="marker-label bg-white px-2 py-1 rounded-md shadow-md text-xs font-medium text-blue-700 mt-1"
              >
                Current Location
              </div>
            </div>
          </mgl-marker>
        }
        <!-- Selected Property Marker -->
        @if (selectedPropertyFeature) {
          <mgl-marker [feature]="selectedPropertyFeature">
            <div class="marker-container flex flex-col items-center cursor-pointer" tabindex="0">
              <div class="marker-icon bg-red-500 text-white rounded-full p-1 shadow-lg">
                <mat-icon>place</mat-icon>
              </div>
              <div
                class="marker-label bg-white px-2 py-1 rounded-md shadow-md text-xs font-medium text-red-700 mt-1 max-w-[150px] truncate"
              >
                {{ selectedPropertyName }}
              </div>
            </div>
          </mgl-marker>
        }
        @if (is3DEnabled) {
          <mgl-layer
            id="3d-buildings"
            source="composite"
            sourceLayer="building"
            [filter]="['==', 'extrude', 'true']"
            type="fill-extrusion"
            [minzoom]="15"
            [paint]="{
              'fill-extrusion-color': '#666666',
              'fill-extrusion-height': [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                0,
                15.05,
                ['get', 'height'],
              ],
              'fill-extrusion-base': [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                0,
                15.05,
                ['get', 'min_height'],
              ],
              'fill-extrusion-opacity': 1,
              'fill-extrusion-vertical-gradient': true,
            }"
            [before]="labelLayerId"
          />
        }
        <!-- Radius circle for nearby searches -->
        <mgl-geojson-source id="radius-circle" [data]="radiusCircleSource">
          <mgl-layer
            id="radius-circle-fill"
            type="fill"
            source="radius-circle"
            [paint]="{
              'fill-color': '#FF9800',
              'fill-opacity': 0.1,
            }"
          />
          <mgl-layer
            id="radius-circle-line"
            type="line"
            source="radius-circle"
            [paint]="{
              'line-color': '#FF9800',
              'line-width': 2,
              'line-opacity': 0.8,
            }"
          />
        </mgl-geojson-source>

        <!-- School markers -->
        <mgl-geojson-source id="amenities" [data]="amenitiesSource">
          <mgl-layer
            id="amenities-layer"
            type="circle"
            source="amenities"
            [paint]="{
              'circle-radius': 8,
              'circle-color': '#FF9800',
              'circle-stroke-width': 1,
              'circle-stroke-color': '#000000',
            }"
            (layerClick)="getAmenitiesPopUp($event)"
          />
          @if (showAmenitiesPopup && amenitiesFeature) {
            <mgl-popup
              class="mapbox-popup-modern"
              [feature]="amenitiesFeature"
              [closeOnClick]="false"
              [closeButton]="false"
              [maxWidth]="'300px'"
            >
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <!-- Header with dynamic color based on amenity type -->
                <div class="w-full flex justify-end">
                  <mat-icon (click)="showAmenitiesPopup = false">close</mat-icon>
                </div>
                <div
                  [ngClass]="{
                    'bg-blue-600': amenitiesPopUpContent.type === 'school',
                    'bg-green-600': amenitiesPopUpContent.type === 'mrt',
                    'bg-red-600': amenitiesPopUpContent.type === 'healthcare',
                    'bg-red-500': amenitiesPopUpContent.type === 'busStop',
                    'bg-orange-500': amenitiesPopUpContent.type === 'hawker',
                    'bg-purple-600': !['school', 'mrt', 'healthcare', 'busStop', 'hawker'].includes(
                      amenitiesPopUpContent.type
                    ),
                  }"
                  class="py-2 px-3 text-white"
                >
                  <div class="flex items-center">
                    <!-- Dynamic icon based on amenity type -->
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-white mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <!-- School icon -->
                      @if (amenitiesPopUpContent.type === 'school') {
                        <path
                          d="M12 14l9-5-9-5-9 5 9 5z"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                        <path
                          d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      }
                      <!-- MRT icon -->
                      @if (amenitiesPopUpContent.type === 'mrt') {
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      }
                      <!-- Healthcare icon -->
                      @if (amenitiesPopUpContent.type === 'healthcare') {
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      }
                      <!-- Bus Stop icon -->
                      @if (amenitiesPopUpContent.type === 'busStop') {
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                        />
                      }
                      <!-- Hawker icon -->
                      @if (amenitiesPopUpContent.type === 'hawker') {
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      }
                      <!-- Default icon for other types -->
                      @if (
                        !['school', 'mrt', 'healthcare', 'busStop', 'hawker'].includes(
                          amenitiesPopUpContent.type
                        )
                      ) {
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      }
                    </svg>
                    <h3 class="text-base font-bold text-white">
                      {{ amenitiesPopUpContent.name }}
                    </h3>
                  </div>
                  <!-- Subtitle if category exists -->
                  @if (amenitiesPopUpContent.category) {
                    <div class="text-xs text-white opacity-80 mt-1 ml-7">
                      {{ amenitiesPopUpContent.category }}
                    </div>
                  }
                </div>

                <!-- Content -->
                <div class="p-3 dark:text-white text-sm">
                  <!-- School specific content -->
                  @if (amenitiesPopUpContent.type === 'school') {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.address) {
                        <p>
                          <span class="font-semibold">Address:</span>
                          {{ amenitiesPopUpContent.address }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.postal) {
                        <p>
                          <span class="font-semibold">Postal Code:</span>
                          {{ amenitiesPopUpContent.postal }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.telephoneNumber) {
                        <p>
                          <span class="font-semibold">Telephone:</span>
                          {{ amenitiesPopUpContent.telephoneNumber }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.email) {
                        <p>
                          <span class="font-semibold">Email:</span>
                          <a
                            href="mailto:{{ amenitiesPopUpContent.email }}"
                            class="text-blue-600 dark:text-blue-400 underline"
                          >
                            {{ amenitiesPopUpContent.email }}
                          </a>
                        </p>
                      }
                      @if (amenitiesPopUpContent.town) {
                        <p>
                          <span class="font-semibold">Town:</span> {{ amenitiesPopUpContent.town }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.schoolLevel) {
                        <p>
                          <span class="font-semibold">Level:</span>
                          {{ amenitiesPopUpContent.schoolLevel }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.schoolType) {
                        <p>
                          <span class="font-semibold">Type:</span>
                          {{ amenitiesPopUpContent.schoolType }}
                        </p>
                      }
                    </div>
                  }

                  <!-- MRT specific content -->
                  @if (amenitiesPopUpContent.type === 'mrt') {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.buildingName) {
                        <p>
                          <span class="font-semibold">Building:</span>
                          {{ amenitiesPopUpContent.buildingName }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.alphaNumericCode) {
                        <p>
                          <span class="font-semibold">Code:</span>
                          {{ amenitiesPopUpContent.alphaNumericCode }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.address) {
                        <p>
                          <span class="font-semibold">Address:</span>
                          {{ amenitiesPopUpContent.address }}
                        </p>
                      }
                      <a
                        href="https://www.google.com/maps/search/?api=1&query={{
                          amenitiesPopUpContent.coordinates[1]
                        }},{{ amenitiesPopUpContent.coordinates[0] }}"
                        target="_blank"
                        class="mt-2 inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 underline text-xs"
                      >
                        <mat-icon>location_on</mat-icon>
                        Get Directions
                      </a>
                    </div>
                  }

                  <!-- Healthcare specific content -->
                  @if (amenitiesPopUpContent.type === 'healthcare') {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.buildingName) {
                        <p>
                          <span class="font-semibold">Building:</span>
                          {{ amenitiesPopUpContent.buildingName }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.address) {
                        <p>
                          <span class="font-semibold">Address:</span>
                          {{ amenitiesPopUpContent.address }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.postal) {
                        <p>
                          <span class="font-semibold">Postal Code:</span>
                          {{ amenitiesPopUpContent.postal }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.facilityType) {
                        <p>
                          <span class="font-semibold">Facility Type:</span>
                          {{ amenitiesPopUpContent.facilityType }}
                        </p>
                      }
                      <a
                        href="https://www.google.com/maps/search/?api=1&query={{
                          amenitiesPopUpContent.coordinates[1]
                        }},{{ amenitiesPopUpContent.coordinates[0] }}"
                        target="_blank"
                        class="mt-2 inline-flex items-center text-blue-600 dark:text-blue-400 underline text-xs"
                      >
                        <mat-icon>directions</mat-icon>
                        Get Directions
                      </a>
                    </div>
                  }

                  <!-- Bus Stop specific content -->
                  @if (amenitiesPopUpContent.type === 'busStop') {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.details) {
                        <p>
                          <span class="font-semibold">Landmark:</span>
                          {{ amenitiesPopUpContent.details }}
                        </p>
                      }
                      <!-- @if (
                      amenitiesPopUpContent.busServices &&
                      amenitiesPopUpContent.busServices.length > 0
                    ) {
                      <div class="mt-2">
                        <p class="font-semibold">Bus Services:</p>
                        <div class="flex flex-wrap gap-1 mt-1">
                          @for (service of amenitiesPopUpContent.busServices; track service) {
                            <span
                              class="px-1.5 py-0.5 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 text-xs rounded-md"
                            >
                              {{ service }}
                            </span>
                          }
                        </div>
                      </div>
                    } -->
                    </div>
                  }

                  <!-- Hawker specific content -->
                  @if (amenitiesPopUpContent.type === 'hawker') {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.photoUrl) {
                        <img
                          src="{{ amenitiesPopUpContent.photoUrl }}"
                          alt="Hawker Center"
                          class="w-full mx-auto h-32 object-cover rounded mb-2"
                        />
                      }
                      @if (amenitiesPopUpContent.description) {
                        <p class="mb-1 text-gray-600 dark:text-gray-400 italic text-xs">
                          {{ amenitiesPopUpContent.description }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.address) {
                        <p>
                          <span class="font-semibold">Address:</span>
                          {{ amenitiesPopUpContent.address }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.postalCode) {
                        <p>
                          <span class="font-semibold">Postal Code:</span>
                          {{ amenitiesPopUpContent.postalCode }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.stallCount) {
                        <p>
                          <span class="font-semibold">Food Stalls:</span>
                          {{ amenitiesPopUpContent.stallCount }}
                        </p>
                      }
                    </div>
                  }

                  <!-- Default content for other types -->
                  @if (
                    !['school', 'mrt', 'healthcare', 'busStop', 'hawker'].includes(
                      amenitiesPopUpContent.type
                    )
                  ) {
                    <div class="space-y-1 text-gray-800 dark:text-gray-200">
                      @if (amenitiesPopUpContent.address) {
                        <p>
                          <span class="font-semibold">Address:</span>
                          {{ amenitiesPopUpContent.address }}
                        </p>
                      }
                      @if (amenitiesPopUpContent.description) {
                        <p>{{ amenitiesPopUpContent.description }}</p>
                      }
                    </div>
                  }
                </div>
              </div>
            </mgl-popup>
          }
        </mgl-geojson-source>
        @if (searchLocationSource.features.length > 0) {
          <mgl-marker [feature]="searchLocation">
            <div
              class="marker-container flex flex-col items-center cursor-pointer"
              tabindex="0"
              (click)="showSearchLocationPopup = true"
            >
              <div
                class="marker-icon absolute top-[-48px] flex flex-row items-center justify-center gap-1 w-32 h-8 bg-blue-500 text-white rounded-full p-1 shadow-lg"
              >
                <p class="font-semibold">More Information</p>
              </div>
              <div class="floating-arrow absolute my-2 top-[-28px]">
                <mat-icon class="text-blue-500">keyboard_arrow_down</mat-icon>
              </div>
            </div>
          </mgl-marker>
        }
        <mgl-geojson-source id="search-location" [data]="searchLocationSource">
          <mgl-layer
            id="search-location-layer"
            type="circle"
            source="search-location"
            [paint]="{
              'circle-radius': 8,
              'circle-color': '#4df7f2',
              'circle-stroke-width': 1,
              'circle-stroke-color': '#000000',
            }"
            (layerClick)="showSearchLocationPopup = true"
          />
          <!-- <mgl-layer
          id="search-location-popup"
          type="circle"
          source="search-location"
          [paint]="{
            'circle-radius': ['interpolate', ['linear'], ['zoom'], 10, 5, 15, 7, 20, 10],
            'circle-color': '#4285F4',
            'circle-stroke-width': 2,
            'circle-stroke-color': '#FFFFFF',
          }"
        />  -->
          @if (showSearchLocationPopup && searchLocation) {
            <mgl-popup
              class="mapbox-popup-modern"
              [feature]="searchLocation"
              [closeOnClick]="false"
              [closeButton]="true"
              [maxWidth]="'300px'"
            >
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <!-- Header -->
                <div
                  class="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-indigo-800 dark:to-blue-900 py-2 px-3"
                >
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-white mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <h3 class="text-base font-bold text-white">Searched Location</h3>
                  </div>
                </div>

                <!-- Content -->
                <div class="p-3 dark:text-white">
                  <!-- Radius Selector -->
                  <div class="mb-3">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-blue-500 dark:text-blue-400 mr-1.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4"
                          />
                        </svg>
                        <span class="text-xs font-medium">Show Nearby Within</span>
                      </div>
                      <select
                        #radiusDropdown
                        class="px-2 py-1 bg-gray-50 border border-gray-300 text-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-xs"
                        [value]="selectedRadius"
                        (change)="updateLocationRadius(+radiusDropdown.value, 'searchLocation')"
                      >
                        <option value="1">1 km</option>
                        <option value="2">2 km</option>
                        <option value="3">3 km</option>
                        <option value="4">4 km</option>
                      </select>
                    </div>
                  </div>

                  <!-- Divider -->
                  <div class="border-t border-gray-200 dark:border-gray-700 mb-3"></div>

                  <!-- Categories -->
                  <h4
                    class="text-xs font-semibold mb-2 text-gray-600 dark:text-gray-300 flex items-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-3.5 w-3.5 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                    NEARBY CATEGORIES
                  </h4>

                  <div class="grid grid-cols-2 gap-1.5">
                    <!-- Schools -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="schools"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.schools"
                        (change)="
                          handleNearbyOptionChange(
                            'schools',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label for="schools" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-blue-500 dark:text-blue-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path d="M12 14l9-5-9-5-9 5 9 5z" />
                          <path
                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                          />
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"
                          />
                        </svg>
                        Schools
                      </label>
                    </div>

                    <!-- Hawker Centers -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="hawker"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.hawker"
                        (change)="
                          handleNearbyOptionChange(
                            'hawker',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label for="hawker" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-orange-500 dark:text-orange-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                          />
                        </svg>
                        Hawker Centres
                      </label>
                    </div>

                    <!-- Bus Stops -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="busStops"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.busStops"
                        (change)="
                          handleNearbyOptionChange(
                            'busStops',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label for="busStops" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                          />
                        </svg>
                        Bus Stops
                      </label>
                    </div>

                    <!-- MRT/LRT Stations -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="mrt"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.mrt"
                        (change)="
                          handleNearbyOptionChange('mrt', $event.target.checked, 'searchLocation')
                        "
                      />
                      <label for="mrt" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-green-500 dark:text-green-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"
                          />
                        </svg>
                        MRT/LRT Stations
                      </label>
                    </div>

                    <!-- Sales Listings -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="salesListings"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.salesListings"
                        (change)="
                          handleNearbyOptionChange(
                            'salesListings',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label
                        for="salesListings"
                        class="ml-1.5 text-xs font-medium flex items-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-purple-500 dark:text-purple-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          />
                        </svg>
                        Sales Listings
                      </label>
                    </div>

                    <!-- HDB Data -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="hdbData"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.hdbData"
                        (change)="
                          handleNearbyOptionChange(
                            'hdbData',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label for="hdbData" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-yellow-500 dark:text-yellow-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                        HDB Data
                      </label>
                    </div>

                    <!-- Healthcare -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="healthcare"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.healthcare"
                        (change)="
                          handleNearbyOptionChange(
                            'healthcare',
                            $event.target.checked,
                            'searchLocation'
                          )
                        "
                      />
                      <label for="healthcare" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                          />
                        </svg>
                        Healthcare
                      </label>
                    </div>
                  </div>

                  <!-- Footer -->
                  <div class="flex justify-end mt-3">
                    <button
                      class="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white text-xs font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                      (click)="showSearchLocationPopup = false; showMoreInfoPopUp = false"
                    >
                      <div class="flex items-center">
                        <span>Close</span>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </mgl-popup>
          }
        </mgl-geojson-source>
        <!-- User location marker -->
        <mgl-geojson-source id="user-location" [data]="userLocationSource">
          <!-- Pulsing circle for user location -->
          <!-- <mgl-layer
          id="user-location-pulse"
          type="circle"
          source="user-location"
          [paint]="{
            'circle-radius': ['interpolate', ['linear'], ['zoom'], 10, 10, 15, 15, 20, 20],
            'circle-color': 'rgba(66, 133, 244, 0.3)',
            'circle-opacity': ['interpolate', ['linear'], ['get', 'pulse'], 0, 0.7, 1, 0],
            'circle-stroke-width': 0,
          }"
          (layerClick)="showUserLocationPopup = true"
        /> -->
          <!-- Main dot for user location -->
          <mgl-layer
            id="user-location-dot"
            type="circle"
            source="user-location"
            [paint]="{
              'circle-radius': ['interpolate', ['linear'], ['zoom'], 10, 5, 15, 7, 20, 10],
              'circle-color': '#4285F4',
              'circle-stroke-width': 2,
              'circle-stroke-color': '#FFFFFF',
            }"
          />
          @if (showUserLocationPopup && userLocation) {
            <mgl-popup
              class="mapbox-popup-modern"
              [feature]="userLocation"
              [closeOnClick]="false"
              [maxWidth]="'300px'"
            >
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <!-- Header -->
                <div
                  class="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-indigo-800 dark:to-blue-900 py-2 px-3"
                >
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-white mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <h3 class="text-base font-bold text-white">Current Location</h3>
                  </div>
                </div>

                <!-- Content -->
                <div class="p-3 dark:text-white">
                  <!-- Radius Selector -->
                  <div class="mb-3">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-blue-500 dark:text-blue-400 mr-1.5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4"
                          />
                        </svg>
                        <span class="text-xs font-medium">Show Nearby Within</span>
                      </div>
                      <select
                        #radiusDropdown
                        class="px-2 py-1 bg-gray-50 border border-gray-300 text-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-xs"
                        [value]="selectedRadius"
                        (change)="updateLocationRadius(+radiusDropdown.value, 'userLocation')"
                      >
                        <option value="1">1 km</option>
                        <option value="2">2 km</option>
                        <option value="3">3 km</option>
                        <option value="4">4 km</option>
                      </select>
                    </div>
                  </div>

                  <!-- Divider -->
                  <div class="border-t border-gray-200 dark:border-gray-700 mb-3"></div>

                  <!-- Categories -->
                  <h4
                    class="text-xs font-semibold mb-2 text-gray-600 dark:text-gray-300 flex items-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-3.5 w-3.5 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                    NEARBY CATEGORIES
                  </h4>

                  <div class="grid grid-cols-2 gap-1.5">
                    <!-- Schools -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="schools"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.schools"
                        (change)="
                          handleNearbyOptionChange('schools', $event.target.checked, 'userLocation')
                        "
                      />
                      <label for="schools" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-blue-500 dark:text-blue-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path d="M12 14l9-5-9-5-9 5 9 5z" />
                          <path
                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
                          />
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"
                          />
                        </svg>
                        Schools
                      </label>
                    </div>

                    <!-- Hawker Centers -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="hawker"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.hawker"
                        (change)="
                          handleNearbyOptionChange('hawker', $event.target.checked, 'userLocation')
                        "
                      />
                      <label for="hawker" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-orange-500 dark:text-orange-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                          />
                        </svg>
                        Hawker Centres
                      </label>
                    </div>

                    <!-- Bus Stops -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="busStops"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.busStops"
                        (change)="
                          handleNearbyOptionChange(
                            'busStops',
                            $event.target.checked,
                            'userLocation'
                          )
                        "
                      />
                      <label for="busStops" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                          />
                        </svg>
                        Bus Stops
                      </label>
                    </div>

                    <!-- MRT/LRT Stations -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="mrt"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.mrt"
                        (change)="
                          handleNearbyOptionChange('mrt', $event.target.checked, 'userLocation')
                        "
                      />
                      <label for="mrt" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-green-500 dark:text-green-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"
                          />
                        </svg>
                        MRT/LRT Stations
                      </label>
                    </div>

                    <!-- Sales Listings -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="salesListings"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.salesListings"
                        (change)="
                          handleNearbyOptionChange(
                            'salesListings',
                            $event.target.checked,
                            'userLocation'
                          )
                        "
                      />
                      <label
                        for="salesListings"
                        class="ml-1.5 text-xs font-medium flex items-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-purple-500 dark:text-purple-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          />
                        </svg>
                        Sales Listings
                      </label>
                    </div>

                    <!-- HDB Data -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="hdbData"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.hdbData"
                        (change)="
                          handleNearbyOptionChange('hdbData', $event.target.checked, 'userLocation')
                        "
                      />
                      <label for="hdbData" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-yellow-500 dark:text-yellow-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                        HDB Data
                      </label>
                    </div>

                    <!-- Healthcare -->
                    <div
                      class="flex items-center p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <input
                        type="checkbox"
                        id="healthcare"
                        class="w-3.5 h-3.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600"
                        [checked]="nearbyOptions.healthcare"
                        (change)="
                          handleNearbyOptionChange(
                            'healthcare',
                            $event.target.checked,
                            'userLocation'
                          )
                        "
                      />
                      <label for="healthcare" class="ml-1.5 text-xs font-medium flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3.5 w-3.5 mr-1 text-red-500 dark:text-red-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                          />
                        </svg>
                        Healthcare
                      </label>
                    </div>
                  </div>

                  <!-- Footer -->
                  <div class="flex justify-end mt-3">
                    <button
                      class="px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white text-xs font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                      (click)="showUserLocationPopup = false"
                    >
                      <div class="flex items-center">
                        <span>Close</span>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </mgl-popup>
          }
        </mgl-geojson-source>
        <!-- Circle markers for HDB blocks -->
        <mgl-geojson-source id="hdb-markers" [data]="hdbSourceData">
          @if (hdbSourceData?.features?.length > 0) {
            @for (feature of hdbSourceData.features; track $index) {
              <mgl-marker
                (click)="getPropertyDetails(feature)"
                [lngLat]="feature.geometry.coordinates"
                [offset]="[0, -20]"
              >
                <div
                  (click)="getPropertyDetails(feature)"
                  class="w-6 h-6 rounded-full text-white text-xs flex items-center justify-center shadow-md"
                  [ngStyle]="{
                    'background-color': townColors[feature.properties.town] || '#999999',
                  }"
                  title="Block {{ feature.properties.blockNumber }}"
                >
                  {{ feature.properties.blockNumber.substring(3) }}
                </div>
              </mgl-marker>
            }
          }
        </mgl-geojson-source>

        <!-- Circle markers for Condo properties -->
        <mgl-geojson-source id="condo-markers" [data]="condoSourceData">
          <mgl-layer
            id="condo-layer"
            type="circle"
            source="condo-markers"
            [paint]="{
              'circle-radius': 8,
              'circle-color': ['get', 'color'],
              'circle-stroke-width': 1,
              'circle-stroke-color': '#ffffff',
              'circle-opacity': 0.8,
            }"
            (layerClick)="getPropertyDetails($event)"
          />
        </mgl-geojson-source>
      </mgl-map>
    </div>

    <!-- @if (showHDBListings || showCondoListings) {
      <div class="overflow-auto md:h-full">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold">
            {{ activeTab === 'HDB' ? 'HDB Sale Listings' : 'Condo Sale Listings' }}
            <span class="text-sm text-gray-600 ml-2">
              ({{ activeTab === 'HDB' ? filteredPropertyData.length : condoData.length }}
              properties)
            </span>
          </h2>
        </div>
        <button
          (click)="openFilterForm()"
          class="px-4 py-2 my-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
        >
          <mat-icon>filter_list</mat-icon>
          <span class="ml-2">Filter</span>
        </button>
        <div class="space-y-4">
          @for (
            property of activeTab === 'HDB' ? propertyData : condoData;
            track property.blockNo;
            let i = $index
          ) {
            <div
              class="p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer"
              [ngClass]="{ 'bg-blue-300 dark:bg-blue-500': i === 0 }"
              (click)="centerMapOnProperty(property)"
            >
              <h3 class="text-lg font-semibold">
                {{ activeTab === 'HDB' ? 'Block ' : '' }}{{ property.blockNo }}
              </h3>
              <p>{{ property.street }}, {{ property.town }}</p>
              <p>Postal Code: {{ property.postalCode }}</p>
              <p class="text-green-600 font-bold">S$ {{ property.salesPrice.toLocaleString() }}</p>
            </div>
          }
        </div>
      </div>
    } -->

    <!-- Loading Indicator -->
    @if (!isMapLoaded) {
      <div
        class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 dark:bg-gray-900 dark:bg-opacity-70 z-10"
      >
        <div class="text-center">
          <div
            class="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"
          ></div>
          <p class="mt-2 text-gray-700 dark:text-gray-300">Loading map...</p>
        </div>
      </div>
    }
    @if (showHDBListings || showCondoListings) {
      <div class="md:col-span-1 overflow-auto md:h-full">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl my-2 font-bold">
            {{ activeTab === 'HDB' ? 'HDB Sale Listings' : 'Condo Sale Listings' }}
            <span class="text-sm text-gray-600 ml-2">
              ({{ activeTab === 'HDB' ? filteredPropertyData.length : condoData.length }}
              properties)
            </span>
          </h2>
          <button
            (click)="showHDBListings = false; showCondoListings = false; clearAllMarkers()"
            title="Clear all markers"
          >
            <mat-icon>cancel</mat-icon>
          </button>
        </div>
        <button
          (click)="openFilterForm()"
          class="px-4 py-2 my-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
        >
          <mat-icon>filter_list</mat-icon>
          <span class="ml-2">Filter</span>
        </button>
        <div class="space-y-4">
          @for (
            property of activeTab === 'HDB' ? propertyData : condoData;
            track property.blockNo;
            let i = $index
          ) {
            <div
              class="p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer"
              [ngClass]="{ 'bg-blue-100 dark:bg-blue-500': i === 0 }"
              (click)="centerMapOnProperty(property)"
            >
              <h3 class="text-lg font-semibold">
                {{ activeTab === 'HDB' ? 'Block ' : ''
                }}{{ activeTab === 'HDB' ? property.blockNo.substring(3) : property.blockNo }}
              </h3>
              <p>{{ property.street }}, {{ property.town }}</p>
              <p>Postal Code: {{ property.postalCode }}</p>
              <p class="text-green-600 font-bold">S$ {{ property.salesPrice.toLocaleString() }}</p>
            </div>
          }
        </div>
      </div>
    }

    <!-- Property Details Panel -->
    @if (showPropertyDetails && selectedProperty) {
      <div
        class="w-full overflow-auto bg-white dark:bg-gray-800 shadow-lg rounded-lg order-2 md:order-2"
        [ngClass]="{
          'md:h-full': showPropertyDetails,
          'h-[calc(100vh-20rem)]': !showHDBListings && !showCondoListings,
        }"
      >
        <!-- Header with close button -->
        <div
          class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-800 z-10"
        >
          <h2 class="text-xl font-bold text-gray-800 dark:text-white">Property Details</h2>
          <button
            class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            (click)="closePropertyDetails()"
            title="Close"
          >
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <!-- Property Information -->
        <div class="p-4 space-y-4">
          <!-- HDB Block Details -->
          @if (selectedProperty.blockNumber) {
            <div>
              <h3 class="text-lg font-semibold">Block {{ selectedProperty.blockNumber }}</h3>
              <p class="text-sm font-medium">{{ selectedProperty.hdbTown }}</p>
              <p class="text-sm italic">{{ selectedProperty?.projectName || 'N/A' }}</p>

              <div class="border-t border-gray-300 my-2"></div>

              <p class="text-sm"><strong>Street:</strong> {{ selectedProperty.streetAddress }}</p>
              <p class="text-sm"><strong>Postal Code:</strong> {{ selectedProperty.postalCode }}</p>
              <p class="text-sm">
                <strong>Lease Start:</strong> {{ selectedProperty?.leaseStart || 'N/A' }}
              </p>

              <div class="border-t border-gray-300 my-2"></div>

              <p class="text-sm">
                <strong>{{ selectedProperty.totalFloors }}</strong> Floors
              </p>
              <p class="text-sm">
                <strong>{{ selectedProperty.unitsPerFloor }}</strong> Units Per Floor
              </p>
              <p class="text-sm font-semibold">
                Total <strong>{{ selectedProperty.totalUnits }}</strong> Units
              </p>
            </div>
          }

          <!-- Condo Details -->
          @if (selectedProperty.projectName && !selectedProperty.blockNumber) {
            <div>
              <h3 class="text-lg font-semibold">{{ selectedProperty.projectName }}</h3>
              <p class="text-sm">{{ selectedProperty.address }}</p>

              <div class="border-t border-gray-300 my-2"></div>

              <p class="text-sm"><strong>District:</strong> {{ selectedProperty.district }}</p>
              <p class="text-sm">
                <strong>Year Completed:</strong> {{ selectedProperty.yearOfCompletion }}
              </p>
              <p class="text-sm"><strong>Tenure:</strong> {{ selectedProperty.tenure }}</p>
              <p class="text-sm"><strong>Total Units:</strong> {{ selectedProperty.totalUnits }}</p>
            </div>
          }

          <!-- Floor Plans if available -->
          @if (selectedProperty.floorPlans?.length) {
            <div class="mt-4">
              <p class="font-semibold text-sm">Floor Plans:</p>
              <img
                src="{{ selectedProperty.floorPlans[0] }}"
                class="mt-2 border rounded-lg w-40 shadow-md"
                alt="Floor Plan"
              />
            </div>
          }

          <!-- Ethnic Query for HDB -->
          @if (selectedProperty.blockNumber) {
            <div class="mt-2 p-4 border rounded-lg shadow-sm">
              <div
                class="flex justify-between items-center cursor-pointer"
                (click)="toggleSellerEthnicQuota()"
              >
                <p class="font-semibold text-sm">
                  Ethnic Query Seller
                  <span class="italic text-gray-500">(Source: <strong>HDB</strong>)</span>:
                </p>
                <mat-icon (click)="showSellerEthnicQuota = !showSellerEthnicQuota">{{
                  showSellerEthnicQuota ? 'remove' : 'add'
                }}</mat-icon>
              </div>

              @if (showSellerEthnicQuota) {
                @if(sellerEthnicData && sellerEthnicData.length > 0){
                  <div class="text-xs space-y-4 mt-4 animate-slideDown">
                    <!-- Singaporean Citizen / Malaysian SPR -->
                    <div>
                      <p class="text-lg font-bold">Singaporean Citizen / Malaysian SPR</p>
                      <ul class="ml-4 mt-1 space-y-1">
                        <li>
                          <span class="italic font-medium">Chinese:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[0]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Malay:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[1]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[2]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <p class="text-lg font-bold">Non Malaysian SPR</p>
                      <ul class="ml-4 mt-1 space-y-1">
                        <li>
                          <span class="italic font-medium">Chinese:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[3]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Malay:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[4]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                          <br />
                          <span class="italic">
                            {{ sellerEthnicData[5]?.details || 'No information available at this moment'}}
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                }
                @else{
                  <p>Loading Information...</p>
                }
              }
            </div>
            <div class="mt-2 p-4 border rounded-lg shadow-sm">
              <div
                class="flex justify-between items-center cursor-pointer"
                (click)="toggleSellerEthnicQuota()"
              >
                <p class="font-semibold text-sm">
                  Ethnic Query Buyer
                  <span class="italic text-gray-500">(Source: <strong>HDB</strong>)</span>:
                </p>
                <mat-icon (click)="showBuyerEthnicQuota = !showBuyerEthnicQuota">{{
                  showBuyerEthnicQuota ? 'remove' : 'add'
                }}</mat-icon>
              </div>

              @if (showBuyerEthnicQuota) {
                @if(buyerEthnicData){
                  <div class="text-xs space-y-4 mt-4 animate-slideDown">
                    <!-- Singaporean Citizen / Malaysian SPR -->
                    <div>
                      <p class="text-lg font-bold">Singaporean Citizen / Malaysian SPR</p>
                      <ul class="ml-4 mt-1 space-y-1">
                        <li>
                          <span class="italic font-medium">Chinese:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[0].data.eligibilityInfo }}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Malay:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[1].data.eligibilityInfo }}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[2].data.eligibilityInfo }}
                          </span>
                        </li>
                      </ul>
                    </div>
                    <div>
                      <p class="text-lg font-bold">Non Malaysian SPR</p>
                      <ul class="ml-4 mt-1 space-y-1">
                        <li>
                          <span class="italic font-medium">Chinese:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[3].data.eligibilityInfo }}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Malay:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[4].data.eligibilityInfo }}
                          </span>
                        </li>
                        <li>
                          <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                          <br />
                          <span class="italic">
                            {{ buyerEthnicData[5].data.eligibilityInfo }}
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                }
                @else{
                  <p>Loading Information...</p>
                }
              }
            </div>
          }
        </div>

        <!-- Transaction Data Section -->
        @if (propertyTransactionData && propertyTransactionData.data?.length > 0) {
          <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold mb-4">{{ propertyTransactionData.heading }}</h3>

            <div class="overflow-x-auto">
              <table class="w-full text-left border-collapse text-sm">
                <thead>
                  <tr class="bg-gray-100 dark:bg-gray-700">
                    @for (header of propertyTransactionData.header; track header) {
                      <th class="px-3 py-2 font-semibold border dark:border-gray-600">
                        {{ header }}
                      </th>
                    }
                  </tr>
                </thead>
                <tbody>
                  @for (row of propertyTransactionData.data; track $index) {
                    <tr class="border hover:bg-gray-50 dark:hover:bg-gray-700 transition-all">
                      @for (cell of row; track cell) {
                        <td class="px-3 py-2 border dark:border-gray-600">{{ cell }}</td>
                      }
                    </tr>
                  }
                </tbody>
              </table>
            </div>

            <!-- Toggle Chart Button -->
            <div class="mt-4 flex justify-end">
              <button
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
                (click)="toggleTransactionChart()"
              >
                {{ showTransactionChart ? 'Hide Charts' : 'Show Charts' }}
              </button>
            </div>
          </div>
        } @else {
          <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex flex-col items-center justify-center py-6 text-center">
              <mat-icon class="text-gray-400 text-4xl mb-2">info</mat-icon>
              <p class="text-gray-500 dark:text-gray-400">
                No transaction data available for this property.
              </p>
            </div>
          </div>
        }

        <!-- Charts Section -->
        <!-- Charts Section -->
        @if (showTransactionChart) {
          @if (transactionMedianPrices.length > 0) {
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold mb-4">Price & PSF Trends</h3>

              <!-- Median Price Chart -->
              <div class="mb-6 border rounded-lg p-4 shadow-sm">
                <h4 class="text-sm font-bold text-center mb-2">Monthly Median Price</h4>
                <canvas #medianPriceChart></canvas>
              </div>

              <!-- Average PSF Chart -->
              <div class="border rounded-lg p-4 shadow-sm">
                <h4 class="text-sm font-bold text-center mb-2">Monthly Average PSF</h4>
                <canvas #avgPsfChart></canvas>
              </div>
            </div>
          } @else {
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex flex-col items-center justify-center py-6 text-center">
                <mat-icon class="text-gray-400 text-4xl mb-2">bar_chart</mat-icon>
                <p class="text-gray-500 dark:text-gray-400">
                  No chart data available for this property.
                </p>
              </div>
            </div>
          }
        }
      </div>
    }

    @if (showSalesPropertyDetails && salesProperty) {
      <div
        class="w-full overflow-auto bg-white dark:bg-gray-800 shadow-lg rounded-lg order-2 md:order-2"
        [ngClass]="{
          'md:h-full': showSalesPropertyDetails,
          'h-[calc(100vh-20rem)]': !showHDBListings && !showCondoListings,
        }"
      >
        <div class="w-full flex justify-between items-center">
          <h2 class="text-xl font-bold text-gray-800 dark:text-white">Property Details</h2>
          <button
            class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            (click)="showSalesPropertyDetails = false; salesProperty = null"
            title="Close"
          >
            <mat-icon>close</mat-icon>
          </button>
        </div>
        <div class="flex flex-col gap-4 p-6 border rounded-lg shadow-lg bg-white">
          <!-- Scrollable content -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-900">
              Price: {{ salesProperty.salesPrice }}
            </h2>

            <div class="mt-2 mb-3">
              <img
                src="/assets/dummy_prop.png"
                alt="Property Image"
                class="w-full h-[200px] rounded-lg shadow-md"
              />
            </div>

            <h2 class="text-xl font-bold text-gray-900">Block: {{ salesProperty.blockNo }}</h2>
            <p class="text-md text-gray-700 font-semibold">{{ salesProperty.hdbTown }}</p>
            <p class="text-sm text-gray-600 italic">{{ salesProperty?.projectName || 'N/A' }}</p>

            <div class="border-t border-gray-200 my-2"></div>

            <div class="flex flex-col gap-1">
              <p class="text-sm text-gray-700">
                <strong>Street:</strong> {{ salesProperty.street }}
              </p>
              <p class="text-sm text-gray-700">
                <strong>Postal Code:</strong> {{ salesProperty.postalCode }}
              </p>
              <p class="text-sm text-gray-700">
                <strong>Lease Start:</strong> {{ salesProperty?.leaseStart || 'N/A' }}
              </p>
            </div>

            <div class="border-t border-gray-200 my-2"></div>

            <div class="flex flex-col gap-1">
              <p class="text-sm">
                <strong>{{ salesProperty.totalFloors }}</strong> Floors
              </p>
              <p class="text-sm">
                <strong>{{ salesProperty.unitsPerFloor }}</strong> Units Per Floor
              </p>
              <p class="text-sm font-semibold text-gray-900">
                Total <strong>{{ salesProperty.totalUnits }}</strong> Units
              </p>
            </div>

            <div class="mt-2 p-4 border rounded-lg shadow-sm">
              <div
                class="flex justify-between items-center cursor-pointer"
                (click)="toggleSellerEthnicQuota()"
              >
                <p class="font-semibold text-sm">
                  Ethnic Query Seller
                  <span class="italic text-gray-500">(Source: <strong>HDB</strong>)</span>:
                </p>
                <mat-icon (click)="showSellerEthnicQuota = !showSellerEthnicQuota">{{
                  showSellerEthnicQuota ? 'remove' : 'add'
                }}</mat-icon>
              </div>

              @if (showSellerEthnicQuota) {
                <div class="text-xs space-y-4 mt-4 animate-slideDown">
                  <!-- Singaporean Citizen / Malaysian SPR -->
                  <div>
                    <p class="text-lg font-bold">Singaporean Citizen / Malaysian SPR</p>
                    <ul class="ml-4 mt-1 space-y-1">
                      <li>
                        <span class="italic font-medium">Chinese:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[0].details }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Malay:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[1].details }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[2].details }}
                        </span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <p class="text-lg font-bold">Non Malaysian SPR</p>
                    <ul class="ml-4 mt-1 space-y-1">
                      <li>
                        <span class="italic font-medium">Chinese:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[3].details }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Malay:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[4].details }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                        <br />
                        <span class="italic">
                          {{ sellerEthnicData[5].details }}
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              }
            </div>
            <div class="mt-2 p-4 border rounded-lg shadow-sm">
              <div
                class="flex justify-between items-center cursor-pointer"
                (click)="toggleSellerEthnicQuota()"
              >
                <p class="font-semibold text-sm">
                  Ethnic Query Buyer
                  <span class="italic text-gray-500">(Source: <strong>HDB</strong>)</span>:
                </p>
                <mat-icon (click)="showBuyerEthnicQuota = !showBuyerEthnicQuota">{{
                  showBuyerEthnicQuota ? 'remove' : 'add'
                }}</mat-icon>
              </div>

              @if (showBuyerEthnicQuota) {
                <div class="text-xs space-y-4 mt-4 animate-slideDown">
                  <!-- Singaporean Citizen / Malaysian SPR -->
                  <div>
                    <p class="text-lg font-bold">Singaporean Citizen / Malaysian SPR</p>
                    <ul class="ml-4 mt-1 space-y-1">
                      <li>
                        <span class="italic font-medium">Chinese:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[0].eligibilityInfo }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Malay:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[1].eligibilityInfo }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[2].eligibilityInfo }}
                        </span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <p class="text-lg font-bold">Non Malaysian SPR</p>
                    <ul class="ml-4 mt-1 space-y-1">
                      <li>
                        <span class="italic font-medium">Chinese:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[3].eligibilityInfo }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Malay:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[4].eligibilityInfo }}
                        </span>
                      </li>
                      <li>
                        <span class="italic font-medium">Indian and Other Ethnic Groups:</span>
                        <br />
                        <span class="italic">
                          {{ buyerEthnicData[5].eligibilityInfo }}
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
        @if (propertyTransactionData && propertyTransactionData.data?.length > 0) {
          <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold mb-4">{{ propertyTransactionData.heading }}</h3>

            <div class="overflow-x-auto">
              <table class="w-full text-left border-collapse text-sm">
                <thead>
                  <tr class="bg-gray-100 dark:bg-gray-700">
                    @for (header of propertyTransactionData.header; track header) {
                      <th class="px-3 py-2 font-semibold border dark:border-gray-600">
                        {{ header }}
                      </th>
                    }
                  </tr>
                </thead>
                <tbody>
                  @for (row of propertyTransactionData.data; track $index) {
                    <tr class="border hover:bg-gray-50 dark:hover:bg-gray-700 transition-all">
                      @for (cell of row; track cell) {
                        <td class="px-3 py-2 border dark:border-gray-600">{{ cell }}</td>
                      }
                    </tr>
                  }
                </tbody>
              </table>
            </div>

            <!-- Toggle Chart Button -->
            <div class="mt-4 flex justify-end">
              <button
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
                (click)="toggleTransactionChart()"
              >
                {{ showTransactionChart ? 'Hide Charts' : 'Show Charts' }}
              </button>
            </div>
          </div>
        } @else {
          <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex flex-col items-center justify-center py-6 text-center">
              <mat-icon class="text-gray-400 text-4xl mb-2">info</mat-icon>
              <p class="text-gray-500 dark:text-gray-400">
                No transaction data available for this property.
              </p>
            </div>
          </div>
        }
        @if (showTransactionChart) {
          @if (transactionMedianPrices.length > 0) {
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold mb-4">Price & PSF Trends</h3>

              <!-- Median Price Chart -->
              <div class="mb-6 border rounded-lg p-4 shadow-sm">
                <h4 class="text-sm font-bold text-center mb-2">Monthly Median Price</h4>
                <canvas #medianPriceChart></canvas>
              </div>

              <!-- Average PSF Chart -->
              <div class="border rounded-lg p-4 shadow-sm">
                <h4 class="text-sm font-bold text-center mb-2">Monthly Average PSF</h4>
                <canvas #avgPsfChart></canvas>
              </div>
            </div>
          } @else {
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex flex-col items-center justify-center py-6 text-center">
                <mat-icon class="text-gray-400 text-4xl mb-2">bar_chart</mat-icon>
                <p class="text-gray-500 dark:text-gray-400">
                  No chart data available for this property.
                </p>
              </div>
            </div>
          }
        }
        <div class="w-full flex justify-center">
          <button
            class="mx-auto my-4 py-2 w-[220px] bg-green-600 text-white font-semibold rounded-lg transition-all"
            (click)="goToListingPage()"
          >
            Go to Listing
          </button>
        </div>
      </div>
    }
  </div>

  <div class="flex space-x-4 p-4 border-b">
    <button
      [class]="
        daisyMerge(
          'px-4 py-2 rounded-md transition-colors flex items-center shadow-md',
          activeTab === 'HDB'
            ? 'bg-blue-600 text-white hover:bg-blue-700'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        )
      "
      (click)="setActiveTab('HDB')"
    >
      <span class="material-icons mr-2">apartment</span>
      HDB
      @if (activeTab === 'HDB') {
        <mat-icon class="ml-2 text-white">check_circle</mat-icon>
      }
    </button>
    <button
      [class]="
        daisyMerge(
          'px-4 py-2 rounded-md transition-colors flex items-center shadow-md',
          activeTab === 'Condo'
            ? 'bg-blue-600 text-white hover:bg-blue-700'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        )
      "
      (click)="setActiveTab('Condo')"
    >
      <span class="material-icons mr-2">domain</span>
      Condo
      @if (activeTab === 'Condo') {
        <mat-icon class="ml-2 text-white">check_circle</mat-icon>
      }
    </button>
    @if (showCondoData && activeTab === 'Condo') {
      <div class="flex items-center p-2 bg-gray-100 border-b">
        <div class="text-base font-medium mr-4">Condo Properties</div>

        <!-- Limit selector -->
        <div class="flex items-center mr-4">
          <label for="condoLimitSelect" class="text-sm mr-2">Show:</label>
          <select
            id="condoLimitSelect"
            class="px-2 py-1 border rounded-md text-sm"
            [(ngModel)]="selectedCondoLimit"
            (change)="loadCondoPage(0)"
          >
            @for (option of paginationOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
        </div>

        <!-- Page navigation -->
        <div class="flex items-center space-x-1">
          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="currentCondoPage === 0"
            (click)="loadCondoPage(currentCondoPage - 1)"
          >
            <mat-icon class="text-sm">chevron_left</mat-icon>
          </button>

          <span class="text-sm px-2">
            {{ currentCondoPage === 0 ? 0 : currentCondoPage + 1 }} /
            {{ Math.ceil(totalCondoCount / selectedCondoLimit) }}
          </span>

          <button
            class="px-2 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            [disabled]="(currentCondoPage + 1) * selectedCondoLimit >= totalCondoCount"
            (click)="loadCondoPage(currentCondoPage + 1)"
          >
            <mat-icon class="text-sm">chevron_right</mat-icon>
          </button>
        </div>

        <div class="ml-auto text-sm text-gray-600">
          Showing {{ apiCondoData.length }} of {{ totalCondoCount }} properties
        </div>
      </div>
    }
  </div>

  @if (activeTab) {
    <div class="flex space-x-4 p-4 border-b">
      <button
        class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
        (click)="displaySaleListings()"
      >
        <mat-icon>attach_money</mat-icon>
        <span class="ml-2">
          {{ activeTab === 'HDB' ? 'HDB Sale Listings' : 'Condo Sale Listings' }}
        </span>
      </button>
      @if (activeTab === 'HDB') {
        <div class="relative inline-block">
          @if (showDropdown) {
            <div class="absolute shadow-lg mt-4 w-64 border rounded p-2 z-10 top-full left-0">
              @for (town of hdbTowns; track $index) {
                <div
                  class="flex items-center space-x-2 cursor-pointer p-1 rounded"
                  (click)="toggleTown(town)"
                >
                  <div
                    class="w-5 h-5 border"
                    [style.border-color]="townColors[town]"
                    [style.background-color]="isSelected(town) ? townColors[town] : 'transparent'"
                  ></div>
                  <span
                    [style.color]="townColors[town]"
                    [style.font-weight]="isSelected(town) ? 'bold' : 'normal'"
                  >
                    {{ town }}
                  </span>
                </div>
              }

              <!-- Reset filter option -->
              @if (selectedTown) {
                <div class="mt-2 pt-2 border-t border-gray-200">
                  <div
                    class="flex items-center px-3 py-2 rounded-md cursor-pointer hover:bg-gray-100 text-blue-600"
                    (click)="selectedTown = ''; clearMarkers()"
                  >
                    <mat-icon class="text-blue-600 mr-2" style="font-size: 18px">refresh</mat-icon>
                    <span>Clear Selection</span>
                  </div>
                </div>
              }
            </div>
          }
          <button
            class="bg-purple-500 text-white px-4 py-2 rounded flex items-center"
            (click)="toggleDropdown()"
          >
            <mat-icon class="mr-2">apartment</mat-icon>
            HDB Data
            <mat-icon class="ml-2">{{
              showDropdown ? 'arrow_drop_up' : 'arrow_drop_down'
            }}</mat-icon>
          </button>
        </div>
      } @else {
        <button
          class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors flex items-center"
          (click)="displayPropertyData()"
        >
          <mat-icon>apartment</mat-icon>
          <span class="ml-2">Condo Data</span>
        </button>
      }
    </div>
  }
</div>
