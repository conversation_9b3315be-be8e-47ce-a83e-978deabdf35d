<div class="p-3 flex flex-col md:flex-row place-content-start items-center">
  <div class="mr-0 flex flex-row place-content-start items-center">
    <span class="material-symbols-outlined">manage_accounts</span>
    <div class="p-3 text-2xl grow-1">Account</div>
  </div>
</div>
<div class="flex flex-row justify-center grow">
  <div class="p-4 grow">
    <div
      class="mx-auto p-4 mb-5 card bg-base-200 text-base-200-content max-w-2xl animate__animated animate__pulse"
    >
      <div class="text-2xl py-4">Available TFA</div>
      <div class="flex flex-col gap-2">
        <div class="flex flex-row items-center">
          <div>Email</div>
          <button class="ml-auto btn">Activated</button>
        </div>
        <div class="flex flex-row items-center">
          <div>Google Authenticator/Authy</div>
          <div class="ml-auto">
            @if (status.TOTPActive) {
              <button class="btn btn-outline btn-error mr-4" (click)="removeTOTP()">Remove</button>
            }

            <button class="btn btn-outline btn-accent" (click)="generateSecret(totpForm)">
              {{ status.TOTPActive ? 'Update' : 'Activate' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="mx-auto p-4 mb-5 card bg-base-200 text-base-200-content max-w-2xl animate__animated animate__pulse"
    >
      <div class="text-2xl mb-5">Enable TFA</div>
      <button class="ml-auto btn btn-outline btn-accent" (click)="setTFAEnable(!status.TFARequire)">
        {{ status.TFARequire ? 'Disable' : 'Enable' }}
      </button>
    </div>
  </div>
</div>

<ng-template #totpForm>
  <div mat-dialog-content class="!flex flex-col gap-6 p-6">
    <img [src]="totpFormData.data.qr" class="m-auto" />
    <div class="text-center">
      <div class="mb-2 text-sm font-medium">{{ totpFormData.data.secret }}</div>
      <div>Scan or enter this secret key in Google Authenticator/ Authy</div>
    </div>
    <label class="form-control w-full">
      <div class="label">
        <span class="label-text">Enter Token to verify</span>
      </div>
      <input
        type="text"
        [formControl]="totpFormData.form"
        placeholder="Token"
        class="input input-bordered w-full max-w-xs"
      />
    </label>
    <button
      class="btn btn-outline btn-success"
      type="submit"
      [disabled]="totpFormData.form.invalid"
      (click)="saveOTPSecret()"
    >
      Save
    </button>
  </div>
</ng-template>
