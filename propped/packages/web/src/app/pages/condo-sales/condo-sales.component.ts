import { Component, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { CondoSalesService } from './condo-sales.service';

@Component({
  selector: 'app-condo-sales',
  templateUrl: './condo-sales.component.html',
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSliderModule,
    ReactiveFormsModule,
    SVGIconComponent,
  ],
})
export class SalesComponent implements OnInit {
  readonly #condoSalesService = inject(CondoSalesService);
  readonly #formModalService = inject(FormModalService);

  filter = {} as any;
  config = { page: 1, limit: 400, sort: 'id', order: 'DESC', lastId: 0 };
  db: any;
  headers = [];
  data = [];
  meta: any = {};

  async ngOnInit(): Promise<void> {
    this.db = await this.#condoSalesService.loadDB();
    this.getData();
    // this.#condoSalesService.getNumberOfUnitSoldByMonth(this.db);
    // this.#condoSalesService.getNumberOfUnitSoldByProject(this.db);
  }

  getData() {
    const res = this.#condoSalesService.generateSqlQuery(this.db, this.filter, this.config);
    this.headers = res.headers;
    this.data = res.data;
    this.meta = res.meta;
    this.config.lastId = this.data.at(-1)[0];
  }

  previous() {
    if (this.config.page === 1) return;
    this.config.page = this.config.page - 1;
    this.getData();
  }

  next() {
    const totalPages = Math.ceil(this.meta.total / this.config.limit);
    if (this.config.page === totalPages) return;
    this.config.page = this.config.page + 1;
    this.getData();
  }

  public async openFilterForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'districtId',
        key: 'districtId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllDistricts(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'marketSegmentId',
        key: 'marketSegmentId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllMarketSegments(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'propertyTypeId',
        key: 'propertyTypeId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllPropertyTypes(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'typeOfAreaId',
        key: 'typeOfAreaId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#condoSalesService.getAllTypeOfAreas(this.db),
      },
      {
        controlType: SilverFieldTypes.DATE_RANGE,
        label: 'Time period ',
        key: 'timePeriod',
        value: { from: null, to: null },
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum lease year',
        placeholder: 'lease year',
        type: 'number',
        key: 'minimumLeaseYear',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum lease year',
        placeholder: 'Maximum lease year',
        type: 'number',
        key: 'maximumLeaseYear',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Storey',
        placeholder: 'Minimum Storey',
        type: 'number',
        key: 'minimumStorey',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Storey',
        placeholder: 'Maximum Storey',
        type: 'number',
        key: 'maximumStorey',
        value: '',
        valid: { required: false },
      },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter', form });
    if (!result.action) return;
    this.filter = result.value;
    this.config.page = 1;
    this.config.lastId = 0;
    this.getData();
  }
}
