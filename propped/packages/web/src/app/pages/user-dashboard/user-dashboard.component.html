<div class="user-dashboard-container w-full mx-auto p-4 space-y-4">
    <!-- User Info Header -->
    <div class="flex flex-col">
      <app-seller-info [username]="userProfile.username"/>
    </div>

    <!-- Profile & Settings Section -->
    <div class="bg-white rounded-lg shadow p-4 space-y-2 border">
      <div class="flex items-center justify-between">
        <span>Your Profile</span>
        <button mat-icon-button aria-label="Edit Profile">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
      <div class="flex items-center justify-between">
        <span>App Settings</span>
        <button mat-icon-button aria-label="Edit App Settings">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
      <div class="flex items-center justify-between">
        <span>App Notifications</span>
        <button mat-icon-button aria-label="Edit App Notifications">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
      <div class="flex items-center justify-between">
        <span>Helpdesk</span>
        <button mat-icon-button aria-label="Helpdesk">
          <mat-icon>help_outline</mat-icon>
        </button>
      </div>
      <div class="flex items-center justify-between">
        <span>Logout</span>
        <button mat-icon-button aria-label="Logout">
          <mat-icon>logout</mat-icon>
        </button>
      </div>
    </div>

    <!-- Notifications Block -->
    <div class="bg-white rounded-lg shadow p-4 border">
      <div class="font-semibold text-lg mb-2">Notifications</div>
      <div class="text-gray-500">You have no new notifications</div>
    </div>

    <!-- Resale Process Section -->
    <div class="bg-white rounded-lg shadow border">
        <button
          class="w-full flex items-center justify-between px-4 py-3 font-semibold text-lg focus:outline-none"
          (click)="toggleResaleProcess()"
          [attr.aria-expanded]="resaleProcessOpen"
          aria-controls="resale-process-content"
        >
          Resale Process
          <mat-icon>{{ resaleProcessOpen ? 'remove' : 'add' }}</mat-icon>
        </button>
        @if (resaleProcessOpen) {
          <div
            id="resale-process-content"
            class="px-4 pb-4 text-gray-600 animate-fadeIn"
          >
            @if (!hasSalesListing && buyerAcceptedOffers.length === 0) {
              <div>
                <div>You have no transaction in progress.</div>
                <div class="mt-2">
                  Sell – Create a Sales Listing
                  <a class="underline cursor-pointer" (click)="goToBuyerSeller()">here</a>
                </div>
                <div>
                  Buy – Search within available Sales Listings
                  <a class="underline cursor-pointer" (click)="goToBuyerSeller()">here</a>
                </div>
              </div>
            } @else {
              <div>
                <div class="font-semibold text-lg mb-2">You have {{ salesListings.length }} sales listing(s).</div>
                <div class="mb-2">You are the Seller</div>

                <!-- Scrollable container for all listings -->
                <div class="h-full pr-1">
                  @for (listing of salesListings; track $index) {
                    <div
                      class="border rounded-lg p-3 bg-white max-w-xs mb-4 cursor-pointer hover:shadow-md transition-shadow"
                      (click)="goToSellerPreview(listing._id)"
                    >
                      <div class="font-bold mb-1">{{ listing?.street || 'Compassvale Bow' }}</div>
                      <div class="font-semibold">Block {{ listing?.blockNumber || '266C' }}</div>
                      <div class="text-sm mb-1">{{ listing?.town || 'Sengkang Town' }}</div>
                      <div class="text-2xl font-bold mb-1">S$ {{ listing?.price | number }}</div>
                      <div class="text-xs mb-2">S${{ listing?.psf || '6157' }} PSF</div>
                      <div class="text-xs mb-1">Listed on 17<sup>th</sup> August 2024 (Listing ID: {{ listing?.listingId || '01234' }})</div>
                      <hr class="my-2" />
                      <div class="text-xs font-semibold mb-1">Sale Conditions</div>
                      <ul class="list-disc ml-5 text-xs mb-2">
                        @for (cond of listing?.salesCondition || ['Extension of Stay (3 months)', 'Vacant Possession']; track $index) {
                          <li>{{ cond }}</li>
                        }
                      </ul>
                      <img
                        [src]="'assets/dummy_prop.png'"
                        alt="Listing Photo"
                        class="rounded-lg w-full max-w-[200px] h-auto border"
                      />
                      <div class="mt-2 text-center">
                        <span (click)="goToSellerPreview(listing._id)" class="text-blue-600 text-sm font-medium cursor-pointer">Go to preview Listing</span>
                      </div>
                    </div>
                  }
                   <!-- Buyer Accepted Offers Section -->
                @if (buyerAcceptedOffers.length > 0) {
                  <div class="mt-6">
                    <div class="font-semibold text-lg mb-2">You have {{ buyerAcceptedOffers.length }} accepted offer(s).</div>
                    <div class="mb-2">You are the Buyer</div>

                    <!-- Scrollable container for all accepted offers -->
                    <div class="W-[350px] pr-1">
                      @for (offer of buyerAcceptedOffers; track $index) {
                        <div class="border rounded-lg p-3 bg-white max-w-lg mb-4 hover:shadow-md transition-shadow">
                          <div class="font-bold mb-1">{{ offer.listingDetails?.street || 'Compassvale Bow' }}</div>
                          <div class="font-semibold">Block {{ offer.listingDetails?.blockNumber || '266C' }}</div>
                          <div class="text-sm mb-1">{{ offer.listingDetails?.town || 'Sengkang Town' }}</div>
                          <div class="text-2xl font-bold mb-1">S$ {{ offer.listingDetails?.price | number }}</div>
                          <div class="text-xs mb-2">Your offer: S$ {{ offer.offerPrice | number }}</div>
                          <div class="text-xs mb-1">Offer accepted on {{ offer.updatedAt | date:'dd MMM yyyy' }}</div>
                          <hr class="my-2" />
                          <div class="text-xs font-semibold mb-1">Sale Conditions</div>
                          <ul class="list-disc ml-5 text-xs mb-2">
                            @for (cond of offer.listingDetails?.saleCondition || ['Extension of Stay (3 months)', 'Vacant Possession']; track $index) {
                              <li>{{ cond }}</li>
                            }
                          </ul>
                          <img
                            [src]="'assets/dummy_prop.png'"
                            alt="Listing Photo"
                            class="rounded-lg w-full max-w-[200px] h-auto border"
                          />
                          <div (click)="getTransactionDetails(offer)" class="mt-4 bg-blue-600 text-white rounded-lg py-2 px-4 text-center cursor-pointer">
                            <div class="font-semibold">Block {{ offer.listingDetails?.blockNumber || '720a' }} Resale Overall Progress</div>
                            <div class="text-xs mt-1">Click to expand</div>
                          </div>

                          @if(buyerTransactionProgressDetails && selectedOffer?._id === offer._id){
                               <!-- Transaction Progress Checkpoints (shown when buyerTransactionProgressDetails exists) -->
                          <div class="mt-4 border rounded-lg p-4 bg-white">
                            <div class="flex justify-between items-center mb-3">
                              <div class="font-semibold text-lg">Transaction Progress</div>
                              <button (click)="closeTransactionDetails()" class="text-gray-500">
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>

                            <div class="flex flex-col md:flex-row justify-between mb-4 gap-3">
                              <app-buyer-info [username]="buyerUsername"/>
                              <app-seller-info [username]="sellerUsername"/>
                            </div>

                            <!-- User Info Section -->
                            <!-- <div class="flex flex-col md:flex-row justify-between mb-4 gap-3">
                              <div>
                                <div class="text-sm text-gray-600 mb-1">Buyer</div>
                                <app-buyer-info [username]="buyerUsername"/>
                              </div>
                              <div>
                                <div class="text-sm text-gray-600 mb-1">Seller</div>
                                <app-seller-info [username]="sellerUsername"/>
                              </div>
                            </div> -->

                            <!-- Checkpoints Grid - 2 columns on larger screens -->
                            <div class="grid grid-cols-1 gap-2">
                              <!-- Left Column -->
                              <div class="space-y-2">
                                <!-- 1. Price Acceptance -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.priceAcceptance?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">1. Price Acceptance</span>
                                  </div>
                                </div>

                                <!-- 2. Buyer Places Option Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.buyerOptionFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">2. Buyer Places Option Fee</span>
                                  </div>
                                </div>

                                <!-- 3. Seller Receives Option Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.sellerOptionFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">3. Seller Receives Option Fee</span>
                                  </div>
                                </div>

                                <!-- 4. Seller Issues OTP -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.sellerIssuesOTP?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">4. Seller Issues OTP - Day 0/21</span>
                                  </div>
                                </div>

                                <!-- 5. Buyer Obtains Valuation -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.buyerValuation?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">5. Buyer Obtains Valuation</span>
                                  </div>
                                </div>

                                <!-- 6. Buyer Obtains Loan Approval -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.buyerLoanApproval?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">6. Buyer Obtains Loan Approval</span>
                                  </div>
                                </div>

                                <!-- 7. Buyer Exercises Option -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.buyerExercisesOption?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">7. Buyer Exercises Option</span>
                                  </div>
                                </div>
                              </div>

                              <!-- Right Column -->
                              <div class="space-y-2">
                                <!-- 8. Buyer Deposits Exercise Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.buyerExerciseFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">8. Buyer Deposits Exercise Fee</span>
                                  </div>
                                </div>

                                <!-- 9. Seller Receives Exercise Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.sellerExerciseFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">9. Seller Receives Exercise Fee</span>
                                  </div>
                                </div>

                                <!-- 10. Submission to HDB Resale Portal -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.hdbPortalSubmission?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">10. Submission to HDB Resale Portal</span>
                                  </div>
                                </div>

                                <!-- 11. HDB Sets Appointment Date -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.hdbAppointment?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">11. HDB Sets Appointment Date</span>
                                  </div>
                                </div>

                                <!-- 12. Receipt of Approval Letter -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.approvalLetter?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">12. Receipt of Approval Letter</span>
                                  </div>
                                </div>

                                <!-- 13. Final Completion -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.finalCompletion?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">13. Final Completion</span>
                                  </div>
                                </div>

                                <!-- 14. Payment to Propped -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': buyerTransactionProgressDetails?.checkpoints?.paymentToPropped?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">14. Payment to Propped</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Completion Status -->
                            <div class="mt-4 text-center">
                              <div class="font-bold">
                                Completion: Steps {{ getCompletedCheckpointsCount() }}/14 ({{ buyerTransactionProgressDetails?.completionPercentage || 0 }}%)
                              </div>
                            </div>
                          </div>
                          }

                          <!-- Buyer Checklist Button -->
                          <div class="flex justify-between items-center mb-3">
                            <div class="font-semibold text-lg">Seller Checklist</div>
                            <button (click)="toggleSellerChecklist()" class="text-gray-500">
                              <mat-icon>close</mat-icon>
                            </button>
                          </div>

                          @if (showSellerChecklist && sellerTransactionProgressDetails?.sellerCheckpoints) {
                            <div class="mt-4 p-4 bg-white rounded-lg shadow">
                              <div class="mb-4">
                                <h3 class="text-lg font-bold text-center">Seller Checklist</h3>
                              </div>

                              <div class="space-y-2">
                                <!-- Find a Property -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.findProperty?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.findProperty?.title || 'Find a Property' }}</span>
                                  </div>
                                </div>

                                <!-- Submit Offer -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.submitOffer?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.submitOffer?.title || 'Submit Offer' }}</span>
                                  </div>
                                </div>

                                <!-- Verify Details in OTP -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.verifyDetailsInOTP?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.verifyDetailsInOTP?.title || 'Verify Details in OTP' }}</span>
                                  </div>
                                </div>

                                <!-- Pay Option Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.payOptionFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.payOptionFee?.title || 'Pay Option Fee' }}</span>
                                  </div>
                                </div>

                                <!-- Receive OTP -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.receiveOTP?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.receiveOTP?.title || 'Receive OTP' }}</span>
                                  </div>
                                </div>

                                <!-- Apply for HDB Valuation -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.applyForHDBValuation?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.applyForHDBValuation?.title || 'Apply for HDB Valuation' }}</span>
                                  </div>
                                </div>

                                <!-- Singpass Sign & Complete OTP -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.singpassSignAndCompleteOTP?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.singpassSignAndCompleteOTP?.title || 'Singpass Sign & Complete OTP' }}</span>
                                  </div>
                                </div>

                                <!-- Pay Exercise Fee -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.payExerciseFee?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.payExerciseFee?.title || 'Pay Exercise Fee' }}</span>
                                  </div>
                                </div>

                                <!-- Submit Documents to HDB -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.submitDocumentsToHDB?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.submitDocumentsToHDB?.title || 'Submit Documents to HDB' }}</span>
                                  </div>
                                </div>

                                <!-- Endorse Documents + Pay Fees -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.endorseDocumentsAndPayFees?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.endorseDocumentsAndPayFees?.title || 'Endorse Documents + Pay Fees' }}</span>
                                  </div>
                                </div>

                                <!-- Receive HDB Appointment Notice -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.receiveHDBAppointmentNotice?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.receiveHDBAppointmentNotice?.title || 'Receive HDB Appointment Notice' }}</span>
                                  </div>
                                </div>

                                <!-- Receive HDB Approval Letter -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.receiveHDBApprovalLetter?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.receiveHDBApprovalLetter?.title || 'Receive HDB Approval Letter' }}</span>
                                  </div>
                                </div>

                                <!-- Attend HDB Completion Appt -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.attendHDBCompletionAppointment?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.attendHDBCompletionAppointment?.title || 'Attend HDB Completion Appt' }}</span>
                                  </div>
                                </div>

                                <!-- Payment to Propped -->
                                <div class="flex items-center">
                                  <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-300': buyerTransactionProgressDetails?.buyerCheckpoints?.paymentToPropped?.completed}"></div>
                                  <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                    <span class="font-medium">{{ buyerTransactionProgressDetails?.buyerCheckpoints?.paymentToPropped?.title || 'Payment to Propped' }}</span>
                                  </div>
                                </div>
                              </div>

                              <!-- Completion Status -->
                              <div class="mt-4 text-center">
                                <div class="font-bold">
                                  Completion: Steps {{ getCompletedBuyerCheckpointsCount() }}/14
                                </div>
                              </div>
                            </div>
                          }
</div>
                      }
                    </div>

                  </div>
                }

                @if(sellerAcceptedOffers.length > 0){
                  <div class="mt-6">
                  <div class="font-semibold text-lg mb-2">You have {{ sellerAcceptedOffers.length }} accepted offer(s).</div>
                  <div class="mb-2">You are the Seller</div>
                  <div class="W-[350px] pr-1">
                    @for (offer of sellerAcceptedOffers; track $index) {
                      <div class="border rounded-lg p-3 bg-white max-w-lg mb-4 hover:shadow-md transition-shadow">
                        <div class="font-bold mb-1">{{ offer.listingDetails?.street || 'Compassvale Bow' }}</div>
                        <div class="font-semibold">Block {{ offer.listingDetails?.blockNumber || '266C' }}</div>
                        <div class="text-sm mb-1">{{ offer.listingDetails?.town || 'Sengkang Town' }}</div>
                        <div class="text-2xl font-bold mb-1">S$ {{ offer.listingDetails?.price | number }}</div>
                        <div class="text-xs mb-2">Your offer: S$ {{ offer.offerPrice | number }}</div>
                        <div class="text-xs mb-1">Offer accepted on {{ offer.updatedAt | date:'dd MMM yyyy' }}</div>
                        <hr class="my-2" />
                        <div class="text-xs font-semibold mb-1">Sale Conditions</div>
                        <ul class="list-disc ml-5 text-xs mb-2">
                          @for (cond of offer.listingDetails?.saleCondition || ['Extension of Stay (3 months)', 'Vacant Possession']; track $index) {
                            <li>{{ cond }}</li>
                          }
                        </ul>
                        <img
                          [src]="'assets/dummy_prop.png'"
                          alt="Listing Photo"
                          class="rounded-lg w-full max-w-[200px] h-auto border"
                        />
                        <div (click)="getSellerTransactionDetails(offer)" class="mt-4 bg-blue-600 text-white rounded-lg py-2 px-4 text-center cursor-pointer">
                          <div class="font-semibold">Block {{ offer.listingDetails?.blockNumber || '720a' }} Resale Overall Progress</div>
                          <div class="text-xs mt-1">Click to expand</div>
                        </div>

                        @if(sellerTransactionProgressDetails && selectedOffer?._id === offer._id){
                             <!-- Transaction Progress Checkpoints (shown when buyerTransactionProgressDetails exists) -->
                        <div class="mt-4 border rounded-lg p-4 bg-white">
                          <div class="flex justify-between items-center mb-3">
                            <div class="font-semibold text-lg">Transaction Progress</div>
                            <button (click)="closeTransactionDetails()" class="text-gray-500">
                              <mat-icon>close</mat-icon>
                            </button>
                          </div>

                          <div class="flex flex-col md:flex-row justify-between mb-4 gap-3">
                            <app-buyer-info [username]="buyerUsername"/>
                            <app-seller-info [username]="sellerUsername"/>
                          </div>

                          <!-- User Info Section -->
                          <!-- <div class="flex flex-col md:flex-row justify-between mb-4 gap-3">
                            <div>
                              <div class="text-sm text-gray-600 mb-1">Buyer</div>
                              <app-buyer-info [username]="buyerUsername"/>
                            </div>
                            <div>
                              <div class="text-sm text-gray-600 mb-1">Seller</div>
                              <app-seller-info [username]="sellerUsername"/>
                            </div>
                          </div> -->

                          <!-- Checkpoints Grid - 2 columns on larger screens -->
                          <div class="grid grid-cols-1 gap-2">
                            <!-- Left Column -->
                            <div class="space-y-2">
                              <!-- 1. Price Acceptance -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.priceAcceptance?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">1. Price Acceptance</span>
                                </div>
                              </div>

                              <!-- 2. Buyer Places Option Fee -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.buyerOptionFee?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">2. Buyer Places Option Fee</span>
                                </div>
                              </div>

                              <!-- 3. Seller Receives Option Fee -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.sellerOptionFee?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">3. Seller Receives Option Fee</span>
                                </div>
                              </div>

                              <!-- 4. Seller Issues OTP -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.sellerIssuesOTP?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">4. Seller Issues OTP - Day 0/21</span>
                                </div>
                              </div>

                              <!-- 5. Buyer Obtains Valuation -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.buyerValuation?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">5. Buyer Obtains Valuation</span>
                                </div>
                              </div>

                              <!-- 6. Buyer Obtains Loan Approval -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.buyerLoanApproval?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">6. Buyer Obtains Loan Approval</span>
                                </div>
                              </div>

                              <!-- 7. Buyer Exercises Option -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.buyerExercisesOption?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">7. Buyer Exercises Option</span>
                                </div>
                              </div>
                            </div>

                            <!-- Right Column -->
                            <div class="space-y-2">
                              <!-- 8. Buyer Deposits Exercise Fee -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.buyerExerciseFee?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">8. Buyer Deposits Exercise Fee</span>
                                </div>
                              </div>

                              <!-- 9. Seller Receives Exercise Fee -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.sellerExerciseFee?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">9. Seller Receives Exercise Fee</span>
                                </div>
                              </div>

                              <!-- 10. Submission to HDB Resale Portal -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.hdbPortalSubmission?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">10. Submission to HDB Resale Portal</span>
                                </div>
                              </div>

                              <!-- 11. HDB Sets Appointment Date -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.hdbAppointment?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">11. HDB Sets Appointment Date</span>
                                </div>
                              </div>

                              <!-- 12. Receipt of Approval Letter -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.approvalLetter?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">12. Receipt of Approval Letter</span>
                                </div>
                              </div>

                              <!-- 13. Final Completion -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.finalCompletion?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">13. Final Completion</span>
                                </div>
                              </div>

                              <!-- 14. Payment to Propped -->
                              <div class="flex items-center">
                                <div class="w-6 h-6 rounded border" [ngClass]="{'bg-blue-500': sellerTransactionProgressDetails?.checkpoints?.paymentToPropped?.completed}"></div>
                                <div class="ml-2 py-2 px-4 bg-blue-200 rounded-lg flex-grow">
                                  <span class="font-medium">14. Payment to Propped</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Completion Status -->
                          <div class="mt-4 text-center">
                            <div class="font-bold">
                              Completion: Steps {{ getCompletedCheckpointsCount() }}/14 ({{ sellerTransactionProgressDetails?.completionPercentage || 0 }}%)
                            </div>
                          </div>
                        </div>
                        }

                        <!-- Buyer Checklist Button -->
                        <!-- Seller Checklist Button -->
                        <div class="mt-2">
                          <button (click)="toggleSellerChecklist()" class="w-full bg-red-100 rounded-lg p-2 font-semibold">
                            {{ showSellerChecklist ? 'Hide Seller Checklist' : 'Go to Seller Checklist' }}
                          </button>
                        </div>
                      </div>
                      <!-- Seller Checklist -->
                      @if (showSellerChecklist && sellerTransactionProgressDetails?.sellerCheckpoints) {
                        <div class="mt-4 p-4 bg-white rounded-lg shadow">
                          <div class="flex justify-between items-center mb-3">
                            <div class="font-semibold text-lg">Seller Checklist</div>
                            <button (click)="toggleSellerChecklist()" class="text-gray-500">
                              <mat-icon>close</mat-icon>
                            </button>
                          </div>

                          <div class="space-y-2">
                            <!-- Find a Buyer -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.findBuyer?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.findBuyer?.title || 'Find a Buyer' }}</span>
                              </div>
                            </div>

                            <!-- Verify Details in OTP -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.verifyDetailsInOTP?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.verifyDetailsInOTP?.title || 'Verify Details in OTP' }}</span>
                              </div>
                            </div>

                            <!-- Receive Option Fee -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveOptionFee?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveOptionFee?.title || 'Receive Option Fee' }}</span>
                              </div>
                            </div>

                            <!-- Singpass Sign & Issue OTP -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.singpassSignAndIssueOTP?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.singpassSignAndIssueOTP?.title || 'Singpass Sign & Issue OTP' }}</span>
                              </div>
                            </div>

                            <!-- Receive HDB Valuation Result -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBValuationResult?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBValuationResult?.title || 'Receive HDB Valuation Result' }}</span>
                              </div>
                            </div>

                            <!-- Receive Completed OTP -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveCompletedOTP?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveCompletedOTP?.title || 'Receive Completed OTP' }}</span>
                              </div>
                            </div>

                            <!-- Receive Exercise Fee -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveExerciseFee?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveExerciseFee?.title || 'Receive Exercise Fee' }}</span>
                              </div>
                            </div>

                            <!-- Submit Documents to HDB -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.submitDocumentsToHDB?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.submitDocumentsToHDB?.title || 'Submit Documents to HDB' }}</span>
                              </div>
                            </div>

                            <!-- Endorse Documents + Pay Fees -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.endorseDocumentsAndPayFees?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.endorseDocumentsAndPayFees?.title || 'Endorse Documents + Pay Fees' }}</span>
                              </div>
                            </div>

                            <!-- Receive HDB Appointment Notice -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBAppointmentNotice?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBAppointmentNotice?.title || 'Receive HDB Appointment Notice' }}</span>
                              </div>
                            </div>

                            <!-- Receive HDB Approval Letter -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBApprovalLetter?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.receiveHDBApprovalLetter?.title || 'Receive HDB Approval Letter' }}</span>
                              </div>
                            </div>

                            <!-- Attend HDB Completion Appt -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.attendHDBCompletionAppointment?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.attendHDBCompletionAppointment?.title || 'Attend HDB Completion Appt' }}</span>
                              </div>
                            </div>

                            <!-- Payment to Propped -->
                            <div class="flex items-center">
                              <div class="w-6 h-6 rounded border" [ngClass]="{'bg-red-300': sellerTransactionProgressDetails?.sellerCheckpoints?.paymentToPropped?.completed}"></div>
                              <div class="ml-2 py-2 px-4 bg-red-200 rounded-lg flex-grow">
                                <span class="font-medium">{{ sellerTransactionProgressDetails?.sellerCheckpoints?.paymentToPropped?.title || 'Payment to Propped' }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- Completion Status -->
                          <div class="mt-4 text-center">
                            <div class="font-bold">
                              Completion: Steps {{ getCompletedSellerCheckpointsCount() }}/13
                            </div>
                          </div>
                        </div>
                        }
                      }
                  </div>
                  </div>
                }

                </div>
              </div>
            }
          </div>
        }
      </div>

    <!-- Chats Section -->
    <div class="bg-white rounded-lg shadow border p-0 flex flex-col">
      <div class="border-b px-4 py-2">
        <input
          type="text"
          class="w-full border rounded px-2 py-1"
          placeholder="Search within Chats"
          aria-label="Search within Chats"
        />
      </div>
      <div>
        @for (section of chatSections; track $index) {
          <button
            class="w-full flex items-center justify-between px-4 py-3 font-semibold border-b focus:outline-none"
            (click)="toggleChatSection($index)"
            [attr.aria-expanded]="section.open"
            [attr.aria-controls]="'chat-section-' + $index"
          >
            {{ section.label }}
            <mat-icon>{{ section.open ? 'remove' : 'add' }}</mat-icon>
          </button>
          @if (section.open) {
            <div
              [id]="'chat-section-' + $index"
              class="px-6 pb-2 text-gray-700 animate-fadeIn"
            >
              <ul>
                @for (user of section.users; track $index) {
                  <li class="py-1">{{ user }}</li>
                }
              </ul>
            </div>
          }
        }
      </div>
      <!-- Chat Footer -->
      <div class="mt-2 px-4 pb-4 flex flex-col gap-2">
        <input
          type="text"
          [(ngModel)]="chatInput"
          class="w-full border rounded px-2 py-1"
          placeholder="Type your message..."
          aria-label="Type your message"
          (keyup.enter)="sendMessage()"
        />
        <div class="flex gap-2">
          <button
            class="flex-1 bg-blue-100 text-blue-700 rounded shadow px-3 py-1 font-semibold"
            (click)="exportChat()"
            aria-label="Export Chat"
          >
            Export Chat
          </button>
          <button
            class="flex-1 bg-blue-600 text-white rounded shadow px-3 py-1 font-semibold"
            (click)="sendMessage()"
            aria-label="Send Message"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Animations (add to global styles if needed) -->
  <style>
    @media (max-width: 640px) {
      .user-dashboard-container {
        max-width: 100% !important;
        padding: 0.5rem !important;
      }
    }
    .animate-fadeIn {
      animation: fadeIn 0.2s;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  </style>
