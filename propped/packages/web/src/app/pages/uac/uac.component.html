<div class="flex flex-col md:flex-row justify-between items-center">
  <div class="mr-0 flex flex-row place-content-start items-center gap-3">
    <mat-icon style="font-size: 20px !important; width: 1em !important; height: 1em !important"
      >security</mat-icon
    >
    <div class="text-lg font-medium grow-1">UAC</div>
  </div>
  <div class="flex flex-row gap-4 items-center">
    <silver-search-bar (query)="search($event)" />
    <button (click)="update()" class="btn btn-primary btn-outline">
      <mat-icon>edit</mat-icon> Update
    </button>
  </div>
</div>

<div class="flex flex-row items-center mt-6 w-full">
  <div class="card bg-base-100 card-bordered w-full">
    <div class="card-body p-0">
      <div>
        <table class="table table-pin-rows table-zebra rounded-box">
          <thead>
            <tr class="text-lg font-medium">
              <td class="font-medium text-base-content/80">Permission Name</td>
              @for (role of roleNames; track role; let i = $index) {
                <td class="font-medium text-base-content/80">{{ role }}</td>
              }
            </tr>
          </thead>
          <tbody>
            @for (item of searchResult; track item; let i = $index) {
              <tr>
                <td class="font-medium text-base-content/80">
                  {{ permissionsTitleCase[item?.name] }}
                </td>
                @for (role of roleNames; track role; let i = $index) {
                  <td>
                    <!-- <mat-checkbox
                      [checked]="permissionMap[item?.name]?.includes(ROLES[role].id)"
                      (change)="onCheckChange(item?.name, ROLES[role].id, $event.checked)"
                    /> -->
                    <input
                      type="checkbox"
                      [checked]="permissionMap[item?.name]?.includes(ROLES[role].id)"
                      (change)="onCheckChange(item?.name, ROLES[role].id, $event.target.checked)"
                      class="checkbox checkbox-primary"
                    />
                  </td>
                }
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
