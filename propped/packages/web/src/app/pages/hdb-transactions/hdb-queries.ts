export const hdbQueries = [
  {
    title: 'Average Price per unit area in sqft by flat type',
    query: /* sql */ `
  SELECT
      flat_types.name AS flat_type,
      ROUND(AVG(price / (area * 10.764)), 2) AS avg_price_per_sqft
  FROM sales
  JOIN flat_types ON sales.flatTypeId = flat_types.id
  GROUP BY flat_types.name;
      `,
  },
  {
    title: 'Average Price per unit area in sqft by Year',
    query: /* sql */ `
  SELECT
      strftime('%Y', resaleAt, 'unixepoch') AS year,
      ROUND(AVG(price / (area * 10.764)), 2) AS avg_price_per_sqft
  FROM sales
  GROUP BY year;
      `,
  },
  {
    title: 'Average Price for Multi Generation Flats',
    query: /* sql */ `
  SELECT
      ROUND(AVG(price),2) AS avg_price
  FROM sales
  JOIN flat_models ON sales.flatModelId = flat_models.id
  WHERE flat_models.name = 'Multi Generation';
      `,
  },
  {
    title: 'Average Price by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      ROUND(AVG(price),2) AS avg_price
  FROM sales
  JOIN towns ON sales.townId = towns.id
  GROUP BY towns.name;
      `,
  },
  {
    title: 'Sales Count by Year',
    query: /* sql */ `
  SELECT
      strftime('%Y', resaleAt, 'unixepoch') AS year,
      COUNT(*) AS sales_count
  FROM sales
  GROUP BY year;
      `,
  },
  {
    title: 'Sales Count by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      COUNT(*) AS sales_count
  FROM sales
  JOIN towns ON sales.townId = towns.id
  GROUP BY towns.name;
      `,
  },
  {
    title: 'Average Lease Year by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      ROUND(AVG(leaseYear), 2) AS avg_lease_year
  FROM sales
  JOIN towns ON sales.townId = towns.id
  GROUP BY towns.name;
      `,
  },
  {
    title: 'Total Sales Volume by Block',
    query: /* sql */ `
  SELECT
      blocks.name AS block,
      SUM(price) AS total_sales_volume
  FROM sales
  JOIN blocks ON sales.blockId = blocks.id
  GROUP BY blocks.name;
      `,
  },
  {
    title: 'Average Floor Area by Flat Type',
    query: /* sql */ `
  SELECT
      flat_types.name AS flat_type,
      ROUND(AVG(area),2) AS avg_area
  FROM sales
  JOIN flat_types ON sales.flatTypeId = flat_types.id
  GROUP BY flat_types.name;
      `,
  },
  {
    title: 'Count of Sales by Flat Model',
    query: /* sql */ `
  SELECT
      flat_models.name AS flat_model,
      COUNT(*) AS sales_count
  FROM sales
  JOIN flat_models ON sales.flatModelId = flat_models.id
  GROUP BY flat_models.name;
      `,
  },
  {
    title: 'Minimum and Maximum Price by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      MIN(price) AS min_price,
      MAX(price) AS max_price
  FROM sales
  JOIN towns ON sales.townId = towns.id
  GROUP BY towns.name;
      `,
  },
  {
    title: 'Average Price by Storey Range',
    query: /* sql */ `
  SELECT
      (endStorey - startStorey + 1) AS storey_range,
      ROUND(AVG(price), 2) AS avg_price
  FROM sales
  GROUP BY storey_range;
      `,
  },
  {
    title: 'Sales Trend by Month',
    query: /* sql */ `
  SELECT
      strftime('%m-%Y', resaleAt, 'unixepoch') AS month_year,
      COUNT(*) AS sales_count
  FROM sales
  GROUP BY month_year;
      `,
  },
  {
    title: 'Average Price per Unit Area in m²',
    query: /* sql */ `
  SELECT
      ROUND(AVG(price / area), 2) AS avg_price_per_m2
  FROM sales;
      `,
  },
  {
    title: 'Count of Sales Before 1990',
    query: /* sql */ `
  SELECT
      COUNT(*) AS sales_count
  FROM sales
  WHERE leaseYear < 1990;
      `,
  },
  {
    title: 'Average Price for Resale after 2000 by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      ROUND(AVG(price),2) AS avg_price
  FROM sales
  JOIN towns ON sales.townId = towns.id
  WHERE leaseYear > 2000
  GROUP BY towns.name;
      `,
  },
  {
    title: 'Total Sales and Average Price by Flat Model',
    query: /* sql */ `
  SELECT
      flat_models.name AS flat_model,
      COUNT(*) AS sales_count,
      ROUND(AVG(price),2) AS avg_price
  FROM sales
  JOIN flat_models ON sales.flatModelId = flat_models.id
  GROUP BY flat_models.name;
      `,
  },
  {
    title: 'Sales Count by Block and Town',
    query: /* sql */ `
  SELECT
      blocks.name AS block,
      towns.name AS town,
      COUNT(*) AS sales_count
  FROM sales
  JOIN blocks ON sales.blockId = blocks.id
  JOIN towns ON sales.townId = towns.id
  GROUP BY blocks.name, towns.name;
      `,
  },
  {
    title: 'Average Remaining Lease (in years) by Town',
    query: /* sql */ `
  SELECT
      towns.name AS town,
      ROUND(AVG(remain)/12.0,2) AS avg_remaining_lease_years
  FROM sales
  JOIN towns ON sales.townId = towns.id
  GROUP BY towns.name;
      `,
  },
  {
    title: 'PSF Distribution by Flat Type',
    query: /* sql */ `
  SELECT
      flat_types.name AS flat_type,
      MIN(ROUND(price/(area*10.73692), 2)) AS min_psf,
      MAX(ROUND(price/(area*10.73692), 2)) AS max_psf,
      ROUND(AVG(ROUND(price/(area*10.73692), 2)), 2) AS avg_psf
  FROM sales
  JOIN flat_types ON sales.flatTypeId = flat_types.id
  GROUP BY flat_types.name
  ORDER BY avg_psf DESC;
      `,
  },
  {
    title: 'Sales Distribution by Street',
    query: /* sql */ `
  SELECT
      street_names.name AS street,
      COUNT(*) AS sales_count
  FROM sales
  JOIN street_names ON sales.streetId = street_names.id
  GROUP BY street_names.name;
      `,
  },
];
