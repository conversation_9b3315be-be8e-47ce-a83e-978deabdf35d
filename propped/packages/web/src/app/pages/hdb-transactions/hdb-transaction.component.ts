import { CommonModule } from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { apiRPC, injectController } from '@api/rpc';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { html } from '@lib/common/jsx-dom';
import { LngLatBounds, type Map as mapBoxMap } from 'mapbox-gl';
import {
  GeoJSONSourceComponent,
  LayerComponent,
  MapComponent,
  PopupComponent,
} from 'ngx-mapbox-gl';
import { firstValueFrom } from 'rxjs';
import { ChartViewerService } from '../../component/data-chart/data-chart.component';
import { StackedChartComponent } from '../../component/data-chart/stacked-chart.component';
import { MapsService } from '../../services/maps.service';
import { hdbQueries } from './hdb-queries';
import { HDBSalesService } from './hdb-transaction.service';

@Component({
  selector: 'app-hdb-sales',
  templateUrl: './hdb-transaction.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatSliderModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    SVGIconComponent,
    // DomInjectorComponent,
    StackedChartComponent,
    MapComponent,
    GeoJSONSourceComponent,
    LayerComponent,
    PopupComponent,
  ],
})
export class HDBTransactionComponent implements OnInit {
  readonly #salesService = inject(HDBSalesService);
  readonly #formModalService = inject(FormModalService);
  readonly #chartViewerService = inject(ChartViewerService);
  readonly #alertModalService = inject(AlertModalService);
  readonly #mapsService = inject(MapsService);
  readonly #snackBarService = inject(SnackBarService);
  readonly #hdbController = injectController(apiRPC.HDBController);
  forecastForm = new FormGroup({
    growthRate: new FormControl(6),
    forecastYears: new FormControl(3),
  });
  mapBoxMap?: mapBoxMap;
  filter = {} as any;
  config = { page: 1, limit: 10, sort: 'transaction_date', order: 'DESC', lastId: 0 };
  db: any;
  headers = [];
  data = [];
  meta: any = {};
  sortColumn = 'transaction_date';
  sortOrder = 'DESC';
  priceColumnIndex = -1;
  pageChange = false;
  initialLoad = true;
  hdbMapMarkers = {
    type: 'FeatureCollection',
    features: [],
  };
  bookmarkMapMarkers = {
    type: 'FeatureCollection',
    features: [],
  };
  style = 'mapbox://styles/mapbox/streets-v12';
  zoom = 12;

  // Bookmarked rows
  bookmarkedRows: any[] = [];
  quarterlyMedianPrices: any[] = [];
  showPropertyInformation = false;
  selectedMarkerCoordinates: [number, number] = [0, 0];
  selectedProperty: {
    blockNumber: string;
    town: string;
    flatType: string;
    price: string;
    psf: string;
    address: string;
    rowId: number;
  } | null = null;
  // Map related properties
  showMap = false;
  showBookmarkedOnMap = false;
  private googleMap: google.maps.Map | null = null;
  private mapElement: HTMLDivElement = html`<div style="width: 100%; height: 100%;"></div>`;
  private markers: google.maps.Marker[] = [];
  private selectedRowId: number | null = null;
  lat = 1.3521;
  lng = 103.8198;
  // Singapore center coordinates
  public readonly singaporeCenter = { lat: 1.3521, lng: 103.8198 };

  // Columns that can be sorted
  sortableColumns = ['transaction_date', 'price', 'psf', 'street_name'];

  // Transaction statistics
  transactionCount = 0;
  priceStats = {
    highest: 0,
    median: 0,
    lowest: 0,
  };
  psfStats = {
    highest: 0,
    median: 0,
    lowest: 0,
  };

  // Stats data
  private allFilteredData: any[] = [];
  private filtersChanged = true;
  private statsDataLoaded = false;
  showChartViewer = false;
  showBookmarkedChartViewer = false;
  monthlyMedianPrices: any[] = [];
  monthlyPSFValues: any[] = [];
  growthPercentage = new FormControl(0);
  growthYears = new FormControl(0);
  forecastPrice = 0;
  forecastPSF = 0;
  medianChartData: any = null;
  psfChartData: any = null;
  loading = true;
  statsLoading = true;

  // Bookmarked chart properties
  showBookmarkedChart = false;
  bookmarkedPriceChartData: any = null;
  bookmarkedPsfChartData: any = null;

  async ngOnInit(): Promise<void> {
    this.db = await this.#salesService.loadDB();
    await this.getData();
    this.growthPercentage.valueChanges.subscribe(() => this.calculateForecast());
    this.growthYears.valueChanges.subscribe(() => this.calculateForecast());
    // Initialize Google Maps API
    await this.#mapsService.loadGoogleMapsApi();
    this.loading = false;

    // Defer stats loading until after the UI has rendered
    setTimeout(() => {
      this.loadStatsData();
    }, 100);
  }

  parseToDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit' });
  }

  calculateForecast(): void {
    if (this.growthPercentage.value <= 0 || this.growthYears.value <= 0) return;
    // Get the latest median price from statistics
    const latestPrice = this.monthlyMedianPrices.at(this.monthlyMedianPrices.length - 1)?.[1];
    // alert(`LATEST PRICE ${latestPrice}`);
    const latestPSF = this.monthlyPSFValues.at(this.monthlyPSFValues.length - 1)?.[1];

    if (!latestPrice || !latestPSF) {
      console.warn('No price data available for forecast calculation');
      return;
    }

    // Get values from form controls
    const growthRate = this.growthPercentage.value;
    const years = this.growthYears.value;

    // Apply the formula: ((1+(growth_percentage/100))^number_of_years)*latest price
    const growthFactor = Math.pow(1 + growthRate / 100, years);

    // Calculate forecast price and PSF
    this.forecastPrice = Math.round(growthFactor * latestPrice);
    this.forecastPSF = Math.round(growthFactor * latestPSF);

    this.medianChartData = {
      heading: 'Monthly Median Price Trends',
      header: ['Date', 'Median Price'],
      data: [...this.monthlyMedianPrices, ['Forecast Price', this.forecastPrice]],
    };

    this.psfChartData = {
      heading: 'Monthly Median PSF Trends',
      header: ['Date', 'Median PSF'],
      data: [...this.monthlyPSFValues, ['Forecast Price', this.forecastPSF]],
    };

    console.log(
      `Forecast calculation: $${latestPrice.toLocaleString()} × (1 + ${growthRate}%)^${years} = $${this.forecastPrice.toLocaleString()}`,
    );
  }

  private async loadStatsData(): Promise<void> {
    this.statsLoading = true;
    try {
      await this.getStatsData();
      this.initialLoad = false;
    } catch (error) {
      console.error('Error loading stats data:', error);
      this.#snackBarService.error('Failed to load statistics data');
    } finally {
      this.statsLoading = false;
    }
  }

  async getData() {
    const res = this.#salesService.generateSqlQuery(this.db, this.filter, this.config);
    // console.log("GENERATED QUERY", res);
    this.headers = res.headers;
    this.data = res.data;
    this.meta = res.meta;
    // this.config.lastId = this.data.at(-1)?.[0] || 0;

    // Find the index of the price column
    this.priceColumnIndex = this.headers.findIndex((header) => header.toLowerCase() === 'price');

    try {
      // Get all filtered data for statistics calculation
      if (!this.pageChange && !this.initialLoad) {
        console.log('FUNCTION CALLED FOR GETTING STATS DATA');
        await this.loadStatsData();
        // this.calculateTransactionStats();
      }
      this.pageChange = false;
    } catch (error) {
      console.error('Error processing statistics:', error);
      // Reset statistics if there's an error
      this.transactionCount = this.meta.total;
      this.priceStats = { highest: 0, median: 0, lowest: 0 };
      this.psfStats = { highest: 0, median: 0, lowest: 0 };
    }

    // Refresh map markers if map is visible
    if (this.showMap && this.mapBoxMap) {
      setTimeout(async () => {
        await this.addHDBMapBoxMarkers();
      }, 100);
    }
  }

  openMedianPriceChart() {
    this.generateMonthlyMedianPrices();

    // Create chart data with or without bookmarked transactions
    const chartData = {
      heading: 'Monthly Median Price Trends',
      header: ['Date', 'Median Price'],
      data: this.monthlyMedianPrices,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[4])),
            }
          : undefined,
    };

    this.#chartViewerService.open(chartData);
  }

  openPSFChart() {
    this.generateMonthlyPSFValues();

    // Create chart data with or without bookmarked transactions
    const chartData = {
      heading: 'Monthly Average PSF Trends',
      header: ['Date', 'Average PSF'],
      data: this.monthlyPSFValues,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[5])),
            }
          : undefined,
    };

    this.#chartViewerService.open(chartData);
  }

  private generateMonthlyPSFValues() {
    if (this.allFilteredData.length === 0) return;

    // Group transactions by year and month
    const psfByYearMonth = new Map<string, number[]>();

    this.allFilteredData.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 2
      const dateStr = row[2];
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // PSF is at index 1
      const psf = row[1];
      if (!psfByYearMonth.has(yearMonth)) {
        psfByYearMonth.set(yearMonth, []);
      }

      psfByYearMonth.get(yearMonth)!.push(psf);
    });

    // Calculate average PSF for each month and sort chronologically
    const averagePSFValues = Array.from(psfByYearMonth.entries())
      .map(([yearMonth, psfValues]) => {
        const total = psfValues.reduce((sum, val) => sum + val, 0);
        const average = total / psfValues.length; // Calculate the average

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, average];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    this.monthlyPSFValues = averagePSFValues;
    this.psfChartData = {
      heading: 'Monthly Average PSF Trends',
      header: ['Date', 'Average PSF'],
      data: this.showChartViewer ? this.monthlyPSFValues : undefined,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[5])),
              labels: this.bookmarkedRows.map((row) => {
                console.log('BOOKMARKED ROW', row);
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[9];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };

    console.log('Monthly average PSF values', this.monthlyPSFValues);
  }

  private generateMonthlyMedianPrices() {
    console.log('All filtered data', this.allFilteredData);

    if (this.allFilteredData.length === 0) return;

    // Group transactions by year and month
    const pricesByYearMonth = new Map();

    this.allFilteredData.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 2
      const dateStr = row[2];
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // Price is at index 0
      const price = row[0];

      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
      }

      pricesByYearMonth.get(yearMonth).push(price);
    });

    // Calculate median for each month and sort chronologically
    const medianPrices = Array.from(pricesByYearMonth.entries())
      .map(([yearMonth, prices]) => {
        const sortedPrices = [...prices].sort((a, b) => a - b);
        const median = this.calculateMedian(sortedPrices);

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, median];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    this.monthlyMedianPrices = medianPrices;
    console.log('Monthly median prices', this.monthlyMedianPrices);
    this.medianChartData = {
      heading: 'Monthly Median Price',
      header: ['Date', 'Median Price'],
      data: this.showChartViewer ? this.monthlyMedianPrices : undefined,
      extraPoint:
        this.showBookmarkedChartViewer && this.bookmarkedRows.length > 0
          ? {
              label: 'Bookmarked Transactions',
              values: this.bookmarkedRows.map((row) => Number(row[4])),
              labels: this.bookmarkedRows.map((row) => {
                // Extract date from row[3] which contains the transaction date
                const dateStr = row[9];
                if (!dateStr) return '';

                // Extract year and month from the date string (format: YYYY-MM-DD)
                const [year, month] = dateStr.split('-');
                const date = new Date(Number(year), Number(month) - 1);
                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                });
              }),
            }
          : undefined,
    };
    // console.log("CHART DATA",this.medianChartData);
  }

  /**
   * Get all filtered data for statistics calculation
   */
  private async getStatsData(): Promise<void> {
    try {
      // Get all data matching the current filters for accurate statistics
      const statsData = await this.#salesService.getAllFilteredDataForStats(this.db, this.filter);
      const { data } = this.#salesService.getStatsQuery(this.db, this.filter);

      this.priceStats = { highest: data[0], lowest: data[1], median: data[4] };
      this.psfStats = { highest: data[2], lowest: data[3], median: data[5] };

      // Check if data is available
      if (statsData && statsData.data) {
        this.allFilteredData = statsData.data;
        this.generateMonthlyMedianPrices();
        this.generateMonthlyPSFValues();
        // Update transaction count from the total filtered data
        this.transactionCount = statsData.count || 0;
      } else {
        console.warn('No stats data available');
        this.allFilteredData = [];
        this.transactionCount = 0;
      }
    } catch (error) {
      console.error('Error getting stats data:', error);
      this.allFilteredData = [];
      this.transactionCount = 0;
    }
  }

  /**
   * Sort the table by the specified column
   * @param column Column name to sort by
   */
  sortByColumn(column: string) {
    // Check if column is sortable
    if (!this.sortableColumns.includes(column)) {
      return;
    }

    // Toggle sort order if already sorting by this column
    if (this.config.sort === column) {
      this.config.order = this.config.order === 'ASC' ? 'DESC' : 'ASC';
    } else {
      this.config.sort = column;
      this.config.order = 'DESC'; // Default to descending
    }

    this.sortColumn = this.config.sort;
    this.sortOrder = this.config.order;

    // Reset pagination
    this.config.page = 1;
    this.config.lastId = 0;

    // Fetch data with new sorting
    this.getData();
  }

  /**
   * Get the current sort direction for a column
   * @param column Column name
   * @returns Sort direction: 'asc', 'desc', or null if not sorted
   */
  getSortDirection(column: string): 'asc' | 'desc' | null {
    if (this.sortColumn === column) {
      return this.sortOrder === 'ASC' ? 'asc' : 'desc';
    }
    return null;
  }

  /**
   * Check if a column is sortable
   * @param column Column name
   * @returns True if the column is sortable
   */
  isSortableColumn(column: string): boolean {
    return this.sortableColumns.includes(column);
  }

  previous() {
    if (this.config.page === 1) return;
    this.config.page = this.config.page - 1;
    this.pageChange = true;
    this.getData();
  }

  next() {
    const totalPages = Math.ceil(this.meta.total / this.config.limit);
    if (this.config.page === totalPages) return;
    this.config.page = this.config.page + 1;
    this.pageChange = true;
    this.getData();
  }

  public async openFilterForm() {
    const form: SilverField[] = [
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Block',
        key: 'blockId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllBlocks(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Town',
        key: 'townId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllTowns(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Flat Type',
        key: 'flatTypeId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllFlatTypes(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'Street',
        key: 'streetId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllStreets(this.db),
      },
      {
        controlType: SilverFieldTypes.DATE_RANGE,
        label: 'Time period ',
        key: 'timePeriod',
        value: { from: null, to: null },
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum PSF',
        placeholder: 'Minimum PSF',
        type: 'number',
        key: 'minimumPsf',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum PSF',
        placeholder: 'Maximum PSF',
        type: 'number',
        key: 'maximumPsf',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Floor Level',
        placeholder: 'Minimum Floor Level',
        type: 'number',
        key: 'minimumStorey',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Floor Level',
        placeholder: 'Maximum Floor Level',
        type: 'number',
        key: 'maximumStorey',
        value: '',
        valid: { required: false },
      },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter', form });
    if (!result.action) return;
    this.filter = result.value;
    this.config.page = 1;
    this.config.lastId = 0;
    this.getData();
  }

  viewChart(query: { title: string; query: string }) {
    const res = this.db.exec(query.query);
    this.#chartViewerService.open({
      heading: query.title,
      header: res[0].columns,
      data: res[0].values,
    });
  }

  async chooseFAQ() {
    const ref = this.#alertModalService.openModal(
      {
        heading: 'FAQ Queries For Graph',
        domClass: 'w-full',
        dom: async (root, onClose) => {
          const dom = html`<div class="flex flex-col gap-4">
            <ul class="menu bg-base-200 rounded-box gap-4">
              ${hdbQueries.map(
                (query) => html` <li><a onClick=${() => ref.close(query)}>${query.title}</a></li>`,
              )}
            </ul>
          </div>`;
          root.appendChild(dom);
          await onClose;
          dom.remove();
        },
        noBtn: true,
      },
      { width: '600px', height: '600px' },
    );
    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    if (!result) return;
    this.viewChart(result);
  }

  /**
   * Toggle bookmark for a row
   * @param row The row to bookmark/unbookmark
   * @param event The checkbox change event
   */
  toggleBookmark(row: any, event: Event): void {
    event.stopPropagation(); // Prevent row selection when clicking the checkbox

    if (this.isBookmarked(row)) {
      // Remove from bookmarks
      this.removeBookmark(row);
    } else {
      // Add to bookmarks
      this.bookmarkRow(row);
    }

    // If map is visible and showing bookmarked items with different color, refresh markers
    if (this.showMap && this.showBookmarkedOnMap) {
      this.addHDBMapBoxMarkers();
    }
  }

  /**
   * Check if a row is bookmarked
   * @param row The row to check
   * @returns True if the row is bookmarked
   */
  isBookmarked(row: any): boolean {
    return this.bookmarkedRows.some(
      (bookmarkedRow) => bookmarkedRow[0] === row[0], // Compare by ID (first column)
    );
  }

  /**
   * Add a row to bookmarks
   * @param row The row to bookmark
   */
  bookmarkRow(row: any[]): void {
    if (!this.isBookmarked(row)) {
      this.bookmarkedRows.push(row);
      localStorage.setItem('bookmarkedHDBRows', JSON.stringify(this.bookmarkedRows));

      // Regenerate bookmarked charts if they're currently visible
      if (this.showBookmarkedChart) {
        this.generateBookmarkedCharts();
      }

      // If map is visible and showing bookmarked items with different color, refresh markers
      if (this.showMap) {
        this.addHDBMapBoxMarkers();
      }
    }
  }

  /**
   * Remove a row from bookmarks
   * @param row The row to remove from bookmarks
   * @param event The button click event (optional)
   */
  removeBookmark(row: any[], event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    const index = this.bookmarkedRows.findIndex((r) => r[0] === row[0]);
    if (index !== -1) {
      this.bookmarkedRows.splice(index, 1);
      localStorage.setItem('bookmarkedHDBRows', JSON.stringify(this.bookmarkedRows));

      // Regenerate bookmarked charts if they're currently visible
      if (this.showBookmarkedChart) {
        this.generateBookmarkedCharts();
      }

      // If map is visible and showing bookmarked items with different color, refresh markers
      // if (this.showMap && this.showBookmarkedOnMap) {
      //   this.addHDBMarkers();
      // }
      if (this.showMap) {
        this.addHDBMapBoxMarkers();
      }
    }
  }

  /**
   * Remove all bookmarks
   */
  removeAllBookmarks(): void {
    this.bookmarkedRows = [];
    localStorage.removeItem('bookmarkedHDBRows');

    // Hide the bookmarked chart since there are no bookmarks
    this.showBookmarkedChart = false;
    this.bookmarkedPriceChartData = null;
    this.bookmarkedPsfChartData = null;

    // If map is visible and showing bookmarked items with different color, refresh markers
    // if (this.showMap && this.showBookmarkedOnMap) {
    //   this.addHDBMarkers();
    // }
    if (this.showMap) {
      this.addHDBMapBoxMarkers();
    }
  }

  /**
   * Toggle showing bookmarked items on map with different color
   */
  toggleBookmarkedOnMap(): void {
    this.showBookmarkedOnMap = !this.showBookmarkedOnMap;

    // If map is visible, refresh markers to show the new colors
    // if (this.showMap && this.googleMap) {
    //   this.addHDBMarkers();
    // }
    if (!this.showBookmarkedOnMap) {
      this.bookmarkMapMarkers = {
        type: 'FeatureCollection',
        features: [],
      };
    }
    if (this.showMap && this.mapBoxMap) {
      this.addHDBMapBoxMarkers();
    }
  }
  onMapLoad(map: mapBoxMap) {
    this.mapBoxMap = map;
    console.log('MAP LOADED', this.mapBoxMap);
    if (this.data && this.data.length > 0) {
      this.addHDBMapBoxMarkers();
    }
  }
  /**
   * Toggle map visibility
   */
  async toggleMap(): Promise<void> {
    this.showMap = !this.showMap;

    if (this.showMap) {
      // Initialize map if not already done
      if (!this.googleMap) {
        this.googleMap = new google.maps.Map(this.mapElement, {
          center: this.singaporeCenter,
          zoom: 12,
          mapTypeControl: true,
        });
      }

      await this.addHDBMarkers();
    } else {
      // Clear markers when hiding map
      this.clearMarkers();
    }
  }

  async toggleMapBoxMap(): Promise<void> {
    this.showMap = !this.showMap;

    if (this.showMap) {
      await this.addHDBMapBoxMarkers();
    } else {
      // Clear markers when hiding map
      this.clearMarkers();
    }
  }
  fitMapToMarkers(coordsArray: number[][]): void {
    const bounds = new LngLatBounds();

    coordsArray.forEach((coord) => {
      bounds.extend(coord as [number, number]);
    });

    const center = bounds.getCenter();
    this.lng = center.lng;
    this.lat = center.lat;
    this.zoom = 12;
  }
  async addHDBMapBoxMarkers(): Promise<void> {
    if (!this.data || this.data.length === 0) return;

    // Clear existing markers
    this.hdbMapMarkers.features = [];
    this.bookmarkMapMarkers.features = [];

    try {
      // Prepare transaction data for API call
      const transactions = this.data.map((row) => ({
        blockNo: row[2], // Block number
        streetName: row[7], // Street name
      }));

      const bookmarkTransactions = this.bookmarkedRows.map((row) => ({
        blockNo: row[2], // Block number
        streetName: row[7], // Street name
      }));

      // Get coordinates from API using the controller directly
      const response = await this.#hdbController.getHDBCoordinates({ transactions });

      // Only fetch bookmarked coordinates if we have bookmarks and showBookmarkedOnMap is true
      let bookmarkResponse: any = { success: false, locations: [] };
      if (this.bookmarkedRows.length > 0) {
        bookmarkResponse = await this.#hdbController.getHDBCoordinates({
          transactions: bookmarkTransactions,
        });
      }

      if (response.success && response.locations.length > 0) {
        // Process regular markers
        response.locations.forEach((location, index) => {
          const position = {
            lat: location.latitude,
            lng: location.longitude,
          };

          // Get the corresponding data row
          const row =
            this.data.find((r) => r[2] === location.block_no && r[7] === location.street_name) ||
            this.data[index];

          const rowId = row[0]; // Assuming row ID is at index 0
          const isBookmarked = this.bookmarkedRows.some((br) => br[0] === rowId);
          const isSelected = this.selectedRowId === rowId;

          // If showBookmarkedOnMap is true, we'll separate bookmarked markers
          // Otherwise, we'll just color them differently in the same layer
          if (isBookmarked && this.showBookmarkedOnMap) {
            // Skip adding to hdbMapMarkers as we'll add it to bookmarkMapMarkers
            // This prevents duplicate markers
            return;
          }

          // For non-bookmarked markers or when not showing bookmarked separately
          let markerColor = '#008000'; // Green for regular markers

          // If not showing bookmarked separately but this is a bookmarked marker
          if (isBookmarked && !this.showBookmarkedOnMap) {
            markerColor = '#e53935'; // Red for bookmarked
          } else if (isSelected) {
            markerColor = '#9c27b0'; // Purple for selected
          }

          this.hdbMapMarkers.features.push({
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [position.lng, position.lat],
            },
            properties: {
              title: `${row[2]} ${row[7]}`, // Block number + Street name
              color: markerColor,
              id: rowId,
              isBookmarked,
              isSelected,
              shape: 'square',
            },
          });
        });

        // Force update of regular markers
        this.hdbMapMarkers = { ...this.hdbMapMarkers };

        // Process bookmarked markers if showing them separately
        if (
          this.showBookmarkedOnMap &&
          bookmarkResponse.success &&
          bookmarkResponse.locations.length > 0
        ) {
          console.log('BOOKMARK RESPONSE ,SHOW BOOKMARKED ON MAP', bookmarkResponse);
          bookmarkResponse.locations.forEach((location) => {
            // Find the corresponding bookmarked row
            const bookmarkedRow = this.bookmarkedRows.find(
              (br) => br[2] === location.blockNo && br[7] === location.streetName,
            );
            console.log('BOOKMARKED ROW', bookmarkedRow);
            if (bookmarkedRow) {
              const position = {
                lat: location.latitude,
                lng: location.longitude,
              };

              const rowId = bookmarkedRow[0];
              const isSelected = this.selectedRowId === rowId;

              // Always red for bookmarked markers
              const markerColor = isSelected ? '#9c27b0' : '#e53935';

              this.bookmarkMapMarkers.features.push({
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [position.lng, position.lat],
                },
                properties: {
                  title: `${bookmarkedRow[2]} ${bookmarkedRow[7]}`,
                  color: markerColor,
                  id: rowId,
                  isBookmarked: true,
                  isSelected,
                },
              });
            }
          });

          // Force update of bookmarked markers
          this.bookmarkMapMarkers = { ...this.bookmarkMapMarkers };
        }

        // Fit map to all markers (both regular and bookmarked)
        const allCoordinates = [
          ...this.hdbMapMarkers.features.map((feature) => feature.geometry.coordinates),
          ...this.bookmarkMapMarkers.features.map((feature) => feature.geometry.coordinates),
        ];

        if (allCoordinates.length > 0) {
          this.fitMapToMarkers(allCoordinates);
        }
      }
    } catch (error) {
      console.error('Error adding markers:', error);
    }
  }
  /**
   * Initialize Google Map
   */
  private initializeMap(): void {
    if (!google || !google.maps) {
      console.error('Google Maps API not loaded');
      return;
    }

    const { Map: GoogleMap, MapTypeId } = google.maps;
    this.googleMap = new GoogleMap(this.mapElement, {
      zoom: 11,
      center: this.singaporeCenter,
      mapTypeId: MapTypeId.ROADMAP,
    });
  }

  /**
   * Add markers for HDB transactions on the map
   */
  private async addHDBMarkers(): Promise<void> {
    if (!this.googleMap || !this.data || this.data.length === 0) return;

    // Clear existing markers
    this.clearMarkers();

    try {
      // Prepare transaction data for API call
      const transactions = this.data.map((row) => ({
        blockNo: row[2], // Block number
        streetName: row[7], // Street name
      }));

      // Get coordinates from API using the controller directly
      const response = await this.#hdbController.getHDBCoordinates({ transactions });

      if (response.success && response.locations.length > 0) {
        const { Marker, InfoWindow } = google.maps;
        const bounds = new google.maps.LatLngBounds();

        // Add a marker for each location
        response.locations.forEach((location, index) => {
          const position = {
            lat: location.latitude,
            lng: location.longitude,
          };

          // Get the corresponding data row
          const row =
            this.data.find((r) => r[2] === location.block_no && r[7] === location.street_name) ||
            this.data[index];

          // Determine if this row is bookmarked or selected
          const isRowBookmarked = this.isBookmarked(row);
          const isSelected = row[0] === this.selectedRowId;

          let markerIcon;

          if (isRowBookmarked) {
            // Red diamond for bookmarked transactions
            markerIcon = {
              path: 'M 0 -10 L 10 0 L 0 10 L -10 0 Z',
              fillColor: '#FF0000',
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          } else if (isSelected) {
            // Purple square for selected transactions
            markerIcon = {
              path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
              fillColor: '#800080', // Purple
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          } else {
            // Green square for regular transactions
            markerIcon = {
              path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
              fillColor: '#008000',
              fillOpacity: 1,
              strokeWeight: 1,
              strokeColor: '#FFFFFF',
              scale: 1,
            };
          }
          // Create marker
          const marker = new Marker({
            position,
            map: this.googleMap,
            title: `${location.block_no} ${location.street_name}`,
            animation: google.maps.Animation.DROP,
            icon: markerIcon,
          });

          console.log('LOCATION', location);
          // Add info window with property details
          const infoWindow = new InfoWindow({
            content: `
              <div style="padding: 5px; max-width: 300px;overflow: hidden">
                <h3 style="font-weight: bold; margin-bottom: 5px;">Block ${location.blockNo}</h3>
                <p style="margin-bottom: 5px;">TOWN: ${row[1]}</p>
                <p style="margin-bottom: 5px;">Flat Type: ${row[3]}</p>
                <p style="margin-bottom: 5px;">Price: $${row[4].toLocaleString()}</p>
                <p style="margin-bottom: 5px;">PSF: $${row[5]}</p>
                <p style="margin-bottom: 0; font-size: 0.8em;">${location.address}</p>
                <button style="background-color: black; color: #FFFFFF; padding: 5px 10px; border: none; border-radius: 5px; cursor: pointer;margin-top: 10px;">Property Details</button>
              </div>
            `,
          });

          marker.addListener('click', () => {
            infoWindow.open(this.googleMap, marker);
            // Update the selected row ID
            this.updateMarkerColors();
            // Scroll the row into view
            const rowElement = document.querySelector(`[data-row-id="${row[0]}"]`);
            if (rowElement) {
              rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          });

          this.markers.push(marker);
          bounds.extend(position);
        });

        // Fit map to bounds if we have markers
        if (this.markers.length > 0) {
          this.googleMap.fitBounds(bounds);

          // If we only have one marker, zoom in a bit
          if (this.markers.length === 1) {
            this.googleMap.setZoom(15);
          }
        }
      } else {
        console.warn('No location data returned from API, using fallback markers');

        // Fallback to random positions if API fails
        this.addFallbackMarkers();
      }
    } catch (error) {
      console.error('Error fetching HDB coordinates:', error.message || error);

      // Fallback to random positions if API fails
      this.addFallbackMarkers();
    }
  }

  /**
   * Add fallback markers with random positions (used if API fails)
   */
  private addFallbackMarkers(): void {
    if (!this.googleMap || !this.data || this.data.length === 0) return;

    const { Marker, InfoWindow } = google.maps;

    // Add a marker for each HDB transaction with random positions
    this.data.forEach((row) => {
      const address = `${row[2]} ${row[7]}, ${row[1]}, Singapore`;

      // Create a marker at a random position around Singapore
      const lat = this.singaporeCenter.lat + (Math.random() - 0.5) * 0.1;
      const lng = this.singaporeCenter.lng + (Math.random() - 0.5) * 0.1;

      // Determine if this row is bookmarked
      const isRowBookmarked = this.isBookmarked(row);

      // Set marker icon color based on bookmark status and showBookmarkedOnMap setting
      const markerIcon = {
        path: google.maps.SymbolPath.CIRCLE,
        fillOpacity: 1,
        fillColor: this.showBookmarkedOnMap && isRowBookmarked ? '#0000FF' : '#FF0000',
        strokeWeight: 1,
        strokeColor: '#FFFFFF',
        scale: 8,
      };

      const marker = new Marker({
        position: { lat, lng },
        map: this.googleMap,
        title: address,
        icon: markerIcon,
      });

      // Add info window
      const infoWindow = new InfoWindow({
        content: `
          <div>
            <h3>Block ${row[2]}</h3>
            <p>${row[7]}, ${row[1]}</p>
            <p>Flat Type: ${row[3]}</p>
            <p>Price: $${row[4]}</p>
            <p>PSF: $${row[5]}</p>
          </div>
        `,
      });

      marker.addListener('click', () => {
        infoWindow.open(this.googleMap, marker);
      });

      this.markers.push(marker);
    });
  }

  /**
   * Clear all markers from the map
   */
  private clearMarkers(): void {
    this.markers.forEach((marker) => marker.setMap(null));
    this.markers = [];
  }

  selectRow(row: any) {
    // Toggle selection - if same row is clicked, deselect it
    this.selectedRowId = this.selectedRowId === row[0] ? null : row[0];
    if (this.showMap) {
      // this.updateMarkerColors();
      this.updateMapMarkerColors();
    }
  }

  /**
   * Check if a row should be highlighted (pink background)
   * Only highlight when the row is selected AND the map is open
   */
  isRowHighlighted(row: any): boolean {
    // Only highlight the row if it's selected AND the map is open
    return row[0] === this.selectedRowId && this.showMap;
  }

  /**
   * Update marker colors
   */
  private updateMarkerColors(): void {
    if (!this.markers || !this.data) return;

    this.markers.forEach((marker, index) => {
      const row = this.data[index];
      const isRowBookmarked = this.isBookmarked(row);
      const isSelected = row[0] === this.selectedRowId;

      let markerIcon;

      if (isRowBookmarked) {
        // Red diamond for bookmarked transactions
        markerIcon = {
          path: 'M 0 -10 L 10 0 L 0 10 L -10 0 Z',
          fillColor: '#FF0000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else if (isSelected) {
        // Purple square for selected transactions
        markerIcon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#800080', // Purple
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      } else {
        // Green square for regular transactions
        markerIcon = {
          path: 'M -10 -10 L 10 -10 L 10 10 L -10 10 Z',
          fillColor: '#008000',
          fillOpacity: 1,
          strokeWeight: 1,
          strokeColor: '#FFFFFF',
          scale: 1,
        };
      }

      marker.setIcon(markerIcon);
    });
  }

  updateMapMarkerColors() {
    if (!this.mapBoxMap) return;

    // Update regular markers
    const updatedFeatures = this.hdbMapMarkers.features.map((feature) => {
      const featureId = feature.properties.id;
      const isBookmarked = feature.properties.isBookmarked;

      // If this feature's ID matches the selected row ID, make it purple
      if (featureId === this.selectedRowId) {
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#9c27b0', // Purple for selected
            isSelected: true,
          },
        };
      } else if (isBookmarked && !this.showBookmarkedOnMap) {
        // If it's bookmarked but not selected, keep it red
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#e53935', // Red for bookmarked
            isSelected: false,
          },
        };
      } else {
        // Regular marker, not selected, not bookmarked
        return {
          ...feature,
          properties: {
            ...feature.properties,
            color: '#008000', // Green for regular
            isSelected: false,
          },
        };
      }
    });

    // Update the markers collection with the new colors
    this.hdbMapMarkers = {
      type: 'FeatureCollection',
      features: updatedFeatures,
    };

    // If showing bookmarked markers separately, update those too
    if (this.showBookmarkedOnMap && this.bookmarkMapMarkers.features.length > 0) {
      const updatedBookmarkFeatures = this.bookmarkMapMarkers.features.map((feature) => {
        const featureId = feature.properties.id;

        // If this feature's ID matches the selected row ID, make it purple
        if (featureId === this.selectedRowId) {
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#9c27b0', // Purple for selected
              isSelected: true,
            },
          };
        } else {
          // Bookmarked but not selected
          return {
            ...feature,
            properties: {
              ...feature.properties,
              color: '#e53935', // Red for bookmarked
              isSelected: false,
            },
          };
        }
      });

      // Update the bookmarked markers collection
      this.bookmarkMapMarkers = {
        type: 'FeatureCollection',
        features: updatedBookmarkFeatures,
      };
    }
  }

  /**
   * Toggle chart viewer visibility and ensure data is available
   */
  toggleChartViewer(): void {
    this.showChartViewer = !this.showChartViewer;

    // Generate chart data if needed
    if (this.showChartViewer) {
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();
    }
  }

  /**
   * Toggle bookmarked chart viewer visibility
   */
  toggleBookmarkedChartViewer(): void {
    this.showBookmarkedChartViewer = !this.showBookmarkedChartViewer;

    if (this.showBookmarkedChartViewer) {
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();

      // Show the chart viewer if it's not already visible
      this.showChartViewer = true;
    } else {
      // Regenerate charts without bookmarked data
      this.generateMonthlyMedianPrices();
      this.generateMonthlyPSFValues();
    }
  }

  /**
   * Calculate transaction statistics (highest, median, lowest price and PSF)
   */
  private calculateTransactionStats(): void {
    if (!this.allFilteredData || this.allFilteredData.length === 0) {
      // Reset stats if no data
      this.priceStats = { highest: 0, median: 0, lowest: 0 };
      this.psfStats = { highest: 0, median: 0, lowest: 0 };
      return;
    }

    // Extract price and PSF values from all filtered data
    // The getAllFilteredDataForStats method returns [price, psf] for each row
    const prices = this.allFilteredData
      .map((row) => Number(row[0]))
      .filter((price) => !isNaN(price));
    const psfValues = this.allFilteredData
      .map((row) => Number(row[1]))
      .filter((psf) => !isNaN(psf));

    // Calculate price statistics using loops instead of spread operator
    // to avoid "Maximum call stack size exceeded" error with large arrays
    let highestPrice = prices.length > 0 ? prices[0] : 0;
    let lowestPrice = prices.length > 0 ? prices[0] : 0;

    for (let i = 0; i < prices.length; i++) {
      if (prices[i] > highestPrice) highestPrice = prices[i];
      if (prices[i] < lowestPrice) lowestPrice = prices[i];
    }

    // Calculate PSF statistics
    let highestPsf = psfValues.length > 0 ? psfValues[0] : 0;
    let lowestPsf = psfValues.length > 0 ? psfValues[0] : 0;

    for (let i = 0; i < psfValues.length; i++) {
      if (psfValues[i] > highestPsf) highestPsf = psfValues[i];
      if (psfValues[i] < lowestPsf) lowestPsf = psfValues[i];
    }

    // Sort values for median calculation
    const sortedPrices = [...prices].sort((a, b) => a - b);
    const sortedPsf = [...psfValues].sort((a, b) => a - b);

    // Calculate price statistics
    this.priceStats = {
      highest: highestPrice,
      median: this.calculateMedian(sortedPrices),
      lowest: lowestPrice,
    };

    // Calculate PSF statistics
    this.psfStats = {
      highest: highestPsf,
      median: this.calculateMedian(sortedPsf),
      lowest: lowestPsf,
    };
  }

  /**
   * Calculate the median value of a sorted array
   * @param sortedArray Sorted array of numbers
   * @returns Median value
   */
  private calculateMedian(sortedArray: number[]): number {
    if (sortedArray.length === 0) return 0;

    const mid = Math.floor(sortedArray.length / 2);

    if (sortedArray.length % 2 === 0) {
      // Even number of elements, average the middle two
      return (sortedArray[mid - 1] + sortedArray[mid]) / 2;
    } else {
      // Odd number of elements, return the middle one
      return sortedArray[mid];
    }
  }

  /**
   * Format a number as currency with a dollar sign
   * @param value Number to format
   * @returns Formatted string
   */
  formatCurrency(value: number): string {
    return `$${value.toLocaleString()}`;
  }

  /**
   * Toggle the display of the bookmarked transactions chart
   */
  toggleBookmarkedChart(): void {
    this.showBookmarkedChart = !this.showBookmarkedChart;

    if (this.showBookmarkedChart && this.bookmarkedRows.length > 0) {
      this.generateBookmarkedCharts();
    }
  }

  /**
   * Generate charts specifically for bookmarked transactions
   */
  private generateBookmarkedCharts(): void {
    if (this.bookmarkedRows.length === 0) return;

    // Group bookmarked transactions by year and month for price chart
    const pricesByYearMonth = new Map<string, number[]>();
    const psfByYearMonth = new Map<string, number[]>();

    this.bookmarkedRows.forEach((row) => {
      // Date is in format "YYYY-MM-DD" at index 9
      const dateStr = row[9];
      if (!dateStr) return;

      // Extract year and month from the date string
      const [year, month] = dateStr.split('-');
      const yearMonth = `${year}-${month}`;

      // Price is at index 4
      const price = Number(row[4]);
      if (!pricesByYearMonth.has(yearMonth)) {
        pricesByYearMonth.set(yearMonth, []);
      }
      pricesByYearMonth.get(yearMonth)!.push(price);

      // PSF is at index 5
      const psf = Number(row[5]);
      if (!psfByYearMonth.has(yearMonth)) {
        psfByYearMonth.set(yearMonth, []);
      }
      psfByYearMonth.get(yearMonth)!.push(psf);
    });

    // Calculate median price for each month and sort chronologically
    const medianPrices = Array.from(pricesByYearMonth.entries())
      .map(([yearMonth, prices]) => {
        const sortedPrices = [...prices].sort((a, b) => a - b);
        const median = this.calculateMedian(sortedPrices);

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, median];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    // Calculate average PSF for each month and sort chronologically
    const averagePSFValues = Array.from(psfByYearMonth.entries())
      .map(([yearMonth, psfValues]) => {
        const total = psfValues.reduce((sum, val) => sum + val, 0);
        const average = total / psfValues.length; // Calculate the average

        // Format the date for display (convert YYYY-MM to MMM YYYY)
        const [year, month] = yearMonth.split('-');
        const date = new Date(Number(year), Number(month) - 1);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
        });

        return [formattedDate, average];
      })
      .sort((a, b) => {
        // Sort by date (convert MMM YYYY back to Date objects for comparison)
        const dateA = new Date(a[0]);
        const dateB = new Date(b[0]);
        return dateA.getTime() - dateB.getTime();
      });

    // Prepare data for price chart
    this.bookmarkedPriceChartData = {
      heading: 'Bookmarked Transactions - Monthly Median Price',
      header: ['Date', 'Median Price'],
      data: medianPrices,
    };

    // Prepare data for PSF chart
    this.bookmarkedPsfChartData = {
      heading: 'Bookmarked Transactions - Monthly Average PSF',
      header: ['Date', 'Average PSF'],
      data: averagePSFValues,
    };
  }
  /**
   * Show property information popup when a marker is clicked
   */
  showPropertyInfo(event: any): void {
    // Get the clicked feature
    console.log('View property details', event);
    const feature = event.features[0];
    if (!feature) return;

    // Get the feature properties and coordinates
    const properties = feature.properties;
    const coordinates = feature.geometry.coordinates.slice() as [number, number];

    // Find the corresponding data row
    const rowId = properties.id;
    const row =
      this.data.find((r) => r[0] === rowId) || this.bookmarkedRows.find((r) => r[0] === rowId);

    if (!row) return;

    // Set the selected property information
    this.selectedProperty = {
      blockNumber: row[2], // Block number
      town: row[1], // Town
      flatType: row[3], // Flat type
      price: Number(row[4]).toLocaleString(), // Price
      psf: row[5], // PSF
      address: `${row[2]} ${row[7]}`, // Block + Street name
      rowId: row[0], // Row ID
    };

    // Set the coordinates and show the popup
    this.selectedMarkerCoordinates = coordinates;
    this.showPropertyInformation = true;

    // Highlight the selected row in the table
    this.selectRow(row);
  }
}
