<div class="p-3 flex flex-col md:flex-row place-content-start items-center">
  <div class="mr-0 flex flex-row place-content-start items-center">
    <app-svg-icon icon="local:hdb" style="color: #d71514" />
    <div class="p-3 text-2xl grow-1">HDB Sales</div>
  </div>
  <div class="flex flex-col md:flex-row gap-2 md:ml-auto w-4/5 md:w-auto items-center">
    <button (click)="openFilterForm()" class="btn btn-primary">Filter</button>
    <button (click)="chooseFAQ()" class="btn btn-primary">FAQ Graph</button>
  </div>
</div>
<table class="min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-600 relative">
  <thead class="sticky top-0 bg-gray-100 dark:bg-gray-700">
    <tr>
      @for (item of headers; track item) {
        <th
          scope="col"
          class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400"
        >
          {{ item }}
        </th>
      }
    </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
    @for (rows of data; track $index) {
      <tr class="hover:bg-gray-100 dark:hover:bg-gray-700">
        @for (item of rows; track $index) {
          <td class="p-4 text-base font-medium whitespace-nowrap">
            {{ item }}
          </td>
        }
      </tr>
    }
  </tbody>
</table>

<div
  class="sticky bottom-0 right-0 items-center w-full p-4 bg-white border-t border-gray-200 sm:flex sm:justify-between dark:bg-gray-800 dark:border-gray-700"
>
  <div class="flex items-center mb-4 sm:mb-0">
    <span class="material-symbols-outlined cursor-pointer" (click)="previous()">
      chevron_left
    </span>
    <span class="material-symbols-outlined cursor-pointer" (click)="next()">chevron_right</span>
    <span class="text-sm font-normal text-gray-500 dark:text-gray-400"
      >Showing
      <span class="font-semibold text-gray-900 dark:text-white"
        >{{ meta.start }}-{{ meta.end }}</span
      >
      of
      <span class="font-semibold text-gray-900 dark:text-white">{{ meta.total }}</span></span
    >
  </div>
  <div class="flex items-center space-x-3">
    <a
      (click)="previous()"
      class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    >
      <span class="material-symbols-outlined cursor-pointer" (click)="previous()">
        chevron_left
      </span>
      Previous
    </a>
    <a
      (click)="next()"
      class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    >
      Next
      <span class="material-symbols-outlined cursor-pointer" (click)="next()">chevron_right</span>
    </a>
  </div>
</div>
