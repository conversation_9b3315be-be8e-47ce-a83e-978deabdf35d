import { Component, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { FormModalService } from '@lib/angular/dynamic-forms/form-modal.service';
import {
  type SilverField,
  SilverFieldTypes,
} from '@lib/angular/dynamic-forms/silver-field.component';
import { SVGIconComponent } from '@lib/angular/svg-icon.component';
import { html } from '@lib/common/jsx-dom';
import { firstValueFrom } from 'rxjs';
import { ChartViewerService } from '../../component/data-chart/data-chart.component';
import { hdbQueries } from './hdb-queries';
import { SalesService } from './hdb-sales.service';
@Component({
  selector: 'app-hdb-sales',
  templateUrl: './hdb-sales.component.html',
  imports: [
    MatSliderModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    SVGIconComponent,
  ],
})
export class SalesComponent implements OnInit {
  readonly #salesService = inject(SalesService);
  readonly #formModalService = inject(FormModalService);
  readonly #chartViewerService = inject(ChartViewerService);
  readonly #alertModalService = inject(AlertModalService);

  filter = {} as any;
  config = { page: 1, limit: 400, sort: 'id', order: 'DESC', lastId: 0 };
  db: any;
  headers = [];
  data = [];
  meta: any = {};

  async ngOnInit(): Promise<void> {
    this.db = await this.#salesService.loadDB();
    this.getData();
  }

  getData() {
    const res = this.#salesService.generateSqlQuery(this.db, this.filter, this.config);
    this.headers = res.headers;
    this.data = res.data;
    this.meta = res.meta;
    this.config.lastId = this.data.at(-1)[0];
  }

  previous() {
    if (this.config.page === 1) return;
    this.config.page = this.config.page - 1;
    this.getData();
  }

  next() {
    const totalPages = Math.ceil(this.meta.total / this.config.limit);
    if (this.config.page === totalPages) return;
    this.config.page = this.config.page + 1;
    this.getData();
  }

  public async openFilterForm() {
    const form: SilverField[] = [
      // {
      //   controlType: SilverFieldTypes.TEXT,
      //   label: 'area',
      //   placeholder: 'area',
      //   type: 'number',
      //   key: 'area',
      //   value: '',
      //   valid: { required: false },
      // },

      // {
      //   controlType: SilverFieldTypes.SELECT,
      //   label: 'blockId',
      //   key: 'blockId',
      //   value: null,
      //   valid: { required: false },
      //   multiple: true,
      //   options: this.#salesService.getAllBlocks(this.db),
      // },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'townId',
        key: 'townId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllTowns(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'flatModelId',
        key: 'flatModelId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllFlatModels(this.db),
      },
      {
        controlType: SilverFieldTypes.SELECT,
        label: 'flatTypeId',
        key: 'flatTypeId',
        value: null,
        nullable: true,
        valid: { required: false },
        multiple: true,
        options: this.#salesService.getAllFlatTypes(this.db),
      },
      {
        controlType: SilverFieldTypes.DATE_RANGE,
        label: 'Time period ',
        key: 'timePeriod',
        value: { from: null, to: null },
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Minimum Price',
        type: 'number',
        key: 'minimumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Price',
        placeholder: 'Maximum Price',
        type: 'number',
        key: 'maximumPrice',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum lease year',
        placeholder: 'lease year',
        type: 'number',
        key: 'minimumLeaseYear',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum lease year',
        placeholder: 'Maximum lease year',
        type: 'number',
        key: 'maximumLeaseYear',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Minimum Storey',
        placeholder: 'Minimum Storey',
        type: 'number',
        key: 'minimumStorey',
        value: '',
        valid: { required: false },
      },
      {
        controlType: SilverFieldTypes.TEXT,
        label: 'Maximum Storey',
        placeholder: 'Maximum Storey',
        type: 'number',
        key: 'maximumStorey',
        value: '',
        valid: { required: false },
      },
      // {
      //   controlType: SilverFieldTypes.DATE_RANGE,
      //   label: 'Time period ',
      //   key: 'timePeriod',
      //   value: { from: null, to: null },
      //   valid: { required: false },
      // },
    ];

    const result = await this.#formModalService.open({ heading: 'Filter', form });
    if (!result.action) return;
    this.filter = result.value;
    this.config.page = 1;
    this.config.lastId = 0;
    this.getData();
  }

  viewChart(query: { title: string; query: string }) {
    const res = this.db.exec(query.query);
    this.#chartViewerService.open({
      heading: query.title,
      header: res[0].columns,
      data: res[0].values,
    });
  }

  async chooseFAQ() {
    const ref = this.#alertModalService.openModal(
      {
        heading: 'FAQ Queries For Graph',
        domClass: 'w-full',
        dom: async (root, onClose) => {
          const dom = html`<div class="flex flex-col gap-4">
            <ul class="menu bg-base-200 rounded-box gap-4">
              ${hdbQueries.map(
                (query) => html` <li><a onClick=${() => ref.close(query)}>${query.title}</a></li>`,
              )}
            </ul>
          </div>`;
          root.appendChild(dom);
          await onClose;
          dom.remove();
        },
        noBtn: true,
      },
      { width: '600px', height: '600px' },
    );
    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    if (!result) return;
    this.viewChart(result);
  }
}
