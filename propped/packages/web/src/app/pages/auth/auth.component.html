@if (isSignup) {
  <form
    class="animate__animated animate__pulse flex flex-col justify-center gap-5 m-auto bg-base-100 p-5 w-4/5 max-w-[400px]"
    [formGroup]="signUpForm"
    (ngSubmit)="signUp()"
  >
    <div>
      <div class="text-2xl font-semibold">SIGN UP</div>
      <div class="text-md flex flex-row">
        Already have an account ?
        <button class="link link-accent ml-auto" (click)="toggle()">Login here</button>
      </div>
    </div>
    <mat-form-field class="grow" appearance="fill">
      <mat-label>Name</mat-label>
      <input type="text" matInput formControlName="name" />
      <mat-error>name required</mat-error>
    </mat-form-field>
    <mat-form-field class="grow" appearance="fill">
      <mat-label>Email</mat-label>
      <input type="email" matInput formControlName="email" />
      <mat-error>valid email required</mat-error>
    </mat-form-field>
    <mat-form-field class="grow" appearance="fill">
      <mat-label>Username</mat-label>
      <input type="text" matInput formControlName="username" />
      <mat-error>6-12 alphanumeric and _ allowed</mat-error>
    </mat-form-field>
    <mat-form-field class="grow" appearance="fill">
      <mat-label>Password</mat-label>
      <input type="password" #password matInput formControlName="password" />
      <mat-error>9-30 char with num, small, capital, special (!&#64;#$%..) char required</mat-error>
      <mat-icon
        matSuffix
        (click)="
          $event.stopPropagation();
          $event.preventDefault();
          password.type = password.type === 'password' ? 'text' : 'password'
        "
        >{{ password.type === 'password' ? 'visibility_off' : 'visibility' }}</mat-icon
      >
    </mat-form-field>
    <mat-form-field class="grow" appearance="fill">
      <mat-label>Confirm Password</mat-label>
      <input type="password" #confirmPwd matInput formControlName="confirmPwd" />
      <mat-error>must be same to password</mat-error>
      <mat-icon
        matSuffix
        (click)="
          $event.stopPropagation();
          $event.preventDefault();
          confirmPwd.type = confirmPwd.type === 'password' ? 'text' : 'password'
        "
        >{{ confirmPwd.type === 'password' ? 'visibility_off' : 'visibility' }}</mat-icon
      >
    </mat-form-field>
    <button type="submit" [disabled]="signUpForm.invalid" class="btn btn-outline btn-success">
      SIGNUP
    </button>
  </form>
} @else {
  <form
    class="animate__animated animate__pulse flex flex-col justify-center gap-5 m-auto bg-base-100 p-5 w-4/5 max-w-[400px]"
    [formGroup]="loginForm"
  >
    <div>
      <div class="text-3xl font-semibold">Welcome</div>
      <div class="text-sm">Please login using your account</div>
    </div>
    <div class="flex flex-col">
      <div class="mb-2 text-sm font-medium text-gray-900 dark:text-white">Username/Email *</div>
      <mat-form-field class="grow" appearance="fill">
        <input type="text" matInput formControlName="username" />
        <mat-error>Please enter valid username/email</mat-error>
      </mat-form-field>
    </div>
    <div class="flex flex-col">
      <div class="mb-2 text-sm font-medium text-gray-900 dark:text-white">Password *</div>
      <mat-form-field class="grow" appearance="fill">
        <input type="password" #password matInput formControlName="password" />
        <mat-error
          >9-30 char with num, small, capital, special (!&#64;#$%..) char required</mat-error
        >
        <mat-icon
          matSuffix
          (click)="
            $event.stopPropagation();
            $event.preventDefault();
            password.type = password.type === 'password' ? 'text' : 'password'
          "
          >{{ password.type === 'password' ? 'visibility_off' : 'visibility' }}</mat-icon
        >
      </mat-form-field>
    </div>

    <button
      [disabled]="loginForm.invalid"
      class="btn btn-outline btn-success"
      (click)="loginUser()"
    >
      LOGIN
    </button>
    <div>
      <button class="link link-accent" (click)="forgotPass()">Forgot your password?</button>
    </div>
    <button class="btn btn-primary" (click)="toggle()">CREATE AN ACCOUNT</button>
  </form>
}
