<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border border-blue-100">
    <!-- Target PSF Method Section -->
    <div class="border border-blue-100 rounded-lg p-6 shadow-sm mb-6">
      <h2 class="text-xl font-bold mb-4">Target PSF Method</h2>

      <!-- Unit Size -->
      <div class="flex justify-between items-center mb-4">
        <div>Your Unit Size</div>
        <div class="flex items-center">
          @if (!isEditingUnitSize) {
            <div class="px-3 py-1 rounded">{{ unitSize }} sq ft</div>
          } @else {
            <input
              type="number"
              [(ngModel)]="unitSize"
              class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          }
          <button (click)="toggleEditUnitSize()" class="ml-2 text-blue-600 hover:text-blue-800">
            {{ isEditingUnitSize ? 'Save' : 'Edit' }}
          </button>
        </div>
      </div>

      <!-- Target Offer Price PSF -->
      <div class="flex justify-between items-center mb-4">
        <div>Target Offer Price PSF</div>
        <div class="flex items-center">
          <span class="mr-1">$</span>
          <input
            type="number"
            [(ngModel)]="targetPSF"
            (change)="updateTargetPSF()"
            class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span class="ml-2">Or</span>
        </div>
      </div>

      <!-- Target Offer Price -->
      <div class="flex justify-between items-center mb-4">
        <div>Target Offer Price</div>
        <div class="flex items-center">
          <span class="mr-1">$</span>
          <input
            type="number"
            [(ngModel)]="targetOfferPrice"
            (change)="updateTargetPrice()"
            class="w-32 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <!-- Difference from Seller's Price -->
      <div class="flex justify-between items-center mb-4">
        <div>Difference from Seller's Price</div>
        <div
          [ngClass]="{ 'text-red-600': priceDifference < 0, 'text-green-600': priceDifference > 0 }"
          class="px-3 py-1 border rounded"
        >
          ${{ formatPrice(priceDifference) }}
        </div>
      </div>

      <!-- Reference Data Toggle -->
      <div class="mt-6">
        <button
          (click)="togglePSFReferenceData()"
          class="flex items-center text-blue-600 hover:text-blue-800"
        >
          <span>Useful Reference Data</span>
          <mat-icon>{{
            showPSFReferenceData ? 'keyboard_arrow_up' : 'keyboard_arrow_down'
          }}</mat-icon>
        </button>

        <!-- Reference Data Content -->
        @if (showPSFReferenceData) {
          <div class="mt-3 p-3 rounded-lg">
            <div class="grid grid-cols-2 gap-4">
              <!-- Left Column -->
              <div class="flex justify-between items-center">
                <div class="text-sm">Highest PSF in Sengkang</div>
                <div class="font-medium">${{ highestPSFInSengkang }}</div>
              </div>
              <div class="flex justify-between items-center">
                <div class="text-sm">Most Recent Transacted PSF in Sengkang</div>
                <div class="font-medium">${{ recentPSFInSengkang }}</div>
              </div>

              <!-- Right Column -->
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <div class="text-sm text-gray-600 mr-2">Highest PSF within</div>
                  <select [(ngModel)]="selectedRadius" class="px-2 py-1 border rounded text-sm">
                    @for (option of radiusOptions; track option) {
                      <option [value]="option">{{ option }}</option>
                    }
                  </select>
                </div>
                <div class="font-medium">${{ highestPSFInRadius }}</div>
              </div>
              <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                  Most Recent Transacted PSF within {{ selectedRadius }}
                </div>
                <div class="font-medium">${{ recentPSFInRadius }}</div>
              </div>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Annualised Capital Gain Method Section -->
    <div class="border border-blue-100 rounded-lg p-6 shadow-sm mb-6">
      <h2 class="text-xl font-bold mb-4">Annualised Capital Gain Method</h2>

      <!-- Purchase Price and Date -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div class="mb-2">Your Purchase Price</div>
          <div class="flex items-center">
            <span class="mr-1">$</span>
            <input
              type="number"
              [(ngModel)]="purchasePrice"
              (change)="calculateProfit()"
              class="w-full px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div>
          <div class="mb-2">Purchase Date</div>
          <button class="w-full px-2 py-1 border rounded text-left">
            {{ purchaseDate }}
          </button>
        </div>
      </div>

      <!-- Target Offer Price and Date -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div class="mb-2">Target Offer Price</div>
          <div class="flex items-center">
            <span class="mr-1">$</span>
            <input
              type="number"
              [(ngModel)]="targetOfferPrice"
              (change)="calculateProfit()"
              class="w-full px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div>
          <div class="mb-2">Offer Date</div>
          <button class="w-full px-2 py-1 border rounded text-left">
            {{ offerDate }}
          </button>
        </div>
      </div>

      <!-- Profit -->
      <div class="flex justify-between items-center mb-4">
        <div>Profit</div>
        <div class="px-3 py-1 border rounded">${{ formatPrice(profit) }}</div>
      </div>

      <!-- Actualised Capital Gain -->
      <div class="flex justify-between items-center mb-4">
        <div>Actualised Capital Gain</div>
        <div class="px-3 py-1 border rounded">
          {{ annualisedCapitalGain }}%
          <span class="text-sm text-gray-500 ml-1"
            >({{ durationYears }} Yrs {{ durationMonths }} Mths)</span
          >
        </div>
      </div>

      <!-- Reference Data Toggle -->
      <div class="mt-6">
        <button
          (click)="toggleCapitalGainReferenceData()"
          class="flex items-center text-blue-600 hover:text-blue-800"
        >
          <span>Useful Reference Data</span>
          <mat-icon>{{
            showCapitalGainReferenceData ? 'keyboard_arrow_up' : 'keyboard_arrow_down'
          }}</mat-icon>
        </button>

        <!-- Reference Data Content -->
        @if (showCapitalGainReferenceData) {
          <div class="mt-3 p-3 rounded-lg">
            <div class="mb-3">
              <div class="flex justify-between items-center">
                <div class="text-sm">Singapore Resale HDB Price Index (same period)</div>
                <div class="font-medium">{{ hdbResaleIndex }}%</div>
              </div>
            </div>

            <div class="mb-2">
              <div class="text-sm text-gray-600 md:font-medium">Data Used</div>
              <div class="flex justify-between items-center">
                <div class="text-sm">{{ indexStartDate }}</div>
                <div class="font-medium">{{ indexStartValue }}</div>
              </div>
              <div class="flex justify-between items-center">
                <div class="text-sm">{{ indexEndDate }}</div>
                <div class="font-medium">{{ indexEndValue }}</div>
              </div>
            </div>

            <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">See Chart</button>
          </div>
        }
      </div>
    </div>

    <!-- Footer Note -->
    <div class="text-md mb-6">
      <p>Use this to calculate either:</p>
      <ol class="list-decimal ml-5">
        <li>Seller's gain from previous transaction</li>
        <li>Your forecast gain from a forecast selling price and time</li>
      </ol>
    </div>

    <!-- Action Button -->
    <div class="mt-6">
      <button
        (click)="proceedWithOffer()"
        class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors"
      >
        Looks Great! Let's Go!
      </button>
    </div>
  </div>
</div>
