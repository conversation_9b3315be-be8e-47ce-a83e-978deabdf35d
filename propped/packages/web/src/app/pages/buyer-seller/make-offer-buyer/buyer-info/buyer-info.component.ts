import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-buyer-info',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div
      class="bg-gray-100 rounded-full px-4 py-1 my-1 flex items-center border border-blue-200 dark:bg-gray-700 w-[300px] max-w-full"
    >
      <div
        class="w-8 h-8 rounded-full bg-blue-300 flex items-center justify-center mr-2 flex-shrink-0"
      >
        <mat-icon class="text-white text-2xl">person</mat-icon>
      </div>
      <div class="flex-grow min-w-0">
        <div class="font-bold text-lg truncate">{{ username }}</div>
        <div class="flex items-center">
          <mat-icon class="text-green-600 text-base mr-1 flex-shrink-0">check</mat-icon>
          <span class="text-xs text-blue-700 font-semibold whitespace-nowrap">HFE Verified</span>
          <span class="text-xs text-gray-600 ml-1 dark:text-white whitespace-nowrap"
            >(Valid till 15<sup>th</sup> June 2026)</span
          >
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      @media (max-width: 640px) {
        :host {
          width: 100%;
        }
      }
    `,
  ],
})
export class BuyerInfoComponent {
  @Input() username = '';
}
