import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sale-conditions',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
  templateUrl: './sale-conditions.component.html',
})
export class SaleConditionsComponent implements OnInit {
  // Sale Conditions
  vacantPossession = 'Yes';
  extensionOfStay = '0';
  otherConditions = '';
  monthOptions: string[] = [];

  // Terms & Conditions
  agreeToTerms = false;

  // Modal
  showOtherConditionsModal = false;
  tempOtherConditions = '';

  ngOnInit(): void {
    // Generate month options from 0 to 12
    this.monthOptions = Array.from({ length: 13 }, (_, i) => i.toString());
  }

  // Open the modal for other conditions
  openOtherConditionsInput(): void {
    this.tempOtherConditions = this.otherConditions;
    this.showOtherConditionsModal = true;
  }

  // Save other conditions and close modal
  saveOtherConditions(): void {
    this.otherConditions = this.tempOtherConditions;
    this.showOtherConditionsModal = false;
  }

  // Cancel editing other conditions
  cancelOtherConditions(): void {
    this.tempOtherConditions = '';
    this.showOtherConditionsModal = false;
  }

  // Preview the listing
  previewListing(): void {
    if (this.agreeToTerms) {
      // Save the sale conditions data
      console.log('Sale conditions saved:', {
        vacantPossession: this.vacantPossession,
        extensionOfStay: this.extensionOfStay,
        otherConditions: this.otherConditions,
      });

      // In a real app, we would store this data in a service
      // to be accessed by the preview component
    } else {
      // If terms not agreed, do nothing or show a message
      console.warn('Please agree to the terms and conditions first');
    }
  }
}
