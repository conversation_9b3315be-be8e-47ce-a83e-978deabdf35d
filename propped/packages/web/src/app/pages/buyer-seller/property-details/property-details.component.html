<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border">
    <!-- Header -->
    <div class="text-center mb-6">
      <h1 class="text-2xl font-bold">Additional Property Details</h1>
    </div>

    <!-- Additional Sections -->
    <div class="space-y-8">
      <!-- 1. Add Home Walkthrough Video -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-2">Add Home Walkthrough Video</h3>
        <p class="mb-4">
          Film a continuous home walkthrough video under 2 mins to give potential buyers a complete
          overview of your home.
        </p>

        <div
          class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded"
        >
          @if (walkthroughVideo) {
            <div class="mb-4 w-full">
              <div class="relative w-full" style="padding-top: 56.25%">
                <!-- 16:9 aspect ratio -->
                <img
                  [src]="walkthroughVideo.thumbnail"
                  alt="Video thumbnail"
                  class="absolute inset-0 w-full h-full object-cover rounded"
                />
                <div
                  class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded"
                >
                  <mat-icon class="text-white text-5xl">play_circle</mat-icon>
                </div>
                <button
                  class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center"
                  (click)="deleteWalkthroughVideo()"
                >
                  <mat-icon>close</mat-icon>
                </button>
              </div>
              <p class="mt-2 text-sm text-center">
                {{ walkthroughVideo.name }} ({{ walkthroughVideo.duration }})
              </p>
            </div>
          }
          @if (!walkthroughVideo) {
            <button
              class="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg"
              (click)="takeWalkthroughVideo()"
            >
              <mat-icon class="mr-2 text-2xl">videocam</mat-icon>
              Tap to take video
            </button>
          }
        </div>
      </div>
      <!-- 5. We can help! - Home Hosts Section -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <div class="text-center mb-6">
          <h3 class="text-2xl font-bold">We can help!</h3>
          <p>
            Or hire any of our dynamic Propped Home Hosts to film an exclusive video for your home
          </p>
        </div>

        <!-- Home Hosts Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <!-- Host 1 -->
          <div class="flex flex-col items-center">
            <div class="w-full aspect-square overflow-hidden rounded-lg mb-2 bg-red-100">
              <img
                src="assets/dummy-host-1.png"
                alt="Home Host John"
                class="w-full h-full object-cover"
                onerror="this.src='assets/placeholder-person.jpg'"
              />
            </div>
            <h4 class="text-xl font-bold">JOHN</h4>
          </div>

          <!-- Host 2 -->
          <div class="flex flex-col items-center">
            <div class="w-full aspect-square overflow-hidden rounded-lg mb-2 bg-green-100">
              <img
                src="assets/dummy-host-2.png"
                alt="Home Host April"
                class="w-full h-full object-cover"
                onerror="this.src='assets/placeholder-person.jpg'"
              />
            </div>
            <h4 class="text-xl font-bold font-cursive">April</h4>
          </div>

          <!-- Host 3 -->
          <div class="flex flex-col items-center">
            <div class="w-full aspect-square overflow-hidden rounded-lg mb-2 bg-yellow-100">
              <img
                src="assets/dummy-host-3.png"
                alt="Home Host Tim"
                class="w-full h-full object-cover"
              />
            </div>
            <h4 class="text-xl font-bold">Tim</h4>
          </div>

          <!-- Host 4 -->
          <div class="flex flex-col items-center">
            <div class="w-full aspect-square overflow-hidden rounded-lg mb-2 bg-purple-100">
              <img
                src="assets/dummy-host-4.png"
                alt="Home Host Joan"
                class="w-full h-full object-cover"
              />
            </div>
            <h4 class="text-xl font-bold font-serif">Joan</h4>
          </div>
        </div>

        <!-- Hire Button -->
        <div class="flex justify-center">
          <button
            class="px-8 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors text-lg font-medium"
          >
            Hire a Home Host
          </button>
        </div>
      </div>

      <!-- 2. Add Floorplan -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-4">Add Floorplan</h3>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            class="flex-1 flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            (click)="uploadFloorplan()"
          >
            <mat-icon class="mr-2">upload_file</mat-icon>
            Tap to upload Floorplan
          </button>

          <button
            class="flex-1 flex items-center justify-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            (click)="browseFloorplans()"
          >
            <mat-icon class="mr-2">search</mat-icon>
            Browse available floorplans
          </button>
        </div>

        @if (floorplanImage) {
          <div class="mt-4 flex justify-center">
            <div class="relative">
              <img
                [src]="floorplanImage"
                alt="Floorplan"
                class="max-h-64 border border-gray-300 rounded"
              />
              <button
                class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center"
                (click)="deleteFloorplan()"
              >
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </div>
        }
      </div>

      <!-- 3. Add a Seller's Message -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-4">Add A Seller's Message</h3>

        <div class="w-full">
          <textarea
            rows="4"
            class="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Write a message that highlights your home's strengths…"
            [(ngModel)]="sellerMessage"
          ></textarea>
        </div>
      </div>

      <!-- 4. Suggestions (AI Assist Section) -->
      <div class="border border-blue-100 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-semibold mb-2">Suggestions</h3>
        <p class="mb-4">
          Enter your own keywords or choose from the suggestions below and use our AI Bot to
          generate a suggested message.
        </p>

        <div class="mb-4">
          <input
            type="text"
            class="w-full p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your keywords"
            [(ngModel)]="aiKeywords"
          />
        </div>

        <div class="flex flex-wrap gap-2 mb-4">
          @for (suggestion of suggestedKeywords; track suggestion) {
            <button
              [class]="
                selectedKeywords.includes(suggestion)
                  ? 'px-3 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-300 text-sm'
                  : 'px-3 py-1 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 text-sm'
              "
              (click)="toggleKeyword(suggestion)"
            >
              {{ suggestion }}
            </button>
          }
        </div>

        <button
          class="w-full sm:w-auto px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          (click)="generateAIMessage()"
          [disabled]="!hasSelectedKeywords()"
        >
          <mat-icon class="mr-1">smart_toy</mat-icon>
          Generate
        </button>

        @if (generatedMessage) {
          <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
            <h4 class="font-medium text-blue-800 mb-2">AI Generated Message:</h4>
            <p class="text-gray-700">{{ generatedMessage }}</p>
            <div class="mt-2 flex justify-end">
              <button
                class="px-4 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                (click)="useGeneratedMessage()"
              >
                Use This Message
              </button>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
      <div class="flex gap-2">
        <button
          routerLink="/buyer-seller/step2"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Back
        </button>
        <button
          class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Save Draft
        </button>
      </div>

      <button
        routerLink="/buyer-seller/step4"
        class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Continue to Pricing
      </button>
    </div>
  </div>
</div>
