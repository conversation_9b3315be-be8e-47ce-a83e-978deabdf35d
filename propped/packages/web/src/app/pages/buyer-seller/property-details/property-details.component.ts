import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

interface WalkthroughVideo {
  id: string;
  name: string;
  thumbnail: string;
  duration: string;
}

@Component({
  selector: 'app-property-details',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
  templateUrl: './property-details.component.html',
})
export class PropertyDetailsComponent {
  // Walkthrough video
  walkthroughVideo: WalkthroughVideo | null = null;

  // Floorplan
  floorplanImage: string | null = null;

  // Se<PERSON>'s message
  sellerMessage = '';

  // AI suggestions
  aiKeywords = '';
  selectedKeywords: string[] = [];
  suggestedKeywords: string[] = [
    'Move-in Condition',
    'Newly Renovated',
    'Central Location',
    'Lots of Natural Light',
    'Windy and Airy',
    'Plenty of Storage',
    'Close to Amenities',
    'Good Schools Nearby',
    'Quiet Neighborhood',
  ];
  generatedMessage = '';

  // Walkthrough video methods
  takeWalkthroughVideo(): void {
    // In a real app, this would open the video camera
    // For demo purposes, we'll just add a sample video
    this.walkthroughVideo = {
      id: Date.now().toString(),
      name: 'Home_Walkthrough.mp4',
      thumbnail: 'assets/images/sample-walkthrough-thumb.jpg',
      duration: '1:45',
    };
  }

  deleteWalkthroughVideo(): void {
    this.walkthroughVideo = null;
  }

  // Floorplan methods
  uploadFloorplan(): void {
    // In a real app, this would open a file picker
    // For demo purposes, we'll just add a sample floorplan
    this.floorplanImage = 'assets/images/sample-floorplan.png';
  }

  browseFloorplans(): void {
    // In a real app, this would open a modal with floorplan options
    // For demo purposes, we'll just add a sample floorplan
    this.floorplanImage = 'assets/images/sample-floorplan-2.png';
  }

  deleteFloorplan(): void {
    this.floorplanImage = null;
  }

  // AI suggestions methods
  toggleKeyword(keyword: string): void {
    const index = this.selectedKeywords.indexOf(keyword);
    if (index === -1) {
      this.selectedKeywords.push(keyword);
    } else {
      this.selectedKeywords.splice(index, 1);
    }
  }

  hasSelectedKeywords(): boolean {
    return this.selectedKeywords.length > 0 || this.aiKeywords.trim().length > 0;
  }

  generateAIMessage(): void {
    // In a real app, this would call an AI service
    // For demo purposes, we'll just generate a static message
    const keywords = [...this.selectedKeywords];
    if (this.aiKeywords.trim()) {
      keywords.push(...this.aiKeywords.split(',').map((k) => k.trim()));
    }

    this.generatedMessage =
      `Welcome to our beautiful home located in a ${keywords.includes('Central Location') ? 'central' : 'convenient'} location. ` +
      `${keywords.includes('Newly Renovated') ? 'The property has been recently renovated with modern finishes. ' : ''}` +
      `${keywords.includes('Lots of Natural Light') ? "You'll love the abundant natural light throughout the day. " : ''}` +
      `${keywords.includes('Windy and Airy') ? 'The unit is well-ventilated and feels spacious and airy. ' : ''}` +
      `${keywords.includes('Plenty of Storage') ? 'There is ample storage space throughout the home. ' : ''}` +
      `${keywords.includes('Move-in Condition') ? 'The property is in move-in condition and ready for immediate occupancy. ' : ''}` +
      `${keywords.includes('Close to Amenities') ? 'Conveniently located near shopping centers, restaurants, and public transportation. ' : ''}` +
      `${keywords.includes('Good Schools Nearby') ? 'Excellent schools are within walking distance. ' : ''}` +
      `${keywords.includes('Quiet Neighborhood') ? 'Situated in a peaceful and family-friendly neighborhood. ' : ''}` +
      'We hope you enjoy your visit and look forward to answering any questions you may have!';
  }

  useGeneratedMessage(): void {
    this.sellerMessage = this.generatedMessage;
    this.generatedMessage = '';
  }
}
