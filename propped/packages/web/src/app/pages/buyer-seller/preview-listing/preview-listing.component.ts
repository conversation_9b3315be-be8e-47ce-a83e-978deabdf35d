import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { type IUserSalesListing } from '@api/controllers/sales-listings/user-sales-listing.model';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { MatDialog } from '@angular/material/dialog';
import { SellerInfoComponent } from '../make-offer-buyer/seller-info/seller-info.component';
import { PreviewListingSellerDialogComponent } from './preview-listing-seller-dialog.component';
import { AuthService } from '@web/services/auth.service';
import { apiRPC, injectController } from '@api/rpc';

interface Profile{
  id: number | null;
  name: string;
}

@Component({
  selector: 'app-preview-listing',
  templateUrl: './preview-listing.component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
})
export class PreviewListingComponent {
  // View state management
  activeView = 'default'; // Possible values: 'default', 'floorplan', 'photos'
  showMapModal = false;
  showPhotosExpanded = false;
  userData: Profile | null = {
    id: null,
    name: ""
  };
  salesListingId: string | null = null;
  // Image slider state
  selectedPhotoType: string | null = null;
  selectedPhotoIndex = 0;
  showPhotoSlider = false;
  buyerOfferData: any | null = null;

  // Inject services
  private snackBarService = inject(SnackBarService);
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private route = inject(ActivatedRoute);
  private authService = inject(AuthService);
  public salesListingData: any = null;


  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);


  // Mock data for the listing preview
  // In a real implementation, this would come from a service that collects data from previous steps
  listingData = {
    address: 'Hougang Ave 8',
    block: '720a',
    town: 'Hougang Town',
    price: 750000,
    pricePSF: 758,
    listedDate: '17th August 2024',
    listingId: '01234',
    mainImage: 'assets/images/property-main.jpg',
    photoCount: 9,
    hasVideo: true,
    thumbnails: [
      'assets/properties/bedroom-1.jpg',
      'assets/properties/kitchen-1.jpg',
      'assets/properties/balcony-1.jpg',
      'assets/properties/living-room-1.jpg',
    ],
    floorplanImage: 'assets/images/floorplan.jpg',
    sellerMessage:
      "Don't miss this stunning, sunlit apartment in the heart of Sengkang. Your perfect home awaits! Modern finishes and unbeatable value make this a must-see.",
    area: {
      sqm: 90,
      sqft: 969,
    },
    saleConditions: ['Extension of Stay (3 months)', 'Vacant Possession'],
    developer: 'HDB',
    tenure: {
      years: 99,
      remaining: 76,
    },
    floor: 'High floor',
    flatType: '4-room',
    flatModel: 'New Generation',
    bedrooms: 3,
    bathrooms: 2,
  };

  async ngOnInit(){
    const user = await this.authService.getActiveUser();
    console.log("USER DATA", user);
    if(user){
      this.userData.id = user.id;
      this.userData.name = user.name
    }


    this.route.queryParams.subscribe(async (params) => {
      this.salesListingId = params['listingId'];
      if(this.salesListingId){
        await this.getSalesListingDetails(this.salesListingId);
        if(this.salesListingData && this.salesListingData.user_id !== this.userData.id){
          console.log("GETTING OFFER DETAILS");
          const response = await this.#hdbSalesOfferController.getOfferByListingId({ listingId: this.salesListingData.listingId, buyerId: this.userData.id });
          if(response.data){
            console.log("BUYER OFFER DATA", response.data);
            this.buyerOfferData = response.data;
          }
        }

      }
    });

    console.log("SALES LISTING DATA",this.salesListingData);

  }

  async getSalesListingDetails(listingId: string){
    try {
      const response = await this.#hdbSalesController.getSalesListingById({ _id: listingId});
      console.log("SALES LISTING DATA", response.data)
      this.salesListingData = response.data;
    } catch (error) {
      this.snackBarService.error('Error getting sales listing details')
      console.error('Error getting sales listing details:', error);
    }
  }

  // No constructor needed as we're using inject()

  // Submit the listing to the backend
  submitListing() {
    try {
      // Create a data object that matches the IUserSalesListing interface
      const salesListingData: Partial<IUserSalesListing> = {
        // Using mock ObjectId strings that would normally come from form data
        propertyType: '507f1f77bcf86cd799439011' as any, // Mock ObjectId for property type
        town: '507f1f77bcf86cd799439012' as any, // Mock ObjectId for town
        street: this.listingData.address,
        block: Number(this.listingData.block),
        postalCode: 123456, // Mock postal code
        floor: 10, // Mock floor number
        unit: 123, // Mock unit number

        // Location would be set from geocoding or previous steps
        location: {
          type: 'Point',
          coordinates: [103.8198, 1.3521], // Singapore coordinates
        },

        propertyMedia: {
          // These would be populated from the property-photos step
          mainDoor: [{ type: 'photo', url: this.listingData.mainImage }],
          floorplan: this.listingData.floorplanImage,
        },

        sellerMessage: {
          text: this.listingData.sellerMessage,
        },

        targetSellingPrice: this.listingData.price,
        targetSellingPSF: this.listingData.pricePSF,

        propertyDetails: {
          area: this.listingData.area.sqft,
          flatType: '507f1f77bcf86cd799439013' as any, // Mock ObjectId for flat type
          bedrooms: this.listingData.bedrooms,
          bathrooms: this.listingData.bathrooms,
          model: this.listingData.flatModel,
          developer: this.listingData.developer,
        },
      };

      this.snackBarService.open('Listing submitted successfully!', 'success');
      this.router.navigate(['/buyer-seller']);
    } catch (error) {
      console.error('Error submitting listing:', error);
      this.snackBarService.open('Failed to submit listing. Please try again.', 'error');
    }
  }

  // Format price with commas
  formatPrice(price: number): string {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Navigation and view state methods
  showOnMap(): void {
    // Navigate to the dedicated property map page instead of showing a modal
    console.log('SALES LISTING DATA PASSED TO THE MAP', this.salesListingData);
    this.router.navigate(['/buyer-seller/property-map'], {
      state: {
        data: this.salesListingData,
      },
    });
  }

  closeMapModal(): void {
    this.showMapModal = false;
  }

  // View state management methods
  viewFloorplan(): void {
    this.activeView = 'floorplan';
    this.showPhotosExpanded = false;
  }

  viewPhotos(): void {
    this.activeView = 'photos';
    this.showPhotosExpanded = true;
  }

  togglePhotos(): void {
    this.showPhotosExpanded = !this.showPhotosExpanded;
  }

  closeDetailView(): void {
    this.activeView = 'default';
    this.showPhotosExpanded = false;
    this.closePhotoSlider();
  }

  // Photo slider methods
  openPhotoSlider(photoType: string, index = 0): void {
    this.selectedPhotoType = photoType;
    this.selectedPhotoIndex = index;
    this.showPhotoSlider = true;
  }

  closePhotoSlider(): void {
    this.showPhotoSlider = false;
    this.selectedPhotoType = null;
    this.selectedPhotoIndex = 0;
  }

  nextPhoto(): void {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > 0) {
      this.selectedPhotoIndex = (this.selectedPhotoIndex + 1) % photoObj.images.length;
    }
  }

  prevPhoto(): void {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > 0) {
      this.selectedPhotoIndex =
        this.selectedPhotoIndex === 0 ? photoObj.images.length - 1 : this.selectedPhotoIndex - 1;
    }
  }

  getCurrentSlideImage(): string {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    if (photoObj && photoObj.images.length > this.selectedPhotoIndex) {
      return photoObj.images[this.selectedPhotoIndex];
    }
    return 'assets/images/photo-placeholder.jpg';
  }

  getPhotoCount(): number {
    const photoObj = this.getDummyPhotos().find((p) => p.type === this.selectedPhotoType);
    return photoObj ? photoObj.images.length : 0;
  }

  // Dummy photo data
  getDummyPhotos() {
    return [
      {
        type: 'bedroom',
        placeholderImg: 'assets/properties/bedroom-1.jpg',
        images: ['assets/properties/bedroom-1.jpg', 'assets/properties/bedroom-2.jpg'],
      },
      {
        type: 'balcony',
        placeholderImg: 'assets/properties/balcony-1.jpg',
        images: ['assets/properties/balcony-1.jpg', 'assets/properties/balcony-2.jpg'],
      },
      {
        type: 'kitchen',
        placeholderImg: 'assets/properties/kitchen-1.jpg',
        images: ['assets/properties/kitchen-1.jpg', 'assets/properties/kitchen-2.jpg'],
      },
    ];
  }

  // Helper to get the current listing data (real or dummy)
  get currentListing() {
    return this.salesListingData || this.listingData;
  }

  openSellerInfoModal() {
    this.dialog.open(PreviewListingSellerDialogComponent, {
      width: '370px',
      panelClass: 'seller-info-modal',
      data: {
        _id: this.salesListingId,
        listing_id: this.salesListingData?.listingId,
        userData: this.userData,
      }
    });
  }

  goToMakeOfferPage(){
    this.router.navigate(['/buyer-seller/make-offer-buyer'], {
      queryParams: {
        listing_id: this.salesListingData?.listingId,
        id: this.salesListingId
      },
    });
  }

  navigateBuyerToChatPage(){
    this.router.navigate(['/buyer-seller/preview'], {
      queryParams: {
        listingId: this.salesListingData?._id,
      },
    });
  }
}

