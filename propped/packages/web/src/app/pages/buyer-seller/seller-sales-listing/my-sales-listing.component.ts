import { Component, type OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { apiRPC, injectController } from '@api/rpc';
import { inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { SnackBarService } from '@lib/angular/snack-bar.service';

@Component({
  selector: 'app-my-sales-listing',
  standalone: true,
  imports: [CommonModule, DatePipe],
  templateUrl: './my-sales-listing.component.html',
  styleUrls: [],
})
export class MySalesListingComponent implements OnInit {
  readonly #hdbSalesController = injectController(apiRPC.HDBSalesController);
  readonly #hdbSalesOfferController = injectController(apiRPC.HDBSalesOfferController);
  readonly router = inject(Router);
  readonly authService = inject(AuthService);
  readonly snackBarService = inject(SnackBarService);

  userId: number | null = null;
  mySalesListings: any[] = [];
  receivedOffers: any[] = [];
  isLoading = true;

  async ngOnInit() {
    try {
      this.isLoading = true;
      const user = await this.authService.getActiveUser();
      this.userId = user.id;

      // Fetch all sales listings by the current user
      const res = await this.#hdbSalesController.getSalesListingsByUserId({ user_id: this.userId });
      this.mySalesListings = res?.data || [];

      // Fetch offers received for user's listings
      await this.fetchReceivedOffers();
    } catch (error) {
      console.error('Error loading my sales listings:', error);
      this.snackBarService.error('Failed to load your sales listings');
    } finally {
      this.isLoading = false;
    }
  }

  async fetchReceivedOffers() {
    if (!this.userId) return;

    try {
      const offersRes = await this.#hdbSalesOfferController.getOffersBySellerId({ sellerId: this.userId });
      this.receivedOffers = offersRes?.data || [];
      console.log('Received offers:', this.receivedOffers);
    } catch (error) {
      console.error('Error fetching received offers:', error);
    }
  }

  // We're keeping the receivedOffers array for future use
  // but not using it for conditional UI display

  viewOffers(listing: any) {
    this.router.navigate(['/buyer-seller/seller-preview'], {
      queryParams: {
        listingId: listing._id,
      },
    });
  }

  editListing(listing: any) {
    this.router.navigate(['/buyer-seller/edit-listing'], {
      queryParams: {
        listingId: listing._id,
      },
    });
  }

  previewListing(listing: any) {
    this.router.navigate(['/buyer-seller/preview'], {
      queryParams: {
        listingId: listing._id
      }
    });
  }

  createNewListing() {
    this.router.navigate(['/buyer-seller/step1']);
  }
}
