<div class="w-full p-4">
  <div class="rounded-lg shadow-lg p-6 border">
    <!-- Header -->
    <div class="text-center mb-6">
      <h1 class="text-2xl font-bold">Step 2 – Add Photos / Videos</h1>
    </div>

    <!-- Two-column layout with room selection on left and upload area on right -->
    <div class="flex flex-col md:flex-row gap-6 mb-6">
      <!-- Left Column: Room Selection Grid -->
      <div class="w-full md:w-1/2">
        <h3 class="text-lg font-semibold mb-3">Select Rooms to Photograph</h3>
        <div class="grid grid-cols-2 sm:grid-cols-3 gap-2 mb-4 sticky top-4">
          @for (room of rooms; track room) {
            <div
              [class]="
                room.selected
                  ? 'p-2 text-center rounded border-2 border-blue-500 bg-blue-100 hover:bg-blue-200 transition-colors dark:text-black'
                  : 'p-2 text-center rounded border border-gray-300 hover:border-blue-500 hover:bg-blue-50 transition-colors'
              "
              (click)="toggleRoomSelection(room)"
            >
              {{ room.name }}
            </div>
          }

          <!-- Add Your Own Category -->
          <button
            class="p-2 text-center rounded border border-dashed border-gray-400 hover:border-blue-500 hover:bg-blue-50 transition-colors"
            (click)="showAddCategoryModal = true"
          >
            Add Your Own Category
          </button>
        </div>

        <!-- Room Selection Instructions -->
        <div class="p-4 rounded border border-blue-100 mb-4">
          <h4 class="font-medium mb-2">Instructions</h4>
          <ul class="text-sm space-y-1 list-disc pl-5">
            <li>Select all rooms you want to photograph</li>
            <li>Take photos from multiple angles for each room</li>
            <li>Ensure good lighting for best results</li>
            <li>Add custom categories if needed</li>
          </ul>
        </div>
      </div>

      <!-- Right Column: Media Upload Area -->
      <div class="w-full md:w-1/2">
        <h3 class="text-lg font-semibold mb-3">Upload Media for Selected Rooms</h3>

        <!-- Selected Rooms with Photos -->
        <div class="space-y-6 max-h-[600px] overflow-y-auto pr-2">
          @for (room of getSelectedRooms(); track room) {
            <div class="border border-gray-200 rounded-lg p-4">
              <h2 class="text-lg font-semibold mb-3 border-b pb-2">{{ room.name }}</h2>

              <!-- Photo Actions -->
              <div class="flex flex-wrap gap-3 mb-4">
                <button
                  class="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  (click)="takePhoto(room)"
                >
                  <mat-icon class="mr-1">photo_camera</mat-icon>
                  Take photo
                </button>

                <button
                  class="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  (click)="takeVideo(room)"
                >
                  <mat-icon class="mr-1">videocam</mat-icon>
                  Take video
                </button>
              </div>

              <!-- Photo Thumbnails -->
              @if (room.photos.length > 0) {
                <div class="grid grid-cols-3 sm:grid-cols-3 gap-3">
                  @for (photo of room.photos; track photo) {
                    <div class="relative">
                      <img
                        [src]="photo.url"
                        [alt]="room.name + ' photo'"
                        class="w-full h-24 object-cover rounded border border-gray-300"
                      />
                      <button
                        class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                        (click)="deletePhoto(room, photo)"
                      >
                        <mat-icon class="text-sm">close</mat-icon>
                      </button>

                      @if (photo.type === 'video') {
                        <div
                          class="absolute bottom-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-6 h-6 flex items-center justify-center"
                        >
                          <mat-icon class="text-sm">play_arrow</mat-icon>
                        </div>
                      }
                    </div>
                  }
                </div>
              }

              <!-- No Photos Message -->
              @if (room.photos.length === 0) {
                <div
                  class="text-center p-3 bg-gray-50 rounded border border-gray-200 text-sm dark:text-black"
                >
                  <p>No photos or videos added yet</p>
                </div>
              }
            </div>
          }

          <!-- No Rooms Selected Message -->
          @if (getSelectedRooms().length === 0) {
            <div class="text-center p-6 rounded border border-gray-200">
              <p>Please select rooms from the left panel to add photos</p>
            </div>
          }
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
      <div class="flex gap-2">
        <button
          routerLink="/buyer-seller/step1"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Back
        </button>
        <button
          class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Save Draft
        </button>
      </div>
      <button
        routerLink="/buyer-seller/step3"
        class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Continue to Additional Details
      </button>
    </div>
  </div>
</div>

<!-- Add Category Modal -->
@if (showAddCategoryModal) {
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h3 class="text-lg font-semibold mb-4">Add Custom Room Category</h3>

      <div class="mb-4">
        <label for="customRoomName" class="block text-sm font-medium text-gray-700 mb-1"
          >Room Name</label
        >
        <input
          type="text"
          id="customRoomName"
          class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          [(ngModel)]="customRoomName"
          placeholder="e.g. Home Office, Gym"
        />
      </div>

      <div class="flex justify-end gap-2">
        <button
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          (click)="showAddCategoryModal = false"
        >
          Cancel
        </button>

        <button
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          (click)="addCustomRoom()"
          [disabled]="!customRoomName"
        >
          Add
        </button>
      </div>
    </div>
  </div>
}
