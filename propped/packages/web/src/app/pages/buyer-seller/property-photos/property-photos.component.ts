import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

interface RoomPhoto {
  id: string;
  url: string;
  type: 'photo' | 'video';
}

interface Room {
  id: string;
  name: string;
  selected: boolean;
  photos: RoomPhoto[];
}

@Component({
  selector: 'app-property-photos',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
  templateUrl: './property-photos.component.html',
})
export class PropertyPhotosComponent {
  // Sample room data
  rooms: Room[] = [
    { id: 'main-door', name: 'Main Door (Outside View)', selected: false, photos: [] },
    {
      id: 'entrance',
      name: 'Entrance Hallway',
      selected: true,
      photos: [
        { id: '1', url: 'assets/images/sample-hallway-1.jpg', type: 'photo' },
        { id: '2', url: 'assets/images/sample-hallway-2.jpg', type: 'photo' },
        { id: '3', url: 'assets/images/sample-hallway-3.jpg', type: 'photo' },
      ],
    },
    {
      id: 'living-room',
      name: 'Living Room',
      selected: true,
      photos: [
        { id: '4', url: 'assets/images/sample-living-1.jpg', type: 'photo' },
        { id: '5', url: 'assets/images/sample-living-2.jpg', type: 'photo' },
        { id: '6', url: 'assets/images/sample-living-3.jpg', type: 'video' },
      ],
    },
    { id: 'kitchen', name: 'Kitchen', selected: true, photos: [] },
    { id: 'balcony', name: 'Balcony', selected: false, photos: [] },
    { id: 'yard', name: 'Yard', selected: false, photos: [] },
    { id: 'common-hallway', name: 'Common Hallway', selected: false, photos: [] },
    { id: 'storeroom', name: 'Storeroom / Bomb Shelter', selected: false, photos: [] },
    { id: 'study', name: 'Study', selected: false, photos: [] },
    { id: 'bedroom-1', name: 'Bedroom 1', selected: false, photos: [] },
    { id: 'bedroom-2', name: 'Bedroom 2', selected: false, photos: [] },
    { id: 'bedroom-3', name: 'Bedroom 3', selected: false, photos: [] },
    { id: 'junior-master', name: 'Junior Master', selected: false, photos: [] },
    { id: 'master-bedroom', name: 'Master Bedroom', selected: false, photos: [] },
    { id: 'common-toilet', name: 'Common Toilet', selected: false, photos: [] },
  ];

  // Custom room modal
  showAddCategoryModal = false;
  customRoomName = '';

  // Toggle room selection
  toggleRoomSelection(room: Room): void {
    room.selected = !room.selected;
  }

  // Get selected rooms
  getSelectedRooms(): Room[] {
    return this.rooms.filter((room) => room.selected);
  }

  // Add custom room
  addCustomRoom(): void {
    if (this.customRoomName) {
      const id = 'custom-' + this.customRoomName.toLowerCase().replace(/\s+/g, '-');
      this.rooms.push({
        id,
        name: this.customRoomName,
        selected: true,
        photos: [],
      });

      this.customRoomName = '';
      this.showAddCategoryModal = false;
    }
  }

  // Take photo
  takePhoto(room: Room): void {
    // In a real app, this would open the camera
    // For demo purposes, we'll just add a sample photo
    const newId = Date.now().toString();
    room.photos.push({
      id: newId,
      url: 'assets/images/sample-photo.jpg',
      type: 'photo',
    });
  }

  // Take video
  takeVideo(room: Room): void {
    // In a real app, this would open the video camera
    // For demo purposes, we'll just add a sample video thumbnail
    const newId = Date.now().toString();
    room.photos.push({
      id: newId,
      url: 'assets/images/sample-video-thumb.jpg',
      type: 'video',
    });
  }

  // Delete photo
  deletePhoto(room: Room, photo: RoomPhoto): void {
    const index = room.photos.findIndex((p) => p.id === photo.id);
    if (index !== -1) {
      room.photos.splice(index, 1);
    }
  }
}
