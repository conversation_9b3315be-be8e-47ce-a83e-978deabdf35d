<div class="container mx-auto p-4">
  <!-- Main Content -->
  <div class="grid grid-cols-1 gap-6">
    <!-- Section 1: Photos + Floorplan Preview (copied from preview-listing) -->
    <div class="mb-6">
      <div class="rounded-lg shadow-lg border border-blue-100 dark:bg-gray-800">
        <!-- Dynamic Floorplan/Photos View -->
        @if (activeView !== 'default') {
          <div class="mb-6">
            <div class="rounded-lg shadow-lg border border-blue-100 dark:bg-gray-800">
              <!-- Header -->
              <div class="border-b dark:bg-gray-800">
                <div class="p-4">
                  <!-- Property Header -->
                  <!-- <div class="flex justify-end items-center">
                  <button (click)="closeDetailView()" class="text-gray-700 dark:text-white">
                    <mat-icon>close</mat-icon>
                  </button>
                </div> -->
                </div>
              </div>

              <!-- Content Area -->
              <div class="p-4 dark:bg-gray-800">
                <!-- Floorplan -->
                <h4 class="font-bold text-xl dark:text-white">Floor plan Details</h4>
                <div class="mb-6 flex justify-center">
                  <img
                    [src]="'assets/floor-plan-1.jpg'"
                    alt="Floorplan"
                    class="max-w-full max-h-[500px] object-contain rounded-lg"
                  />
                </div>

                <!-- Photos Section Toggle -->
                <div
                  class="flex justify-between items-center py-3 px-4 bg-gray-100 rounded-lg cursor-pointer mb-4 dark:border border-blue-200 dark:bg-transparent"
                  (click)="togglePhotos()"
                >
                  <span class="font-medium dark:text-white">Photos</span>
                  <mat-icon>{{ showPhotosExpanded ? 'remove' : 'add' }}</mat-icon>
                </div>
                <!-- Photos Grid -->
                @if (showPhotosExpanded && !showPhotoSlider) {
                  <div class="mb-6">
                    <div class="rounded-lg border border-blue-200 p-4">
                      <h3 class="text-xl font-bold mb-4">Photos</h3>

                      <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
                        @for (photo of getDummyPhotos(); track photo.type) {
                          <div class="relative">
                            <div class="flex flex-col">
                              <div
                                class="relative cursor-pointer"
                                (click)="openPhotoSlider(photo.type)"
                              >
                                <img
                                  [src]="photo.placeholderImg"
                                  [alt]="photo.type"
                                  class="w-full aspect-square object-cover rounded-lg"
                                />
                                <div
                                  class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full"
                                >
                                  {{ photo.type }} ({{ photo.images.length }})
                                </div>
                              </div>
                            </div>
                          </div>
                        }
                      </div>
                    </div>
                  </div>
                }

                <!-- Photo Slider (when an image is clicked) -->
                @if (showPhotoSlider) {
                  <div class="mb-6">
                    <div class="rounded-lg border border-blue-200 p-4">
                      <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold">
                          {{ selectedPhotoType }} ({{ selectedPhotoIndex + 1 }}/{{
                            getPhotoCount()
                          }})
                        </h3>
                        <button (click)="closePhotoSlider()" class="font-semibold text-lg">
                          <mat-icon>close</mat-icon>
                        </button>
                      </div>

                      <div class="relative">
                        <!-- Main Image -->
                        <div class="flex justify-center">
                          <img
                            [src]="getCurrentSlideImage()"
                            [alt]="selectedPhotoType"
                            class="max-w-full max-h-[400px] object-contain rounded-lg"
                          />
                        </div>

                        <!-- Navigation Arrows -->
                        <button
                          (click)="prevPhoto()"
                          class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white py-1 px-2 rounded-full"
                        >
                          <mat-icon>chevron_left</mat-icon>
                        </button>
                        <button
                          (click)="nextPhoto()"
                          class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white py-1 px-2 rounded-full"
                        >
                          <mat-icon>chevron_right</mat-icon>
                        </button>
                      </div>

                      <!-- Thumbnails -->
                      <div class="flex justify-center mt-4 space-x-2">
                        <!-- Using a simpler approach to avoid complex expressions in template -->
                        @if (selectedPhotoType) {
                          <div>
                            <div
                              @for(i
                              of
                              [0,
                              1];
                              let
                              idx="index)"
                              class="w-16 h-16 rounded-lg cursor-pointer border-2"
                              [class.border-blue-500]="idx === selectedPhotoIndex"
                              [class.border-transparent]="idx !== selectedPhotoIndex"
                              (click)="selectedPhotoIndex = idx"
                            >
                              <img
                                [src]="
                                  'assets/properties/' +
                                  selectedPhotoType +
                                  '-' +
                                  (idx + 1) +
                                  '.jpg'
                                "
                                class="w-full h-full object-cover rounded-lg"
                                [alt]="selectedPhotoType + ' ' + (idx + 1)"
                              />
                            </div>
                          </div>
                        }
                      </div>
                    </div>
                  </div>
                }
              </div>
            </div>
          </div>
        }

        <!-- Default View with Thumbnails -->
        @if (activeView === 'default') {
          <div class="p-4">
            <!-- Thumbnails Grid -->
            <div class="grid grid-cols-2 gap-2">
              <div
                *ngFor="let image of listingData.thumbnails.slice(0, 4); let i = index"
                class="relative"
              >
                <img
                  [src]="image"
                  alt="Property thumbnail"
                  class="w-full h-32 object-cover rounded-lg"
                />
              </div>
            </div>
            <!-- Action Buttons -->
            <div class="flex flex-wrap justify-between mt-4 gap-2">
              <button
                (click)="toggleFloorplan()"
                class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <mat-icon class="mr-1">grid_on</mat-icon>
                <span>Floorplan</span>
              </button>
              <button
                (click)="togglePhotos()"
                class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <mat-icon class="mr-1">photo_library</mat-icon>
                <span>Photos</span>
              </button>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Section 2: Annotate on Floorplan -->
    <div class="mb-6">
      <div class="rounded-lg shadow-lg p-4 border border-blue-200">
        <h2 class="text-xl font-bold mb-2">Annotate on Floorplan</h2>
        <p class="mb-4">
          Use this feature to mark any location on the floorplan to make it easier to ask the seller
          a question.
        </p>
        <button
          (click)="openAnnotationTool()"
          class="w-full py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Tap to Mark on Floorplan
        </button>
      </div>
    </div>

    <!-- Section 3: Send Seller a Message -->
    <div class="mb-6">
      <div class="rounded-lg shadow-lg p-4 border border-blue-200">
        <h2 class="text-xl font-bold mb-2">Send Seller A Message</h2>
        <div class="flex items-center my-2 gap-2 flex-wrap">
          <div class="text-xl font-bold mr-4">Seller</div>
          <app-seller-info [username]="sellerProfileData.name" />
          <button
            class="bg-blue-600 px-4 py-1 text-white rounded-lg hover:bg-blue-700 flex items-center"
            (click)="toggleSearchBar()"
          >
            <mat-icon class="text-white text-xl mr-1">search</mat-icon>
            Search Within chat
          </button>
        </div>

        @if (userData.id === listingData?.user_id) {
          <div class="mt-4 border rounded-lg overflow-hidden shadow-sm bg-white my-2">
            <mat-expansion-panel
              [expanded]="isChatsExpanded"
              (opened)="isChatsExpanded = true"
              (closed)="isChatsExpanded = false"
              class="border-0 shadow-none"
            >
              <mat-expansion-panel-header class="bg-gradient-to-r from-blue-50 to-white py-3">
                <mat-panel-title>
                  <div class="flex items-center">
                    <mat-icon class="mr-2 text-blue-600">forum</mat-icon>
                    <span class="font-semibold text-gray-800">List of Chats</span>
                    @if (sellerChats.length > 0) {
                      <span class="ml-2 bg-blue-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-medium">
                        {{ sellerChats.length }}
                      </span>
                    }
                  </div>
                </mat-panel-title>
                <mat-panel-description class="text-sm text-gray-500 hidden md:block">
                  Manage your conversations with potential buyers
                </mat-panel-description>
              </mat-expansion-panel-header>

              <!-- Chats List Content -->
              <div class="max-h-80 overflow-y-auto bg-white">
                @if (isChatsLoading) {
                  <div class="flex justify-center items-center py-6">
                    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
                  </div>
                } @else if (sellerChats.length === 0) {
                  <div class="py-8 text-center text-gray-500 flex flex-col items-center">
                    <mat-icon class="text-gray-400 mb-2" style="font-size: 32px; width: 32px; height: 32px;">chat_bubble_outline</mat-icon>
                    <div>No chats available yet</div>
                    <div class="text-xs mt-1">When buyers contact you, conversations will appear here</div>
                  </div>
                } @else {
                  <div class="divide-y">
                    @for (chat of sellerChats; track chat._id) {
                      <div
                        class="p-4 cursor-pointer hover:bg-blue-50 transition-colors duration-150 flex justify-between items-start"
                        [ngClass]="{'bg-blue-50 border-l-4 border-blue-600': selectedChat?._id === chat._id}"
                        (click)="loadChatMessages(chat)"
                      >
                        <div class="flex items-start">
                          <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3 font-semibold">
                            {{ chat.buyerId.toString().charAt(0).toUpperCase() }}
                          </div>
                          <div>
                            <div class="text-sm font-medium text-gray-800 flex items-center">
                              Buyer #{{ chat.buyerId }}
                              @if (chat.unreadCount && chat.unreadCount > 0) {
                                <span class="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                                  {{ chat.unreadCount }} new
                                </span>
                              }
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                              {{ chat.createdAt | date:'MMM d, y • h:mm a' }}
                            </div>
                            @if (chat.lastMessage) {
                              <div class="text-xs text-gray-600 mt-1 line-clamp-1">
                                {{ chat.lastMessage }}
                              </div>
                            }
                          </div>
                        </div>
                        <div class="text-right">
                          <div class="text-xs font-medium bg-gray-100 rounded-full px-2 py-1">
                            {{ chat.messages.length }} {{ chat.messages.length === 1 ? 'message' : 'messages' }}
                          </div>
                          @if (chat.offerStatus === 'pending') {
                            <div class="text-xs text-yellow-600 bg-yellow-50 rounded px-2 py-1 mt-1">Offer Pending</div>
                          } @else if (chat.offerStatus === 'accepted') {
                            <div class="text-xs text-green-600 bg-green-50 rounded px-2 py-1 mt-1">Offer Accepted</div>
                          } @else if (chat.offerStatus === 'rejected') {
                            <div class="text-xs text-red-600 bg-red-50 rounded px-2 py-1 mt-1">Offer Rejected</div>
                          }
                        </div>
                      </div>
                    }
                  </div>
                }
              </div>
            </mat-expansion-panel>
          </div>
        }

         <!-- Accepted Offer Information Box -->
         @if (userData.id === listingData?.user_id && sellerAcceptedOffer) {
          <div class="mt-4 border border-green-300 rounded-lg bg-green-50 p-4 shadow-sm">
            <div class="flex items-start">
              <div class="mr-3">
                <mat-icon class="text-green-600">check_circle</mat-icon>
              </div>
              <div>
                <h3 class="font-semibold text-green-800">Offer Accepted</h3>
                <p class="text-sm text-green-700 mt-1">
                  You have already accepted an offer for this listing from Buyer #{{ sellerAcceptedOffer.buyerId }}
                  for <span class="font-medium">S$ {{ sellerAcceptedOffer.offerPrice | number }}</span>
                </p>
                <p class="text-xs text-green-600 mt-2">
                  Accepted on {{ sellerAcceptedOffer.updatedAt | date:'MMM d, y • h:mm a' }}
                </p>
                <div class="mt-3 flex items-center">
                  <a routerLink="/user-dashboard" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                    <mat-icon class="text-sm mr-1">assignment</mat-icon>
                    View transaction progress in your dashboard
                  </a>
                </div>
              </div>
            </div>
          </div>
        }


        <!-- Search Bar - appears when button is clicked -->
        @if (showSearchBar) {
          <div class="mt-2 w-full animate-slideDown">
            <div class="flex flex-col bg-white rounded-lg shadow-md p-2 border border-blue-200">
              <div class="flex items-center mb-2">
                <div class="relative flex-grow">
                  <input
                    type="text"
                    [(ngModel)]="searchQuery"
                    (input)="searchInChat()"
                    class="w-full p-2 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Search in conversation..."
                  />
                  @if (searchQuery) {
                    <button
                      (click)="searchQuery = ''; searchResults = []"
                      class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      <mat-icon>close</mat-icon>
                    </button>
                  }
                </div>
                <button
                  (click)="toggleSearchBar()"
                  class="ml-2 p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
                  title="Close search"
                >
                  <mat-icon>close</mat-icon>
                </button>
              </div>

              <!-- Search Results -->
              @if (searchResults.length > 0) {
                <div class="max-h-40 overflow-y-auto">
                  @for (result of searchResults; track $index) {
                    <div
                      class="p-2 hover:bg-gray-100 rounded-md mb-1 cursor-pointer border-l-4 border-blue-500"
                    >
                      <div class="flex justify-between text-xs text-gray-500 mb-1">
                        <span>{{
                          result.sender === 'seller' ? sellerUsername : buyerUsername
                        }}</span>
                        <span>{{ result.time }}</span>
                      </div>
                      <div class="text-sm">{{ result.message }}</div>
                    </div>
                  }
                </div>
              } @else if (searchQuery && searchQuery.length > 0) {
                <div class="p-3 text-center text-gray-500">
                  No results found for "{{ searchQuery }}"
                </div>
              }
            </div>
          </div>
        }

        <!-- Chat Box -->
        <div class="w-full border p-4 min-h-[300px] overflow-y-auto">
          <!-- Chat Messages -->
          <div class="space-y-4">

            @if (chatOffer && userData.id === chatOffer.buyerId) {
              <div class="bg-gradient-to-r from-blue-50 to-white rounded-lg p-5 my-5 border border-blue-200 w-full shadow-sm">
                <div class="flex justify-between items-center mb-3">
                  <div class="font-bold text-blue-700 text-lg flex items-center">
                    <mat-icon class="mr-2">local_offer</mat-icon>
                    Your Offer
                  </div>
                  <div class="text-sm text-gray-500">{{ chatOffer.createdAt | date:'medium' }}</div>
                </div>
                <div class="flex items-center gap-2 mb-3">
                  <span class="text-xl font-bold text-blue-700">S$ {{ chatOffer.amount | number }}</span>
                  <span class="text-sm px-2 py-1 rounded-full" [ngClass]="{
                    'bg-green-100 text-green-700': chatOffer.amount > askingPrice,
                    'bg-red-100 text-red-700': chatOffer.amount < askingPrice,
                    'bg-blue-100 text-blue-700': chatOffer.amount === askingPrice
                  }">
                    {{ chatOffer.amount > askingPrice ? '+' : '' }}{{ chatOffer.amount - askingPrice | number }} from asking price
                  </span>
                </div>
                <div class="flex items-center mb-3">
                  <div class="text-sm mr-2">Status:</div>
                  <div class="px-3 py-1 rounded-full text-sm font-medium" [ngClass]="{
                    'bg-yellow-100 text-yellow-800': chatOffer.status === 'pending',
                    'bg-green-100 text-green-800': chatOffer.status === 'accepted',
                    'bg-red-100 text-red-800': chatOffer.status === 'rejected',
                    'bg-blue-100 text-blue-800': chatOffer.status === 'countered'
                  }">
                    <div class="flex items-center">
                      <mat-icon class="mr-1" style="font-size: 16px; width: 16px; height: 16px;" [ngClass]="{
                        'text-yellow-800': chatOffer.status === 'pending',
                        'text-green-800': chatOffer.status === 'accepted',
                        'text-red-800': chatOffer.status === 'rejected',
                        'text-blue-800': chatOffer.status === 'countered'
                      }">
                        {{
                          chatOffer.status === 'pending' ? 'hourglass_empty' :
                          chatOffer.status === 'accepted' ? 'check_circle' :
                          chatOffer.status === 'rejected' ? 'cancel' : 'swap_horiz'
                        }}
                      </mat-icon>
                      {{ chatOffer.status | titlecase }}
                    </div>
                  </div>
                </div>

                @if (chatOffer.status === 'accepted'){
                  <div class="p-4 bg-green-50 border border-green-200 rounded-lg mt-3">
                    <div class="flex items-start">
                      <mat-icon class="text-green-600 mr-2">celebration</mat-icon>
                      <div>
                        <div class="font-semibold text-green-800 mb-1">Congratulations! Your offer has been accepted!</div>
                        <div class="text-sm text-green-700">
                          Resale transaction has commenced. Check the progress in your dashboard.
                          <span (click)="navigateToDashboard()" class="font-semibold underline cursor-pointer hover:text-green-900">Go to your Dashboard</span>
                        </div>
                      </div>
                    </div>
                  </div>
                }

                @if (chatOffer.status === 'countered') {
                  <div class="mt-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center mb-2">
                      <mat-icon class="text-blue-600 mr-2">swap_horiz</mat-icon>
                      <div class="text-md font-semibold text-blue-800">Counter Offer Received</div>
                    </div>
                    <div class="flex items-center gap-2 mb-2">
                      <span class="text-lg font-bold text-blue-700">S$ {{ chatOffer.counterOfferPrice | number }}</span>
                      <span class="text-sm px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                        {{ chatOffer.counterOfferPrice > chatOffer.amount ? '+' : '' }}{{ chatOffer.counterOfferPrice - chatOffer.amount | number }} from your offer
                      </span>
                    </div>
                    @if (chatOffer.counterOfferMessage) {
                      <div class="text-sm text-blue-700 italic mt-2">"{{ chatOffer.counterOfferMessage }}"</div>
                    }
                    <div class="mt-3 flex gap-2">
                      <button class="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm flex items-center">
                        <mat-icon class="mr-1" style="font-size: 16px; width: 16px; height: 16px;">check</mat-icon>
                        Accept Counter
                      </button>
                      <button class="px-3 py-1.5 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 text-sm flex items-center">
                        <mat-icon class="mr-1" style="font-size: 16px; width: 16px; height: 16px;">refresh</mat-icon>
                        Make New Offer
                      </button>
                    </div>
                  </div>
                }

                @if (chatOffer.status === 'rejected') {
                  <div class="p-4 bg-red-50 border border-red-200 rounded-lg mt-3">
                    <div class="flex items-start">
                      <mat-icon class="text-red-600 mr-2">error_outline</mat-icon>
                      <div>
                        <div class="font-semibold text-red-800 mb-1">Your offer was rejected</div>
                        <div class="text-sm text-red-700">
                          You can make a new offer or continue the conversation with the seller.
                        </div>
                      </div>
                    </div>
                  </div>
                }
              </div>
            }

            @if (chatOffer && userData.id !== chatOffer.buyerId) {
              <div class="mb-5 bg-gradient-to-r from-blue-50 to-white rounded-lg p-5 border border-blue-200 shadow-sm">
                <div class="flex justify-between items-center mb-3">
                  <div class="flex items-center">
                    <mat-icon class="text-blue-600 mr-2">local_offer</mat-icon>
                    <div class="text-lg font-bold text-gray-800">
                      Offer Received: <span class="text-blue-700">S$ {{ chatOffer.amount | number }}</span>
                    </div>
                  </div>
                  <div class="px-3 py-1 rounded-full text-sm font-medium" [ngClass]="{
                    'bg-yellow-100 text-yellow-800': chatOffer.status === 'pending',
                    'bg-green-100 text-green-800': chatOffer.status === 'accepted',
                    'bg-red-100 text-red-800': chatOffer.status === 'rejected',
                    'bg-blue-100 text-blue-800': chatOffer.status === 'countered'
                  }">
                    <div class="flex items-center">
                      <mat-icon class="mr-1" style="font-size: 16px; width: 16px; height: 16px;">
                        {{
                          chatOffer.status === 'pending' ? 'hourglass_empty' :
                          chatOffer.status === 'accepted' ? 'check_circle' :
                          chatOffer.status === 'rejected' ? 'cancel' : 'swap_horiz'
                        }}
                      </mat-icon>
                      {{ chatOffer.status | titlecase }}
                    </div>
                  </div>
                </div>
                <div class="text-sm text-gray-500 mb-3">
                  {{ chatOffer.timestamp | date:'medium' }}
                </div>

                <div class="flex items-center gap-2 mb-3">
                  <div class="text-sm px-2 py-1 rounded-full" [ngClass]="{
                    'bg-green-100 text-green-700': chatOffer.amount > askingPrice,
                    'bg-red-100 text-red-700': chatOffer.amount < askingPrice,
                    'bg-blue-100 text-blue-700': chatOffer.amount === askingPrice
                  }">
                    {{ chatOffer.amount > askingPrice ? '+' : '' }}{{ chatOffer.amount - askingPrice | number }} from asking price
                  </div>
                </div>

                <!-- Counter Offer Display (if exists) -->
                @if (chatOffer.status === 'countered' && chatOffer.counterOfferPrice) {
                  <div class="mt-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center">
                      <mat-icon class="text-blue-600 mr-2">swap_horiz</mat-icon>
                      <div class="text-md font-semibold text-blue-800">Your Counter Offer: S$ {{ chatOffer.counterOfferPrice | number }}</div>
                    </div>
                    @if (chatOffer.counterOfferMessage) {
                      <div class="text-sm text-blue-700 italic mt-2">"{{ chatOffer.counterOfferMessage }}"</div>
                    }
                  </div>
                }

                <!-- Offer Action Buttons (only show for pending offers) -->
                @if (chatOffer.status === 'pending') {
                <div>
                  <div class="mt-4 flex justify-end gap-3">
                    @if(sellerAcceptedOffer){
                      <button
                      disabled
                      class="px-4 py-2 bg-green-600 opacity-50 text-white rounded-lg hover:bg-green-700 flex items-center shadow-sm"
                    >
                      <mat-icon class="mr-1">check_circle</mat-icon>
                      Accept Offer
                    </button>
                    }
                    @else{
                    <button
                      (click)="onAcceptOffer(chatOffer)"
                      class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center shadow-sm"
                    >
                      <mat-icon class="mr-1">check_circle</mat-icon>
                      Accept Offer
                    </button>
                    }

                    <button
                      (click)="onRejectOffer(chatOffer)"
                      class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center shadow-sm"
                    >
                      <mat-icon class="mr-1">cancel</mat-icon>
                      Reject Offer
                    </button>
                    <button
                      (click)="showCounterOfferInput()"
                      class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center shadow-sm"
                    >
                      <mat-icon class="mr-1">swap_horiz</mat-icon>
                      Counter-offer
                    </button>
                  </div>
                  @if(sellerAcceptedOffer){
                    <p>You have already accepted an offer</p>
                  }
                </div>
                }

                <!-- Counter Offer Input (conditionally shown) -->
                @if (showCounterInput) {
                  <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                    <div class="text-sm font-semibold mb-3 text-gray-700">Enter Counter Offer Amount:</div>
                    <div class="flex items-center gap-3">
                      <div class="flex-grow">
                        <div class="relative">
                          <span class="absolute left-3 top-2 text-gray-500">S$</span>
                          <input
                            type="number"
                            [(ngModel)]="counterOfferPrice"
                            class="w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter amount"
                          />
                        </div>
                      </div>
                      <button
                        (click)="submitCounterOffer()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                      >
                        <mat-icon class="mr-1">send</mat-icon>
                        Submit
                      </button>
                      <button
                        (click)="cancelCounterOffer()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                }
              </div>
            }
            <!-- Chat Messages -->
            @for (message of chatMessages; track $index) {
              <div [ngClass]="{
                'flex justify-start': message.user_id !== userData.id,
                'flex justify-end': message.user_id === userData.id
              }" class="mb-4">
                <!-- Message Content -->
                @if (editingMessageId === message._id) {
                  <div class="bg-white border border-blue-300 rounded-lg p-3 max-w-[80%] shadow-sm">
                    <div class="flex justify-between items-center mb-2">
                      <div class="text-sm font-semibold">
                        {{ message.user_id === userData.id ? 'You' : (message.role === 'seller' ? 'Seller' : 'Buyer') }}
                      </div>
                    </div>
                    <textarea
                      [(ngModel)]="editMessageText"
                      class="w-full p-2 border rounded mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows="3"
                    ></textarea>
                    <div class="flex justify-end gap-2">
                      <button
                        (click)="cancelEditMessage()"
                        class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm"
                      >
                        Cancel
                      </button>
                      <button
                        (click)="saveEditedMessage()"
                        class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                } @else {
                  <div [ngClass]="{
                    'bg-gray-100 text-gray-800 rounded-tl-lg rounded-tr-lg rounded-br-lg pl-4 pr-4 py-3 max-w-[75%] shadow-sm border-l-4 border-gray-300': message.role === 'seller' && message.user_id !== userData.id,
                    'bg-blue-600 text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg pl-4 pr-4 py-3 max-w-[75%] shadow-sm': message.user_id === userData.id
                  }" class="relative">
                    <div class="flex justify-between items-center mb-1">
                      <div class="text-sm font-semibold">
                        {{ message.user_id === userData.id ? 'You' : (message.role === 'seller' ? 'Seller' : 'Buyer') }}
                      </div>
                    </div>

                    <div class="text-sm">{{ message.message }}</div>
                    <div class="text-xs mt-2" [ngClass]="{'text-gray-500': message.user_id !== userData.id, 'text-blue-200': message.user_id === userData.id}">
                      {{ message.timestamp | date:'short' }}
                      @if (message.edited) {
                        <span class="ml-1 italic">(edited)</span>
                      }
                    </div>

                    <!-- Edit/Delete Options (only for user's own messages) -->
                    @if (message.user_id === userData.id) {
                      <div class="absolute bottom-1 right-1">
                        <button
                          class="w-6 h-6 rounded-full flex items-center justify-center hover:bg-blue-700"
                          [matMenuTriggerFor]="messageMenu"
                        >
                          <mat-icon class="text-sm text-white">more_vert</mat-icon>
                        </button>

                        <mat-menu #messageMenu="matMenu">
                          <button mat-menu-item (click)="startEditMessage(message)">
                            <mat-icon>edit</mat-icon>
                            <span>Edit</span>
                          </button>
                          <button mat-menu-item (click)="deleteMessage(message._id)">
                            <mat-icon class="text-red-600">delete</mat-icon>
                            <span class="text-red-600">Delete</span>
                          </button>
                        </mat-menu>
                      </div>
                    }
                  </div>
                }
              </div>
            }
          </div>
        </div>


        <!-- Chat Input -->
        <div class="my-2">
          <div class="flex">
            <textarea
              [(ngModel)]="chatMessage"
              rows="2"
              class="w-full p-2 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Type your message here..."
            ></textarea>
            <div class="flex flex-col">
              <!-- <button class="h-1/2 px-3 bg-gray-100 border-t border-r border-b hover:bg-gray-200">
                <mat-icon>search</mat-icon>
              </button> -->
              <button
                (click)="sendMessage()"
                class="h-full px-3 bg-blue-600 text-white border-r border-b rounded-br-lg hover:bg-blue-700"
              >
                <mat-icon>send</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between mb-4">
          <button
            (click)="exportChat()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Export Chat
          </button>
        </div>

        <!-- Suggested Questions -->
        <div>
          <h3 class="font-medium mb-2">Suggestions</h3>
          <div class="flex flex-wrap gap-2">
            @for (question of suggestedQuestions; track $index) {
              <button
                (click)="insertSuggestedQuestion(question)"
                class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200"
              >
                {{ question }}
              </button>
            }
          </div>
        </div>
      </div>
    </div>

    <!-- Section 4: Make Offer -->
    <div class="mb-6">

      @if(listingData?.user_id !== userData?.id){
      <div class="rounded-lg shadow-lg p-4 border border-blue-200">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold mb-4">Make Offer</h2>
        </div>
        @if (userProfile.role === 'buyer') {
        <div class="flex items-center gap-4 mb-2">
            @if(chatOffer && (chatOffer.status === 'pending' || chatOffer.status === 'accepted')){
            <button
              class="bg-gray-200 text-black font-bold px-6 py-2 rounded-lg mr-2 disabled:opacity-60"
              disabled="true"
            >
              Proceed to Make Offer
            </button>
            }
            @else{
            <button
              class="bg-blue-600 text-white font-bold px-6 py-2 rounded-lg mr-2 disabled:opacity-60"
              (click)="onMakeOffer()"
            >
              Proceed to Make Offer
            </button>
            }
            <p class="text-lg">Offer Price: </p>
            <input
              type="number"
              [value]="listingData?.price"
              [readonly]="offerAccepted"
              disabled="true"
              class="border border-gray-300 rounded-lg px-4 py-2 text-2xl font-semibold w-60 text-right"
              placeholder="S$ 0"
            />
          </div>
          @if(chatOffer && chatOffer.status === 'countered'){
            <p class="text-sm my-1">Seller has Countered your offer with the price <span class="font-semibold text-blue-600">${{chatOffer.counterOfferPrice}}</span></p>
          }
          @if(chatOffer && chatOffer.status === 'rejected'){
            <p class="text-sm my-1">Sorry, your offer price has been rejected, But you can still make a new offer</p>
          }
          <app-buyer-info [username]="userData.name" />
           @if(chatOffer && userData.id === chatOffer.buyerId){
            <div class="text-right text-sm text-blue-700 mb-4">
              Your offer price is {{ chatOffer.amount}}
            </div>
           }
          @if (offerAccepted && lastOfferAmount !== null) {
            <div class="text-right text-lg font-bold text-purple-700 mt-2">
              Offer Submitted: S$ {{ lastOfferAmount | number }}
            </div>
          }
        }
      </div>
      }

        <!-- Offer Accepted Success UI -->
        @if (offerAccepted) {
          <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
              <div class="text-center">
                <div class="bg-blue-600 text-white py-3 px-4 rounded-t-lg mb-4">
                  <div class="text-lg font-semibold">Congrats!</div>
                  <div>You Have Accepted the Price Offer!</div>
                </div>
                <div class="text-sm text-gray-500 mb-4">
                  {{ offerAcceptedTimestamp | date:'medium' }}
                </div>

                <div class="bg-gray-100 p-4 rounded-lg mb-4">
                  <div class="font-semibold mb-1">Resale Transaction Has Commenced!</div>
                  <div class="text-sm text-gray-600 mb-2">Check the progress in your dashboard</div>
                  <button
                    class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 w-full"
                    (click)="navigateToDashboard()">
                    Go to your Dashboard
                  </button>
                </div>

                <div class="text-sm text-gray-500">
                  {{ offerAcceptedTimestamp | date:'medium' }}
                </div>
              </div>
            </div>
          </div>
        }
    </div>
  </div>
</div>
