import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { apiRPC, injectController } from '@api/rpc';

interface PropertyDetails {
  area: {
    sqm: number;
    sqft: number;
  };
  flatType: string;
  flatModel: string;
  completionYear: number;
  tenure: {
    years: number;
    remaining: number;
  };
  developer: string;
  floor: string;
  bedrooms: number;
  bathrooms: number;
  floorplanImage: string;
}

@Component({
  selector: 'app-property-address',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, RouterModule],
  template: `
    <div class="w-full p-4">
      <div class="rounded-lg shadow-lg p-6 border border-blue-100">
        <!-- Header -->
        <div class="text-center mb-6">
          <h1 class="text-2xl font-bold">Enter Property Details</h1>
        </div>

        <!-- Two-column layout with form inputs on left and property details on right -->
        <div class="flex flex-col md:flex-row gap-6 mb-6">
          <!-- Left Column: All Form Inputs -->
          <div class="w-full md:w-1/2 space-y-4">
            <!-- Property Type -->
            <div>
              <label for="propertyType" class="block text-sm font-medium mb-1">Property Type</label>
              <div class="relative">
                <select
                  id="propertyType"
                  class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-8"
                  [(ngModel)]="propertyType"
                >
                  <option value="" disabled selected>Select a property type</option>
                  @for (type of buildingTypes; track type) {
                    <option [value]="type.name">{{ type.name }}</option>
                  }
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Town -->
            <div>
              <label for="town" class="block text-sm font-medium b-1">Town</label>
              <div class="relative">
                <select
                  id="town"
                  class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-8"
                  [(ngModel)]="town"
                >
                  <option value="" disabled selected>Select a town</option>
                  @for (townOption of towns; track townOption) {
                    <option [value]="townOption.name">{{ townOption.name }}</option>
                  }
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Street -->
            <div>
              <label for="street" class="block text-sm font-medium mb-1">Street</label>
              <input
                type="text"
                id="street"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="street"
                placeholder="e.g. Ang Mo Kio Ave 1"
              />
            </div>

            <!-- Block -->
            <div>
              <label for="block" class="block text-sm font-medium mb-1">Block</label>
              <input
                type="text"
                id="block"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="block"
                placeholder="e.g. 123"
              />
            </div>

            <!-- Postal Code -->
            <div>
              <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-1"
                >Postal Code</label
              >
              <input
                type="text"
                id="postalCode"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="postalCode"
                placeholder="e.g. 560123"
              />
            </div>

            <!-- Floor -->
            <div>
              <label for="floor" class="block text-sm font-medium mb-1">Floor</label>
              <input
                type="text"
                id="floor"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="floor"
                placeholder="e.g. 05"
              />
            </div>

            <!-- Unit -->
            <div>
              <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">Unit</label>
              <input
                type="text"
                id="unit"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="unit"
                placeholder="e.g. 123"
              />
            </div>
          </div>

          <!-- Right Column: Property Details -->
          <div class="w-full md:w-1/2">
            <div class="sticky top-4">
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-lg font-semibold">Property Details</h2>
                <button class="text-sm text-blue-600 hover:text-blue-800 dark:text-white">
                  (Click to Change)
                </button>
              </div>

              <div class="p-4 rounded-lg border border-gray-200">
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <!-- Left Column -->
                  <div class="space-y-2">
                    <div>
                      <span class="text-sm">Area</span>
                      <p class="font-medium">
                        {{ propertyDetails.area.sqm }} sqm / {{ propertyDetails.area.sqft }} sqft
                      </p>
                    </div>

                    <div>
                      <span class="text-sm">Completion / TOP</span>
                      <p class="font-medium">{{ propertyDetails.completionYear }}</p>
                    </div>

                    <div>
                      <span class="text-sm">Tenure (Remaining)</span>
                      <p class="font-medium">
                        {{ propertyDetails.tenure.years }} ({{ propertyDetails.tenure.remaining }})
                      </p>
                    </div>

                    <div>
                      <span class="text-sm">Developer</span>
                      <p class="font-medium">{{ propertyDetails.developer }}</p>
                    </div>
                  </div>

                  <!-- Middle Column -->
                  <div class="space-y-2">
                    <div>
                      <span class="text-sm">Flat Type</span>
                      <p class="font-medium">{{ propertyDetails.flatType }}</p>
                    </div>

                    <div>
                      <span class="text-sm">Flat Model</span>
                      <p class="font-medium">{{ propertyDetails.flatModel }}</p>
                    </div>

                    <div>
                      <span class="text-sm">Floor</span>
                      <p class="font-medium">{{ propertyDetails.floor }}</p>
                    </div>

                    <div>
                      <span class="text-sm">Bedrooms</span>
                      <p class="font-medium">{{ propertyDetails.bedrooms }}</p>
                    </div>

                    <div>
                      <span class="text-sm">Bathrooms</span>
                      <p class="font-medium">{{ propertyDetails.bathrooms }}</p>
                    </div>
                  </div>

                  <!-- Right Column (Floorplan) -->
                  <div class="col-span-2 md:col-span-1 flex justify-center items-center">
                    <div class="text-center">
                      <span class="text-sm block mb-2">Suggested Floorplan</span>
                      <img
                        [src]="propertyDetails.floorplanImage"
                        alt="Floorplan"
                        class="max-w-full h-auto border border-gray-300 rounded"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Row 3: Renovation Section -->
        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-4">Add Additional Details</h2>

          <h3 class="font-medium mb-2">Renovation</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Style -->
            <div>
              <label for="renovationStyle" class="block text-sm font-medium mb-1">Style</label>
              <div class="relative">
                <select
                  id="renovationStyle"
                  class="w-full p-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [(ngModel)]="renovationStyle"
                >
                  <option value="">Choose</option>
                  <option value="Modern">Modern</option>
                  <option value="Contemporary">Contemporary</option>
                  <option value="Minimalist">Minimalist</option>
                  <option value="Traditional">Traditional</option>
                  <option value="Industrial">Industrial</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                  <svg
                    class="fill-current h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Last Done -->
            <div>
              <label for="renovationYear" class="block text-sm font-medium  mb-1">Last Done</label>
              <input
                type="text"
                id="renovationYear"
                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                [(ngModel)]="renovationYear"
                placeholder="Year"
              />
            </div>

            <!-- Renovation Cost -->
            <div>
              <label for="renovationCost" class="block text-sm font-medium  mb-1"
                >Renovation Cost</label
              >
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500">$</span>
                </div>
                <input
                  type="text"
                  id="renovationCost"
                  class="w-full p-2 pl-7 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  [(ngModel)]="renovationCost"
                  placeholder="Amount"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex justify-between mt-8">
          <div class="flex gap-2">
            <button
              routerLink="/buyer-seller"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
            >
              Back
            </button>
            <button
              class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Save Draft
            </button>
          </div>

          <button
            routerLink="/buyer-seller/step2"
            class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Continue to Add Photos
          </button>
        </div>
      </div>
    </div>
  `,
})
export class PropertyAddressComponent implements OnInit {
  // Inject HDB controller
  readonly #hdbController = injectController(apiRPC.HDBController);

  // Arrays to store dropdown options
  buildingTypes: any[] = [];
  towns: any[] = [];
  // Form fields
  propertyType = '';
  town = '';
  street = '';
  block = '';
  postalCode = '';
  floor = '';
  unit = '';

  // Renovation fields
  renovationStyle = '';
  renovationYear = '';
  renovationCost = '';

  ngOnInit(): void {
    // Fetch building types and towns when component initializes
    this.fetchBuildingTypes();
    this.fetchTowns();
  }

  // Fetch building types from API
  async fetchBuildingTypes(): Promise<void> {
    try {
      const response = await this.#hdbController.getAllBuildingTypes();
      this.buildingTypes = response.data;
      console.log('Building types loaded:', this.buildingTypes);
    } catch (error) {
      console.error('Error fetching building types:', error);
    }
  }

  // Fetch towns from API
  async fetchTowns(): Promise<void> {
    try {
      const response = await this.#hdbController.getAllTowns();
      this.towns = response.data;
      console.log('Towns loaded:', this.towns);
    } catch (error) {
      console.error('Error fetching towns:', error);
    }
  }

  // Sample property details (in a real app, this would come from an API)
  propertyDetails: PropertyDetails = {
    area: {
      sqm: 92,
      sqft: 990,
    },
    flatType: '4 Room',
    flatModel: 'New Generation',
    completionYear: 1989,
    tenure: {
      years: 99,
      remaining: 64,
    },
    developer: 'HDB',
    floor: 'High Floor',
    bedrooms: 3,
    bathrooms: 2,
    floorplanImage: 'assets/images/sample-floorplan.png',
  };
}
