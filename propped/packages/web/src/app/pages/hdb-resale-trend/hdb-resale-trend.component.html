<div class="container mx-auto p-4 text-white">
  <div class="flex items-center mb-4">
    <app-svg-icon icon="local:hdb" style="color: #d71514" />
    <div class="p-3 text-2xl text-black grow-1 dark:text-white">
      HDB Resale Trends & Top Performers
    </div>
  </div>

  <!-- Filter Section -->
  <div class="flex flex-col gap-4">
    <!-- Town & Street -->
    <div class="flex flex-wrap items-center gap-6 mb-2">
      <div class="flex items-center gap-2">
        <label for="town-select" class="text-black font-semibold dark:text-white">Town:</label>
        <select
          id="town-select"
          [(ngModel)]="selectedTown"
          (ngModelChange)="onTownChange()"
          class="bg-blue-500 text-white py-2 px-3 rounded cursor-pointer dark:bg-blue-700"
        >
          @for (town of towns; track $index) {
            <option [value]="town">{{ town }}</option>
          }
        </select>
      </div>

      <div class="flex items-center gap-2">
        <label for="street-select" class="text-black font-semibold dark:text-white">Street:</label>
        <select
          id="street-select"
          [(ngModel)]="selectedStreet"
          class="bg-blue-500 text-white py-2 px-3 rounded cursor-pointer"
        >
          @for (street of streets; track $index) {
            <option [value]="street">{{ street }}</option>
          }
        </select>
      </div>
    </div>

    <!-- Flat Type -->
    <div class="flex items-center gap-4 mb-2">
      <div class="w-28 text-black font-semibold dark:text-white">Flat Type:</div>
      <div class="flex flex-wrap gap-2">
        @for (type of flatTypes; track $index) {
          <button
            (click)="toggleFlatType(type)"
            [ngClass]="{
              'bg-blue-700': selectedFlatTypes.includes(type),
              'bg-blue-500': !selectedFlatTypes.includes(type),
            }"
            class="text-white py-2 px-6 rounded hover:bg-blue-600 transition-colors"
          >
            {{ type }}
          </button>
        }
      </div>
    </div>

    <!-- Time Period -->
    <div class="flex items-center gap-4 mb-2">
      <div class="w-28 text-black font-semibold dark:text-white">Time Period:</div>
      <div class="flex gap-4 items-center">
        <input
          id="from-date"
          type="date"
          [(ngModel)]="fromDate"
          class="bg-blue-500 text-white py-2 px-3 rounded cursor-pointer"
          placeholder="From"
        />
        <span class="text-black dark:text-white">to</span>
        <input
          id="to-date"
          type="date"
          [(ngModel)]="toDate"
          class="bg-blue-500 text-white py-2 px-3 rounded cursor-pointer"
          placeholder="To"
        />
      </div>
    </div>

    <!-- Sort By -->
    <div class="flex items-center gap-4 mb-2">
      <div class="w-28 text-black font-semibold dark:text-white">Sort By:</div>
      <div class="flex flex-wrap gap-2">
        @for (option of sortOptions; track $index) {
          <button
            (click)="selectSortOption(option)"
            [ngClass]="{
              'bg-blue-700': selectedSortOption === option,
              'bg-blue-500': selectedSortOption !== option,
            }"
            class="text-white py-2 px-6 rounded hover:bg-blue-600 transition-colors"
          >
            {{ option }}
          </button>
        }
      </div>
    </div>

    <!-- Chart Type -->
    <div class="flex items-center gap-4 mb-2">
      <div class="w-28 text-black font-semibold dark:text-white">Chart Type:</div>
      <div class="flex flex-wrap gap-2">
        @for (type of chartTypes; track $index) {
          <button
            (click)="selectChartType(type)"
            [ngClass]="{
              'bg-blue-700': selectedChartType === type,
              'bg-blue-500': selectedChartType !== type,
            }"
            class="text-white py-2 px-6 rounded hover:bg-blue-600 transition-colors"
          >
            {{ type }}
          </button>
        }
      </div>
    </div>
  </div>

  <!-- Chart and Table Area -->
  <div
    class="w-full mt-8 p-4 rounded-lg shadow-lg flex flex-col md:flex-row items-start justify-center bg-white dark:bg-gray-800"
  >
    <!-- Chart Section -->
    <div class="w-full md:w-1/2 h-96">
      <app-resale-trend-chart [chartData]="chartData" />
    </div>

    <!-- Top Performers Table -->
    <div class="w-full md:w-1/2 p-4">
      <!-- Table Title and Subtitle -->
      <div class="mb-4">
        <h3 class="text-xl font-bold text-center text-gray-800 dark:text-white">Top Performers</h3>

        <!-- Filter Summary - matching chart component style -->
        <div class="flex items-center justify-center gap-2 flex-wrap mt-2">
          @for (filter of tableFilterSummary; track $index) {
            <button
              class="px-2 py-1 text-sm text-gray-600 dark:text-gray-300 border border-gray-700 dark:border-gray-500 rounded"
            >
              {{ filter }}
            </button>
          }
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full border-collapse border border-gray-300 dark:border-gray-700">
          <thead>
            <tr class="bg-gray-100 dark:bg-gray-700">
              <th
                class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-left text-gray-700 dark:text-gray-200"
              >
                Rank
              </th>
              <th
                class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-left text-gray-700 dark:text-gray-200"
              >
                {{ getTableTownDisplay() }} (street name)
              </th>
              <th
                class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-left text-gray-700 dark:text-gray-200"
              >
                {{ getTableColumnTitle() }}
              </th>
            </tr>
          </thead>
          <tbody>
            @for (performer of paginatedPerformers; track $index) {
              <tr
                class="hover:bg-gray-50 dark:hover:bg-gray-700"
                [ngClass]="{
                  'bg-gray-50 dark:bg-gray-800': $index % 2 === 0,
                  'bg-white dark:bg-gray-900': $index % 2 !== 0,
                }"
              >
                <td
                  class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                >
                  {{ (currentPage - 1) * 10 + $index + 1 }}
                </td>
                <td
                  class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                >
                  {{ performer.street }}
                </td>
                <td
                  class="py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                >
                  {{ performer.volume }}
                </td>
              </tr>
            }

            @if (topPerformers.length === 0) {
              <tr>
                <td
                  colspan="3"
                  class="py-4 px-4 text-center text-gray-500 dark:text-gray-400 border border-gray-300 dark:border-gray-600"
                >
                  No data available for the selected filters
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      @if (totalPages > 1) {
        <div class="flex justify-center mt-4">
          <nav class="flex items-center">
            <button
              (click)="changePage(currentPage - 1)"
              [disabled]="currentPage === 1"
              class="px-3 py-1 rounded-l border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              &laquo;
            </button>

            <div>
              @for (page of getPaginationArray(); track $index) {
                @if (page !== '...') {
                  <button
                    (click)="changePage(page)"
                    [ngClass]="{
                      'bg-blue-500 text-white': currentPage === page,
                      'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300':
                        currentPage !== page,
                    }"
                    class="px-3 py-1 border-t border-b border-gray-300 dark:border-gray-600"
                  >
                    {{ page }}
                  </button>
                }
                @if (page === '...') {
                  <span
                    class="px-3 py-1 border-t border-b border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                  >
                    {{ page }}
                  </span>
                }
              }
            </div>
            <button
              (click)="changePage(currentPage + 1)"
              [disabled]="currentPage === totalPages"
              class="px-3 py-1 rounded-r border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              &raquo;
            </button>
          </nav>
        </div>
      }
    </div>
  </div>
</div>
