import {
  Component,
  EventEmitter,
  Input,
  Output,
  type AfterViewInit,
  type OnChanges,
  type <PERSON><PERSON><PERSON><PERSON>,
  type SimpleChang<PERSON>,
} from '@angular/core';

interface TableDataI {
  heading: string;
  header: string[];
  data: any[][];
}

@Component({
  selector: 'app-stacked-table',
  template: `
    <div class="w-full h-full flex flex-col border rounded-lg shadow-lg relative">
      <!-- Close Button -->
      <button
        class="absolute top-2 right-2 text-gray-500 hover:text-red-500 text-lg font-bold"
        (click)="closeTable()"
      >
        ✖
      </button>

      <!-- Table Heading -->
      <div class="p-2 text-center font-bold text-lg border-b">{{ heading }}</div>

      <!-- Scrollable Table Wrapper -->
      <div class="overflow-x-auto max-h-80">
        <table class="w-full text-left border-collapse text-xs">
          <thead>
            <tr>
              @for (col of tableData.header; track col) {
                <th class="px-2 py-1 font-bold border">
                  {{ col }}
                </th>
              }
            </tr>
          </thead>
          <tbody>
            @for (row of tableData.data; track row) {
              <tr class="border">
                @for (cell of row; track cell) {
                  <td class="px-2 py-1 whitespace-nowrap border">
                    {{ cell }}
                  </td>
                }
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  `,
})
export class StackedTableComponent implements AfterViewInit, OnDestroy, OnChanges {
  @Input() tableData!: TableDataI;
  @Output() tableClose = new EventEmitter<void>(); // Renamed to avoid conflict

  public heading = '';
  private isViewInitialized = false;

  ngAfterViewInit() {
    this.isViewInitialized = true;
    if (this.tableData) {
      this.initializeTable();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['tableData'] && !changes['tableData'].firstChange && this.isViewInitialized) {
      this.initializeTable();
    }
  }

  private initializeTable() {
    this.heading = this.tableData.heading;
  }

  closeTable() {
    this.tableClose.emit(); // Updated emit call accordingly
  }

  ngOnDestroy() {
    console.log('Table component destroyed.');
  }
}
