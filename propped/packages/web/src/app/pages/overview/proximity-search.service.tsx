import { Injectable, inject } from '@angular/core';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { FuzzySearch } from '@lib/common/fuzzy-search';
import { h, render } from 'preact';
import { useState } from 'preact/hooks';
import { firstValueFrom } from 'rxjs';
import { VirtualList } from './virtual-scroll';

@Injectable({ providedIn: 'root' })
export class ProximitySearchService {
  readonly #alertModalService = inject(AlertModalService);

  async openList(dataArray: any[], key: string) {
    const fuzzy = new FuzzySearch(dataArray, [key], { sort: true });
    const ref = this.#alertModalService.openModal(
      {
        heading: 'Search School',
        domClass: 'h-full w-full',
        dom: async (root, onClose) => {
          const SearchComponent = () => {
            const [value, setValue] = useState(fuzzy.search(''));
            return (
              <div class="flex flex-col gap-4 h-full">
                <label
                  class="input input-bordered input-primary flex items-center gap-2"
                  style="outline: none"
                >
                  <input
                    type="text"
                    class="grow"
                    placeholder="Search"
                    onInput={(e: any) => setValue(fuzzy.search(e.target.value))}
                  />
                  <span class="material-symbols-outlined text-primary">search</span>
                </label>
                <div class="grow">
                  <VirtualList
                    items={value}
                    itemHeight={40}
                    height={400}
                    renderItem={(school) => (
                      <div
                        class="px-3 h-full flex items-center hover:bg-primary hover:text-primary-content cursor-pointer border-b border-gray-200 transition-colors"
                        onClick={() => ref.close(school)}
                      >
                        <p class="font-medium text-sm">{school[key]}</p>
                      </div>
                    )}
                  />
                </div>
              </div>
            );
          };

          render(<SearchComponent />, root);
          await onClose;
          render(null, root);
        },
        noBtn: true,
      },
      { width: '600px', height: '600px' },
    );
    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    if (!result) return;
    return result;
  }
}
