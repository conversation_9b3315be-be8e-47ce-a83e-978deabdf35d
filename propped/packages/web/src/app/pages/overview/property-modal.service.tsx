import { Injectable, inject } from '@angular/core';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { SnackBarService } from '@lib/angular/snack-bar.service';
import { Chart, registerables } from 'chart.js';
import { h, render } from 'preact';
import { useEffect, useRef, useState } from 'preact/hooks';
import { firstValueFrom } from 'rxjs';
import { DualChartViewerService } from '../../component/data-chart/dual-data-chart.component';

// Register Chart.js components
Chart.register(...registerables);

@Injectable({ providedIn: 'root' })
export class BlockDetailsModalService {
  readonly #alertModalService = inject(AlertModalService);
  readonly #snackBarService = inject(SnackBarService);
  readonly #dualChartViewerService = inject(DualChartViewerService);

  async open(
    blockDetails: any,
    tableData?: any,
    transactionMedianPrices?: any,
    transactionAveragePSF?: any,
  ) {
    const ref = this.#alertModalService.openModal(
      {
        heading: 'Property Details',
        domClass: 'h-screen max-h-screen overflow-y-auto',
        dom: async (root, onClose) => {
          const BlockDetailsComponent = () => {
            const [showTransactions, setShowTransactions] = useState(false);
            const [showChart, setShowChart] = useState(false);
            const transactionTableData = tableData;
            const medianChartRef = useRef<HTMLCanvasElement>(null);
            const psfChartRef = useRef<HTMLCanvasElement>(null);

            // Charts instances
            const [medianChart, setMedianChart] = useState<Chart | null>(null);
            const [psfChart, setPsfChart] = useState<Chart | null>(null);

            const handleShowTransactions = () => {
              if (transactionTableData?.data.length > 0) {
                setShowTransactions(true);
              } else {
                this.#snackBarService.error('No transactions found for this property');
              }
            };

            const handleShowChart = () => {
              // if (!showChart) {
              //   setShowChart(true);
              //   this.#dualChartViewerService.open({
              //     heading: 'HDB Transactions - Price & PSF Trend',
              //     header: ['Date', 'Median Price', 'Average PSF'],
              //     data: transactionMedianPrices.map((entry, index) => {
              //       const psfEntry = transactionAveragePSF[index];
              //       return [entry[0], entry[1], psfEntry[1]];
              //     }),
              //   });
              // }
              setShowChart(true);
            };

            const drawCharts = () => {
              if (!medianChartRef.current || !psfChartRef.current) return;

              // Destroy previous charts if they exist
              if (medianChart) medianChart.destroy();
              if (psfChart) psfChart.destroy();

              const chartData = transactionMedianPrices.map((entry, index) => {
                const psfEntry = transactionAveragePSF[index];
                return [entry[0], entry[1], psfEntry[1]];
              });

              const labels = chartData.map((d: any) => d[0]);
              const medianPrices = chartData.map((d: any) => d[1]);
              const averagePSFs = chartData.map((d: any) => d[2]);

              const newMedianChart = new Chart(medianChartRef.current, {
                type: 'line',
                data: {
                  labels,
                  datasets: [
                    {
                      label: 'Median Price',
                      data: medianPrices,
                      fill: false,
                      tension: 0.2,
                      borderColor: '#4f46e5',
                      backgroundColor: 'rgba(79, 70, 229, 0.2)',
                    },
                  ],
                },
                options: { responsive: true, scales: { y: { beginAtZero: false } } },
              });

              const newPsfChart = new Chart(psfChartRef.current, {
                type: 'line',
                data: {
                  labels,
                  datasets: [
                    {
                      label: 'Average PSF',
                      data: averagePSFs,
                      fill: false,
                      tension: 0.2,
                      borderColor: '#10b981',
                      backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    },
                  ],
                },
                options: { responsive: true, scales: { y: { beginAtZero: false } } },
              });

              setMedianChart(newMedianChart);
              setPsfChart(newPsfChart);
            };

            useEffect(() => {
              if (showChart && medianChartRef.current && psfChartRef.current) {
                console.log('DRAWING CHARTS');
                drawCharts();
              }

              return () => {
                if (medianChart) medianChart.destroy();
                if (psfChart) psfChart.destroy();
              };
            }, [showChart]);

            return (
              <div class="flex flex-col gap-4 h-full max-h-[90vh] p-6 border rounded-lg shadow-lg overflow-y-auto relative">
                <h2 class="text-xl font-bold">Block {blockDetails.blockNumber}</h2>
                <p class="text-sm font-semibold">{blockDetails.hdbTown}</p>
                <p class="text-sm italic">{blockDetails?.projectName || 'N/A'}</p>
                <div class="border-t border-gray-300 my-2" />
                <p class="text-sm">
                  <strong>Street:</strong> {blockDetails.streetAddress}
                </p>
                <p class="text-sm">
                  <strong>Postal Code:</strong> {blockDetails.postalCode}
                </p>
                <p class="text-sm">
                  <strong>Lease Start:</strong> {blockDetails?.leaseStart || 'N/A'}
                </p>
                <div class="border-t border-gray-300 my-2" />
                <p class="text-sm">
                  <strong>{blockDetails.totalFloors}</strong> Floors
                </p>
                <p class="text-sm">
                  <strong>{blockDetails.unitsPerFloor}</strong> Units Per Floor
                </p>
                <p class="text-sm font-semibold">
                  Total <strong>{blockDetails.totalUnits}</strong> Units
                </p>
                <div className="mt-2 p-4 border rounded-lg shadow-sm">
                  <p className="font-semibold text-sm mb-2">
                    Ethnic Query{' '}
                    <span className="italic text-gray-500">
                      (Source: <strong>HDB</strong>)
                    </span>
                    :
                  </p>

                  <div className="text-xs space-y-4">
                    {/* Singaporean Citizen / Malaysian SPR */}
                    <div>
                      <p className="font-semibold">Singaporean Citizen / Malaysian SPR</p>
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>
                          <span className="italic font-medium">Chinese:</span>
                          <br />
                          <span className="italic">
                            You can only buy from Chinese flat sellers, regardless of their
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span className="italic font-medium">Malay:</span>
                          <br />
                          <span className="italic">
                            You can buy from any flat seller, regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span className="italic font-medium">
                            Indian and Other Ethnic Groups:
                          </span>
                          <br />
                          <span className="italic">
                            You can buy from any flat seller regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                      </ul>
                    </div>

                    {/* Non-Malaysian SPR */}
                    <div>
                      <p className="font-semibold">Non-Malaysian SPR</p>
                      <ul className="ml-4 mt-1 space-y-1">
                        <li>
                          <span className="italic font-medium">Chinese:</span>
                          <br />
                          <span className="italic">
                            You can only buy from Chinese flat sellers, regardless of their
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span className="italic font-medium">Malay:</span>
                          <br />
                          <span className="italic">
                            You can buy from any flat seller, regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                        <li>
                          <span className="italic font-medium">
                            Indian and Other Ethnic Groups:
                          </span>
                          <br />
                          <span className="italic">
                            You can buy from any flat seller regardless of their ethnic group and
                            citizenship.
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                {blockDetails.floorPlans?.length && (
                  <div class="mt-4">
                    <p class="font-semibold text-sm">Floor Plans:</p>
                    <img
                      src={blockDetails.floorPlans[0]}
                      class="mt-2 border rounded-lg w-40 shadow-md"
                    />
                  </div>
                )}
                <button
                  type="button"
                  class="mt-4 p-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-all"
                  onClick={() => {
                    console.log('Show past transactions', transactionTableData);
                    handleShowTransactions();
                  }}
                >
                  Show Past Transactions
                </button>
                {showTransactions && (
                  <div class="fixed top-4 left-[90%] h-auto bg-white rounded-lg shadow-lg p-4 z-50 border border-gray-300 overflow-auto md:h-[500px]">
                    <div class="flex justify-between items-center mb-4">
                      <h3 class="font-bold text-lg">{transactionTableData.heading}</h3>
                      <button
                        onClick={() => setShowTransactions(false)}
                        class="text-gray-500 hover:text-red-500 text-lg font-bold"
                      >
                        ✖
                      </button>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="w-full text-left border-collapse text-sm">
                        <thead>
                          <tr class="bg-gray-100">
                            {transactionTableData.header.map((col) => (
                              <th class="px-3 py-2 font-semibold border">{col}</th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {transactionTableData.data.map((row) => (
                            <tr class="border hover:bg-gray-50 transition-all">
                              {row.map((cell) => (
                                <td class="px-3 py-2 border">{cell}</td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    <div class="mt-4 flex justify-end">
                      {!showChart && (
                        <button
                          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
                          onClick={handleShowChart}
                        >
                          Show Chart
                        </button>
                      )}
                    </div>
                    {showChart && (
                      <div>
                        <div class="flex justify-between items-center mb-4">
                          <h3 class="text-lg font-semibold">Price & PSF Trends</h3>
                          <button
                            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            onClick={() => setShowChart(false)}
                          >
                            ✕
                          </button>
                        </div>
                        <div class="grow border rounded p-3 shadow-md mb-4">
                          <div class="text-sm font-bold text-center mb-2">Monthly Median Price</div>
                          <canvas ref={medianChartRef} class="w-full"></canvas>
                        </div>
                        <div class="grow border rounded p-3 shadow-md">
                          <div class="text-sm font-bold text-center mb-2">Monthly Average PSF</div>
                          <canvas ref={psfChartRef} class="w-full"></canvas>
                        </div>
                        {/* <button
                        class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-all"
                        onClick={() => setShowChart(false)}
                      >
                        Hide Chart
                      </button> */}
                      </div>
                    )}
                  </div>
                )}
                {/* {showChart && (
                  <div class="fixed bottom-0 left-[10%] w-full overflow-y-auto h-[400px] bg-white dark:bg-gray-800 z-10 flex flex-col p-4">
                    <div class="flex justify-between items-center mb-4">
                      <h3 class="text-lg font-semibold">Price & PSF Trends</h3>
                      <button
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        onClick={() => setShowChart(false)}
                      >
                        ✕
                      </button>
                    </div>
                    <div class="grow border rounded p-3 shadow-md mb-4">
                      <div class="text-sm font-bold text-center mb-2">Monthly Median Price</div>
                      <canvas ref={medianChartRef} class="w-full"></canvas>
                    </div>
                    <div class="grow border rounded p-3 shadow-md">
                      <div class="text-sm font-bold text-center mb-2">Monthly Average PSF</div>
                      <canvas ref={psfChartRef} class="w-full"></canvas>
                    </div>
                  </div>
                )} */}
              </div>
            );
          };

          render(<BlockDetailsComponent />, root);
          await onClose;
          render(null, root);
        },
        noBtn: true,
      },
      { width: '450px', height: 'full' },
    );

    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    return result as { showTransactions: boolean };
  }

  async openCondo(
    property: any,
    tableData?: any,
    transactionMedianPrices?: any,
    transactionAveragePSF?: any,
  ) {
    const ref = this.#alertModalService.openModal(
      {
        heading: 'Property Details',
        domClass: 'h-full w-full',
        dom: async (root, onClose) => {
          const PropertyDetailsComponent = () => {
            const [showTransactions, setShowTransactions] = useState(tableData ? false : true);
            const [showChart, setShowChart] = useState(false);
            const transactionTableData = tableData;

            // Use Preact refs for chart canvases
            const medianChartRef = useRef<HTMLCanvasElement>(null);
            const psfChartRef = useRef<HTMLCanvasElement>(null);

            // Charts instances
            const [medianChart, setMedianChart] = useState<Chart | null>(null);
            const [psfChart, setPsfChart] = useState<Chart | null>(null);

            const handleShowTransactions = () => {
              if (transactionTableData?.data.length > 0) {
                console.log('TRANSACTION DATA AVAILABLE', transactionTableData);
                setShowTransactions(true);
              } else {
                this.#snackBarService.error('No transactions found for this property');
              }
            };

            const handleShowChart = () => {
              setShowChart(true);
            };

            const drawCharts = () => {
              if (!medianChartRef.current || !psfChartRef.current) return;

              // Destroy previous charts if they exist
              if (medianChart) medianChart.destroy();
              if (psfChart) psfChart.destroy();

              const chartData = transactionMedianPrices.map((entry, index) => {
                const psfEntry = transactionAveragePSF[index];
                return [entry[0], entry[1], psfEntry[1]];
              });

              const labels = chartData.map((d: any) => d[0]);
              const medianPrices = chartData.map((d: any) => d[1]);
              const averagePSFs = chartData.map((d: any) => d[2]);

              const newMedianChart = new Chart(medianChartRef.current, {
                type: 'line',
                data: {
                  labels,
                  datasets: [
                    {
                      label: 'Median Price',
                      data: medianPrices,
                      fill: false,
                      tension: 0.2,
                      borderColor: '#4f46e5',
                      backgroundColor: 'rgba(79, 70, 229, 0.2)',
                    },
                  ],
                },
                options: { responsive: true, scales: { y: { beginAtZero: false } } },
              });

              const newPsfChart = new Chart(psfChartRef.current, {
                type: 'line',
                data: {
                  labels,
                  datasets: [
                    {
                      label: 'Average PSF',
                      data: averagePSFs,
                      fill: false,
                      tension: 0.2,
                      borderColor: '#10b981',
                      backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    },
                  ],
                },
                options: { responsive: true, scales: { y: { beginAtZero: false } } },
              });

              setMedianChart(newMedianChart);
              setPsfChart(newPsfChart);
            };

            // Draw charts when they should be displayed and refs are available
            useEffect(() => {
              if (
                showChart &&
                medianChartRef.current &&
                psfChartRef.current &&
                transactionMedianPrices &&
                transactionAveragePSF
              ) {
                console.log('DRAWING CHARTS');
                drawCharts();
              }

              // Cleanup function to destroy charts when component unmounts
              return () => {
                if (medianChart) medianChart.destroy();
                if (psfChart) psfChart.destroy();
              };
            }, [showChart]);

            return (
              <div class="flex flex-col gap-4 h-full max-h-[90vh] p-6 border rounded-lg shadow-lg overflow-y-auto relative">
                <h2 class="text-xl font-bold text-base-content">{property.projectName}</h2>
                <p class="text-sm font-semibold">Condominium</p>
                <div class="border-t border-gray-300 my-2" />
                <p class="text-sm">
                  <strong>Street:</strong> {property.address}
                </p>
                <p class="text-sm">
                  <strong>District:</strong> {property.district}
                </p>
                <p class="text-sm">
                  <strong>Completed:</strong> {property?.yearOfCompletion || 'N/A'}
                </p>
                <p class="text-sm">
                  <strong>Tenure:</strong> {property?.tenure || 'N/A'}
                </p>
                <p class="text-sm font-semibold">
                  Total <strong>{property.totalUnits}</strong> Units
                </p>

                {property.floorPlans && property.floorPlans.length > 0 && (
                  <div class="mt-4">
                    <p class="font-semibold text-sm">Floor Plans:</p>
                    <img
                      src={property.floorPlans[0]}
                      class="mt-2 border rounded-lg w-40 shadow-md"
                    />
                  </div>
                )}
                <button
                  type="button"
                  class="mt-4 p-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-all"
                  onClick={handleShowTransactions}
                >
                  Show Past Transactions
                </button>
                {showTransactions && transactionTableData?.data.length > 0 && (
                  <div class="fixed top-[4%] left-[90%] h-auto bg-white rounded-lg shadow-lg p-4 z-50 border border-gray-300 overflow-auto md:h-[500px]">
                    <div class="flex justify-between items-center mb-4">
                      <h3 class="font-bold text-lg">{transactionTableData?.heading}</h3>
                      <button
                        onClick={() => setShowTransactions(false)}
                        class="text-gray-500 hover:text-red-500 text-lg font-bold"
                      >
                        ✖
                      </button>
                    </div>
                    <div class="overflow-x-auto">
                      <table class="w-full text-left border-collapse text-sm">
                        <thead>
                          <tr class="bg-gray-100">
                            {transactionTableData?.header.map((col) => (
                              <th class="px-3 py-2 font-semibold border">{col}</th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {transactionTableData?.data.map((row) => (
                            <tr class="border hover:bg-gray-50 transition-all">
                              {row.map((cell) => (
                                <td class="px-3 py-2 border">{cell}</td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    <div class="mt-4 flex justify-end">
                      {!showChart && (
                        <button
                          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-all"
                          onClick={handleShowChart}
                        >
                          Show Chart
                        </button>
                      )}
                    </div>
                    {showChart && (
                      <div>
                        <div class="flex justify-between items-center mb-4">
                          <h3 class="text-lg font-semibold">Price & PSF Trends</h3>
                          <button
                            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            onClick={() => setShowChart(false)}
                          >
                            ✕
                          </button>
                        </div>
                        <div class="grow border rounded p-3 shadow-md mb-4">
                          <div class="text-sm font-bold text-center mb-2">Monthly Median Price</div>
                          <canvas ref={medianChartRef} class="w-full"></canvas>
                        </div>
                        <div class="grow border rounded p-3 shadow-md">
                          <div class="text-sm font-bold text-center mb-2">Monthly Average PSF</div>
                          <canvas ref={psfChartRef} class="w-full"></canvas>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          };

          render(<PropertyDetailsComponent />, root);
          await onClose;
          render(null, root);
        },
        noBtn: true,
      },
      { width: '450px', height: 'full' },
    );

    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    return result as { showTransactions: boolean };
  }
}
