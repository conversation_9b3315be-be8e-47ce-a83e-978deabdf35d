import { h } from 'preact';
import { useState } from 'preact/hooks';

export const VirtualList = ({
  items,
  itemHeight,
  height,
  renderItem,
}: {
  items: any[];
  itemHeight: number;
  height: number;
  renderItem: (item: any, index: number) => any;
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.floor(scrollTop / itemHeight);
  const visibleCount = Math.ceil(height / itemHeight);
  const endIndex = Math.min(items.length - 1, startIndex + visibleCount);
  const visibleItems = items.slice(startIndex, endIndex + 1);
  const onScroll = (e: Event) => setScrollTop((e.target as HTMLDivElement).scrollTop);

  return (
    <div style={{ 'overflow-y': 'auto', height: `${height}px` }} onScroll={onScroll}>
      <div style={{ position: 'relative', height: `${totalHeight}px` }}>
        {visibleItems.map((item, i) => {
          const index = startIndex + i;
          return (
            <div
              key={index}
              style={{
                position: 'absolute',
                top: `${index * itemHeight}px`,
                height: `${itemHeight}px`,
                width: '100%',
              }}
            >
              {renderItem(item, index)}
            </div>
          );
        })}
      </div>
    </div>
  );
};
