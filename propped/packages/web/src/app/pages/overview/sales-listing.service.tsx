import { Injectable, inject } from '@angular/core';
import { AlertModalService } from '@lib/angular/alert-modal.service';
import { h, render } from 'preact';
import { firstValueFrom } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SalesServiceModal {
  readonly #alertModalService = inject(AlertModalService);

  async open(blockDetails: any) {
    const ref = this.#alertModalService.openModal(
      {
        heading: `Property Details`,
        domClass: 'h-screen max-h-screen overflow-y-auto',
        dom: async (root, onClose) => {
          const BlockDetailsComponent = () => {
            return (
              <div class="flex flex-col gap-4 h-full max-h-[90vh] p-6 border rounded-lg shadow-lg bg-white overflow-y-auto">
                {/* Scrollable content */}
                <div class="">
                  <h2 class="text-2xl font-semibold text-gray-900">
                    Price: ${blockDetails.salesPrice}
                  </h2>

                  <div class="mt-2 mb-3">
                    <img
                      src="/assets/dummy_prop.png"
                      alt="Property Image"
                      class="w-full h-auto rounded-lg shadow-md"
                    />
                  </div>

                  <h2 class="text-xl font-bold text-gray-900">Block: {blockDetails.blockNo}</h2>
                  <p class="text-md text-gray-700 font-semibold">{blockDetails.hdbTown}</p>
                  <p class="text-sm text-gray-600 italic">{blockDetails?.projectName || 'N/A'}</p>

                  <div class="border-t border-gray-200 my-2"></div>

                  <div class="flex flex-col gap-1">
                    <p class="text-sm text-gray-700">
                      <strong>Street:</strong> {blockDetails.street}
                    </p>
                    <p class="text-sm text-gray-700">
                      <strong>Postal Code:</strong> {blockDetails.postalCode}
                    </p>
                    <p class="text-sm text-gray-700">
                      <strong>Lease Start:</strong> {blockDetails?.leaseStart || 'N/A'}
                    </p>
                  </div>

                  <div class="border-t border-gray-200 my-2"></div>

                  <div class="flex flex-col gap-1">
                    <p class="text-sm">
                      <strong>{blockDetails.totalFloors}</strong> Floors
                    </p>
                    <p class="text-sm">
                      <strong>{blockDetails.unitsPerFloor}</strong> Units Per Floor
                    </p>
                    <p class="text-sm font-semibold text-gray-900">
                      Total <strong>{blockDetails.totalUnits}</strong> Units
                    </p>
                  </div>

                  <div class="mt-2 p-3 border rounded-lg bg-gray-50 shadow-sm">
                    <p class="font-semibold text-sm mb-2">
                      Ethnic Query{' '}
                      <span class="italic text-gray-500">
                        (Source: <strong>HDB</strong>)
                      </span>
                      :
                    </p>

                    <div class="text-xs text-gray-700 space-y-4">
                      <div>
                        <p class="font-bold text-gray-800 underline">
                          Singaporean Citizen / Malaysian SPR
                        </p>
                        <ul class="ml-4 mt-1 space-y-1">
                          <li>
                            <span class="italic font-semibold">Chinese:</span>
                            <br />
                            <span class="italic">
                              You can only buy from Chinese flat sellers, regardless of their
                              citizenship.
                            </span>
                          </li>
                          <li>
                            <span class="italic font-semibold">Malay:</span>
                            <br />
                            <span class="italic">
                              You can buy from any flat seller, regardless of their ethnic group and
                              citizenship.
                            </span>
                          </li>
                          <li>
                            <span class="italic font-semibold">
                              Indian and Other Ethnic Groups:
                            </span>
                            <br />
                            <span class="italic">
                              You can buy from any flat seller regardless of their ethnic group and
                              citizenship.
                            </span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <p class="font-bold text-gray-800 underline">Non-Malaysian SPR</p>
                        <ul class="ml-4 mt-1 space-y-1">
                          <li>
                            <span class="italic font-semibold">Chinese:</span>
                            <br />
                            <span class="italic">
                              You can only buy from Chinese flat sellers, regardless of their
                              citizenship.
                            </span>
                          </li>
                          <li>
                            <span class="italic font-semibold">Malay:</span>
                            <br />
                            <span class="italic">
                              You can buy from any flat seller, regardless of their ethnic group and
                              citizenship.
                            </span>
                          </li>
                          <li>
                            <span class="italic font-semibold">
                              Indian and Other Ethnic Groups:
                            </span>
                            <br />
                            <span class="italic">
                              You can buy from any flat seller regardless of their ethnic group and
                              citizenship.
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {blockDetails.floorPlans?.length > 0 && (
                    <div class="mt-4">
                      <p class="font-semibold text-sm">Floor Plans:</p>
                      <img
                        src={blockDetails.floorPlans[0]}
                        class="mt-2 border rounded-lg w-48 shadow-md"
                      />
                    </div>
                  )}
                </div>

                {/* Sticky footer button */}
                <div class="p-2 bg-white border-t border-gray-300">
                  <button
                    class="w-full py-3 mb-2 px-4 bg-primary text-white font-semibold rounded-lg transition-all"
                    onClick={() => ref.close({ goToListing: true })}
                  >
                    Past transactions
                  </button>
                  <button
                    class="w-full py-3 px-4 bg-green-600 text-white font-semibold rounded-lg transition-all"
                    onClick={() => ref.close({ goToListing: true })}
                  >
                    Go to Listing
                  </button>
                </div>
              </div>
            );
          };

          render(<BlockDetailsComponent />, root);
          await onClose;
          render(null, root);
        },
        noBtn: true,
      },
      { width: '450px', height: 'full' },
    );

    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    return result;
  }

  async condoSalesOpen(blockDetails: any) {
    const ref = this.#alertModalService.openModal(
      {
        heading: `Property Details`,
        domClass: 'h-screen max-h-screen overflow-y-auto',
        dom: async (root, onClose) => {
          const BlockDetailsComponent = () => {
            return (
              <div class="flex flex-col gap-4 h-full max-h-[90vh] p-6 border rounded-lg shadow-lg bg-white overflow-y-auto">
                {/* Scrollable content */}
                <div class="">
                  <h2 class="text-2xl font-semibold text-gray-900">
                    Price: ${blockDetails.salesPrice}
                  </h2>
                  <div class="mt-2 mb-3">
                    <img
                      src="/assets/dummy_prop.png"
                      alt="Property Image"
                      class="w-full h-auto rounded-lg shadow-md"
                    />
                  </div>
                  <h2 class="text-xl font-bold text-gray-900">Block: {blockDetails.blockNo}</h2>
                  <p class="text-md text-gray-700 font-semibold">{blockDetails.hdbTown}</p>
                  <p class="text-sm text-gray-600 italic">{blockDetails?.projectName || 'N/A'}</p>

                  <div class="border-t border-gray-200 my-2"></div>

                  <div class="flex flex-col gap-1">
                    <p class="text-sm text-gray-700">
                      <strong>Street:</strong> {blockDetails.street}
                    </p>
                    <p class="text-sm text-gray-700">
                      <strong>Postal Code:</strong> {blockDetails.postalCode}
                    </p>
                    <p class="text-sm text-gray-700">
                      <strong>Lease Start:</strong> {blockDetails?.leaseStart || 'N/A'}
                    </p>
                  </div>

                  <div class="border-t border-gray-200 my-2"></div>

                  <div class="flex flex-col gap-1">
                    <p class="text-sm">
                      <strong>{blockDetails.totalFloors}</strong> Floors
                    </p>
                    <p class="text-sm">
                      <strong>{blockDetails.unitsPerFloor}</strong> Units Per Floor
                    </p>
                    <p class="text-sm font-semibold text-gray-900">
                      Total <strong>{blockDetails.totalUnits}</strong> Units
                    </p>
                  </div>
                  {blockDetails.floorPlans?.length > 0 && (
                    <div class="mt-4">
                      <p class="font-semibold text-sm">Floor Plans:</p>
                      <img
                        src={blockDetails.floorPlans[0]}
                        class="mt-2 border rounded-lg w-48 shadow-md"
                      />
                    </div>
                  )}
                </div>

                {/* Sticky footer button */}
                <div class="p-2 bg-white border-t border-gray-300">
                  <button
                    class="w-full py-3 mb-2 px-4 bg-primary text-white font-semibold rounded-lg transition-all"
                    onClick={() => ref.close({ goToListing: true })}
                  >
                    Past transactions
                  </button>
                  <button
                    class="w-full py-3 px-4 bg-green-600 text-white font-semibold rounded-lg transition-all"
                    onClick={() => ref.close({ goToListing: true })}
                  >
                    Go to Listing
                  </button>
                </div>
              </div>
            );
          };

          render(<BlockDetailsComponent />, root);
          await onClose;
          render(null, root);
        },
        noBtn: true,
      },
      { width: '450px', height: 'full' },
    );

    const result = await firstValueFrom(ref.dialogRef.afterClosed());
    return result;
  }
}
