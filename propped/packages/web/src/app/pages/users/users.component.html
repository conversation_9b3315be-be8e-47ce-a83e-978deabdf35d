<div class="p-3 flex flex-col md:flex-row place-content-start items-center">
  <div class="mr-0 flex flex-row place-content-start items-center">
    <mat-icon>person_outline</mat-icon>
    <div class="p-3 text-2xl grow-1">Users</div>
  </div>
  <div class="flex flex-col md:flex-row gap-2 md:ml-auto w-4/5 md:w-auto">
    <silver-search-bar (submitText)="searchResult($event)" />
    <button matTooltip="Add User" class="btn btn-primary" (click)="openUserForm()">
      <mat-icon>person</mat-icon> Add User
    </button>
  </div>
</div>
@if (!users?.length) {
  <div class="grow flex flex-col justify-center items-center mt-20">
    <div class="text-3xl text-error font-semibold">OOPS !</div>
    <div class="text-2xl mt-5">No user found for this match.</div>
  </div>
}
@if (users?.length) {
  <div class="grow flex flex-row flex-wrap justify-center gap-6 content-start mt-3 overflow-auto">
    @for (user of users; track user; let i = $index) {
      <div (click)="userAction(user)" class="zo-general-card">
        @if (user.pic) {
          <img
            class="w-28 h-28 rounded-lg object-cover"
            [ngSrc]="user.pic"
            placeholder
            width="112"
            height="112"
          />
        }
        <div class="mt-3">{{ user.name }}</div>
        <div class="text-sm">({{ rolesNamesById(user.roles) }})</div>
      </div>
    }
  </div>
}

<div>
  <mat-paginator
    class="!bg-transparent mt-3"
    [length]="meta.total"
    [pageSize]="50"
    [pageSizeOptions]="[30, 50, 100]"
  />
</div>
