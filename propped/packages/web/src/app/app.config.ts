import { IMAGE_CONFIG, IMAGE_LOADER, NgOptimizedImage } from '@angular/common';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import {
  type ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withEnabledBlockingInitialNavigation } from '@angular/router';
import { awsImageLoader } from '@lib/angular/aws-image-loader';
import { provideMapboxGL } from 'ngx-mapbox-gl';
import { appRoutes } from './app.routes';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { CUSTOM_DATE_FORMAT } from './shared/date-format.config';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes, withEnabledBlockingInitialNavigation()),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideAnimationsAsync(),
    importProvidersFrom([
      FormsModule,
      MatBottomSheetModule,
      MatDatepickerModule,
      MatDialogModule,
      MatFormFieldModule,
      MatNativeDateModule,
      MatSnackBarModule,
      NgOptimizedImage,
      ReactiveFormsModule,
    ]),
    provideHttpClient(withInterceptors([AuthInterceptor])),
    ...CUSTOM_DATE_FORMAT,
    { provide: IMAGE_LOADER, useValue: awsImageLoader },
    {
      provide: IMAGE_CONFIG,
      useValue: { disableImageSizeWarning: true, disableImageLazyLoadWarning: true },
    },
    provideMapboxGL({
      accessToken:
        'pk.eyJ1IjoiaGFtemE1MjUzIiwiYSI6ImNtYXV5OWZ1OTAxdDMyanNlNTh1OHVnYmkifQ.lqXdIozpjUNnnuWA_ZqHpQ',
    }),
  ],
};
