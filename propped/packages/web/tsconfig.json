{"compilerOptions": {"target": "esnext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "jsx": "react", "jsxFactory": "h"}, "files": [], "include": [], "references": [{"path": "./tsconfig.editor.json"}, {"path": "./tsconfig.app.json"}], "extends": "../../tsconfig.base.json", "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": false, "strictInputAccessModifiers": false, "strictTemplates": false}}