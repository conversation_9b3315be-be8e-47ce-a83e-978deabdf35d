import { createGlobPatternsForDependencies } from '@nx/angular/tailwind';
import daisyuiPlugin from 'daisyui';
import theme from 'daisyui/src/theming/themes';
import { dirname, join, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';

const currentDir = dirname(fileURLToPath(import.meta.url));

export default {
  content: [
    join(currentDir, 'src/**/!(*.stories|*.spec).{ts,tsx,html}'),
    resolve('packages/shared/src/lib/angular/**/!(*.stories|*.spec).{ts,tsx,html}'),
    ...createGlobPatternsForDependencies(currentDir),
  ],
  darkMode: ['selector', '[data-theme="dark"]'],
  theme: {
    extend: {
      colors: {
        'content-background': 'var(--content-background)',
        'leftmenu-background': 'var(--leftmenu-background)',
      },
      fontSize: { xs: '11px', sm: '13px', base: '15px' },
    },
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '2rem',
        md: '3rem',
        lg: '4rem',
        xl: '5rem',
        '2xl': '6rem',
      },
    },
    fontFamily: { body: ['Inter'] },
  },
  daisyui: { themes: [{ light: theme.light, dark: theme.night }], logs: false },
  plugins: [daisyuiPlugin],
};
