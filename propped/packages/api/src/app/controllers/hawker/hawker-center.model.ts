import { type Model, Schema, model } from 'mongoose';

export interface IHawkerCentre {
  name: string;
  description?: string;
  buildingName?: string;
  blockHouseNumber?: number;
  postalCode?: number;
  streetName?: string;
  photoUrl?: string;
  landX?: number;
  landY?: number;
  status?: string;
  addressMyEnv?: string;
  originalCompletionDate?: Date;
  hupCompletionDate?: Date;
  cookedFoodStalls?: number;
  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
  iconName?: string;
}

const HawkerCentreSchema = new Schema<IHawkerCentre>({
  name: { type: String, required: true },
  description: { type: String },
  buildingName: { type: String },
  blockHouseNumber: { type: Number },
  postalCode: { type: Number },
  streetName: { type: String },
  photoUrl: { type: String },
  landX: { type: Number },
  landY: { type: Number },
  status: { type: String },
  addressMyEnv: { type: String },
  originalCompletionDate: { type: Date },
  hupCompletionDate: { type: Date },
  cookedFoodStalls: { type: Number },
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], required: true },
  },

  iconName: { type: String },
});

HawkerCentreSchema.index({ location: '2dsphere' });

export const HawkerCentreModel: Model<IHawkerCentre> = model<IHawkerCentre>(
  'HawkerCentre',
  HawkerCentreSchema,
);
