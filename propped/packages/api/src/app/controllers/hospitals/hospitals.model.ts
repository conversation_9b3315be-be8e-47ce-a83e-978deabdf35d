import { Schema, model, type Document, type Model } from 'mongoose';

export interface IHospital extends Document {
  blkNo: number;
  roadName: string;
  buildingName: string;
  address: string;
  postal: number;
  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
}

const HospitalSchema = new Schema<IHospital>({
  blkNo: { type: Number },
  roadName: { type: String },
  buildingName: { type: String },
  address: { type: String },
  postal: { type: Number },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
    },
  },
});

HospitalSchema.index({ location: '2dsphere' });

export const HospitalModel: Model<IHospital> = model<IHospital>('Hospital', HospitalSchema);
