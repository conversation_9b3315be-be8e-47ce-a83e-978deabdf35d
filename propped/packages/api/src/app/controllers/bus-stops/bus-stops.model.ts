import { type Model, Schema, model } from 'mongoose';

export interface IBusStop {
  name: string;
  details: string;
  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
}

const BusStopSchema = new Schema<IBusStop>({
  name: { type: String, required: true, unique: true },
  details: { type: String },
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], required: true },
  },
});

BusStopSchema.index({ location: '2dsphere' });

export const BusStopModel: Model<IBusStop> = model<IBusStop>('BusStop', BusStopSchema);
