import { and, eq, inArray } from 'drizzle-orm';
import { drizzleDb } from '../../common/db';
import { type UserRoleTableSchema, rolesTable, userRoleTable } from './auth.entity';

export class RolesService {
  public hasRoles() {
    return {
      get: (userId: number) => this.getItems(userId),
      add: (userId: number, roleIds: number[]) => this.addItems(userId, roleIds),
      set: (userId: number, roleIds: number[]) => this.setItems(userId, roleIds),
      remove: (userId: number) => this.removeItems(userId),
    };
  }
  public getItems(userId: number) {
    return drizzleDb
      .select({ id: rolesTable.id, name: rolesTable.name })
      .from(userRoleTable)
      .leftJoin(rolesTable, eq(userRoleTable.roleId, rolesTable.id))
      .where(eq(userRoleTable.userId, userId));
  }

  public addItems(userId: number, roleIds: number[]) {
    const toInsert = roleIds.map((v) => ({ userId, roleId: v }) as UserRoleTableSchema);
    return drizzleDb.insert(userRoleTable).values(toInsert);
  }

  public removeItems(userId: number) {
    return drizzleDb.delete(userRoleTable).where(eq(userRoleTable.userId, userId));
  }

  public async setItems(userId: number, roleIds: number[]) {
    await drizzleDb.transaction(async (trx) => {
      const serverRoles = await trx
        .select({ roleId: userRoleTable.roleId })
        .from(userRoleTable)
        .where(eq(userRoleTable.userId, userId));

      const serverRoleIds = serverRoles.map((v) => v.roleId);
      const toAdd = roleIds.filter((v) => !serverRoleIds.includes(v));
      const toDelete = serverRoleIds.filter((v) => !roleIds.includes(v));

      if (toAdd.length) {
        const roleToInsert = toAdd.map((roleId) => ({ userId, roleId }));
        await trx.insert(userRoleTable).values(roleToInsert);
      }

      if (toDelete.length) {
        await trx
          .delete(userRoleTable)
          .where(and(eq(userRoleTable.userId, userId), inArray(userRoleTable.roleId, toDelete)));
      }
    });
    return true;
  }
}
