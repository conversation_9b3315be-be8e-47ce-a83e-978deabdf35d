import { int, json, mysqlTable, serial, text, timestamp, varchar } from 'drizzle-orm/mysql-core';

// Files table
export const filesTable = mysqlTable('files', {
  id: serial('id').primaryKey(),
  tableId: int('tableId', { unsigned: true }).notNull(),
  itemId: int('itemId', { unsigned: true }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  desc: text('desc'), // nullable field
  size: int('size').notNull(),
  type: varchar('type', { length: 255 }).notNull(),
  path: varchar('path', { length: 255 }).notNull(),
  meta: json('meta'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

export type FileTableSchema = typeof filesTable.$inferSelect;
