import { relations } from 'drizzle-orm';
import {
  boolean,
  int,
  json,
  mysqlTable,
  primaryKey,
  serial,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core';
import { filesTable } from './files.entity';

// Roles table
export const rolesTable = mysqlTable('roles', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Users table
export const usersTable = mysqlTable('users', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }),
  username: varchar('username', { length: 255 }).unique('unique_username'),
  email: varchar('email', { length: 255 }).notNull().unique('unique_email'),
  password: varchar('password', { length: 255 }).notNull(),
  pic: int('pic', { unsigned: true }).references(() => filesTable.id), // references files.id
  isActive: boolean('isActive').default(true).notNull(),
  meta: json('meta').$type<{ TFARequire?: boolean }>().notNull(),
  secret: json('secret').$type<{ totp?: string }>().notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

export type UserTableSchema = typeof usersTable.$inferSelect;

export const userRoleTable = mysqlTable(
  'user_role',
  {
    userId: int('userId', { unsigned: true })
      .notNull()
      .references(() => usersTable.id, { onDelete: 'cascade' }),
    roleId: int('roleId', { unsigned: true })
      .notNull()
      .references(() => rolesTable.id, { onDelete: 'cascade' }),
    meta: json('meta'),
  },
  (table) => [primaryKey({ columns: [table.userId, table.roleId] })],
);

export type UserRoleTableSchema = typeof userRoleTable.$inferSelect;

export const userTokenTable = mysqlTable('user_token', {
  email: varchar('email', { length: 255 }).notNull().primaryKey(),
  token: varchar('token', { length: 6 }).notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

export const usersRolesRelations = relations(userRoleTable, ({ one }) => ({
  role: one(rolesTable, { fields: [userRoleTable.roleId], references: [rolesTable.id] }),
  user: one(usersTable, { fields: [userRoleTable.userId], references: [usersTable.id] }),
}));
