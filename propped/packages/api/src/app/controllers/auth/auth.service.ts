import { HttpException } from '@lib/common/common.error';
import { ROLES } from '@lib/common/const/roles';
import { HttpStatus } from '@lib/common/http-status';
import { addMinutes, isBefore } from 'date-fns';
import { eq, getTableColumns, or, sql } from 'drizzle-orm';
import { decode, sign } from 'jsonwebtoken';
import * as OTPAuth from 'otpauth';
import { DBI } from '../../../environments/env-modules';
import { environment } from '../../../environments/environment';
import { badRequestException, unauthorizedException } from '../../../errors/common.error';
import { ActiveUser } from '../../common/active-user';
import { drizzleDb } from '../../common/db';
import { DrizzleHelper } from '../../common/drizzle-helper';
import { parseMarkDown } from '../../common/marked';
import { TABLES } from '../../common/tables.const';
import { DefaultEncryptionEngine } from '../../services/encryption.service';
import { FileMgrService } from '../../services/file-mgr.service';
import { PasswordHashEngine } from '../../services/hash.service';
import { MailBuilderService, MailClientService } from '../../services/mail.service';
import { MySQLJsonService } from '../../services/mysql-json.service';
import { inject } from '../../services/services.register';
import { type UserTableSchema, userRoleTable, userTokenTable, usersTable } from './auth.entity';
import { RolesService } from './roles.service';

export const EmailAlreadyExistError = badRequestException('email already exist');
export const UserNameAlreadyExistError = badRequestException('username already exist');

export const genOTP = (length: number) => {
  const chars = '2346789ABDEFGHJKMNPQRTUVWXYZ';
  let otp = '';
  for (let i = 0; i < length; i++) {
    otp += chars[Math.floor(Math.random() * chars.length)];
  }
  return otp;
};

export const UserCacheBox = new Map<number, any>();

export class AuthService {
  readonly #fileMgrService = inject(FileMgrService);
  readonly #mailBuilderService = inject(MailBuilderService);
  readonly #mailClientService = inject(MailClientService);
  readonly #mySQLJsonService = inject(MySQLJsonService);
  readonly #rolesService = inject(RolesService);

  public readonly table = TABLES.USERS;

  readonly roles = this.#rolesService.hasRoles();
  readonly thumbnail = this.#fileMgrService.hasThumbnail(this.table);
  readonly jsonCol = this.#mySQLJsonService.hasJSONCol(this.table);

  async searchUserForAuth(usernameOrEmail: string) {
    usernameOrEmail = usernameOrEmail.toLowerCase();
    const [user] = await drizzleDb
      .select()
      .from(usersTable)
      .where(or(eq(usersTable.username, usernameOrEmail), eq(usersTable.email, usernameOrEmail)))
      .limit(1);
    return user;
  }

  async validateUser(credentials: { username: string; password: string }) {
    const user = await this.searchUserForAuth(credentials.username);
    if (!user) throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    const isPwdMatch = await PasswordHashEngine.check(credentials.password, user.password);
    if (!isPwdMatch) throw unauthorizedException('Password not match');
    return user;
  }

  login(user: UserTableSchema, device?: string) {
    const payload = { username: user.username, sub: user.id };
    return {
      accessToken: sign(payload, user.password + environment.auth.secret, {
        expiresIn:
          device === 'android'
            ? environment.auth.expireIn.android
            : device === 'ios'
              ? environment.auth.expireIn.ios
              : environment.auth.expireIn.web,
      }),
    };
  }

  async getUserWithRoles(userId: number) {
    const [user] = await drizzleDb
      .select({
        ...getTableColumns(usersTable),
        roleIds: sql<number[]>`JSON_ARRAYAGG(${userRoleTable.roleId})`.as('roleIds'),
      })
      .from(usersTable)
      .leftJoin(userRoleTable, eq(userRoleTable.userId, usersTable.id))
      .where(eq(usersTable.id, userId))
      .groupBy(usersTable.id)
      .limit(1);
    return user;
  }

  getToken(jwtToken: string) {
    try {
      const data: any = decode(jwtToken);
      if (!data) return;
      const userId = Number(data.sub);
      if (!userId) return;
      return this.getUserWithRoles(userId);
    } catch {
      return;
    }
  }

  async verifyToken(token: { email: string; token: string }) {
    const userToken = await DrizzleHelper.findOne(userTokenTable, {
      where: { email: token.email },
    });
    return (
      userToken &&
      userToken.token === token.token &&
      isBefore(new Date(), addMinutes(userToken.updatedAt, 10))
    );
  }

  async getTokenInfo(jwtToken: string) {
    const data: any = decode(jwtToken);
    if (!data) return;
    let user: any;
    const userId = Number(data.sub);
    if (!UserCacheBox.has(userId)) {
      user = await this.getUserWithRoles(userId);
      if (!user) return;
      user = new ActiveUser(user);
      UserCacheBox.set(userId, user);
    } else {
      user = UserCacheBox.get(userId);
    }
    return { user, secret: user.password + environment.auth.secret };
  }

  sendForgotPasswordMail(user: UserTableSchema, token: number | string) {
    const markdownContent = [
      'You are receiving this email because we received a password reset request for your account.',
      `#### Reset Password Token is **${token}**`,
      'If you did not request a password reset, no further action is required.',
      '---',
      'Best Regards,',
      `${environment.appName} IT Team`,
    ].join('\n\n');
    const markdownHTML = parseMarkDown(markdownContent);
    const htmlContent = this.#mailBuilderService.build({ content: markdownHTML });
    return this.#mailClientService.send({
      to: { name: user.name, address: user.email },
      subject: 'Password Reset Request',
      html: htmlContent,
      text: markdownContent,
    });
  }

  sendTFAMail(user: UserTableSchema, token: string) {
    const markdownContent = [
      'Before you sign in, we need to verify your identity. Enter the following code on the sign-in page.',
      `# \`\`\`${token}\`\`\``,
      `If you have not recently tried to sign into ${environment.appName}, we recommend changing your password and setting up Two-Factor Authentication to keep your account safe. Your verification code expires after 5 minutes.`,
      '---',
      'Best Regards,',
      `${environment.appName} IT Team`,
    ].join('\n\n');
    const markdownHTML = parseMarkDown(markdownContent);
    const htmlContent = this.#mailBuilderService.build({ content: markdownHTML });
    return this.#mailClientService.send({
      to: { name: user.name, address: user.email },
      subject: 'Verify your Identity',
      html: htmlContent,
      text: markdownContent,
    });
  }

  sendEmailChangeMail(user: UserTableSchema, token: string) {
    const markdownContent = [
      'You are receiving this email because we received a Email reset request for your account.',
      '#### Reset Email Token is',
      `# **${token}**`,
      'If you did not request a Email reset, no further action is required.',
      '---',
      'Best Regards,',
      `${environment.appName} IT Team`,
    ].join('\n\n');
    const markdownHTML = parseMarkDown(markdownContent);
    const htmlContent = this.#mailBuilderService.build({ content: markdownHTML });
    return this.#mailClientService.send({
      to: { name: user.name, address: user.email },
      subject: 'Email Change Request',
      html: htmlContent,
      text: markdownContent,
    });
  }

  sendEmailVerificationMail(email: string, token: number | string) {
    const markdownContent = [
      'You are receiving this email because we received a sign up request for your account.',
      `#### Email Verification Token is **${token}**`,
      'If you did not request for sign up, no further action is required.',
      '---',
      'Best Regards,',
      `${environment.appName} IT Team`,
    ].join('\n\n');
    const markdownHTML = parseMarkDown(markdownContent);
    const htmlContent = this.#mailBuilderService.build({ content: markdownHTML });
    return this.#mailClientService.send({
      to: { name: email, address: email },
      subject: 'Email Verification Request',
      html: htmlContent,
      text: markdownContent,
    });
  }

  isEmailExists(val: string) {
    return DrizzleHelper.isExist(usersTable, 'email', val);
  }

  isUserNameExists(val: string) {
    return DrizzleHelper.isExist(usersTable, 'username', val);
  }

  async createUser(user: any) {
    const isAlreadyExist = await this.isEmailExists(user.email);
    if (isAlreadyExist) throw EmailAlreadyExistError;
    user.password = await PasswordHashEngine.make(user.password);
    user.secret = {};
    const newUser = await DrizzleHelper.create(usersTable, user);
    await this.roles.add(newUser.insertId, user.roleId?.length ? user.roleId : [ROLES.USER.id]);
    return { user: { id: newUser.insertId } };
  }

  async updateUser(id: number, user: Partial<UserTableSchema & { roleId?: number[] }>) {
    user.email = user.email?.toLowerCase();
    user.username = user.username?.toLowerCase();
    UserCacheBox.delete(id);
    const updateData: any = {};
    let isAlreadyExist: any;
    const serverUser = await DrizzleHelper.findOne(usersTable, { where: { id } });
    if (user.email) {
      isAlreadyExist = await this.isEmailExists(user.email);
      if (isAlreadyExist && isAlreadyExist.email !== serverUser.email) throw EmailAlreadyExistError;
      updateData.email = user.email;
    }
    if (user.username) {
      isAlreadyExist = await this.isUserNameExists(user.username);
      if (isAlreadyExist && isAlreadyExist.username !== serverUser.username)
        throw UserNameAlreadyExistError;
      updateData.username = user.username;
    }
    if (user.password) {
      user.password = await PasswordHashEngine.make(user.password);
      updateData.password = user.password;
    }
    if (user.name) updateData.name = user.name;
    if (user.roleId) await this.roles.set(id, user.roleId);
    return DrizzleHelper.update(usersTable, { id }, updateData);
  }

  async isTFARequire(usernameOrEmail: string) {
    usernameOrEmail = usernameOrEmail.toLowerCase();
    const [user] = await drizzleDb
      .select({ TFARequire: sql<string>`${DBI.g('users.meta', 'TFARequire')}`.as('TFARequire') })
      .from(usersTable)
      .where(or(eq(usersTable.username, usernameOrEmail), eq(usersTable.email, usernameOrEmail)))
      .limit(1);
    return { TFARequire: !!(user.TFARequire && user.TFARequire !== 'false') };
  }

  setTOTPSecret(id: number, secret: string) {
    UserCacheBox.delete(id);
    const enc = DefaultEncryptionEngine.encrypt(secret);
    return this.jsonCol.set({ id }, { secret: { totp: enc } });
  }

  removeTOTP(id: number) {
    UserCacheBox.delete(id);
    return this.jsonCol.remove({ id }, { secret: ['totp'] });
  }

  public verifyTOTP(user: UserTableSchema, token: string) {
    const secret = DefaultEncryptionEngine.decrypt(user.secret.totp);
    const totp = new OTPAuth.TOTP({
      issuer: '',
      label: '',
      algorithm: 'SHA1',
      digits: 6,
      period: 30,
      secret,
    });
    const delta = totp.validate({ token, window: 1 });
    return delta !== null;
  }

  setTFARequire(id: number, type: boolean) {
    UserCacheBox.delete(id);
    return this.jsonCol.set({ id }, { meta: { TFARequire: type } });
  }
}
