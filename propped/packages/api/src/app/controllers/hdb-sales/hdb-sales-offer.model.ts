import { Schema, model, type Types } from 'mongoose';

export interface IHDBSalesListingOffer {
  _id?: Types.ObjectId;
  listingId: number; // Reference to HDBSalesListing
  buyerId: number;   // User ID of the buyer
  sellerId: number;  // User ID of the seller
  offerPrice: number;
  status: 'pending' | 'accepted' | 'rejected' | 'countered';
  message?: string; // Optional message from buyer
  createdAt?: Date;
  updatedAt?: Date;
  counterOfferPrice?: number;
  counterOfferMessage?: string;
}

const HDBSalesListingOfferSchema = new Schema<IHDBSalesListingOffer>(
  {
    listingId: { type: Number, required: true },
    buyerId: { type: Number, required: true },
    sellerId: { type: Number, required: true },
    offerPrice: { type: Number, required: true },
    status: { type: String, enum: ['pending', 'accepted', 'rejected', 'countered'], default: 'pending' },
    message: { type: String },
    counterOfferMessage: {type: String},
    counterOfferPrice: {type: Number}
  },
  { timestamps: true, collection: 'HDBSalesListingOffer' }
);

export const HDBSalesListingOfferModel = model<IHDBSalesListingOffer>('HDBSalesListingOffer', HDBSalesListingOfferSchema);
