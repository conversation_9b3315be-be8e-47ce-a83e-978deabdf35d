import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { HDBSalesTransactionModel } from './transaction-resale-progress.model';
import { HDBSalesListingOfferModel } from './hdb-sales-offer.model';
import { HDBSalesListingModel } from './hdb-sales.model';
import { Types } from 'mongoose';

@Auth()
export class HDBSalesTransactionController {
  /**
   * Create a new transaction resale progress document
   * This is typically called when an offer is accepted
   */
  @Compress()
  async createTransaction({
    offerId,
    listingId,
    buyerId,
    sellerId,
    transactionAmount
  }: {
    offerId: string;
    listingId: string;
    buyerId: number;
    sellerId: number;
    transactionAmount: number;
  }) {
    try {
      console.log("Creating new transaction progress", offerId, listingId, buyerId, sellerId);

      // Check if a transaction already exists for this offer
      const existingTransaction = await HDBSalesTransactionModel.findOne({
        offerId: new Types.ObjectId(offerId)
      });

      if (existingTransaction) {
        return {
          data: existingTransaction._id.toString(),
          message: 'Transaction already exists for this offer'
        };
      }

      // Create a new transaction with initial checkpoint (price acceptance) completed
      const transaction = await HDBSalesTransactionModel.create({
        offerId: new Types.ObjectId(offerId),
        listingId: new Types.ObjectId(listingId),
        buyerId,
        sellerId,
        transactionAmount,
        status: 'in_progress',
        checkpoints: {
          priceAcceptance: {
            completed: true,
            completedAt: new Date()
          }
        },
        buyerCheckpoints:{
          makePriceOffer:{
            completed:true,
            completedAt:new Date()
          }
        },
        sellerCheckpoints:{
          findBuyer:{
            completed:true,
            completedAt:new Date()
          }
        }
      });

      return {
        data: transaction._id.toString(),
        message: 'Transaction created successfully'
      };
    } catch (error) {
      console.error('Error creating transaction:', error);
      return { error: 'Failed to create transaction', status: 500 };
    }
  }

  /**
   * Get transaction by buyer ID and listing ID
   */
  @Compress()
  async getTransactionByBuyerAndListing({
    buyerId,
    listingId
  }: {
    buyerId: number;
    listingId: string;
  }) {
    try {
      // Find the transaction
      const transaction = await HDBSalesTransactionModel.findOne({
        buyerId,
        listingId
      }).lean();

      console.log("TRANSACTION", transaction);
      if (!transaction) {
        return {
          data: null,
          message: 'No transaction found for this buyer and listing'
        };
      }

      // Get the offer details
      const offer = await HDBSalesListingOfferModel.findById(transaction.offerId.toString()).lean();

      // Get the listing details
      const listing = await HDBSalesListingModel.findOne({
        _id: transaction.listingId.toString()
      }).lean();

      // Enrich the transaction with offer and listing de tails
      const enrichedTransaction = {
        ...transaction,
        offerDetails: offer || null,
        listingDetails: listing ? {
          _id: listing._id,
          blockNumber: listing.blockNumber,
          street: listing.street,
          town: listing.town,
          price: listing.price,
          salesCondition: listing.salesCondition
        } : null
      };

      return { data: enrichedTransaction };
    } catch (error) {
      console.error('Error fetching transaction:', error);
      return { error: 'Failed to fetch transaction', status: 500 };
    }
  }

  @Compress()
  async getTransactionBySellerAndListing({
    sellerId,
    listingId
  }: {
    sellerId: number;
    listingId: string;
  }) {
    try {
      // Find the transaction
      const transaction = await HDBSalesTransactionModel.findOne({
        sellerId,
        listingId
      }).lean();

      console.log("TRANSACTION", transaction);
      if (!transaction) {
        return {
          data: null,
          message: 'No transaction found for this seller and listing'
        };
      }

      // Get the offer details
      const offer = await HDBSalesListingOfferModel.findById(transaction.offerId.toString()).lean();

      // Get the listing details
      const listing = await HDBSalesListingModel.findOne({
        _id: transaction.listingId.toString()
      }).lean();

      // Enrich the transaction with offer and listing de tails
      const enrichedTransaction = {
        ...transaction,
        offerDetails: offer || null,
        listingDetails: listing ? {
          _id: listing._id,
          blockNumber: listing.blockNumber,
          street: listing.street,
          town: listing.town,
          price: listing.price,
          salesCondition: listing.salesCondition
        } : null
      };

      return { data: enrichedTransaction };
    } catch (error) {
      console.error('Error fetching transaction:', error);
      return { error: 'Failed to fetch transaction', status: 500 };
    }
  }

  /**
   * Update a specific checkpoint in the transaction
   */
  @Compress()
  async updateCheckpoint({
    transactionId,
    checkpoint,
    completed,
    checkpointData
  }: {
    transactionId: string;
    checkpoint: string;
    completed: boolean;
    checkpointData?: Record<string, any>;
  }) {
    try {
      const updateData: any = {
        [`checkpoints.${checkpoint}.completed`]: completed,
      };

      // If completing the checkpoint, add completion date
      if (completed) {
        updateData[`checkpoints.${checkpoint}.completedAt`] = new Date();
      }

      // Add any additional checkpoint-specific data
      if (checkpointData) {
        Object.keys(checkpointData).forEach(key => {
          updateData[`checkpoints.${checkpoint}.${key}`] = checkpointData[key];
        });
      }

      const transaction = await HDBSalesTransactionModel.findByIdAndUpdate(
        transactionId,
        { $set: updateData },
        { new: true }
      );

      return {
        data: transaction,
        message: `Checkpoint ${checkpoint} updated successfully`
      };
    } catch (error) {
      console.error('Error updating checkpoint:', error);
      return { error: 'Failed to update checkpoint', status: 500 };
    }
  }

  /**
   * Get all transactions for a buyer
   */
  @Compress()
  async getTransactionsByBuyer({ buyerId }: { buyerId: number }) {
    try {
      const transactions = await HDBSalesTransactionModel.find({
        buyerId
      }).sort({ createdAt: -1 }).lean();

      // Get all listing IDs from the transactions
      const listingIds = [...new Set(transactions.map(transaction => transaction.listingId))];

      // Fetch the listings
      const listings = await HDBSalesListingModel.find({
       _id: { $in: listingIds }
      }).lean();

      // Enrich the transactions with listing details
      const enrichedTransactions = transactions.map(transaction => {
        const listing = listings.find(l => l._id === transaction.listingId);
        return {
          ...transaction,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            price: listing.price,
            salesCondition: listing.salesCondition
          } : null
        };
      });

      return { data: enrichedTransactions };
    } catch (error) {
      console.error('Error fetching buyer transactions:', error);
      return { error: 'Failed to fetch buyer transactions', status: 500 };
    }
  }

  /**
   * Get all transactions for a seller
   */
  @Compress()
  async getTransactionsBySeller({ sellerId }: { sellerId: number }) {
    try {
      const transactions = await HDBSalesTransactionModel.find({
        sellerId
      }).sort({ createdAt: -1 }).lean();

      // Get all listing IDs from the transactions
      const listingIds = [...new Set(transactions.map(transaction => transaction.listingId))];

      // Fetch the listings
      const listings = await HDBSalesListingModel.find({
        _id: { $in: listingIds }
      }).lean();

      // Enrich the transactions with listing details
      const enrichedTransactions = transactions.map(transaction => {
        const listing = listings.find(l => l._id === transaction.listingId);
        return {
          ...transaction,
          listingDetails: listing ? {
            _id: listing._id,
            blockNumber: listing.blockNumber,
            street: listing.street,
            town: listing.town,
            price: listing.price,
            salesCondition: listing.salesCondition
          } : null
        };
      });

      return { data: enrichedTransactions };
    } catch (error) {
      console.error('Error fetching seller transactions:', error);
      return { error: 'Failed to fetch seller transactions', status: 500 };
    }
  }
}
