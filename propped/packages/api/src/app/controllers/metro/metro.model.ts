import { Schema, model, type Document, type Model } from 'mongoose';

export interface IMetroStation extends Document {
  alphaNumericCode: string;
  stationName: string;
  roadName: string;
  buildingName: string;
  postal: number;
  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
}

const MetroStationSchema = new Schema<IMetroStation>({
  alphaNumericCode: { type: String },
  stationName: { type: String },
  roadName: { type: String },
  buildingName: { type: String },
  postal: { type: Number },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
    },
  },
});

MetroStationSchema.index({ location: '2dsphere' });

export const MetroStationModel: Model<IMetroStation> = model<IMetroStation>(
  'MetroStation',
  MetroStationSchema,
);
