import { Schema, type Types, model } from 'mongoose';

interface SellerEthnicData {
  details: string;
  summary: string;
}

export interface IHDBData {
  _id?: Types.ObjectId;
  title?: string;
  blockNumber?: string;
  hdbTown?: string;
  hdbEstate?: string;
  streetAddress?: string;
  postalCode?: number;
  projectName?: string;
  projectCode?: string;
  unitNumbers?: number[];
  completionYear?: number;
  completionYearOtherSources?: number;
  yearDemolished?: number;
  leaseCommenceDate?: Date;
  buildingType?: Types.ObjectId[];
  originalNumberOfUnits?: {
    totalUnits?: number;
    flatTypes?: { apartmentType: Types.ObjectId; count: number }[];
  };
  numberOfUnitsByYear2023?: {
    totalUnits?: number;
    flatTypes?: { apartmentType: Types.ObjectId; count: number }[];
  };
  description?: {
    blockEra?: number[];
    buildingType?: string;
    blockNaming?: string;
    sersNotes?: string;
    notes?: string;
  };
  floorPlans?: {
    launchBrochure?: string;
    btoFullBrochure?: string;
    sersFullBrochure?: string;
    possibleFloorPlans?: string[];
  };
  zones?: {
    mndTownCouncil?: string;
    electoralDivision?: string;
  };
  location?: {
    type: 'Point';
    coordinates: [number, number] | null;
  };
  oldDetails?: {
    unitsByFlat?: { apartmentType: Types.ObjectId; count: number }[];
    upgradingProgrammes?: { batch?: string; startDate?: Date; completionDate?: Date }[];
    upgradingList?: string;
  };
  highestFloor?: number;
  totalFloors?: number;
  residentialFloors?: number;
  unitsPerFloor?: number;
  totalUnits?: number;
  sellerEthnicData?: SellerEthnicData[];
}

const HDBDataSchema = new Schema<IHDBData>(
  {
    title: { type: String },
    blockNumber: { type: String },
    hdbTown: { type: String },
    hdbEstate: { type: String },
    streetAddress: { type: String },
    postalCode: { type: Number },
    projectName: { type: String, default: null },
    projectCode: { type: String, default: null },
    unitNumbers: [{ type: Number, default: null }],
    completionYear: { type: Number },
    completionYearOtherSources: { type: Number, default: null },
    yearDemolished: { type: Number, default: null },
    leaseCommenceDate: { type: Date, default: null },

    // Building Type
    buildingType: [{ type: Schema.Types.ObjectId, ref: 'HDBBuildingType', default: null }],

    // Number of Units at the time of construction
    originalNumberOfUnits: {
      totalUnits: { type: Number, default: null },
      flatTypes: [
        {
          apartmentType: { type: Schema.Types.ObjectId, ref: 'ApartmentType' },
          count: { type: Number, default: 0 },
        },
      ],
    },

    // Number of Units as of 2023
    numberOfUnitsByYear2023: {
      totalUnits: { type: Number, default: null },
      flatTypes: [
        {
          apartmentType: { type: Schema.Types.ObjectId, ref: 'ApartmentType' },
          count: { type: Number, default: 0 },
        },
      ],
    },

    // Description
    description: {
      blockEra: [{ type: Number }],
      buildingType: { type: String, default: null },
      blockNaming: { type: String, default: null },
      sersNotes: { type: String, default: null },
      notes: { type: String, default: null },
    },

    // Floor Plans
    floorPlans: {
      launchBrochure: { type: String, default: null },
      btoFullBrochure: { type: String, default: null },
      sersFullBrochure: { type: String, default: null },
      possibleFloorPlans: [{ type: String }],
    },

    // Zones
    zones: {
      mndTownCouncil: { type: String, default: null },
      electoralDivision: { type: String, default: null },
    },

    // Location (GeoJSON format)
    location: {
      type: { type: String, enum: ['Point'], default: 'Point' },
      coordinates: { type: [Number], default: null },
    },

    // Old Details
    oldDetails: {
      unitsByFlat: [
        {
          apartmentType: { type: Schema.Types.ObjectId, ref: 'ApartmentType' },
          count: { type: Number, default: 0 },
        },
      ],
      upgradingProgrammes: [
        {
          batch: { type: String, default: null },
          startDate: { type: Date, default: null },
          completionDate: { type: Date, default: null },
        },
      ],
      upgradingList: { type: String, default: null },
    },

    // Floors & Units
    highestFloor: { type: Number, default: null },
    totalFloors: { type: Number, default: null },
    residentialFloors: { type: Number, default: null },
    unitsPerFloor: { type: Number, default: null },
    totalUnits: { type: Number, default: null },
  },
  { timestamps: true },
);

HDBDataSchema.index({ location: '2dsphere' });

export const HDBDataModel = model<IHDBData>('HDBData', HDBDataSchema);
