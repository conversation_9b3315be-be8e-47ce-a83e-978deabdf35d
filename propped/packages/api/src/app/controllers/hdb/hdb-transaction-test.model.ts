import { Schema, model, type Document, type Model } from 'mongoose';

/**
 * TypeScript Interface for HDB Transaction Document
 */
export interface IHDB extends Document {
  month: string; // Format: YYYY-MM
  town: string;
  flat_type: string;
  block: number;
  street_name: string;
  storey_range: string;
  floor_area_sqm: number;
  flat_model: string;
  lease_commence_date: number;
  remaining_lease: string;
  resale_price: number;
}

/**
 * Mongoose Schema for HDB Transaction
 */
const hdbSchema = new Schema<IHDB>(
  {
    month: { type: String, required: true },
    town: { type: String, required: true },
    flat_type: { type: String, required: true },
    block: { type: Number, required: true },
    street_name: { type: String, required: true },
    storey_range: { type: String, required: true },
    floor_area_sqm: { type: Number, required: true },
    flat_model: { type: String, required: true },
    lease_commence_date: { type: Number, required: true },
    remaining_lease: { type: String, required: true },
    resale_price: { type: Number, required: true },
  },
  {
    collection: 'hdb',
  },
);

/**
 * Create and export the Mongoose model
 */
export const HDB: Model<IHDB> = model<IHDB>('HDB', hdbSchema);
