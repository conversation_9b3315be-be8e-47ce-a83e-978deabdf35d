import { type Types } from 'mongoose';
import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { checkEthnicQuota } from './check-ethnic-quota.fun';
import { BuildingTypeModel } from './hdb-building-type.model';
import { TownModel } from './hdb-towns.model';
import { HDBTransactionDump, type IHDBTransactionDump } from './hdb-transaction-dump.model';
import { HDBDataModel, type IHDBData } from './hdb.model';


export type HDBByLocationWithDistance = Pick<
  IHDBData,
  'title' | 'blockNumber' | 'hdbTown' | 'postalCode' | 'projectName' | 'unitNumbers' | 'location'
> & { distance: number };

@Auth()
export class HDBController {
  /**
   * Get all HDB data
   * @returns All HDB data with total count
   */
  @Compress()
  async getAll({ page, limit }: { page?: number; limit?: number }) {
    const hdbData = await HDBDataModel.find()
      .skip((page || 0) * (limit || 50))
      .limit(limit || 50)
      .select('title blockNumber hdbTown postalCode projectName unitNumbers location')
      .lean<IHDBData[]>();

    return { data: hdbData, total: await HDBDataModel.countDocuments() };
  }

  /**
   * Get HDB data by ID
   * @param params Request parameters containing HDB ID
   * @returns HDB data for the specified ID
   */
  @Compress()
  async getById(params: { id: string | Types.ObjectId }) {
    const hdbData = await HDBDataModel.findById(params.id)
      .select(
        'title blockNumber hdbTown postalCode projectName unitNumbers location totalFloors unitsPerFloor totalUnits floorPlans streetAddress',
      )
      .lean<IHDBData>();
    return { data: hdbData };
  }

  /**
   * Find within 2km radius of a location
   * @param body Request body containing latitude and longitude and optional minDistance, maxDistance and limit
   * @returns within 2km radius
   */
  @Compress()
  async findByLocation({
    latitude,
    longitude,
    minDistance,
    maxDistance,
    limit,
  }: {
    latitude: number;
    longitude: number;
    minDistance?: number;
    maxDistance?: number;
    limit?: number;
  }) {
    minDistance ||= 0;
    maxDistance ||= 1000;
    limit ||= 50;

    const hdb: HDBByLocationWithDistance[] = await HDBDataModel.aggregate([
      {
        $geoNear: {
          near: { type: 'Point', coordinates: [longitude, latitude] },
          distanceField: 'distance',
          minDistance,
          maxDistance,
          spherical: true,
        },
      },
      {
        $project: {
          title: 1,
          blockNumber: 1,
          hdbTown: 1,
          postalCode: 1,
          projectName: 1,
          unitNumbers: 1,
          location: 1,
          distance: 1,
        },
      },
      { $limit: limit },
    ]);
    return { success: true, center: { latitude, longitude }, hdb };
  }

  /**
   * Extract latest 10 HDB transactions based on street name, block number, and town
   * @param body Request body containing street_name, block_number, and town
   * @returns Latest 10 transactions
   */
  @Compress()
  async extractHDBTransactionData(body: { latitude: number; longitude: number }) {
    const { latitude, longitude } = body;
    try {
      const transactions: IHDBTransactionDump[] = await HDBTransactionDump.find({
        location: {
          $near: {
            $geometry: { type: 'Point', coordinates: [longitude, latitude] },
            $maxDistance: 10,
          },
        },
      })
        .sort({ month: -1 })
        .select(
          'month flat_type resale_price storey_range floor_area_sqm location block street_name',
        );

      if (transactions.length === 0) {
        return { success: false, message: 'No transactions found near this location' };
      }

      return { success: true, data: transactions };
    } catch (error) {
      console.error('Error fetching HDB transactions by location:', error);
      return { success: false, message: 'Internal Server Error' };
    }
  }

  /**
   * Get location coordinates for HDB transactions
   * @param body Request body containing array of objects with block_no and street_name
   * @returns Array of location coordinates
   */
  @Compress()
  async getHDBCoordinates(body: { transactions: { blockNo: string; streetName: string }[] }) {
    try {
      const { transactions } = body;
      const locationResults = [];

      for (const transaction of transactions) {
        try {
          const searchVal = `${transaction.blockNo} ${transaction.streetName}`;
          const response = await fetch(
            `https://www.onemap.gov.sg/api/common/elastic/search?searchVal=${encodeURIComponent(searchVal)}&returnGeom=Y&getAddrDetails=Y`,
            { method: 'GET', headers: { 'Content-Type': 'application/json' } },
          );

          const data = await response.json();

          if (data.results && data.results.length > 0) {
            const result = data.results[0];
            locationResults.push({
              blockNo: transaction.blockNo,
              streetName: transaction.streetName,
              latitude: Number(result.LATITUDE),
              longitude: Number(result.LONGITUDE),
              address: result.ADDRESS,
            });
          } else {
            console.log(
              `No location data found for ${transaction.blockNo} ${transaction.streetName}`,
            );
          }
        } catch (error) {
          console.error(
            `Error fetching location for ${transaction.blockNo} ${transaction.streetName}:`,
            error,
          );
        }
      }

      return { success: true, locations: locationResults };
    } catch (error) {
      console.error('Error in getHDBCoordinates:', error);
      return { success: false, error: 'Failed to fetch location coordinates' };
    }
  }

  /**
   * Find within 2km radius of a location
   * @param body Request body containing latitude and longitude and optional minDistance, maxDistance and limit
   * @returns within 2km radius
   */
  @Compress()
  async findHDBPropertiesNearUserLocation({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    const hdb: HDBByLocationWithDistance[] = await HDBDataModel.aggregate([
      {
        $geoNear: {
          near: { type: 'Point', coordinates: [longitude, latitude] },
          distanceField: 'distance',
          maxDistance: radius,
          spherical: true,
        },
      },
      {
        $project: {
          title: 1,
          blockNumber: 1,
          hdbTown: 1,
          postalCode: 1,
          projectName: 1,
          unitNumbers: 1,
          location: 1,
          distance: 1,
        },
      },
    ]);
    return { success: true, center: { latitude, longitude }, hdb };
  }

  @Compress()
  async filterDataByTown({ town }: { town: string }) {
    const townLower = town.toLowerCase();

    const matchingProperties = await HDBDataModel.find({
      hdbTown: { $regex: new RegExp(`^${townLower}$`, 'i') },
    }).select('title blockNumber hdbTown postalCode projectName unitNumbers location');

    return {
      message: `Found ${matchingProperties.length} properties in ${town}.`,
      data: matchingProperties,
    };
  }

  @Compress()
  async getAllBuildingTypes() {
    const buildingTypes = await BuildingTypeModel.find();
    return { data: buildingTypes };
  }

  @Compress()
  async getAllTowns() {
    const towns = await TownModel.find();
    return { data: towns };
  }

  @Compress()
  async getSellerEthnicGroupData({ blockId, street }: { blockId: string; street: string }) {

    console.log("API REQUEST",blockId,street);
    const groups = [
      { citizenship: 'SC', ethnicGrp: 'C' },
      { citizenship: 'SC', ethnicGrp: 'M' },
      { citizenship: 'SC', ethnicGrp: 'I' },
      { citizenship: 'NSPR', ethnicGrp: 'C' },
      { citizenship: 'NSPR', ethnicGrp: 'M' },
      { citizenship: 'NSPR', ethnicGrp: 'I' },
    ];

    const results = [];
    for (const { ethnicGrp, citizenship } of groups) {
      results.push(
        await checkEthnicQuota({ enquiryBy: 'SLR', blockId, street, ethnicGrp, citizenship }),
      );
    }
    console.log("SELLER ETHNIC DATA RESULTS",results);
    return { data: results };
  }

  @Compress()
  async getBuyerEthnicGroupData({ blockId, street }: { blockId: string; street: string }) {
    const groups = [
      { citizenship: 'SC', ethnicGrp: 'C' },
      { citizenship: 'SC', ethnicGrp: 'M' },
      { citizenship: 'SC', ethnicGrp: 'I' },
      { citizenship: 'NSPR', ethnicGrp: 'C' },
      { citizenship: 'NSPR', ethnicGrp: 'M' },
      { citizenship: 'NSPR', ethnicGrp: 'I' },
    ];
    const results = [];
    for (const { ethnicGrp, citizenship } of groups) {
      results.push(
        await this.checkEthnicQuota({ enquiryBy: 'BYR', blockId, street, ethnicGrp, citizenship }),
      );
    }

    console.log("BUYER ETHNIC DATA RESULTS",results);
    return { data: results };
  }

  async checkEthnicQuota(body: {
    enquiryBy: string;
    blockId: string;
    street: string;
    ethnicGrp: string;
    citizenship: string;
  }) {
    return { data: await checkEthnicQuota(body) };
  }
}
