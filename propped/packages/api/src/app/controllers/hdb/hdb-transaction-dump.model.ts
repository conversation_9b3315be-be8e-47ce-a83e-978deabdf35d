import { Schema, model, type Document, type Model } from 'mongoose';

export interface IHDBTransactionDump extends Document {
  month: string; // Format: YYYY-MM
  town: string;
  flat_type: string;
  block: string;
  street_name: string;
  storey_range: string;
  floor_area_sqm: number;
  flat_model: string;
  lease_commence_date: number;
  remaining_lease: string | null;
  resale_price: number;
  location: {
    type: { type: string; enum: ['Point']; default: 'Point' };
    coordinates: { type: [number]; default: null };
  };
}

/**
 * Mongoose Schema for HDB Transaction Dump
 */
const hdbTransactionDumpSchema = new Schema<IHDBTransactionDump>(
  {
    month: { type: String, required: true },
    town: { type: String, required: true },
    flat_type: { type: String, required: true },
    block: { type: String, required: true },
    street_name: { type: String, required: true },
    storey_range: { type: String, required: true },
    floor_area_sqm: { type: Number, required: true },
    flat_model: { type: String, required: true },
    lease_commence_date: { type: Number, required: true },
    remaining_lease: { type: String, default: null },
    resale_price: { type: Number, required: true },
    location: {
      type: { type: String, enum: ['Point'], default: 'Point', required: true },
      coordinates: { type: [Number], default: null },
    },
  },
  {
    collection: 'hdbtransactiondumps',
  },
);

// Create geospatial index for 'location'
hdbTransactionDumpSchema.index({ location: '2dsphere' });

/**
 * Create and export the Mongoose model
 */
export const HDBTransactionDump: Model<IHDBTransactionDump> = model<IHDBTransactionDump>(
  'hdbtransactiondumps',
  hdbTransactionDumpSchema,
);
