import { JsonWebTokenError, TokenExpiredError, verify } from 'jsonwebtoken';
import { unauthorizedException } from '../../errors/common.error';
import { AuthService } from '../controllers/auth/auth.service';
import { inject } from '../services/services.register';
import type { APIContext } from './guards';
import { SetMetaData, UseGuard, applyDecorators } from './guards';

const AuthKey = Symbol('AUTH');

export const SkipAuth: any = () => SetMetaData(AuthKey, { skip: true });

export const Auth: any = (opt: { skip?: boolean; roles?: number[] } = {}) =>
  applyDecorators([SetMetaData(AuthKey, opt), UseGuard(AuthGuard)]);

export const AuthGuard = async ({ c, getMeta }: APIContext) => {
  const opt: { skip?: boolean } = getMeta(AuthKey);
  if (opt.skip) return;
  const token = (c.req.header('Authorization') ?? '').replace('Bearer ', '');
  if (!token) throw unauthorizedException();
  const authService = inject(AuthService);
  const info = await authService.getTokenInfo(token);
  if (!info) throw unauthorizedException();
  try {
    verify(token, info.secret);
    c.set('user', () => info.user);
  } catch (err) {
    if (err instanceof TokenExpiredError) throw unauthorizedException('TokenExpiredError');
    if (err instanceof JsonWebTokenError) throw unauthorizedException('JsonWebTokenError');
    throw unauthorizedException();
  }
};
