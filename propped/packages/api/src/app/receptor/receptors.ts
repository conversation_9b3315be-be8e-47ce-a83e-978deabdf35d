import type { Context } from 'hono';
import { getOrInsertToMap } from '../common/fun';

type ReceptorHandler = (c: Context, response: any) => any;
type AnyMethod = (...rest: any[]) => any;

export const Receptors = new Map<AnyMethod, ReceptorHandler[]>();

export const pushToReceptors = (target: AnyMethod, handler: ReceptorHandler) => {
  const handlers = getOrInsertToMap(Receptors, target, []);
  handlers.push(handler);
};

export const processReceptors = async (c: Context, handlers: ReceptorHandler[], response: any) => {
  for (const handler of handlers) {
    const res = await handler(c, response);
    if (res instanceof Response) return res;
    response = res;
  }
  return response;
};
