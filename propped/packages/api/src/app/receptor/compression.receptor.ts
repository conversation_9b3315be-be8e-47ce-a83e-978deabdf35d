import type { Context } from 'hono';
import { promisify } from 'node:util';
import { brotliCompress, constants, gzip } from 'node:zlib';
import { pushToReceptors } from './receptors';

//#---------gZip-------------
const gz = promisify(gzip);
const compressGz = (x: string) => gz(x, { level: 5 });

//#---------Brotli-----------
const br = promisify(brotliCompress);
const compressBr = (x: string) =>
  br(x, {
    params: {
      [constants.BROTLI_PARAM_MODE]: constants.BROTLI_MODE_TEXT,
      [constants.BROTLI_PARAM_QUALITY]: 5,
    },
  });

const txtResponseCompressor = async (c: Context, raw: any) => {
  const acceptEncodings: string = c.req.header('accept-encoding');
  if (acceptEncodings.includes('br')) {
    const data = (await compressBr(JSON.stringify(raw))) as Buffer;
    c.header('Vary', 'Accept-Encoding');
    c.header('Content-Type', 'application/json');
    c.header('Content-Encoding', 'br');
    c.header('Content-Length', Buffer.byteLength(data).toString());
    return data;
  }
  if (acceptEncodings.includes('gzip')) {
    const data = (await compressGz(JSON.stringify(raw))) as Buffer;
    c.header('Vary', 'Accept-Encoding');
    c.header('Content-Type', 'application/json');
    c.header('Content-Encoding', 'gzip');
    c.header('Content-Length', Buffer.byteLength(data).toString());
    return data;
  }
  return raw;
};

export const Compress: any = () => {
  return <This, Args extends any[], Return>(target: (this: This, ...args: Args) => Return) => {
    pushToReceptors(target, async (c: Context, response: any) =>
      c.body(await txtResponseCompressor(c, response)),
    );
    return target;
  };
};
