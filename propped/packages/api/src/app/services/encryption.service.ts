import { createCipheriv, createDecipheriv, randomBytes } from 'node:crypto';

export class EncryptionEngine {
  public keyBuffer: Buffer;
  constructor(
    private key: string,
    private algo = 'aes-256-ctr',
  ) {
    this.keyBuffer = Buffer.from(this.key, 'hex');
  }

  public encrypt(text: string) {
    const iv = randomBytes(16);
    const cipher = createCipheriv(this.algo, this.keyBuffer, iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return `${iv.toString('base64')}$${encrypted.toString('base64')}`;
  }

  public decrypt(text: string) {
    const [textIv, textEncryptedData] = text.split('$');
    const iv = Buffer.from(textIv, 'base64');
    const encryptedText = Buffer.from(textEncryptedData, 'base64');
    const decipher = createDecipheriv(this.algo, this.keyBuffer, iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  }
}

export const DefaultEncryptionEngine = new EncryptionEngine(
  '77A2026949212F316F2F99C5D4547F8ED7F8429BF39A810BF5AE4972A773C4F6',
);
