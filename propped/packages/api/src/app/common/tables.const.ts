export class TableMeta {
  public id!: number;
  public name!: string;
}

const TablesList = {
  FILES: new TableMeta(),
  ROLES: new TableMeta(),
  SETTINGS: new TableMeta(),
  USER_ROLE: new TableMeta(),
  USER_TOKEN: new TableMeta(),
  USERS: new TableMeta(),
};

const numHash = (t: string) =>
  [...t].map((v, i) => (i + 1) * v.charCodeAt(0)).reduce((a, b) => a + b);

const genTablesName = ({ p, s }: { p: string; s: string }) => {
  Object.keys(TablesList).forEach((v) => {
    const key: keyof typeof TablesList = v as any;
    TablesList[key].id = numHash(key);
    TablesList[key].name = p + key.toLowerCase() + s;
  });
  const ids = Object.values(TablesList).map((v) => v.id);
  if (ids.length !== new Set(ids).size) throw new Error('Non Unique Id');
  return TablesList;
};

export const TABLES = genTablesName({ p: '', s: '' });
