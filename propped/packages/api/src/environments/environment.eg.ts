import chalk from 'chalk';

export const environment = {
  production: false,
  debug: true,
  verbose: true,
  // chalk: (x: string) => x,
  chalk,
  appName: 'PropTech',
  appUrl: 'https://proptech.webiers.com',
  appKey: 'local',
  appFullName: 'PropTech',
  appRun: { port: 3333, TZ: 'UTC' },
  mail: { from: { name: 'IT Team', address: '<EMAIL>' } },
  AWS: {
    S3V2: false as any,
    ACCESS_KEY_ID: 'ACCESS_KEY_ID',
    SECRET_ACCESS_KEY: 'SECRET_ACCESS_KEY',
    MAIN_FOLDER: 'MAIN_FOLDER',
    REGION: 'us-east-1',
    BUCKET: 'BUCKET',
    DEFAULT_REGION: 'us-east-1',
    URL: 'http://localhost:9000/',
    VIEW_URL: 'http://localhost:9000/',
  },
  mysql: {
    connectorPackage: 'mysql2',
    database: 'database',
    host: '127.0.0.1',
    port: 33061,
    username: 'root',
    password: 'password',
  },
  mongodb: {
    username: 'username',
    password: 'password',
    database: 'database',
    logDatabase: 'database-logs',
    host: 'localhost',
    port: 'port',
    auth: 'admin',
  },
  auth: {
    secret: 'secret',
    passwordSalt: 'passwordSalt',
    expireIn: { web: 8 * 60 * 60, android: 8 * 60 * 60, ios: 8 * 60 * 60 },
  },
  mailerConfig: { host: 'localhost', port: 10251 },
};
