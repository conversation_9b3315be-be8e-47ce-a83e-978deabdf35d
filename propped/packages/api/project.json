{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/packages/api", "format": ["cjs"], "bundle": false, "main": "packages/api/src/main.ts", "tsConfig": "packages/api/tsconfig.app.json", "assets": ["packages/api/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api:build"}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}}}