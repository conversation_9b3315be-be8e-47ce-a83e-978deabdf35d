import { ROLES } from '@lib/common/const/roles';
import { MFA_STATUS } from '@lib/common/enums/tfa-status';
import { type JData } from '@lib/common/j-data.interface';
import { addMinutes, isBefore } from 'date-fns';
import { badRequestException, unauthorizedException } from '../../../errors/common.error';
import { Dr<PERSON>zleHelper } from '../../common/drizzle-helper';
import { columnListToSelect, dataViewer, mapColumns, paginateQuery } from '../../common/paginator';
import { TABLES } from '../../common/tables.const';
import { Auth, SkipAuth } from '../../guards/auth.guard';
import { useLoginUser } from '../../hooks/auth.hooks';
import { Compress } from '../../receptor/compression.receptor';
import { type APIFileMeta, FileMgrService, type IHasPic } from '../../services/file-mgr.service';
import { PasswordHashEngine } from '../../services/hash.service';
import { inject } from '../../services/services.register';
import { type UserTableSchema, userTokenTable, usersTable } from './auth.entity';
import { AuthService, UserCacheBox, genOTP } from './auth.service';

@Auth()
export class AuthController {
  readonly #authService = inject(AuthService);
  readonly #fileMgrService = inject(FileMgrService);

  @Compress()
  async getAll() {
    return { data: await DrizzleHelper.findAll(usersTable) };
  }

  async getById(id: number) {
    console.log('USER ID',id);
    return { data: await DrizzleHelper.findOne(usersTable, { where: { id } }) };
  }

  @Compress()
  async getAllUsers(query: { jData: JData }) {
    const userTable = TABLES.USERS.name;
    const columnList: any = {
      id: { table: userTable, column: 'id' },
      name: { table: userTable, column: 'name' },
      username: { table: userTable, column: 'username' },
      email: { table: userTable, column: 'email' },
      createdAt: { table: userTable, column: 'createdAt' },
      meta: { table: userTable, column: 'meta' },
      pic: {
        table: '',
        column: `ANY_VALUE(${TABLES.FILES.name}.path)`,
        valueMapper: (path: any, user: any) =>
          path &&
          this.#fileMgrService.genFileUrl({ tableId: TABLES.USERS.id, itemId: user.id, path }),
      },
      isActive: { table: userTable, column: 'isActive', valueMapper: (v) => (v ? 'YES' : 'NO') },
      roles: {
        table: '',
        column: `JSON_ARRAYAGG(${TABLES.ROLES.name}.id)`,
        valueMapper: (roles: any) => roles && [...new Set(roles)].filter(Boolean),
      },
    };

    const filterList = {
      id: { table: userTable, column: 'id' },
      name: { table: userTable, column: 'name' },
      username: { table: userTable, column: 'username' },
      isActive: { table: userTable, column: 'isActive', valueMapper: (v) => Number(v === 'YES') },
    };

    const { filters, configs } = dataViewer({ data: query.jData, filterList });

    const sqlQuery = /* sql */ `
SELECT ${columnListToSelect(columnList).join(', ')}
FROM ${userTable}
LEFT JOIN ${TABLES.FILES.name} ON ${TABLES.FILES.name}.id = ${userTable}.pic
LEFT JOIN ${TABLES.USER_ROLE.name} ON ${TABLES.USER_ROLE.name}.userId = ${userTable}.id
LEFT JOIN ${TABLES.ROLES.name} ON ${TABLES.ROLES.name}.id = ${TABLES.USER_ROLE.name}.roleId
${filters.sql ? `WHERE ${filters.sql}` : ''}
GROUP BY ${userTable}.id
`;

    const paginatedData = await paginateQuery(sqlQuery, configs, userTable);
    if (paginatedData.data.length)
      paginatedData.data = paginatedData.data.map(mapColumns(paginatedData.data[0], columnList));
    return { data: paginatedData.data, meta: paginatedData.meta };
  }

  async create(body: Partial<UserTableSchema>) {
    body.password = await PasswordHashEngine.make(body.password);
    return { data: await DrizzleHelper.create(usersTable, body) };
  }

  async update(query: { id: number }, body: Partial<UserTableSchema>) {
    if (body.password) body.password = await PasswordHashEngine.make(body.password);
    else delete body.password;
    await DrizzleHelper.update(usersTable, { id: query.id }, body);
    return { success: true };
  }

  @SkipAuth()
  async login(body: { username?: string; email?: string; password: string; device?: string }) {
    const { username, email, password } = body;
    const loginIdentifier = username || email;

    if (!loginIdentifier || !password) throw badRequestException('Missing username/email or password');

    const user = await this.#authService.validateUser({ username: loginIdentifier, password });
    if (!user) throw unauthorizedException('Invalid credentials');

    // Check if account is not active (for email verification flow)
    if (!user.isActive) {
      return {
        message: 'Account not activated. Please verify your email.',
        is_active: 0,
        user: {
          id: user.id,
          name: user.name,
          username: user.username,
          email: user.email,
          is_email_verified: false,
          isActive: false,
          role_id: ROLES.USER.id
        }
      };
    }

    // Handle TFA if required
    if (user.meta?.TFARequire === true) {
      if (user.secret?.totp) return { MFAStatus: MFA_STATUS.TOTP_REQUIRED };
      this.sendTFAEmail({ username: user.username });
      return { MFAStatus: MFA_STATUS.OTP_SENT };
    }

    // Normal login flow
    const loginResult = this.#authService.login(user, body.device);

    return {
      message: 'Login successful',
      token: loginResult.accessToken,
      authExpireTime: '24h', // or calculate actual expiry based on device
      user: {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
        is_email_verified: true,
        isActive: true,
        role_id: ROLES.USER.id
      }
    };
  }

  async getProfile() {
    const user = useLoginUser();
    if (user.roles.length === 0) user.roles.push({ id: 1, role: 'user' } as any);
    const pic = await this.#authService.thumbnail.getThumbnailUrl(user);
    return { data: { ...user.toJSON(), pic } };
  }

  @SkipAuth()
  async forgotPassword({ email }: { email: string }) {
    const user = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (!user) throw badRequestException('Please, check your email and try again');
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email }, { token });
      this.#authService.sendForgotPasswordMail(user, token);
      return { success: true };
    }
    await DrizzleHelper.create(userTokenTable, { token, email: user.email });
    this.#authService.sendForgotPasswordMail(user, token);
    return { success: true };
  }

  @SkipAuth()
  async sendTFAEmail({ username }: { username: string }) {
    const user = await this.#authService.searchUserForAuth(username);
    if (!user) throw badRequestException('Please, check your email and try again');
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, {
      where: { email: user.email },
    });
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email: user.email }, { token });
      this.#authService.sendTFAMail(user, token);
      return { success: true };
    }
    await DrizzleHelper.create(userTokenTable, { token, email: user.email });
    this.#authService.sendTFAMail(user, token);
    return { success: true };
  }

  @SkipAuth()
  async verifyTFAEmail(body: { username: string; token: string; device?: string }) {
    const user = await this.#authService.searchUserForAuth(body.username);
    if (!user) throw badRequestException('Please, check your email and try again');
    const userToken = await DrizzleHelper.findOne(userTokenTable, { where: { email: user.email } });
    const token = body.token;
    if (userToken.token === token && isBefore(new Date(), addMinutes(userToken.updatedAt, 5))) {
      return this.#authService.login(user, body.device);
    }
    throw badRequestException('Invalid Token or Token Time Expired');
  }

  userTFA({ username }: { username: string }) {
    return this.#authService.isTFARequire(username);
  }

  async setTOTPSecretTFA(data: { secret: string }) {
    const user = useLoginUser();
    const { secret } = data;
    await this.#authService.setTOTPSecret(user.id, secret);
    return { success: true };
  }

  @SkipAuth()
  async verifyTOTP(data: { token: string; username: string; device?: string }) {
    const user = await this.#authService.searchUserForAuth(data.username);
    if (!user) throw badRequestException('User not found');
    const res = this.#authService.verifyTOTP(user, data.token);
    if (res) return this.#authService.login(user, data.device);
    throw badRequestException('Invalid Token or Token Time Expired');
  }

  getTFAStatus() {
    const user = useLoginUser();
    return Promise.resolve({
      data: { TFARequire: user.meta.TFARequire, TOTPActive: !!user.secret.totp },
    });
  }

  async setTFA(data: { enable: boolean }) {
    const user = useLoginUser();
    await this.#authService.setTFARequire(user.id, data.enable);
    return { success: true };
  }

  async removeTOTP() {
    const user = useLoginUser();
    await this.#authService.removeTOTP(user.id);
    return { success: true };
  }

  @SkipAuth()
  async resetPwd(body: { email: string; token: string; password: string }) {
    const user = await DrizzleHelper.findOne(usersTable, { where: { email: body.email } });
    if (!user) throw badRequestException('User not found');
    const userToken = await DrizzleHelper.findOne(userTokenTable, { where: { email: body.email } });
    if (
      userToken.token === body.token &&
      isBefore(new Date(), addMinutes(userToken.updatedAt, 10))
    ) {
      const password = await PasswordHashEngine.make(body.password);
      await DrizzleHelper.update(usersTable, { id: user.id }, { password });
      await DrizzleHelper.destroy(userTokenTable, { email: body.email });
      UserCacheBox.delete(user.id);
      return { success: true };
    }
    throw badRequestException('Invalid Token or Token Time Expired');
  }

  async updateEmail(token: { email: string; token: string }) {
    const activeUser = useLoginUser();
    const email = token.email;
    const user = await DrizzleHelper.findOne(usersTable, { where: { id: activeUser.id } });
    const userToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });
    if (
      userToken.token === token.token &&
      isBefore(new Date(), addMinutes(userToken.updatedAt, 10))
    ) {
      await DrizzleHelper.update(usersTable, { id: user.id }, { email });
      UserCacheBox.delete(user.id);
      return { success: true };
    }
    throw badRequestException('Invalid Token or Token Time Expired');
  }

  @SkipAuth()
  async verifyEmail({ email }: { email: string }) {
    const already = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (already) throw badRequestException('Email Already registered.');
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email }, { token });
      this.#authService.sendEmailVerificationMail(email, token);
      return { success: true };
    }
    await DrizzleHelper.create(userTokenTable, { token, email });
    this.#authService.sendEmailVerificationMail(email, token);
    return { success: true };
  }

  @SkipAuth()
  async register(body: { name: string; username: string; email: string; password: string; authProvider?: string }) {
    const { name, username, email, password, authProvider } = body;

    // Check if email already exists
    const existingUser = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (existingUser) throw badRequestException('Email already registered');

    // Check if username already exists
    const existingUsername = await DrizzleHelper.findOne(usersTable, { where: { username } });
    if (existingUsername) throw badRequestException('Username already taken');

    // Hash password
    const hashedPassword = await PasswordHashEngine.make(password);

    // Create user with inactive status
    const userData = {
      name,
      username,
      email,
      password: hashedPassword,
      isActive: false,
      meta: { TFARequire: false },
      secret: {}
    };

    const newUser = await DrizzleHelper.create(usersTable, userData);

    // Add default user role
    await this.#authService.roles.add(newUser.insertId, [ROLES.USER.id]);

    // Generate OTP for email verification
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email }, { token });
    } else {
      await DrizzleHelper.create(userTokenTable, { token, email });
    }

    // Send verification email
    this.#authService.sendEmailVerificationMail(email, token);

    return {
      message: 'Registration successful. Please check your email for verification.',
      user: {
        id: newUser.insertId,
        name,
        username,
        email,
        is_email_verified: false,
        isActive: false,
        role_id: ROLES.USER.id
      },
      otp: token, // For development/testing - remove in production
      email_sent: true
    };
  }

  @SkipAuth()
  async verifyRegisterOtp(body: { email: string; otp: string }) {
    const { email, otp } = body;

    const user = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (!user) throw badRequestException('User not found');

    const userToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });
    if (!userToken) throw badRequestException('No verification token found');

    // Check if OTP is valid and not expired (5 minutes)
    if (userToken.token === otp && isBefore(new Date(), addMinutes(userToken.updatedAt, 5))) {
      // Activate user account
      await DrizzleHelper.update(usersTable, { id: user.id }, { isActive: true });

      // Remove the used token
      await DrizzleHelper.destroy(userTokenTable, { email });

      // Clear user cache
      UserCacheBox.delete(user.id);

      // Generate login token
      const loginResult = this.#authService.login(user);

      return {
        message: 'Email verified successfully',
        token: loginResult.accessToken,
        authExpireTime: '24h', // or calculate actual expiry
        user: {
          id: user.id,
          name: user.name,
          username: user.username,
          email: user.email,
          is_email_verified: true,
          isActive: true,
          role_id: ROLES.USER.id
        }
      };
    }

    throw badRequestException('Invalid OTP or OTP has expired');
  }

  @SkipAuth()
  async regenerateOtp(body: { email: string }) {
    const { email } = body;

    const user = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (!user) throw badRequestException('User not found');

    // Generate new OTP
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, { where: { email } });

    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email }, { token });
    } else {
      await DrizzleHelper.create(userTokenTable, { token, email });
    }

    // Send verification email
    this.#authService.sendEmailVerificationMail(email, token);

    return {
      message: 'New verification code sent to your email',
      otp: token, // For development/testing - remove in production
      otpExpireTime: '5 minutes',
      email_sent: true
    };
  }

  @SkipAuth()
  async activeAccount(body: { email: string }) {
    const { email } = body;

    const user = await DrizzleHelper.findOne(usersTable, { where: { email } });
    if (!user) throw badRequestException('User not found');

    if (user.isActive) {
      // User is already active, just return login token
      const loginResult = this.#authService.login(user);

      return {
        message: 'Account is already active',
        token: loginResult.accessToken,
        authExpireTime: '24h',
        user: {
          id: user.id,
          name: user.name,
          username: user.username,
          email: user.email,
          is_email_verified: true,
          isActive: true,
          role_id: ROLES.USER.id
        }
      };
    }

    // Activate the account
    await DrizzleHelper.update(usersTable, { id: user.id }, { isActive: true });

    // Clear user cache
    UserCacheBox.delete(user.id);

    // Generate login token
    const loginResult = this.#authService.login(user);

    return {
      message: 'Account activated successfully',
      token: loginResult.accessToken,
      authExpireTime: '24h',
      user: {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
        is_email_verified: true,
        isActive: true,
        role_id: ROLES.USER.id
      }
    };
  }

  async updateUserProfile({ id }: { id: number }, user: any) {
    if (!id) throw badRequestException('Invalid User ID');
    const allData = await DrizzleHelper.findOne(usersTable, { where: { id } });
    if (user.password) {
      const oldPassword = await PasswordHashEngine.check(user.oldPassword, allData.password);
      if (!oldPassword) throw badRequestException('Please check the old password!!!');
    }
    await this.#authService.updateUser(id, user);
    return { success: true };
  }

  async updateProfile(
    { id }: { id: number },
    user: Partial<UserTableSchema & { roleId?: number[] }>,
  ) {
    const data = await this.#authService.updateUser(id, user);
    return { data };
  }

  @Compress()
  public getAllUser() {
    return DrizzleHelper.findAll(usersTable, { select: ['id', 'name', 'pic'] });
  }

  async createUser(user: any) {
    const isValid = await this.#authService.verifyToken(user);
    if (!isValid) throw badRequestException('Invalid Token or Token Time Expired');
    const newUser = await this.#authService.createUser(user);
    await this.#authService.roles.set(newUser.user.id, [(user as any).for]);
    return { data: newUser };
  }

  async updateUser(query: { id: number }, user: Partial<UserTableSchema & { roleId?: number[] }>) {
    const id = query.id;
    await this.#authService.updateUser(id, user);
    return { success: true };
  }

  @Auth({ roles: [ROLES.ADMIN.id, ROLES.SUPER_ADMIN.id] })
  async updatePwd(query: { id: number }, data: { password: string }) {
    const id = query.id;
    UserCacheBox.delete(id);
    const user = await DrizzleHelper.findOne(usersTable, { where: { id } });
    if (!user) throw badRequestException('User not found');
    user.password = await PasswordHashEngine.make(data.password);
    await DrizzleHelper.update(usersTable, { id }, { password: user.password });
    return { data: user };
  }

  async getSingleUser(query: { id: number }) {
    const id = query.id;
    const user = await this.#authService.getUserWithRoles(id);
    if (!user) throw badRequestException('User not found');
    (user as any).pic = await this.#authService.thumbnail.getThumbnailUrl(user as IHasPic);
    user.password = undefined;
    user.secret = undefined;
    return user;
  }

  async getUserInfo(query: { id: number }) {
    const id = query.id;
    const user = await DrizzleHelper.findOne(usersTable, { where: { id } });
    if (!user) throw badRequestException('User not found');
    const { name, username } = user;
    const pic = await this.#authService.thumbnail.getThumbnailUrl(user as IHasPic);
    return { data: { id, name, username, pic } };
  }

  async remove({ id }: { id: number }) {
    UserCacheBox.delete(id);
    await DrizzleHelper.destroy(usersTable, { id });
    return { success: true };
  }

  async setThumbnail(query: { id: number }, data: { file: APIFileMeta }) {
    const id = query.id;
    UserCacheBox.delete(id);
    const item = await DrizzleHelper.findOne(usersTable, { where: { id } });
    const res = await this.#authService.thumbnail.setThumbnail(item, data.file);
    return { data: res };
  }

  async createUserByAdmin(user: any) {
    return { data: await this.#authService.createUser(user) };
  }
}
